import { useState } from "react";
import { useDispatch } from "react-redux";
import { ApiPostAuth } from "../../pages/api/api";
import { ExclaimCircle } from "../svgs";
import Dialog from "../Dialog";

const NotifyMailsModal = ({ show, setShow, user }: any) => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);

  const handleSuccess = () => {
    dispatch({
      type: "notify",
      payload: {
        error: false,
        message:
          show === "password"
            ? "Reset Password Link has been sent successfully."
            : "Welcome email has been resent successfully.",
      },
    });
    setShow("");
  };

  const sendEmail = () => {
    setLoading(true);
    ApiPostAuth("/customer/password-reset", {
      emailId: user.email,
    })
      .then((res) => {
        setLoading(false);
        setShow("");
        dispatch({
          type: "notify",
          payload: {
            error: false,
            message: res.data.message,
          },
        });
      })
      .catch((err) => {
        setLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: err.response.data.message,
          },
        });
      });
  };

  return (
    <Dialog
      confirmButtonText={
        show === "password"
          ? "Yes, Send link"
          : show === "welcome"
            ? "Yes, Re-send"
            : ""
      }
      open={show}
      headerIcon={<ExclaimCircle />}
      headerTitle={
        show === "password"
          ? "Send Password Reset Link ?"
          : show === "welcome"
            ? "Re-send welcome email?"
            : ""
      }
      onClose={() => {
        setShow("");
      }}
      headerSubtitle={`Email: ${user?.email}`}
      confirmButtonOnClick={() => handleSuccess()}
      size="sm"
      cancelButtonText="Cancel"
    ></Dialog>
  );
};

export default NotifyMailsModal;
