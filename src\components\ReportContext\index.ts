import { createContext } from "react";
import { Action, State, initialReportState } from "../utils/reportReducer";

type ReportContextType = {
	state: State;
	actions: { dispatch: (value: Action) => void };
};

export const ReportContext = createContext<ReportContextType>({
	state: initialReportState,
	actions: {} as { dispatch: (value: Action) => void },
});

export const ReportContextProvier = ReportContext.Provider;
