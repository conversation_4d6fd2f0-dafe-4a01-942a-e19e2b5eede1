import styles from "./support-ticket-modal.module.scss";
import Modal from "../Modal";
import { AddUser, Plus } from "../svgs";
import { Input } from "../Input";
import { useState } from "react";
import { validateAll } from "indicative/src/Validator";
import { useDispatch } from "react-redux";
import {
  clearInput,
  createStateObject,
  getMessages,
  getRules,
  handleInputChange,
  labels,
  placeholders,
} from "../utils/InputHandlers";
import Button from "../Button";
import { useNavigate } from "react-router-dom";
import SelectDropdown from "../SelectDropdown";
import { countryListAlpha2 } from "../utils/countryList";
import Priority from "../Priority";
import TextArea from "../TextArea";
import { ApiPostAuth } from "src/pages/api/api";
import Category from "../Category";

const fields = ["title", "body", "priority", "category", "status"];
const rules = getRules(fields);
const messages = getMessages(fields);

const CreateSupportTicketModal = ({
  show,
  setShow,
  customerEmail,
  repopulate,
  close,
}: any) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const options = {
    agentId: [{ name: "test", id: 201006510951 }].map((agent: any) => {
      return {
        label: agent.name,
        value: agent.id,
      };
    }),
    category: [
      {
        value: "Mobilise",
        label: <Category category="Mobilise" />,
      },
      {
        value: "Technical Support",
        label: <Category category="Technical Support" />,
      },
      {
        value: "Finance",
        label: <Category category="Finance" />,
      },
      {
        value: "Product Management",
        label: <Category category="Product Management" />,
      },
    ],
    status: [
      {
        label: "Open",
        value: 2,
      },
      {
        label: "Pending",
        value: 3,
      },
      {
        label: "Resolved",
        value: 4,
      },
      {
        label: "Closed",
        value: 5,
      },
    ],
    priority: [
      {
        label: <Priority priority="Urgent" />,
        value: 4,
      },
      {
        label: <Priority priority="High" />,
        value: 3,
      },
      {
        label: <Priority priority="Medium" />,
        value: 2,
      },
      {
        label: <Priority priority="Low" />,
        value: 1,
      },
    ],
  } as any;

  const [data, setData] = useState(createStateObject(fields));

  const reset = () => {
    setData(createStateObject(fields));
  };

  const [loading, setLoading] = useState(false);

  const handleSelectChange = (prop: string, option: any) => {
    setData({
      ...data,
      [prop]: option,
      errors: {
        ...data.errors,
        [prop]: "",
      },
    });
  };

  const createTicket = () => {
    setLoading(true);
    console.log(data);
    ApiPostAuth("/tickets/support", {
      email: customerEmail,
      title: data.title,
      body: data.body,
      status: data.status.value,
      category: data.category.value,
      priority: data.priority.value,
    })
      .then((response) => {
        setLoading(false);
        repopulate();
        setShow(false);
        close();
        setTimeout(reset, 300);
        dispatch({
          type: "notify",
          payload: {
            error: false,
            message: response.data.message,
          },
        });
      })
      .catch((error) => {
        setLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: error.response.data.message,
          },
        });
      });
  };

  return (
    <Modal
      saveButton={<>Create Support Ticket</>}
      cancelButton="Cancel"
      image="/edit_user_graphic.svg"
      show={show}
      proceed={createTicket}
      close={() => {
        setShow(false);
        setTimeout(() => {
          reset();
        }, 300);
      }}
      loading={loading}
      fullSize
      title="Create Support Ticket"
      clearContainer
    >
      <div className={styles.main}>
        {fields.map((prop) => {
          if (prop === "title") {
            return (
              <Input
                white
                key={prop}
                label={labels[prop]}
                placeholder={placeholders[prop]}
                value={data[prop]}
                onChange={(e: any) => {
                  handleInputChange(prop, e, data, setData);
                }}
                error={data.errors[prop]}
                clear={() => {
                  clearInput(prop, setData);
                }}
                disabled={loading}
                onKeyDown={createTicket}
              />
            );
          } else if (prop === "body") {
            return (
              <TextArea
                white
                key={prop}
                label={labels[prop]}
                placeholder={placeholders[prop]}
                value={data[prop]}
                onChange={(e: any) => {
                  handleInputChange(prop, e, data, setData);
                }}
                error={data.errors[prop]}
                clear={() => {
                  clearInput(prop, setData);
                }}
                disabled={loading}
                autoHeight
              />
            );
          } else {
            return (
              <SelectDropdown
                dropDownMaxHeight={
                  prop === "priority" ? 150 : prop === "category" ? 250 : ""
                }
                key={prop}
                value={data[prop]}
                error={data.errors[prop]}
                onChange={(option: any) => {
                  handleSelectChange(prop, option);
                }}
                options={options[prop]}
                disabled={loading}
                placeholder={placeholders[prop]}
              />
            );
          }
        })}
      </div>
    </Modal>
  );
};

export default CreateSupportTicketModal;
