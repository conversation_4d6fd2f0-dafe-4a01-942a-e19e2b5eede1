import Dialog from "@/components/Dialog";
import { PlusCircle } from "@/components/svgs";
import {
  clearInput,
  createStateObject,
  handleInputChange,
  labels,
} from "@/components/utils/InputHandlers";
import { useState } from "react";
import styles from "./create-product-modal.module.scss";
import { Input } from "@/components/Input";
import SelectInput from "@/components/SelectInput";
import ToggleButtonGroup from "@/components/ToggleButtonGroup";
import TextArea from "@/components/TextArea";

type CreateProductModalProps = {
  open: boolean;
  onClose: () => void;
};

const CreateProductModal = ({ open, onClose }: CreateProductModalProps) => {
  const fields = [
    "family",
    "wholesaleName",
    "wholesalePrice",
    "sizeGB",
    "talkAndText",
    "approach",
    "billing",
    "addToRetail",
    "retailName",
    "description",
    "retailPrice",
    "status",
  ];
  const [formData, setFormData] = useState(createStateObject(fields));

  return (
    <Dialog
      open={open}
      onClose={onClose}
      size="sm"
      headerIcon={<PlusCircle />}
      headerTitle="Create Product"
      confirmButtonText="Create Product"
      confirmButtonOnClick={onClose}
      cancelButtonText="Cancel"
    >
      <div className={styles.formContainer}>
        {fields.map((field) => {
          if (["talkAndText", "approach", "billing"].includes(field)) {
            return (
              <SelectInput
                key={"product-" + field}
                options={selectOptionsByField[field]}
                label={labels[field]}
                selected={formData[field]}
                onChange={(e: any) => {
                  handleInputChange(
                    field,
                    { target: { value: e } },
                    formData,
                    setFormData
                  );
                }}
                error={formData.errors[field]}
                infoTooltipText
              />
            );
          } else if (["status", "addToRetail"].includes(field)) {
            return (
              <ToggleButtonGroup
                selected={formData[field]}
                options={toggleButtonGroupOptionsByField[field]}
                label={labels[field]}
                onChange={(value) => {
                  handleInputChange(
                    field,
                    { target: { value } },
                    formData,
                    setFormData
                  );
                }}
              />
            );
          } else if (["description"].includes(field)) {
            return (
              <TextArea
                label={labels[field]}
                value={formData[field]}
                clear={() => clearInput(field, setFormData)}
                onChange={(e: any) => {
                  handleInputChange(field, e, formData, setFormData);
                }}
              />
            );
          } else {
            return (
              <Input
                key={"product-" + field}
                label={labels[field]}
                value={formData[field]}
                onChange={(e: any) => {
                  handleInputChange(field, e, formData, setFormData);
                }}
                error={formData.errors[field]}
                clear={() => {
                  clearInput(field, setFormData);
                }}
                infoTooltipText
              />
            );
          }
        })}
      </div>
    </Dialog>
  );
};

export default CreateProductModal;

const selectOptionsByField: Record<string, any> = {
  talkAndText: ["-", "Unlimited"],
  approach: ["Overage", "Flat"],
  billing: ["Flat", "First GB", "Additional Charges"],
};

const toggleButtonGroupOptionsByField: Record<string, any> = {
  status: [
    {
      label: "Active",
      key: "active",
    },
    {
      label: "Inactive",
      key: "inactive",
    },
  ],
  addToRetail: [
    {
      label: "True",
      key: "true",
    },
    {
      label: "False",
      key: "false",
    },
  ],
};
