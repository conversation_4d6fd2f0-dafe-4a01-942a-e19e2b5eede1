@use "../styles/theme.scss" as *;

@mixin table {
  .tableContainer {
    overflow-x: auto;
    border: 1px solid var(--gray-100);
    border-radius: 16px;

    @media (min-width: 1440px) {
      table {
        table-layout: fixed;
      }
    }
  }

  table {
    border-spacing: 0px;
    text-align: start;
    color: #061632;
    border-radius: 14px;
    width: 100%;
    border-collapse: collapse;
    table-layout: auto;

    th {
      padding: 10px 12px 12px 12px;
      text-align: start;
      background: $lightblue;
      font-size: 12px;
      font-weight: 700;
      line-height: 14px;
      white-space: nowrap;
      border: 1px solid $lightgrey;
      position: relative;
      text-overflow: ellipsis;
      overflow-x: hidden;
    }

    tbody {
      td {
        padding: 2px 12px;
        height: 40px;
        font-size: 12px;
        font-weight: 400;
        line-height: 15px;
        background: #fff;
        border: 1px solid $lightgrey;
        text-overflow: ellipsis;
        -webkit-line-clamp: 1;
        line-clamp: 1;
        white-space: nowrap;
        overflow-x: hidden;

        & > * {
          text-overflow: ellipsis;
          -webkit-line-clamp: 1;
          line-clamp: 1;
          white-space: nowrap;
          overflow-x: hidden;
        }
      }
    }

    /* curved edges - start */
    tr {
      td:first-of-type, th:first-of-type {
        border-left: none;
      }
      td:last-of-type, th:last-of-type {
        border-right: none;
      }
    }

    tr:first-of-type {
      td, th {
        border-top: none;
      }
    }

    tr:last-of-type {
      td {
        border-bottom: none;
      }
    }
    /* curved edges - end */

    .resizer {
      position: absolute;
      top: 0;
      height: 100%;
      width: 5px;
      background: #2e70e5;
      cursor: ew-resize;
      user-select: none;
      touch-action: none;
    }

    .resizer.ltr {
      right: 0;
    }

    .resizer.rtl {
      left: 0;
    }

    .resizer.isResizing {
      opacity: 1;
    }

    @media (hover: hover) {
      .resizer {
        opacity: 0;
      }

      *:hover > .resizer {
        opacity: 1;
      }
    }
  }
}

@mixin curvedEdgesTwoColumnTableContainer {
  border: 1px solid var(--gray-100);
  border-radius: 16px;
  overflow: hidden;
  padding: 0;
  
  table {
    border-collapse: collapse;
    border: none;
    overflow: hidden;

    td {
      border: 1px solid var(--gray-100);
    }

    tr {
      td:first-of-type, th:first-of-type {
        border-left: none;
      }
      td:last-of-type, th:last-of-type {
        border-right: none;
      }
    }


    tr:first-of-type {
      td, th {
        border-top: none;
      }
    }

    tr:last-of-type {
      td {
        border-bottom: none;
      }
    }
  }
}