@use "../../styles/theme.scss" as *;

@mixin resizing($bp1, $bp2, $bp3) {
  .topPanel {
    @media (max-width: $bp1) {
      flex-direction: column;
      align-items: flex-start;
    }
    .order {
      @media (max-width: $bp1) {
        margin: 0 0 16px 0;
      }
    }
  }
  .topGrid {
    @media (max-width: $bp2) {
      flex-direction: column-reverse;
    }
  }
  .exportContainer {
    @media (max-width: $bp2) {
      align-self: flex-end;
      margin: 0 0 12px 0;
    }
  }
  .filters {
    @media (max-width: $bp3) {
      flex-wrap: wrap;
    }
  }
}

.main {
  width: 100%;
  background-color: #fff;
  border-radius: 24px;
  padding: 17px 40px 24px 40px;
  &.ticketOpen {
    @include resizing(1945px, 1705px, 1550px);
  }
  &.sidebarOpen {
    @include resizing(1405px, 1165px, 1000px);
  }
}

.topMain {
  display: flex;
  flex-direction: column;
  gap: 32px;
  width: 100%;
}

.topGrid {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.topPanel {
  display: flex;
  justify-content: start;
  align-items: center;
  padding-top: 3px;
  width: 100%;
  white-space: nowrap;
}

.exportContainer {
  align-self: flex-start;
  padding-top: 3px;
}

.ticketsSelected {
  display: flex;
  align-items: center;
  padding-left: 32px;
  justify-content: space-evenly;
  width: 100%;
  margin-right: 32px;
}

.ticketsNumber {
  margin-left: 18px;
  font-size: 14px;
}

.bulkEdit {
  display: flex;
  align-items: center;
  margin-left: 61px;
  gap: 24px;
  margin-right: auto;
  flex-grow: 1;
}

.bulkEditButtons {
  display: flex;
  align-items: center;
  margin-left: auto;
  gap: 20px;
}

.change {
  font-size: 14px;
}

.order {
  display: flex;
  gap: 12px;
  align-items: center;
  width: 203px;
  margin-right: 36px;
}

.filters {
  display: flex;
  gap: 12px;
  align-items: center;
}

.removeFilters {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  align-items: center;
}

.label {
  font-size: 14px;
  line-height: 21px;
  margin-right: 1px;
}

.divider {
  width: 100%;
  border-bottom: 1px solid $disabled;
  margin-top: 12px;
}

.tickets {
  padding-top: 32px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  overflow: auto;
}

.clearAll {
  color: $orange;
  font-weight: 500;
  font-size: 14px;
  line-height: 21px;
  padding-left: 4px;
  transition: all 0.2s ease;
  align-self: flex-start;
  margin-top: 5px;
  cursor: pointer;
  &:hover {
    color: $dark-orange;
  }
}

/*******   BULK EDIT     *******/

.bulkTitle {
  font-weight: 700;
  font-size: 24px;
  line-height: 36px;
  color: $black;
  max-width: 304px;
  text-align: center;
  margin: 0;
  margin-bottom: 44px;
}

.bulkModalContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.bulkReminderName {
  width: 75px;
  margin-left: auto;
  font-size: 14px;
}

.bulkReminderValue {
  height: 41px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f7f6f6;
  border-radius: 8px;
  padding: 0 10px;
}

.bulkReminderGrid {
  width: 100%;
  display: grid;
  grid-template-columns: 1fr 1fr;
  column-gap: 55px;
  row-gap: 24px;
  justify-items: start;
  align-items: center;
}

.noneFound {
  display: grid;
  width: 100%;
  min-height: 510px;
  align-items: center;
  justify-content: center;
  padding-top: 55px;
  img,
  h3 {
    grid-area: 1 / 1 / 2 / 2;
    font-size: 24px;
    font-weight: 700;
    line-height: 36px;
  }
  h3 {
    width: 100%;
    text-align: center;
  }
}
