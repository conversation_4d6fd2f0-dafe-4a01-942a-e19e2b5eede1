import { countryListAlpha2 } from "../utils/countryList";
import styles from "./country-display.module.scss";

interface CountryDisplayProps {
  country: string;
  width?: number;
  height?: number;
}

const CountryDisplay = ({ country, width = 20, height = 20 }: CountryDisplayProps) => {
  return (
    <div className={styles.country} style={{ gridTemplateColumns: `${width}px 1fr` }}>
      <div
        className={styles.flag}
        style={{
          backgroundImage: `url(https://public-gist-bucket.s3.eu-north-1.amazonaws.com/flag-icons/png/${country}.png)`,
          width: `${width}px`,
          height: `${height}px`
        }}
      />
      {
        countryListAlpha2.filter(
          (countryItem: any) => country === countryItem.code
        )[0]?.name
      }
    </div>
  );
};

export default CountryDisplay;
