@import "../../../styles/mixins.module.scss";

.panel {
  @include panel;
  padding-bottom: 22px;
  margin-inline: 16px;
  margin-top: 16px;

  h2 {
    font-size: 16px;
    font-weight: 700;
    line-height: 22px;
    margin-bottom: 16px;
    padding-top: 6px;
  }
}

.container {
  margin-top: 24px;
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  gap: 16px;
}

.columnsContainer {
  margin-top: 24px;
  display: flex;
  gap: 16px;
}

.column {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.faqItem {
  border-radius: 12px;
  background: var(--primary-50-half);
  overflow: hidden;
}

.questionButton {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border: none;
  background: none;
  cursor: pointer;
  text-align: left;
  font-size: 14px;
  font-weight: 700;
  color: var(--primary-900);
}

.arrow {
  transition: transform 0.3s ease;
  display: flex;
  align-items: center;
  color: var(--primary-500);

  &.rotated {
    transform: rotate(180deg);
  }
}

.answer {
  padding: 0 24px 16px;
  color: var(--primary-900);
  line-height: 1.5;
  font-size: 14px;
}
