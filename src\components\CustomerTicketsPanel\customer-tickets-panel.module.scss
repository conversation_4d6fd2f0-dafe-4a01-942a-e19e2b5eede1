@use "../../styles/theme.scss" as *;

.container {
  width: 100%;
  flex-grow: 1;
  overflow-y: auto;
  min-height: 0;
  display: flex;
  flex-direction: column;
  align-items: start;
}
.panelTopBar {
  width: 100%;
  display: flex;
  align-items: center;
  margin-top: 16px;
  .actions {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
  h4 {
    font-size: 16px;
    font-weight: 700;
    line-height: 22px;
  }
}

.main {
  width: 100%;
  margin-top: 16px;
  padding-right: 12px;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-row-gap: 11px;
  grid-column-gap: 11px;
  align-items: start;
  overflow-x: hidden;
  position: relative;
  min-height: 0;
  flex-grow: 1;
  overflow-y: auto;
}
