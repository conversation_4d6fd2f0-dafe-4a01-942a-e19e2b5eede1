@use "../../styles/theme.scss" as *;

.pageNumbers {
  display: flex;
  align-items: center;
  -webkit-user-select: none; /* Safari */
  -ms-user-select: none; /* IE 10 and IE 11 */
  user-select: none;
  color: #3e4451;
  font-size: 14px;
  line-height: 18px;
  &.small {
    * {
      margin-right: 8px;
      &:last-child {
        margin-right: 0px;
      }
    }
    .pageNumber {
      width: 24px;
      height: 24px;
      font-size: 14px;
    }
    .pageArrowButton {
      width: 24px;
      height: 24px;
    }
    .dots {
      width: 24px;
      height: 24px;
    }
  }
}

.pageNumber {
  cursor: pointer;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 4px;
  &:hover {
    background: #d6e3fa;
  }
  &.activePageNumber {
    background: #d6e3fa;
    color: #000;
    cursor: auto;
    &:hover {
      background: #d6e3fa;
    }
  }
}

.pageArrowButton {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  padding: 9px;
  border-radius: 8px;
  border: none;
  svg {
    vertical-align: middle;
  }
  &:hover {
    background: #d6e3fa;
  }
}

.dots {
  font-size: 10px;
  width: 36px;
  font-weight: 300;
  letter-spacing: 0.5px;
  display: flex;
  justify-content: center;
  align-items: center;
}
