import { ArrowsClockwise, FloppyDisk, PencilCircle, Plus, PlusCircle, WarningCircle } from "@/components/svgs";

type InnerObject = {
    title: string;
    subTitle?: string;
    content?: string,
    subBtnText: string | React.ReactNode,
    closeBtnText: string,
    icon?: React.ReactNode,
    subBtnColor?: string,
    closeBtnColor?: string,
    fields?: string[],
    success?: string
};

type OuterObject = {
  [key: string]: InnerObject;
};

export const modalsData: OuterObject = {
    cancelSubscription: {
        title: 'Cancel Subscription?',
        subTitle: 'Number: ',
        content: 'This subscription will still be available under the same terms for 60 days in case you need to "Re-Activate”',
        subBtnText: 'Yes, Cancel',
        closeBtnText: 'Cancel',
        subBtnColor: 'customerActionRed',
        icon:<WarningCircle />,
        closeBtnColor: 'secondary',
        success: 'Subscription has been cancelled.'
    },
    removeSubscription: {
        title: 'Remove Subscription?',
        subTitle: 'Number: ',
        content: 'Once removed, it cannot be recovered.',
        subBtnText: 'Yes, Remove',
        icon:<WarningCircle />,
        closeBtnText: 'Cancel',
        subBtnColor: 'customerActionRed',
        closeBtnColor: 'secondary',
        success: 'Subscription has been removed.'
    },
    addTopups: {
        title: 'Add Top-ups',
        icon: <PlusCircle />,
        subBtnText: <><Plus /> Add Topup</>,
        subTitle: 'Once successfully applied the top up will reflect as a data balance increase in the customer’s subscription.',
        closeBtnText: 'Cancel',
        success: 'Top-up has been added successfully.'
    },
    resumeSubscription: {
        title: 'Resume Subscription?',
        subTitle: 'Number: ',
        subBtnText: 'Yes, resume',
        closeBtnText: 'Cancel',
        success: 'Subscription has been resumed.'
    },
    resubmitActivation: {
        title: 'Re-Submit Activation?',
        subTitle: 'Number: ',
        subBtnText: 'Yes, Re-submit',
        closeBtnText: 'Cancel',
    },
    changemdn: {
        title: 'Change MDN',
        subTitle: 'Number: ',
        icon: <PencilCircle />,
        subBtnText: <><ArrowsClockwise /> Request MDN Change</>,
        closeBtnText: 'Cancel',
        success: 'MDN has beed changed.'
    },
    restoreSubscription: {
        title: 'Restore Subscription?',
        subTitle: 'Number: ',
        subBtnText: 'Yes, Restore',
        closeBtnText: 'Cancel',
        success: 'Subscription has been restored.'
    },
    deactiviteSubscription: {
        title: 'Deactivate Subscription?',
        subTitle: 'Number: ',
        subBtnText: 'Yes, Deactivate',
        closeBtnText: 'Cancel',
        success: 'Subscription has been deactivited.'
    },
    activiteSubscription: {
        title: 'Activite Subscription?',
        subTitle: 'Number: ',
        subBtnText: 'Yes, Activate',
        closeBtnText: 'Cancel',
        success: 'Subscription has been activited.'
    },
    suspendNow: {
        title: 'Suspend Subscription Now?',
        content: `Number 071234567890 is set to be suspended on 15 Aug 2024. Are you sure you’d like to suspend the subscription now?`,
        subBtnText: 'Yes, Suspend Now',
        closeBtnText: 'Cancel',
        success: 'Subscription has been suspended.'
    },
    cancelSuspension: {
        title: 'Cancel Suspension?',
        content: `Number 071234567890 is set to be suspended on 15 Aug 2024. Are you sure you’d like to cancel the suspension?`,
        subBtnText: 'Yes, cancel suspension',
        closeBtnText: 'Cancel',
        success: 'Suspension has been cancelled.'
    },
    changeimei: {
        title: 'Change IMEI?',
        subTitle: 'Number: ',
        icon: <PencilCircle />,
        subBtnText: 'Save changes',
        closeBtnText: 'Cancel',
        success: 'IMEI has been changed.',
        fields: ['imei', 'newImei']
    },
    addIccid: {
        title: 'Add ICCID',
        subTitle: 'Number: ',
        icon: <PlusCircle />,
        subBtnText: 'Add ICCID',
        closeBtnText: 'Cancel',
        success: 'IMEI has been added.',
        fields: ['iccid']
    },
    editSubscriptionDetails: {
        title: 'Edit subscription details',
        subTitle: 'Number: ',
        icon: <PencilCircle />,
        subBtnText: <><FloppyDisk/> Save Changes</>,
        closeBtnText: 'Cancel',
        success: 'Subscription details has been edited.',
        fields: ['firstName', 'lastName', "streetNumber", "streetDirection", "streetName", "state", "city", "zipCode", 'phoneNumber']
    },
    changeIccid: {
        title: 'Change ICCID?',
        subTitle: 'Number: ',
        icon: <PencilCircle />,
        subBtnText: <><FloppyDisk/> Save Changes</>,
        closeBtnText: 'Cancel',
        success: 'ICCID has been changed.',
        fields: ['iccid', 'newIccid']
    },
    suspendSubscription: {
        title: 'Suspend Subscription?',
        subTitle: 'Number: ',
        subBtnText: 'Suspend',
        closeBtnText: 'Cancel',
        success: 'Subscription has been suspended.',
        fields: ['effectiveDate']
    },
    changeBill: {
        title: 'Change Bill Cycle',
        subTitle: 'Number: ',
        icon: <PencilCircle />,
        subBtnText: <><FloppyDisk/> Save Changes</>,
        closeBtnText: 'Cancel',
    }

}