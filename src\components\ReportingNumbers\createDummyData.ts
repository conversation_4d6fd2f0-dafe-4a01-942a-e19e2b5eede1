import { faker } from "@faker-js/faker";

export const createReport = (size: number) => {
  return Array.from({ length: size }).map(() => createEntry());
};

const createEntry = () => {
  return {
    registeredTo: faker.internet.email(),
    provisionedTimestamp: faker.date.recent({ days: 100 }),
    assignmentTimestamp: faker.date.recent({ days: 100 }),
    msisdn: faker.phone.number("+###########"),
    imsi: faker.string.numeric(12),
    type: "DID",
    provisionedStatus: "Available",
    status: true,
  };
};
