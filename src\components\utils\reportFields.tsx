import { faker } from "@faker-js/faker";

export const reportFields = {
  Voice: [
    "mid",
    "email",
    "timestamp",
    "type",
    "source",
    "destination",
    "duration",
    "cost",
    "chargeMethod",
  ],
  SMS: [
    "userMid",
    "email",
    "timestamp",
    "source",
    "destination",
    "cost",
    "chargeMethod",
  ],
  Numbers: [
    "type",
    "msisdn",
    "imsi",
    "provisionedStatus",
    "provisionedTimestamp",
    "assignmentTimestamp",
    "registeredTo",
    "status",
  ],
  Finance: [
    "userMid",
    "email",
    "product",
    "paymentType",
    "currencyCode",
    "amount",
    "paymentStatus",
    "paymentDate",
  ],
} as any;

export const reportLabels = {
  userId: "User ID",
  userMid: "User ID",
  mid: "User ID",
  email: "Email Address",
  timestamp: "Timestamp",
  type: "Type",
  source: "Source",
  destination: "Destination",
  duration: "Duration",
  cost: "Cost",
  chargeMethod: (
    <>
      Charge
      <br />
      Method
    </>
  ),
  msisdn: "MSISDN",
  imsi: "IMSI",
  provisionedStatus: (
    <>
      Provisioned
      <br />
      Status
    </>
  ),
  provisionedTimestamp: (
    <>
      Provisioned
      <br />
      Timestamp
    </>
  ),
  assignmentTimestamp: (
    <>
      Assignment
      <br />
      Timestamp
    </>
  ),
  registeredTo: "Registered To",
  status: "Status",
  product: "Product",
  paymentType: "Payment Type",
  currency: "Currency",
  currencyCode: "Currency",
  totalAmount: "Total Amount",
  amount: "Total Amount",
  paymentStatus: "Payment Status",
  paymentDate: "Payment Date",
} as any;

const emptyTimeFilter = {
  start: null,
  end: null,
  startTime: {
    hh: "09",
    mm: "00",
  },
  endTime: {
    hh: "21",
    mm: "00",
  },
} as any;

export const emptyFilters = {
  userId: [],
  userMid: [],
  mid: [],
  email: [],
  timestamp: emptyTimeFilter,
  type: [],
  source: [],
  destination: [],
  duration: [],
  cost: [],
  chargeMethod: [],
  msisdn: [],
  imsi: [],
  provisionedStatus: [],
  provisionedTimestamp: emptyTimeFilter,
  assignmentTimestamp: emptyTimeFilter,
  registeredTo: [],
  status: [],
  product: [],
  paymentType: [],
  currency: [],
  currencyCode: [],
  totalAmount: [],
  amount: [],
  paymentStatus: [],
  paymentDate: emptyTimeFilter,
} as any;

export const timeFields = [
  "timestamp",
  "provisionedTimestamp",
  "assignmentTimestamp",
  "paymentDate",
];
