@use "./theme.scss" as *;
@import "./table-mixin.module.scss";
@import "./mixins.module.scss";

.main {
  width: 100%;
  padding: 24px;
  transition: all 0.5s ease;
}

.overview {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.panel {
  @include panel;
  @include table;
  position: relative;
  margin-top: 16px;
}

.actions {
  display: flex;
  justify-content: end;
}

.tableContainer {
  overflow: auto;
  margin-top: 20px;
  border-radius: 16px;

  .viewRowBtn {
    color: var(--button-tertiary-text);
    font-size: 14px;
    font-weight: 700;
    text-decoration: underline;
    cursor: pointer;
    display: flex;
    align-items: center;

    @media (pointer: fine) {
      visibility: hidden;
    }

    span {
      width: 57px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  tr:first-of-type .viewRowBtn {
    visibility: visible;
  }
  tr:hover .viewRowBtn{
    visibility: visible;
  }
}