import styles from "./create-ticket-modal.module.scss";
import { FloppyDisk, PencilCircle, PlusCircle } from "../svgs";
import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { Input } from "../Input";
import {
  labels,
  handleInputChange,
  clearInput,
  createStateObject,
} from "../utils/InputHandlers";
import DatePicker from "../DatePicker";
import TextArea from "../TextArea";
import SelectInput from "../SelectInput";
import { convertPriority } from "../Priority";
import { convertStatus } from "../StatusBadge";
import Dialog from "../Dialog";

const fields = [
  "subject",
  "description",
  "assignee",
  "category",
  "status",
  "priority",
  "dueDate",
];

const dateInitalDate: { [key: string]: any } = {
  start: null,
  end: null,
  startTime: {
    hh: "09",
    mm: "00",
  },
  endTime: {
    hh: "21",
    mm: "00",
  },
};

const CreateTicketModal = ({ show, setShow, repopulate, ticket }: any) => {
  const dispatch = useDispatch();
  const [data, setData] = useState(createStateObject(fields));
  const [mode, setMode] = useState<"create" | "edit">("create");

  const handleSuccess = () => {
    dispatch({
      type: "notify",
      payload: {
        error: false,
        message: `Ticket has been ${mode === "create" ? "created" : "updated"} successfully`,
      },
    });
    setShow(false);
    setMode("create");
    repopulate(15);
  };

  useEffect(() => {
    if (ticket) {
      setMode("edit");
      setData({
        subject: ticket.subject,
        description: ticket.description,
        assignee: ticket.assignee,
        status: convertStatus(ticket.status),
        priority: convertPriority(ticket.priority),
        category: ticket.type,
        dueDate: ticket.dueDate,
        errors: {},
      });
    }
  }, [ticket]);

  const [loading, setLoading] = useState(false);

  return (
    <Dialog
      confirmButtonText={mode === "create" ? "Create Ticket" : "Save changes"}
      confirmButtonIcon={mode !== "create" && <FloppyDisk />}
      headerTitle={mode === "create" ? "Create Ticket" : "Edit Ticket"}
      headerIcon={mode === "create" ? <PlusCircle /> : <PencilCircle />}
      open={show}
      onClose={() => {
        setShow(false);
      }}
      confirmButtonOnClick={() => handleSuccess()}
      cancelButtonText="Cancel"
      size="sm"
    >
      <div className={styles.main}>
        {fields.map((field: string) => {
          if (field === "subject") {
            return (
              <Input
                key={"ticket-" + field}
                label={labels[field]}
                value={data[field]}
                onChange={(e: any) => {
                  handleInputChange(field, e, data, setData);
                }}
                error={data.errors[field]}
                clear={() => {
                  clearInput(field, setData);
                }}
                infoTooltipText
              />
            );
          } else if (field === "dueDate") {
            return (
              <DatePicker
                label={field}
                field={field}
                masterFrom={dateInitalDate.start}
                masterUntil={dateInitalDate.end}
                startTime={dateInitalDate.startTime}
                endTime={dateInitalDate.endTime}
              />
            );
          } else if (field === "description") {
            return (
              <TextArea
                key={`${field}-input`}
                label={labels[field]}
                value={data[field]}
                onChange={(e: any) => {
                  handleInputChange(field, e, data, setData);
                }}
                error={data.errors[field]}
                clear={() => {
                  clearInput(field, setData);
                }}
                disabled={loading}
              />
            );
          } else {
            return (
              <SelectInput
                key={"ticket-" + field}
                options={selectOptionsByField[field]}
                label={labels[field]}
                selected={data[field]}
                onChange={(e: any) => {
                  handleInputChange(
                    field,
                    { target: { value: e } },
                    data,
                    setData,
                    "select"
                  );
                }}
                error={data.errors[field]}
                infoTooltipText
              />
            );
          }
        })}
      </div>
    </Dialog>
  );
};

export default CreateTicketModal;

const selectOptionsByField: Record<string, any> = {
  category: ["Billing", "Support", "Payment", "Maintenance", "Invoice"],
  status: ["Closed", "Resolved", "Open", "Pending"],
  priority: ["Major", "Critical", "Medium", "Low", "Minor"],
  assignee: ["Robin", "Conor"],
};
