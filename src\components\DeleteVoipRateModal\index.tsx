import Dialog from "@/components/Dialog";
import { WarningCircle } from "@/components/svgs";
import styles from "./delete-voip-rate-modal.module.scss";
import StatusPill from "@/components/StatusPill";
import CountryDisplay from "@/components/CountryDisplay";
import { formatDateWithTime } from "@/components/utils/formatDate";

type DeleteVoipRateModalProps = {
  onClose: () => void;
  open: boolean;
  rateData: any;
};

const DeleteVoipRateModal = ({
  onClose,
  open,
  rateData,
}: DeleteVoipRateModalProps) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      size="sm"
      headerIcon={<WarningCircle />}
      headerTitle="Delete Rate?"
      headerSubtitle="Once removed, it cannot be recovered"
      confirmButtonText="Yes, Delete Rate"
      confirmButtonOnClick={onClose}
      confirmButtonVariant="destructive"
      cancelButtonText="Cancel"
    >
      <div className={styles.tableContainer}>
        <table className={styles.table}>
          <tbody>
            <tr>
              <td className={styles.label}>Country</td>
              <td className={styles.value}>
                {rateData.country && rateData.country.countryCode ? (
                  <CountryDisplay country={rateData.country.countryCode} />
                ) : (
                  "-"
                )}
              </td>
            </tr>
            <tr>
              <td className={styles.label}>Provider</td>
              <td className={styles.value}>{rateData.name}</td>
            </tr>
            <tr>
              <td className={styles.label}>Type</td>
              <td className={styles.value}>{rateData.type}</td>
            </tr>
            <tr>
              <td className={styles.label}>Buy Rate</td>
              <td className={styles.value}>
                {Number(rateData.buyRate).toFixed(2)}
              </td>
            </tr>
            <tr>
              <td className={styles.label}>Sell Rate</td>
              <td className={styles.value}>
                {Number(rateData.sellRate).toFixed(2)}
              </td>
            </tr>
            <tr>
              <td className={styles.label}>Currency</td>
              <td className={styles.value}>{rateData.currency}</td>
            </tr>
            <tr>
              <td className={styles.label}>Date Added</td>
              <td className={styles.value}>
                {formatDateWithTime(rateData.date)}
              </td>
            </tr>
            <tr>
              <td className={styles.label}>Status</td>
              <td className={styles.value}>
                <StatusPill
                  status={rateData.status === 1 ? "Active" : "Inactive"}
                  color={rateData.status === 1 ? "active" : "inactive"}
                />
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </Dialog>
  );
};

export default DeleteVoipRateModal;
