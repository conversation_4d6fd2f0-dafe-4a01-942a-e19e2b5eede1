import styles from "./portin-status.module.scss";

const portStatuses = ["COMPLETED", "PENDING"];

const PortinStatus = ({ status, hover }: any) => {
  return (
    <div
      className={`${styles.main} ${hover && styles.hover} ${
        styles[`status-${portStatuses.indexOf(status)}`]
      }`}
    >
      {status !== "" ? status[0] + status.slice(1).toLowerCase() : "No Status"}
    </div>
  );
};

export default PortinStatus;
