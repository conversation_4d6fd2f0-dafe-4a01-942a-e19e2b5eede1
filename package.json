{"name": "hero-crm", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@mui/material": "^5.15.20", "@szhsin/react-menu": "^4.0.0", "@tanstack/react-table": "^8.20.5", "@tiptap/extension-placeholder": "^2.11.0", "@tiptap/extension-underline": "^2.11.0", "@tiptap/pm": "^2.11.0", "@tiptap/react": "^2.11.0", "@tiptap/starter-kit": "^2.11.0", "@vitejs/plugin-react": "^4.0.4", "axios": "^1.4.0", "chart.js": "^4.4.0", "framer-motion": "^10.12.17", "indicative": "^7.4.4", "jquery": "^3.7.0", "jwt-decode": "^3.1.2", "libphonenumber-js": "^1.10.51", "moment": "^2.29.4", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-redux": "^8.1.1", "react-router-dom": "^6.14.0", "react-select": "^5.7.4", "react-slick": "^0.30.3", "react-transition-group": "^4.4.5", "redux": "^4.2.1", "sass": "^1.63.6", "slick-carousel": "^1.8.1", "typescript": "^4.9.5", "uuid": "^9.0.0", "vite": "4.4.9", "vite-plugin-svgr": "3.2.0", "web-vitals": "^2.1.4", "zod": "^3.23.8"}, "scripts": {"dev": "vite", "build": "tsc && vite build", "serve": "vite preview"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@faker-js/faker": "^8.0.2", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/jquery": "^3.5.16", "@types/node": "^16.18.37", "@types/react": "^18.2.14", "@types/react-dom": "^18.2.6", "@types/react-slick": "^0.23.13", "@types/uuid": "^9.0.2", "husky": "^8.0.0", "lint-staged": "^15.2.2", "prettier": "^3.2.5", "vite-tsconfig-paths": "^4.3.2"}, "lint-staged": {"*.{js,css,md,ts,tsx,scss,html}": "prettier --write"}}