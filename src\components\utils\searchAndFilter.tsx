import { statuses } from "../StatusPill";

// Handle adding and removal of column filters
export const handleFilterChange = (
  type: string,
  option: string,
  filterObject: any,
  setFilterObject: any,
) => {
  let current = filterObject[type];
  if (current.includes(option)) {
    current = current.filter((item: any) => item !== option);
  } else {
    current.push(option);
  }
  setFilterObject({
    ...filterObject,
    [type]: current,
  });
};

// Handles filtering of data list each time the filters change
export const filterList = (data: any, filterObject: any) => {
  const filterKeys = Object.keys(filterObject);
  let filteredArr = [...data];
  filterKeys.forEach((key: string) => {
    // Check if a standard filter array of strings
    if (Array.isArray(filterObject[key])) {
      if (filterObject[key].length > 0) {
        filteredArr = filteredArr.filter((sub: any) => {
          if (key === "country") {
            return filterObject[key].includes(
              typeof sub.country == "string"
                ? sub.country
                : sub.country?.countryName,
            );
          } else if (key === "countries") {
            return filterObject[key].includes(sub.countries[0].countryCode);
          } else if (key === "buyRate") {
            if (filterObject[key].length === 1) {
              return sub[key] >= filterObject[key][0];
            } else {
              const minValue = filterObject[key][0];
              const maxValue = filterObject[key][1];
              return sub[key] >= minValue && sub[key] <= maxValue;
            }
          } else if (key === "sellRate") {
            if (filterObject[key].length === 1) {
              return sub[key] >= filterObject[key][0];
            } else {
              const minValue = filterObject[key][0];
              const maxValue = filterObject[key][1];
              return sub[key] >= minValue && sub[key] <= maxValue;
            }
          } else {
            return filterObject[key].includes(sub[key]);
          }
        });
      }
    } else if (["usd", "gbp", "eur"].includes(key)) {
      if (filterObject[key].to) {
        console.log(filterObject[key]);
        filteredArr = filteredArr.filter((item: any) => {
          const cost = item.prices.find(
            (price: any) => price.currencyCode.toLowerCase() === key,
          ).cost;
          return cost >= filterObject[key].from && cost <= filterObject[key].to;
        });
      } else {
        filteredArr = filteredArr.filter((item: any) => {
          return (
            filterObject[key].from ==
              item.prices.find(
                (price: any) => price.currencyCode.toLowerCase() === key,
              ).cost || filterObject[key].from === ""
          );
        });
      }

      // Otherwise it's a date range, so filter date accordingly
    } else {
      const filterInbetweenDates = (comparator: string) => {
        filteredArr = filteredArr.filter((sub: any) => {
          if (sub[key]) {
            return (
              new Date(sub[key]).getTime() >=
                filterObject[key].start.getTime() &&
              new Date(sub[key]).getTime() <
                filterObject[key][comparator].getTime() + 86400000
            );
          } else {
            return false;
          }
        });
      };

      if (filterObject[key].start) {
        if (filterObject[key].end === null) {
          filterInbetweenDates("start");
        } else {
          filterInbetweenDates("end");
        }

        // Check if using time as well
        if ("startTime" in filterObject[key]) {
          console.log(filterObject[key]);
          if (filterObject[key].endTime) {
            // Now filter the time constraints
            let contraints = {
              startH: parseInt(filterObject[key].startTime.hh),
              endH: parseInt(filterObject[key].endTime.hh),
              startM: parseInt(filterObject[key].startTime.mm),
              endM: parseInt(filterObject[key].endTime.mm),
            };
            filteredArr = filteredArr.filter((sub: any) => {
              const subDate = new Date(sub[key]);
              const subH = subDate.getHours();
              const subM = subDate.getMinutes();

              if (sub[key]) {
                if (subH >= contraints.startH && subH <= contraints.endH) {
                  if (subH === contraints.startH) {
                    if (subM < contraints.startM) {
                      return false;
                    }
                  }
                  if (subH === contraints.endH) {
                    if (subM > contraints.endM) {
                      return false;
                    }
                  }
                  return true;
                }
                return false;
              } else {
                return false;
              }
            });
          } else {
            const startH = parseInt(filterObject[key].startTime.hh);
            const startM = parseInt(filterObject[key].startTime.mm);

            filteredArr = filteredArr.filter((sub: any) => {
              const subDate = new Date(sub[key]);
              const subH = subDate.getHours();
              const subM = subDate.getMinutes();

              if (sub[key]) {
                if (subH === startH && subM === startM) {
                  return true;
                }
                return false;
              } else {
                return false;
              }
            });
          }
        }
      }
    }
  });
  return filteredArr;
};

// Submits a search for user - filters users and then highlights query in the table
export const submitSearch = (
  data: any,
  setFilteredData: any,
  searchQuery: string,
  setQueryDisplay: any,
) => {
  if (searchQuery === "") {
    setFilteredData(data);
    return;
  }
  setQueryDisplay(searchQuery);
  let query = searchQuery.split(" ");

  // Filter users to only show users that contain query string
  let usersToShow = data.filter((singleUser: any) => {
    let userObj = { ...singleUser };

    // Replace numerical status with string versions
    userObj.subscriberNumberStatus = statuses[userObj.subscriberNumberStatus];

    let values = Object.values(userObj);
    return values.some((objValue: any) => {
      if (typeof objValue === "string") {
        return query.some((singleQuery: string) =>
          objValue.toLowerCase().includes(singleQuery.toLowerCase()),
        );
      } else {
        return false;
      }
    });
  });

  // Highlight the query string within the table data
  /*let highlightedUsersToShow = usersToShow.map((singleUser: any) => {
    let entries = Object.entries(singleUser);
    let result = {} as any;
    entries.forEach((entry: any) => {
      if (
        typeof entry[1] === "string" &&
        entry[0] !== "action" &&
        entry[0] !== "time"
      ) {
        let searchingIn = entry[1];
        const combinedRegex = new RegExp(query.join("|"), "gi");
        searchingIn = searchingIn.replace(
          combinedRegex,
          (prev: any) => `<span class="highlight-search-result">${prev}</span>`
        );

        result[entry[0]] = (
          <span dangerouslySetInnerHTML={{ __html: searchingIn }}></span>
        );
      } else {
        result[entry[0]] = entry[1];
      }
    });
    return result;
  }) as any;*/

  setFilteredData(usersToShow);
};

export const highlightSearch = (
  singleItem: any,
  prop: string,
  searchQuery: string,
) => {
  let query = searchQuery.replace(/[.*+?^${}()|[\]\\]/g, "\\$&").split(" ");
  let result = singleItem as any;
  if (searchQuery !== "") {
    if (
      typeof singleItem === "string" &&
      prop !== "action" &&
      prop !== "time"
    ) {
      let searchingIn = singleItem;
      if (prop === "phoneNumber") {
        searchingIn = searchingIn.replaceAll(" ", "");
      }
      const combinedRegex = new RegExp(query.join("|"), "gi");
      searchingIn = searchingIn.replace(
        combinedRegex,
        (prev: any) => `<span class="highlight-search-result">${prev}</span>`,
      );

      result = <span dangerouslySetInnerHTML={{ __html: searchingIn }}></span>;
    }
  }
  return result;
};

export const submitFilterSelectSearch = (
  data: any,
  setFilteredData: any,
  searchQuery: string,
  setQueryDisplay: any,
) => {
  if (searchQuery === "") {
    setFilteredData(data);
    return;
  }
  setQueryDisplay(searchQuery);
  let query = searchQuery.split(" ");

  // Filter users to only show users that contain query string
  let usersToShow = data.filter((singleUser: any) => {
    let userObj = { ...singleUser };

    /* //Replace numerical status with string versions
    userObj.subscriberNumberStatus = statuses[userObj.subscriberNumberStatus];*/

    let values = Object.values(userObj);
    return values.some((objValue: any) => {
      if (typeof objValue === "string") {
        return query.some((singleQuery: string) =>
          objValue.toLowerCase().includes(singleQuery.toLowerCase()),
        );
      } else {
        return false;
      }
    });
  });

  setFilteredData(usersToShow);
};

// Sort data list by a-z or z-a based on selection and field key
export const sortData = (dataList: any, orderBy: string, key: string) => {
  const data = [...dataList];
  const compare = (a: any, b: any) => {
    if (a[key] < b[key]) {
      return orderBy === "a-z" ? -1 : 1;
    } else if (a[key] > b[key]) {
      return orderBy === "a-z" ? 1 : -1;
    } else {
      return 0;
    }
  };

  return data.sort(compare);
};
