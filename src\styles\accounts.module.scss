@import './mixins.module.scss';
@import "./table-mixin.module.scss";

.main {
  padding: 16px;
}

.overview {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
}

.fields {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-top: 20px
}

.actions {
    display: flex;
    justify-content: end;
}

.panel {
    @include panel;
    @include table;
    margin-top: 16px;
}

.tableContainer {
    overflow: auto;
    padding-bottom: 5px;
    margin-top: 20px;
    border-radius: 16px;
    tr {
      td {
        &:last-of-type {
          padding: 0;
        }
        &:nth-of-type(8) {
            > div {
                flex-direction: column;
            }
        }
      }
    }
    .actionCell {
      width: 1px; // make the cell only as wide as its content
      padding: 0;
    }
  
    .viewRowBtn {
      color: var(--button-tertiary-text);
      font-size: 14px;
      font-weight: 700;
      cursor: pointer;
      display: flex;
      gap: 16px;
      justify-content: center;
    }
  }