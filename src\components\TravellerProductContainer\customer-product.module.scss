@use "../../styles/theme.scss" as *;

.modalContent {
  display: flex;
  flex-direction: column;
  justify-content: center;
  background-color: #f7f6f6;
  border-radius: 12px;
  border-radius: 12px;
  border: 0.5px solid rgba(0, 0, 0, 0.3);
  background: var(--primary-pallete-lightest-gray, #f2f2f2);

  div {
    display: flex;
    justify-content: space-between;
    gap: 16px;
  }
}
.flag {
  background-size: cover;
  background-position: center;
  width: 24px;
  height: 24px;
  border-radius: 1000px;
}

.title {
  font-family: Poppins;
  font-size: 20px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
}
.subSelection {
  height: 47px;
  border-radius: 1000px;
  color: $black;
  font-size: 14px;
  padding: 0 24px;
  display: flex;
  align-items: center;
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
  &:hover {
    color: $dark-orange;
  }
  span {
    position: relative;
    z-index: 6;
  }
}

.filterSection {
  display: flex;
  align-items: center;
  .spacer {
    margin-right: 12px;
  }
}

.activeSelection {
  text-shadow: 0px 0px 0.5px $black;
  cursor: auto;
  &:hover {
    color: $black;
  }
}
.background {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 1000px;
  background-color: $light-orange;
  z-index: 5;
  left: 0;
}

.panelTopBar {
  width: 100%;
  display: flex;
  align-items: center;
  margin-top: 16px;
  .actions {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
  h4 {
    font-size: 16px;
    font-weight: 700;
    line-height: 22px;
  }
}

.plansContainer {
  display: grid;
  width: 100%;
  grid-template-columns: 1fr 1fr 1fr;
  grid-row-gap: 11px;
  grid-column-gap: 11px;
  margin-top: 16px;
  padding-right: 12px;
  position: relative;
  flex-grow: 1;
  overflow-y: auto;
  min-height: 0;
}

.noneFound {
  display: grid;
  width: 100%;
  align-items: center;
  justify-content: center;
  padding: 55px;
  img {
    max-width: 500px;
  }
  img,
  h3 {
    grid-area: 1 / 1 / 2 / 2;
    font-size: 20px;
    font-weight: 700;
    line-height: 30px;
    width: 100%;
  }
  h3 {
    width: 100%;
    text-align: center;
  }
}
