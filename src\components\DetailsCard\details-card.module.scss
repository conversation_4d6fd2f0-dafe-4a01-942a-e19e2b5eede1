.detailCard {
  padding: 16px 12px;
  border: 1px solid var(--gray-100);
  border-radius: 16px;

  h4 {
    font-size: 14px;
    font-weight: 700;
  }
}

.detailCardItem {
  margin-top: 12px;
  display: flex;
  gap: 8px;

  &:not(:last-child) {
    margin-bottom: 8px;
  }
}

.cardItemContent {
  display: flex;
  flex-direction: column;
  gap: 2px;

  .itemTitle {
    font-size: 12px;
    line-height: 15px;
    color: var(--gray-500);
  }

  .itemValue {
    font-size: 14px;
    line-height: 18px;
  }
}
