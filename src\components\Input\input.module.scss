@use "../../styles/theme.scss" as *;

.input {
  height: 48px;
  width: 100%;
  border-radius: 16px;
  border: none;
  border: 1px solid var(--textField-border-primary);
  padding-left: 16px;
  padding-right: 48px;
  transition: border 0.1s ease;
  font-size: 14px;
  color: #061632;
  background: #f1f6fd;
  &.noClear {
    padding-right: 16px;
  }
  &::placeholder {
    color: #667085;
  }
  &:hover {
    transition: all 0.4s ease;
    border: 1px solid var(--textField-border-active);
  }
  &:focus {
    border: 1px solid var(--textField-border-active);
    caret-color: #2e70e5;
    outline: none;
    & ~ .errorIcon {
      display: none;
    }
    & ~ .iconsWrapper > .clearIcon {
      display: block;
    }
  }
  &.error {
    border: 1px solid var(--textField-border-error);
  }
  &:disabled {
    background: #fff;
    text-overflow: ellipsis;
    padding-right: 36px;
    &.readonly {
      color: $black;
      border: 1px solid #cacaca;
      & + .label {
        color: $placeholder;
      }
    }
    & + .label {
      color: $disabled-text;
    }
    & ~ .eyeIcon {
      opacity: 0.38;
    }
  }
  &.numberInput {
    /* Chrome, Safari, Edge, Opera */
    &::-webkit-outer-spin-button,
    &::-webkit-inner-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }

    /* Firefox */
    &[type="number"] {
      -moz-appearance: textfield;
      -webkit-appearance: none;
      appearance: none;
    }
  }
}

.password {
  padding-right: 80px;
}

.inputContainer {
  width: 100%;
  margin-bottom: 16px;
  position: relative;
  a {
    position: absolute;
    font-size: 14px;
    color: #2e70e5;
    font-weight: 500;
    cursor: pointer;
    right: 0;
  }
  .directionInput {
    margin-bottom: 24px;
    font-size: 14px;
    font-weight: 400;
    height: 100%;
  }
}

.label {
  display: flex;
  gap: 6px;
  align-items: center;
  line-height: 18px;
  font-size: 14px;
  pointer-events: none;
  color: #061632;
}

.inputWrapper {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-top: 4px;
}

.iconsWrapper {
  position: absolute;
  right: 15px;
  display: flex;
  gap: 8px;
  span {
    cursor: pointer;
  }
}

.clearIcon,
.eyeIcon {
  cursor: pointer;
}

.clearIcon {
  display: none;
}

.errorText {
  font-size: 14px;
  color: $error;
  text-align: start;
  display: flex;
  align-items: center;
  svg {
    width: 17px;
  }
}
