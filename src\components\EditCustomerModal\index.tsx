import styles from "./edit-customer.module.scss";
import Modal from "../Modal";
import { FloppyDisk } from "../svgs";
import { Input } from "../Input";
import SelectInput from "../SelectInput";
import { useEffect, useState } from "react";
import { validateAll } from "indicative/src/Validator";
import { useDispatch } from "react-redux";
import { ApiPatch, ApiPostAuth } from "../../pages/api/api";
import Toggle from "../Toggle";
import {
  clearInput,
  createStateObject,
  displayErrors,
  getMessages,
  getRules,
  handleInputChange,
  labels,
  placeholders,
} from "../utils/InputHandlers";
import StatusSwitch from "../StatusSwitch";
import CodecsSwitch from "../CodecsSwitch";

const EditCustomerModal = ({
  show,
  setShow,
  provider,
  resetActiveUser,
  page,
  repopulateUsers,
  mid,
}: any) => {
  const dispatch = useDispatch();
  const [PageState, setPageState] = useState<string[]>([]);

  let providerinputs = ["firstName", "lastName", "email", "phoneNumber"];
  const rules = getRules(providerinputs);
  const messages = getMessages(providerinputs);

  const InputsChange = () => {
    setPageState(providerinputs);
  };

  const [data, setData] = useState(createStateObject(providerinputs));
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    InputsChange();
  }, [page]);

  // Populate with current data
  useEffect(() => {
    if (provider && show) {
      // const [firstName, lastName] = provider?.name?.split(" ");
      setData({
        ...data,
        firstName: provider.firstName,
        lastName: provider.lastName,
        email: provider.email,
        phoneNumber: provider?.phoneNumber || provider?.registeredPhoneNumber,
      });
    }
  }, [provider, show]);

  // Reset modal data when closed
  const reset = () => {
    setTimeout(() => {
      setData(createStateObject(providerinputs));
    }, 300);
    setLoading(false);
    setShow(false);
  };

  // Handles creation of new user
  const editUser = () => {
    const testData = {
      firstName: data.firstName.trim(),
      lastName: data.lastName.trim(),
      email: data.email.trim(),
      phoneNumber: data.phoneNumber,
    };

    validateAll(testData, rules, messages)
      .then((response) => {
        setLoading(true);
        ApiPatch(`/customer/${mid}/edit`, testData)
          .then((response: any) => {
            repopulateUsers();
            setLoading(false);
            setShow(false);
            dispatch({
              type: "notify",
              payload: {
                error: false,
                message: response.data.message,
              },
            });
          })
          .catch((error) => {
            setLoading(false);
            dispatch({
              type: "notify",
              payload: {
                error: true,
                message: error.response.data.message,
              },
            });
          });
      })
      .catch((errors) => {
        displayErrors(errors, setData);
      });
  };

  return (
    <Modal
      saveButton={
        <>
          <FloppyDisk />
          Save Changes
        </>
      }
      image="/edit_user_graphic.svg"
      show={show}
      setShow={setShow}
      proceed={editUser}
      close={() => setShow(false)}
      onClose={() => setShow(false)}
      loading={loading}
    >
      <div className={`${styles.main} `}>
        <h3>Edit Customer </h3>
        {PageState.map((prop: any) => (
          <Input
            key={`${prop}`}
            label={labels[prop]}
            placeholder={placeholders[prop]}
            value={data[prop]}
            onChange={(e: any) => {
              handleInputChange(prop, e, data, setData);
            }}
            error={data.errors[prop]}
            onKeyDown={editUser}
            clear={() => {
              clearInput(prop, setData);
            }}
            disabled={loading}
            white
          />
        ))}
      </div>
    </Modal>
  );
};

export default EditCustomerModal;
