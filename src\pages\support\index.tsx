import SwitchBar from "@/components/SwitchBar";
import Title from "@/components/Title";
import { useState } from "react";
import styles from "@/styles/support.module.scss";
import FAQTab from "@/pages/support/faq-tab";
import GuidesTab from "@/pages/support/guides-tab";

const Support = () => {
  const [activeTabId, setActiveTabId] = useState("faqs");

  return (
    <>
      <Title>Support</Title>
      <SwitchBar
        options={[
          {
            label: "FAQs",
            id: "faqs",
          },
          {
            label: "Guides",
            id: "guides",
          },
        ]}
        selected={activeTabId}
        setSelected={setActiveTabId}
        layoutId="support-type-switch"
      />
      <div style={{ paddingBottom: "16px" }}>
        {activeTabId === "faqs" && <FAQTab />}
        {activeTabId === "guides" && <GuidesTab />}
      </div>
    </>
  );
};

export default Support;
