import { faker } from "@faker-js/faker";
import { v4 } from "uuid";

export const getChannels = (size: number) => {
  return Array.from({ length: size }).map(() => createEntry());
};

const status = ["Active", "Inactive"];

const createEntry = () => {
  return {
    name: faker.location.city(),
    description: "channel description",
    users: faker.number.int(6000),
    subscribers: faker.number.int(6000),
    subscriptions: faker.number.int(10000),
    status: status[faker.number.int(1)],
    id: v4(),
  };
};

export const channelFields = [
  {
    label: "Name",
    labelStr: "Name",
    key: "name",
  },
  {
    label: "Description",
    labelStr: "Description",
    key: "description",
  },
  {
    label: "Users",
    labelStr: "Users",
    key: "users",
  },
  {
    label: "Subscribers",
    labelStr: "Subscribers",
    key: "subscribers",
  },
  {
    label: "Subscriptions",
    labelStr: "Subscriptions",
    key: "subscriptions",
  },
  {
    label: "Status",
    labelStr: "Status",
    key: "status",
  },
];
