import { useDispatch, useSelector } from "react-redux";
import { Link, useNavigate } from "react-router-dom";
import styles from "./project-tile.module.scss";

const ProjectTile = ({ project, mvne }: any) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const userInfo = useSelector((state: any) => state.userInfo);

  return (
    <div
      className={styles.main}
      onClick={() => {
        if (mvne) {
          navigate(`/select-project/${encodeURIComponent(project.name)}`);
        } else if (project.name === "Gist Mobile") {
          window.open("https://gist-crm.mobiliseconnect.com/", "_blank");
        } else {
          let userInfoStore = { ...userInfo };
          userInfoStore.brandLogo = project.brandLogo;
          userInfoStore.brandName = project.name;
          localStorage.setItem("crmUserInfo", JSON.stringify(userInfoStore));
          localStorage.setItem("productType", project.productType);

          dispatch({
            type: "set",
            userInfo: userInfoStore,
            productType: project.productType,
          });

          navigate("/dashboard");
        }
      }}
    >
      <div className={styles.logoSection}>
        <img
          alt={project.name}
          src={project.brandLogo}
          className={styles.img}
          title={project.name}
        />
      </div>
      <div className={styles.details}>
        <div className={styles.company}>
          <h5 style={{ marginRight: 8 }}>{project.name}</h5>
          <span className={project.type === "MVNE" ? styles.mvne : styles.mvno}>
            {project.type}
          </span>
        </div>
        <div className={styles.data}>
          <div>
            <p className={styles.title}>Services</p>
            <p className={styles.value}>{project.services}</p>
          </div>
          <div>
            <p className={styles.title}>Service Providers</p>
            <p className={styles.value}>{project.providers}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectTile;
