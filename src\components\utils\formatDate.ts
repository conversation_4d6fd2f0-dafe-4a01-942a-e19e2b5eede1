import moment from "moment";

export const months = [
  "Jan",
  "Feb",
  "Mar",
  "Apr",
  "May",
  "Jun",
  "Jul",
  "Aug",
  "Sep",
  "Oct",
  "Nov",
  "Dec",
];

const monthsLong = [
  "January",
  "February",
  "March",
  "April",
  "May",
  "June",
  "July",
  "August",
  "September",
  "October",
  "November",
  "December",
];

const formatDate = (dateInput: any, shortDate = false) => {
  const date = new Date(dateInput);
  const day = date.getDate().toString().padStart(2, "0");
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const year = shortDate
    ? date.getFullYear().toString().slice(2)
    : date.getFullYear().toString();
  return `${day}/${month}/${year}`;
};

export const formatChartDate = (dateInput: any) => {
  const date = moment(dateInput);
  const day = date.date().toString();
  const daySuffix = getSuffix(day);
  const month = monthsLong[date.month()];
  const year = date.year();
  const hours = date.hour().toString().padStart(2, "0");
  const minute = date.minute().toString().padStart(2, "0");
  return `${day}${daySuffix} ${month} ${year} ${dateInput.includes("T") ? `${hours}:${minute}` : ""}`;
};

export const formatChartDateLabel = (dateInput: any) => {
  const date = moment(dateInput);
  const day = date.date().toString().padStart(2, "0");
  const month = months[date.month()];
  const hours = date.hour().toString().padStart(2, "0");
  const minute = date.minute().toString().padStart(2, "0");
  return dateInput.includes("T") ? `${hours}:${minute}` : [day, month];
};

export const formatChartTitle = (startDate: any, endDate: any) => {
  const date1 = moment(startDate);
  const date2 = moment(endDate);
  const day1 = date1.utc().date().toString();
  const day2 = date2.utc().date().toString();
  const daySuffix1 = getSuffix(day1);
  const daySuffix2 = getSuffix(day2);
  const month1 = monthsLong[date1.month()];
  const month2 = monthsLong[date2.month()];
  const year1 = date1.year().toString();
  const year2 = date2.year().toString();

  if (year1 === year2) {
    if (month1 === month2) {
      if (day1 === day2) {
        return `${day2}${daySuffix2} ${month2}, ${year2}`;
      } else {
        return `${day1}${daySuffix1} - ${day2}${daySuffix2} ${month2}, ${year2}`;
      }
    } else {
      return `${day1}${daySuffix1} ${month1} - ${day2}${daySuffix2} ${month2}, ${year2}`;
    }
  } else {
    return `${day1}${daySuffix1} ${month1}, ${year1} - ${day2}${daySuffix2} ${month2}, ${year2}`;
  }
};

export const formatReportsDate = (dateInput: any, shortDate = false) => {
  let [day, month, year] = formatDate(dateInput, shortDate).split("/");
  return `${year}-${month}-${day}`;
};

export const formatDateWords = (dateInput: any, shortDate = false) => {
  const date = moment(dateInput);
  const day = date.utc().date().toString();
  const month = months[date.month()];
  const year = shortDate
    ? date.year().toString().slice(2)
    : date.year().toString();
  return `${day} ${month} ${year}`;
};

export const formatDateWithTime = (dateInput: any) => {
  const date = moment(dateInput);
  const day = date.utc().date().toString();
  const month = months[date.month()];
  const year = date.year().toString();
  const hour = date.hour().toString().padStart(2, "0");
  const minutes = date.minute().toString().padStart(2, "0");
  const seconds = date.seconds().toString().padStart(2, "0");
  return `${day} ${month} ${year}, ${hour}:${minutes}:${seconds}`;
};

const getSuffix = (day: string) => {
  if (day === "11" || day === "12" || day === "13") {
    return "th";
  }

  const end = day.slice(day.length - 1, day.length);
  switch (end) {
    case "1":
      return "st";
    case "2":
      return "nd";
    case "3":
      return "rd";
    default:
      return "th";
  }
};

export default formatDate;
