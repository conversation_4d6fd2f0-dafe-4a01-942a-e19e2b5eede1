import styles from "../../styles/channel-management.module.scss";
import {
  Plus,
  MagnifyingGlass,
  Export,
  Pencil,
  Delete,
} from "../../components/svgs";
import { useState, useEffect, useRef } from "react";
import { useDispatch } from "react-redux";
import Button from "../../components/Button";
import UserSkeleton from "../../components/UserSkeleton";
import { ApiPut } from "../api/api";
import AddEditChannelModal from "../../components/AddEditChannelModal";
import DeleteChannelModal from "../../components/DeleteChannelModal";
import { getChannels, channelFields } from "./dummyData";
import Title from "@/components/Title";
import CollapsiblePanel from "@/components/CollapsiblePanel";
import InsightList from "@/components/InsightList";
import InsightCard from "@/components/InsightCard";
import Tag from "@/components/Tag";
import { Input } from "@/components/Input";
import SelectInput from "@/components/SelectInput";
import {
  createStateObject,
  labels,
  handleInputChange,
  clearInput,
} from "@/components/utils/InputHandlers";
import TableControl from "@/components/TableControl";
import StatusPill from "@/components/StatusPill";
import CheckboxDropdownInput from "@/components/CheckboxDropdownInput";

const fields = ["name", "status"];

const ChannelManagement = () => {
  const dispatch = useDispatch();
  const overviewPanelRef = useRef<any>(null);
  const searchPanelRef = useRef<any>(null);
  const [initialLoading, setInitialLoading] = useState(true);

  const [data, setData] = useState(createStateObject(fields));
  const [channels, setChannels] = useState([] as any);
  const [showSearchResults, setShowSearchResults] = useState(false);

  const repopulate = () => {
    setInitialLoading(true);
    setInitialLoading(false);
    setChannels(getChannels(channelsPerPage));
  };

  useEffect(repopulate, []);

  const [currentPage, setCurrentPage] = useState(1);
  const [channelsPerPage, setChannelsPerPage] = useState(10);

  /***********   Add Channel     ***********/

  const [showAddEditChannelModal, setShowAddEditChannelModal] = useState(false);

  const [activeChannel, setActiveChannel] = useState(null as any);

  /********       Delete Channel         **********/

  const [showDeleteChannelModal, setShowDeleteChannelModal] = useState(false);

  // Handles deleting channel
  const handleDeleteChannel = (channel: any, action: "delete" | "edit") => {
    setActiveChannel(channel);
    if (action === "delete") {
      setShowDeleteChannelModal(true);
    } else {
      setShowAddEditChannelModal(true);
    }
  };

  const handleChangeStatus = (channel: any, newStatus: any) => {
    ApiPut(`/channels/${channel.id}/status`, {
      status: newStatus,
    })
      .then((response) => {
        repopulate();
        dispatch({
          type: "notify",
          payload: {
            error: false,
            message: response.data.message,
          },
        });
      })
      .catch((error) => {
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: error.response.data.message,
          },
        });
      });
  };

  const formatDataItem = (item: any, key: string) => {
    if (key === "status") {
      return <StatusPill status={item[key] === "Active"} />;
    } else {
      return item[key];
    }
  };

  return (
    <>
      <Title>Channel Managment</Title>
      <div className={styles.main}>
        <AddEditChannelModal
          show={showAddEditChannelModal}
          setShow={setShowAddEditChannelModal}
          repopulate={repopulate}
          channel={activeChannel}
        />
        <DeleteChannelModal
          show={showDeleteChannelModal}
          setShow={setShowDeleteChannelModal}
          channel={activeChannel}
          repopulate={repopulate}
        />
        <CollapsiblePanel
          title="Overview"
          summaryWhenClosed={
            <div style={{ marginLeft: 22 }}>
              <InsightList insights={overviewStats} />
            </div>
          }
          actionBtn={
            <Button
              style={{ marginRight: 10 }}
              onClick={(e) => {
                e.stopPropagation();
                setShowAddEditChannelModal(true);
              }}
            >
              <Plus /> Create Channel
            </Button>
          }
          ref={overviewPanelRef}
        >
          <div className={styles.overview}>
            {overviewStats.map((stat, index) => (
              <InsightCard
                key={index}
                title={stat.title}
                value={stat.value.toString()}
              />
            ))}
          </div>
        </CollapsiblePanel>

        <div style={{ marginTop: 16 }}>
          <CollapsiblePanel
            title="Search Channels"
            summaryWhenClosed={
              <div
                style={{
                  display: "flex",
                  flex: 1,
                  justifyContent: "space-between",
                  marginLeft: 16,
                  marginRight: 8,
                }}
              >
                <Tag text="3 filters applied" />
                <Button color="secondary">Clear Filters</Button>
              </div>
            }
            ref={searchPanelRef}
          >
            <div className={styles.fields}>
              <Input
                label={labels["name"]}
                value={data["name"]}
                onChange={(e: any) => {
                  handleInputChange("name", e, data, setData);
                }}
                error={data.errors["name"]}
                clear={() => {
                  clearInput("name", setData);
                }}
                infoTooltipText
              />
              <CheckboxDropdownInput
                options={selectOptionsByField["status"]}
                label={labels["status"]}
                selected={data["status"]}
                onChange={(values) => {
                  handleInputChange("status", values, data, setData, "select");
                }}
                error={data.errors["status"]}
                infoTooltipText
              />
            </div>
            <div className={styles.actions}>
              <Button
                color="blue"
                onClick={() => {
                  searchPanelRef.current.close();
                  overviewPanelRef.current.close();
                  setShowSearchResults(true);
                }}
              >
                <MagnifyingGlass /> Search
              </Button>
            </div>
          </CollapsiblePanel>
        </div>

        {showSearchResults && (
          <div className={styles.panel}>
            <div className={styles.panelTopBar}>
              <h4>Channels</h4>
              <div className={styles.actions}>
                <Button color="secondary">
                  <Export /> Export to CSV
                </Button>
              </div>
            </div>

            <div className={`${styles.tableContainer} table-scroll`}>
              <table>
                <thead>
                  <tr>
                    {channelFields.map((field: any) => (
                      <th>{field.label}</th>
                    ))}
                    <th></th>
                  </tr>
                </thead>
                <tbody>
                  {!initialLoading
                    ? channels.map((item: any) => {
                        return (
                          <tr
                            key={`order-${item.name}`}
                            style={{ cursor: "pointer" }}
                          >
                            {channelFields.map((field: any) => (
                              <td
                                key={`order-${item.orderNumber}-${field.key}`}
                              >
                                <div
                                  style={{
                                    display: "flex",
                                    justifyContent: "flex-start",
                                  }}
                                >
                                  {formatDataItem(item, field.key)}
                                </div>
                              </td>
                            ))}
                            <td>
                              <span className={styles.viewRowBtn}>
                                <span
                                  className={styles.rowActionBtn}
                                  onClick={() =>
                                    handleDeleteChannel(item, "edit")
                                  }
                                >
                                  <Pencil />
                                </span>
                                <span
                                  className={styles.rowActionBtn}
                                  onClick={() =>
                                    handleDeleteChannel(item, "delete")
                                  }
                                >
                                  <Delete />
                                </span>
                              </span>
                            </td>
                          </tr>
                        );
                      })
                    : Array.from({ length: channelsPerPage }, (v, i) => i).map(
                        (i) => (
                          <UserSkeleton
                            key={"order-skeleton-" + i}
                            noOfStandard={8}
                          />
                        )
                      )}
                </tbody>
              </table>
            </div>
            <div style={{ marginTop: "16px" }}>
              <TableControl
                show
                itemsPerPage={channelsPerPage}
                setItemsPerPage={(val: any) => setChannelsPerPage(val)}
                currentPage={currentPage}
                setCurrentPage={setCurrentPage}
                numberOfPages={Math.ceil(channels.length / channelsPerPage)}
                label="users"
                loading={initialLoading}
              />
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default ChannelManagement;

const overviewStats = [
  { title: "Total Channels", value: "63,629" },
  { title: "Active Channels", value: "63,629" },
  { title: "Inactive Channels", value: "63,629" },
];
const selectOptionsByField = {
  status: ["Active", "Inactive"].map((v) => ({
    label: v,
    value: v,
  })),
};
