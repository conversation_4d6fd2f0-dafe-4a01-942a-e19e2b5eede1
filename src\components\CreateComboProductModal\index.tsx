import Dialog from "@/components/Dialog";
import { PlusCircle } from "@/components/svgs";
import {
  clearInput,
  createStateObject,
  handleInputChange,
  labels,
} from "@/components/utils/InputHandlers";
import { useState } from "react";
import styles from "./create-combo-product-modal.module.scss";
import { Input } from "@/components/Input";
import SelectInput from "@/components/SelectInput";
import ToggleButtonGroup from "@/components/ToggleButtonGroup";
import { countryList } from "@/components/utils/countries";
import { getCountryOptions } from "../utils/getCountryOptions";

type CreateComboProductModalProps = {
  open: boolean;
  onClose: () => void;
};

const CreateComboProductModal = ({
  open,
  onClose,
}: CreateComboProductModalProps) => {
  const fields = [
    "country",
    "validity",
    "dataAllowance",
    "smsAllowance",
    "callAllowance",
    "usdPrice",
    "gbpPrice",
    "eurPrice",
    "status",
  ];
  const [formData, setFormData] = useState(createStateObject(fields));

  return (
    <Dialog
      open={open}
      onClose={onClose}
      size="sm"
      headerIcon={<PlusCircle />}
      headerTitle="Create Combo Product"
      confirmButtonText="Create Product"
      confirmButtonOnClick={onClose}
      cancelButtonText="Cancel"
    >
      <div className={styles.formContainer}>
        {fields.map((field) => {
          if (["country", "validity"].includes(field)) {
            return (
              <SelectInput
                key={"product-" + field}
                options={selectOptionsByField[field]}
                label={labels[field]}
                selected={formData[field]}
                onChange={(e: any) => {
                  handleInputChange(
                    field,
                    { target: { value: e } },
                    formData,
                    setFormData
                  );
                }}
                error={formData.errors[field]}
                infoTooltipText
              />
            );
          } else if (["status"].includes(field)) {
            return (
              <ToggleButtonGroup
                key={"product-" + field}
                selected={formData[field]}
                options={toggleButtonGroupOptionsByField[field]}
                label={labels[field]}
                onChange={(value) => {
                  handleInputChange(
                    field,
                    { target: { value } },
                    formData,
                    setFormData
                  );
                }}
              />
            );
          } else {
            if (["usdPrice", "gbpPrice", "eurPrice"].includes(field)) {
              if (field === "usdPrice") {
                return (
                  <div key={"price-fields"} className={styles.priceFields}>
                    {["usdPrice", "gbpPrice", "eurPrice"].map((priceField) => (
                      <Input
                        key={"product-" + priceField}
                        label={labels[priceField]}
                        value={formData[priceField]}
                        onChange={(e: any) => {
                          handleInputChange(
                            priceField,
                            e,
                            formData,
                            setFormData
                          );
                        }}
                        error={formData.errors[priceField]}
                        clear={() => {
                          clearInput(priceField, setFormData);
                        }}
                        infoTooltipText
                        number
                      />
                    ))}
                  </div>
                );
              }
              return null;
            }

            return (
              <Input
                key={"product-" + field}
                label={labels[field]}
                value={formData[field]}
                onChange={(e: any) => {
                  handleInputChange(field, e, formData, setFormData);
                }}
                error={formData.errors[field]}
                clear={() => {
                  clearInput(field, setFormData);
                }}
                infoTooltipText
                number={[
                  "dataAllowance",
                  "smsAllowance",
                  "callAllowance",
                ].includes(field)}
              />
            );
          }
        })}
      </div>
    </Dialog>
  );
};

export default CreateComboProductModal;

const selectOptionsByField: Record<string, any> = {
  country: getCountryOptions(),
  validity: ["30 days", "7 days", "5 days"],
};

const toggleButtonGroupOptionsByField: Record<string, any> = {
  status: [
    {
      label: "Active",
      key: "active",
    },
    {
      label: "Inactive",
      key: "inactive",
    },
  ],
};
