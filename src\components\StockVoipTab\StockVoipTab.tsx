import CollapsiblePanel from "@/components/CollapsiblePanel";
import InsightCard from "@/components/InsightCard";
import styles from "@/styles/orders.module.scss";
import stockStyles from "@/styles/stock-managment.module.scss";
import InsightList from "@/components/InsightList";
import AddProviderModal from "../AddProviderModal";
import BlobSwitchBar from "../BlobSwitchBar";
import {
  handleInputChange,
  createStateObject,
  labels,
  clearInput,
} from "@/components/utils/InputHandlers";
import { useEffect, useRef, useState } from "react";
import { Input } from "@/components/Input";
import Button from "@/components/Button";
import {
  Export,
  MagnifyingGlass,
  Plus,
  Pencil,
  Delete,
} from "@/components/svgs";
import SelectInput from "@/components/SelectInput";
import {
  getStockDidNumbers,
  getStockRates,
  getProviders,
} from "@/components/utils/dataCreator";
import {
  didNumbersStats,
  ratesStats,
  providersStats,
  didNumbersFields,
  ratesFields,
  ProviderFields,
} from "@/components/utils/stockesimFields";
import { formatDateWithTime } from "@/components/utils/formatDate";
import UserSkeleton from "@/components/UserSkeleton";
import TableControl from "@/components/TableControl";
import Tag from "@/components/Tag";
import CountryDisplay from "@/components/CountryDisplay";
import Checkbox from "../Checkbox";
import AvailabilityPill from "../AvailabilityPill";
import DeleteVoipProviderModal from "../DeleteVoipProviderModal";
import DeleteVoipRateModal from "../DeleteVoipRateModal";
import DeleteDIDNumberModal from "../DeleteDIDNumberModal";
import StatusPill from "../StatusPill";
import EditProviderModal from "../EditProviderModal";
import EditRateModal from "../EditRateModal";
import CheckboxDropdownInput from "../CheckboxDropdownInput";

const sections = [
  {
    id: "didNumbers",
    label: "DID Numbers",
  },
  {
    id: "rates",
    label: "Rates",
  },
  {
    id: "providers",
    label: "Providers",
  },
];

const searchdidFields = ["didNumber", "availability", "numberType", "status"];
const searchProvidersFields = ["provider", "type", "status"];
const searchRatesFields = ["provider", "rateType", "currency", "status"];

const StockVoipTab = () => {
  const [data, setData] = useState(createStateObject(searchdidFields));
  const [loading, setLoading] = useState(true);
  const [results, setResults] = useState([]);
  const [tabName, setTabName] = useState("didNumbers");
  const [selectedFields, setSelectedFields] = useState(searchdidFields);
  const [overviewStats, setOverviewStats] = useState(didNumbersStats);
  const [CheckedNumber, setCheckedNumber] = useState([] as any);
  const [deleteModal, setDeleteModal] = useState(false);
  const [fields, setFields] = useState(didNumbersFields);
  const [deleteEditItem, setDeleteEditItem] = useState();
  const [editModal, setEditModal] = useState(false);
  const [addModal, setAddModal] = useState(false);

  const populate = (itemsPerPage: number) => {
    setLoading(true);
    setTimeout(() => {
      if (tabName === "didNumbers") {
        setResults(getStockDidNumbers(itemsPerPage));
      } else if (tabName === "rates") {
        setResults(getStockRates(itemsPerPage));
      } else {
        setResults(getProviders(itemsPerPage));
      }
      setLoading(false);
    }, 500);
  };

  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [numberOfPages, setNumberOfPages] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  useEffect(() => {
    populate(itemsPerPage);
  }, [itemsPerPage, currentPage, tabName]);

  useEffect(() => {
    if (tabName === "didNumbers") {
      setData(createStateObject(searchdidFields));
      setOverviewStats(didNumbersStats);
      setSelectedFields(searchdidFields);
      setFields(didNumbersFields);
    } else if (tabName === "rates") {
      setData(createStateObject(searchRatesFields));
      setOverviewStats(ratesStats);
      setSelectedFields(searchRatesFields);
      setFields(ratesFields);
    } else {
      setData(createStateObject(searchProvidersFields));
      setOverviewStats(providersStats);
      setSelectedFields(searchProvidersFields);
      setFields(ProviderFields);
    }
    setShowSearchResults(false);
  }, [tabName]);

  const searchPanelRef = useRef<any>(null);
  const overviewPanelRef = useRef<any>(null);

  const [showSearchResults, setShowSearchResults] = useState(false);

  const formatDataItem = (item: any, key: string) => {
    if (key === "date") {
      return formatDateWithTime(item[key]);
    } else if (key === "country") {
      if (item[key] !== "-") {
        return <CountryDisplay country={item[key].countryCode} />;
      } else {
        return item[key];
      }
    } else if (key === "status") {
      return (
        <StatusPill
          status={item[key]}
          color={item[key] === "Active" ? "active" : "inactive"}
        />
      );
    } else if (key === "availability") {
      return <AvailabilityPill status={item.availability} />;
    } else {
      return item[key];
    }
  };

  const handleDeleteEdit = (item: any, action: string) => {
    if (action === "delete") {
      setDeleteModal(true);
    } else {
      setEditModal(true);
    }
    setDeleteEditItem(item);
  };

  return (
    <>
      {deleteModal && tabName === "providers" && (
        <DeleteVoipProviderModal
          open={deleteModal}
          onClose={() => setDeleteModal(false)}
          providerData={deleteEditItem}
        />
      )}
      {deleteModal && tabName === "rates" && (
        <DeleteVoipRateModal
          open={deleteModal}
          onClose={() => setDeleteModal(false)}
          rateData={deleteEditItem}
        />
      )}
      {deleteModal && tabName === "didNumbers" && (
        <DeleteDIDNumberModal
          open={deleteModal}
          onClose={() => setDeleteModal(false)}
          phoneNumber={String((deleteEditItem as any)?.didNumber || "")}
        />
      )}

      <AddProviderModal
        open={addModal}
        onClose={() => setAddModal(false)}
        onAdd={(data) => {
          populate(itemsPerPage);
        }}
      />
      {editModal && tabName === "providers" && (
        <EditProviderModal
          open={editModal}
          onClose={() => setEditModal(false)}
          data={deleteEditItem}
        />
      )}
      {editModal && tabName === "rates" && (
        <EditRateModal
          open={editModal}
          onClose={() => setEditModal(false)}
          data={deleteEditItem}
        />
      )}
      <div className={styles.main} style={{ paddingTop: 0 }}>
        <BlobSwitchBar
          options={sections}
          selected={tabName}
          setSelected={(val: string) => {
            setTabName(val);
            setCurrentPage(1);
            setShowSearchResults(false);
            overviewPanelRef.current?.open();
            searchPanelRef.current?.open();
          }}
          layoutId="stock-voip-selection"
        />
        <CollapsiblePanel
          title="Overview"
          summaryWhenClosed={
            <div style={{ marginLeft: 22 }}>
              <InsightList insights={overviewStats} />
            </div>
          }
          actionBtn={
            tabName === "rates" ? (
              <Button>
                <Export />
                Upload Rates
              </Button>
            ) : tabName === "providers" ? (
              <Button
                onClick={(e) => {
                  e.stopPropagation();
                  setAddModal(true);
                }}
              >
                <Plus />
                Add New Provider
              </Button>
            ) : (
              ""
            )
          }
          ref={overviewPanelRef}
        >
          <div className={styles.overview}>
            {overviewStats.map((stat, index) => (
              <InsightCard
                key={index}
                title={stat.title}
                value={stat.value.toString()}
              />
            ))}
          </div>
        </CollapsiblePanel>

        {/* Search orders */}
        <div style={{ marginTop: 16 }}>
          <CollapsiblePanel
            title={`Search ${tabName === "rates" ? "Rates" : tabName === "providers" ? "Providers" : "DID Numbers"}`}
            summaryWhenClosed={
              <div
                style={{
                  display: "flex",
                  flex: 1,
                  justifyContent: "space-between",
                  marginLeft: 16,
                  marginRight: 8,
                }}
              >
                <Tag text="3 filters applied" />
                <Button color="secondary">Clear Filters</Button>
              </div>
            }
            ref={searchPanelRef}
          >
            <div className={styles.fields}>
              {selectedFields.map((prop) => {
                if (["didNumber"].includes(prop)) {
                  return (
                    <Input
                      key={"orders-" + prop}
                      label={labels[prop]}
                      value={data[prop]}
                      onChange={(e: any) => {
                        handleInputChange(prop, e, data, setData);
                      }}
                      error={data.errors[prop]}
                      clear={() => {
                        clearInput(prop, setData);
                      }}
                      infoTooltipText
                    />
                  );
                } else if (
                  [
                    "provider",
                    "type",
                    "currency",
                    "availability",
                    "numberType",
                    "status",
                  ].includes(prop)
                ) {
                  return (
                    <CheckboxDropdownInput
                      key={"voip-tab-" + prop}
                      options={selectOptionsByField[prop]}
                      label={labels[prop]}
                      selected={data[prop]}
                      onChange={(values) => {
                        handleInputChange(
                          prop,
                          values,
                          data,
                          setData,
                          "select"
                        );
                      }}
                      error={data.errors[prop]}
                      infoTooltipText
                    />
                  );
                }
              })}
            </div>
            <div className={styles.actions}>
              <Button
                color="blue"
                onClick={() => {
                  searchPanelRef.current.close();
                  overviewPanelRef.current.close();
                  setShowSearchResults(true);
                }}
              >
                <MagnifyingGlass /> Search
              </Button>
            </div>
          </CollapsiblePanel>
        </div>

        {showSearchResults && (
          <div className={styles.panel}>
            <div className={styles.panelTopBar}>
              {tabName === "didNumbers" && CheckedNumber.length > 0 ? (
                <p>{CheckedNumber.length} items selected</p>
              ) : (
                <h4>
                  {tabName === "rates"
                    ? "Rates"
                    : tabName === "providers"
                      ? "Providers"
                      : "DID Numbers"}
                </h4>
              )}
              <div className={styles.actions}>
                {tabName === "didNumbers" && CheckedNumber.length > 0 ? (
                  <div style={{ display: "flex", gap: "8px" }}>
                    <Button color="secondary" onClick={() => {}}>
                      Activate
                    </Button>
                    <Button color="secondary" onClick={() => {}}>
                      Deactivate
                    </Button>
                    <Button color="customerActionRed" onClick={() => {}}>
                      Delete
                    </Button>
                  </div>
                ) : (
                  <Button color="secondary">
                    <Export /> Export to CSV
                  </Button>
                )}
              </div>
            </div>
            <div className={`${styles.tableContainer} table-scroll`}>
              <table>
                <thead>
                  <tr>
                    {tabName === "didNumbers" && (
                      <th>
                        <div style={{ width: 24 }}>
                          <Checkbox
                            checked={CheckedNumber.length > 0}
                            onClick={() => {
                              if (CheckedNumber.length > 0) {
                                setCheckedNumber([]);
                              } else {
                                setCheckedNumber(results);
                              }
                            }}
                          />
                        </div>
                      </th>
                    )}
                    {fields.map((field: any) => (
                      <th>{field.label}</th>
                    ))}
                    <th></th>
                  </tr>
                </thead>
                <tbody>
                  {!loading
                    ? results.map((item: any) => {
                        return (
                          <tr key={`order-${item.iccid}`}>
                            {tabName === "didNumbers" && (
                              <td>
                                <div style={{ width: 24 }}>
                                  <Checkbox
                                    checked={CheckedNumber.includes(item)}
                                    onClick={() => {
                                      if (CheckedNumber.includes(item)) {
                                        setCheckedNumber(
                                          CheckedNumber.filter(
                                            (change: any) => change !== item
                                          )
                                        );
                                      } else {
                                        setCheckedNumber([
                                          ...CheckedNumber,
                                          item,
                                        ]);
                                      }
                                    }}
                                  />
                                </div>
                              </td>
                            )}
                            {fields.map((field: any) => (
                              <td key={`order-${item.iccid}-${field.key}`}>
                                <div
                                  style={{
                                    display: "flex",
                                    justifyContent: "flex-start",
                                  }}
                                >
                                  {formatDataItem(item, field.key)}
                                </div>
                              </td>
                            ))}
                            <td className={styles.actionCell}>
                              <div className={styles.viewRowBtn}>
                                {tabName !== "didNumbers" && (
                                  <span
                                    className={stockStyles.qrCode}
                                    onClick={() =>
                                      handleDeleteEdit(item, "edit")
                                    }
                                  >
                                    <Pencil />
                                  </span>
                                )}
                                <span
                                  className={stockStyles.qrCode}
                                  onClick={() =>
                                    handleDeleteEdit(item, "delete")
                                  }
                                >
                                  <Delete />
                                </span>
                              </div>
                            </td>
                          </tr>
                        );
                      })
                    : Array.from({ length: itemsPerPage }, (v, i) => i).map(
                        (i) => (
                          <UserSkeleton
                            key={"order-skeleton-" + i}
                            noOfStandard={8}
                          />
                        )
                      )}
                </tbody>
              </table>
            </div>
            <div style={{ marginTop: "16px" }}>
              <TableControl
                show
                itemsPerPage={itemsPerPage}
                setItemsPerPage={(val: any) => setItemsPerPage(val)}
                currentPage={currentPage}
                setCurrentPage={setCurrentPage}
                numberOfPages={numberOfPages}
                label="orders"
                loading={loading}
              />
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default StockVoipTab;

const selectOptionsByField: Record<string, any> = {
  availability: ["Available", "Pending", "Registered"].map((v) => ({
    label: v,
    value: v,
  })),
  type: ["SMS", "Call"].map((v) => ({
    label: v,
    value: v,
  })),
  rateType: ["SMS", "Call"].map((v) => ({
    label: v,
    value: v,
  })),
  status: ["Active", "Inactive"].map((v) => ({
    label: v,
    value: v,
  })),
  numberType: ["Mobile", "Landline"].map((v) => ({
    label: v,
    value: v,
  })),
  currency: ["GBP", "USD"].map((v) => ({
    label: v,
    value: v,
  })),
  provider: [
    "Alpha Provisioning Services",
    "Omega Mobile Systems",
    "Theta Provisioning Platform",
    "Zeta SIM Services",
    "Epsilon Connectivity",
    "Delta Profile Management",
    "Gamma eSIM Solutions",
    "Beta Secure Systems",
  ].map((v) => ({
    label: v,
    value: v,
  })),
};
