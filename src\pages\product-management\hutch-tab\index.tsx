import BlobSwitchBar from "@/components/BlobSwitchBar";
import HutchDataTab from "@/pages/product-management/hutch-tab/data-tab";
import { useState } from "react";
import HutchComboTab from "./combo-tab";
import HutchGlobalCreditTab from "./global-credit-tab";

const HutchProductManagementTab = () => {
  const [activeTabId, setActiveTabId] = useState("data");

  return (
    <div>
      <BlobSwitchBar
        options={[
          {
            label: "Data",
            id: "data",
          },
          {
            label: "Combo",
            id: "combo",
          },
          {
            label: "Global Credit",
            id: "globalCredit",
          },
        ]}
        selected={activeTabId}
        setSelected={setActiveTabId}
        layoutId="hutch-product-management-tabs"
      />

      {activeTabId === "data" && <HutchDataTab />}
      {activeTabId === "combo" && <HutchComboTab />}
      {activeTabId === "globalCredit" && <HutchGlobalCreditTab />}
    </div>
  );
};

export default HutchProductManagementTab;
