import { AxiosError } from "axios";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { ApiGet } from "src/pages/api/api";
import SettingsSkeleton from "../SettingsSkeleton";
import TableControl from "../TableControl";
import { filterList, sortData } from "../utils/searchAndFilter";
import styles from "./activities-table.module.scss";
import AToZOrder from "../AToZOrder";
import DatePicker from "../DatePicker";
import Search from "../Search";
import SearchSelect from "../SearchSelect";
import { formatDateWithTime } from "../utils/formatDate";
import RemoveFiltersBar from "../RemoveFiltersBar";
import { getActivityLog } from "../utils/dataCreator";

const ActivitiesTable = ({ setExportFilter }: any) => {
  const dispatch = useDispatch();
  const { activityLog } = useSelector((state: any) => state);

  const emptyFilters = {
    user: [],
    activity: [],
    description: [],
    timeStamp: {
      start: null,
      end: null,
      startTime: {
        hh: "09",
        mm: "00",
      },
      endTime: {
        hh: "21",
        mm: "00",
      },
    },
    ipAddress: [],
    email: [],
  } as any;

  const [queryDisplay, setQueryDisplay] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [nameOrder, setNameOrder] = useState("a-z");
  const [error, setError] = useState<null | string>(null);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [initialLoading, setInitialLoading] = useState(true);
  const [filters, setFilters] = useState(emptyFilters);
  const [filteredActivityLog, setFilteredActivityLog] = useState([] as any);

  useEffect(() => {
    let sortedActivityLog = sortData(activityLog, nameOrder, "username");
    let filteredActivityLog = filterList(sortedActivityLog, filters);
    setFilteredActivityLog(filteredActivityLog);
    setExportFilter(filteredActivityLog);
    setCurrentPage(1);
  }, [activityLog, filters, nameOrder]);

  const getAllOfType = (data: any, key: string) => {
    let allUsers = [] as any;
    data.forEach((log: any) => {
      if (!allUsers.some((item: any) => item.key === log[key])) {
        allUsers.push({ key: log[key], label: log[key] });
      }
    });
    return allUsers;
  };

  const getActivities = () => {
    dispatch({ type: "set", activityLog: getActivityLog(itemsPerPage) });
    setInitialLoading(false);
  };

  useEffect(() => {
    getActivities();
  }, []);

  const sortByDate = (a: any, b: any) => {
    return new Date(b.timeStamp).getTime() - new Date(a.timeStamp).getTime();
  };

  useEffect(() => {
    setExportFilter(filteredActivityLog.sort(sortByDate));
  }, [filteredActivityLog]);

  return (
    <>
      <RemoveFiltersBar
        filters={filters}
        setFilters={setFilters}
        resetFilters={() => {
          setFilters(emptyFilters);
        }}
      />
      <div className={styles.panel}>
        <div className={`${styles.tableContainer} table-scroll`}>
          <table>
            <thead style={{ backgroundColor: "white" }}>
              <tr>
                <th>
                  <DatePicker
                    label={<div>Date &amp; Time</div>}
                    masterFrom={filters.timeStamp.start}
                    masterUntil={filters.timeStamp.end}
                    startTime={filters.timeStamp.startTime}
                    endTime={filters.timeStamp.endTime}
                    onChange={(
                      newFrom: Date,
                      newUntil: Date,
                      newStartTime: any,
                      newEndTime: any
                    ) => {
                      setFilters({
                        ...filters,
                        timeStamp: {
                          start: newFrom,
                          end: newUntil,
                          startTime: newStartTime,
                          endTime: newEndTime,
                        },
                      });
                    }}
                  />
                </th>
                <th>
                  <Search
                    label={"IP Address"}
                    data={activityLog}
                    setFilteredData={setFilteredActivityLog}
                    setQueryDisplay={setQueryDisplay}
                    placeholder={`Search IP Address`}
                  />
                </th>
                <th>
                  <AToZOrder
                    label={"User"}
                    order={nameOrder}
                    setOrder={setNameOrder}
                  />
                </th>
                <th>
                  <Search
                    label={"Email"}
                    data={activityLog}
                    setFilteredData={setFilteredActivityLog}
                    setQueryDisplay={setQueryDisplay}
                    placeholder={`Search Email`}
                  />
                </th>
                <th>
                  <SearchSelect
                    label={"Activity"}
                    selected={filters["activity"]}
                    options={getAllOfType(activityLog, "activity")}
                    setSelected={(state: any) => {
                      setFilters({
                        ...filters,
                        activity: state,
                      });
                    }}
                    search={true}
                    // noClear={field.key !== "country"}
                  />
                </th>
                <th>
                  <Search
                    label={"Description"}
                    data={activityLog}
                    setFilteredData={setFilteredActivityLog}
                    setQueryDisplay={setQueryDisplay}
                    placeholder={`Search Description`}
                  />
                </th>
              </tr>
            </thead>
            <tbody>
              {initialLoading
                ? Array.from({ length: 7 }, (_, i) => i).map((i) => (
                    <SettingsSkeleton
                      key={"settings-skeleton-" + i}
                      noOfStandard={6}
                    />
                  ))
                : null}
              {error && (
                <tr>
                  <td colSpan={6}>{error}</td>
                </tr>
              )}

              {!error && !initialLoading && filteredActivityLog.length < 1 ? (
                <tr>
                  <td colSpan={6}>No data found</td>
                </tr>
              ) : null}

              {!error && !initialLoading && filteredActivityLog.length > 0
                ? filteredActivityLog
                    .sort(sortByDate)
                    .slice(
                      (currentPage - 1) * itemsPerPage,
                      currentPage * itemsPerPage
                    )
                    .map((provider: any) => (
                      <tr key={provider.id}>
                        <td>{formatDateWithTime(provider.timeStamp)}</td>
                        <td>{provider.ipAddress}</td>
                        <td>{provider.username}</td>
                        <td>{provider.email}</td>
                        <td>{provider.activity}</td>
                        <td>{provider.description}</td>
                      </tr>
                    ))
                : null}
            </tbody>
          </table>
        </div>
        <div style={{ marginTop: "16px" }}>
          <TableControl
            show={true}
            itemsPerPage={itemsPerPage}
            setItemsPerPage={setItemsPerPage}
            currentPage={currentPage}
            setCurrentPage={setCurrentPage}
            numberOfPages={Math.ceil(filteredActivityLog.length / itemsPerPage)}
            label="numbers"
          />
        </div>
      </div>
    </>
  );
};

export default ActivitiesTable;
