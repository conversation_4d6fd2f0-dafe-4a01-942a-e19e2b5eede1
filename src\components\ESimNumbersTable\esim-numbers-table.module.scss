@use "../../styles/theme.scss" as *;
@import "../../styles/mixins.module.scss";

.panel {
  @include stripedTablePanel;
  position: relative;
  thead {
    background: #fff;
  }
}

.tableContainer {
  overflow: auto;
}

.country {
  display: grid;
  grid-template-columns: 24px 1fr;
  grid-column-gap: 8px;
  align-items: center;
  font-weight: 400;
  .flag {
    background-size: cover;
    background-position: center;
    width: 24px;
    height: 24px;
    border-radius: 1000px;
  }
}

.actionButton {
  background: none;
  border: none;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background 0.15s ease;
  border-radius: 8px;
  margin-right: 12px;
  &:last-child {
    margin-right: 0px;
  }
  &:hover {
    background: #fff;
  }
}
.hideDiv {
  display: none;
}
.hideTh {
  opacity: 0;
}

.showDiv {
  display: block; /* or any other desired display value */
}
.newHead {
  width: calc(100% - 48px);
  display: flex;
  gap: 24px;
  align-items: center;
  padding: 0 24px 13px 24px;
  font-size: 14px;
  position: absolute;
  top: 16px;
  z-index: 6000;
  border-bottom: 1px solid #0000141f;
}
