import { useContext, useEffect } from "react";
import { formatDateWithTime, formatReportsDate } from "../utils/formatDate";
import { ApiPostAuth } from "../../pages/api/api";
import styles from "../../styles/reporting.module.scss";
import type { Interval, NewReport } from "@/types/report";
import { v4 } from "uuid";
import { reportFields } from "../utils/reportFields";
import { ReportContext } from "../ReportContext";
import { reportDispatchActions } from "../utils/reportReducer";
import { padArrayToLength } from "../utils/padArray";
import UserSkeleton from "../UserSkeleton";
import { formatNumber } from "../utils/formatNumber";
import { createReport } from "./createDummyData";

const interval: Interval = {
  "last day": 1,
  "last 7 days": 7,
  "last month": 30,
  "last 3 months": 90,
  "last 6 months": 180,
  "last 12 months": 365,
};

const ReportingSMS = () => {
  const {
    state: {
      chartTimeRange,
      filteredReports,
      currentPage,
      reportsPerPage,
      is_loading,
    },
    actions: { dispatch },
  } = useContext(ReportContext);

  useEffect(() => {
    dispatch({ type: reportDispatchActions.IS_LOADING, payload: true });
    const newAllReports = {
      SMS: createReport(reportsPerPage),
    } as NewReport;
    dispatch({
      type: reportDispatchActions.SET_REPORT,
      payload: newAllReports,
    });
  }, [chartTimeRange]);

  return (
    <>
      {!is_loading ? (
        filteredReports.length !== 0 ? (
          padArrayToLength(
            filteredReports.slice(
              (currentPage - 1) * reportsPerPage,
              currentPage * reportsPerPage
            ),
            reportsPerPage,
            null
          ).map((singleProduct: any) => {
            if (singleProduct === null) {
              return (
                <tr
                  key={v4()}
                  style={{
                    visibility: "hidden",
                    pointerEvents: "none",
                  }}
                ></tr>
              );
            } else {
              return (
                <tr key={v4()}>
                  {reportFields["SMS"].map((field: any) => {
                    if (field === "timestamp") {
                      return (
                        <td key={v4()}>
                          {formatDateWithTime(singleProduct[field])}
                        </td>
                      );
                    } else if (field === "source" || field === "destination") {
                      return (
                        <td key={v4()}>{formatNumber(singleProduct[field])}</td>
                      );
                    } else if (field === "cost") {
                      return (
                        <td key={v4()}>
                          {singleProduct[field] === null
                            ? "-"
                            : singleProduct[field].toFixed(2)}
                        </td>
                      );
                    } else {
                      return <td key={v4()}>{singleProduct[field] || "-"}</td>;
                    }
                  })}
                </tr>
              );
            }
          })
        ) : (
          <tr
            style={{
              background: "none",
            }}
          >
            <td colSpan={100}>
              <div className={styles.noneFound}>
                <img src="/none_found.svg" />
                <h3>We couldn't find anything matching these filters</h3>
              </div>
            </td>
          </tr>
        )
      ) : (
        Array.from({ length: reportsPerPage }, (v, i) => i).map((i) => (
          <UserSkeleton
            key={"user-skeleton-" + i}
            noOfStandard={reportFields["SMS"].length}
          />
        ))
      )}
    </>
  );
};

export default ReportingSMS;
