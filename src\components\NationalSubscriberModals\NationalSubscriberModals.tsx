import AddNewNumberModal from "../AddNewNumberModal/AddNewNumberModal";
import { ExclaimCircle } from "../svgs";
import DatePicker from "../DatePicker";
import { useDispatch } from "react-redux";
import { modalsData } from "@/utils/modals-data";
import ActiviteSubscriberModal from "../ActiviteSubscriberModal/ActiviteSubscriberModal";
import { createStateObject, labels, clearInput, handleInputChange } from "../utils/InputHandlers";
import { InputFieldsStep, PlansStep } from "../ModalsSteps/ModalsSteps";
import { useState, useEffect } from "react";
import { Input } from "../Input";
import formatDate from "../utils/formatDate";
import ChangeSubscriptionModal from "../ChangeSubscriptionModal/ChangeSubscriptionModal";
import Dialog from "../Dialog";

const dateInitalDate: { [key: string]: any } = {
  start: null,
  end: null,
  startTime: {
    hh: "09",
    mm: "00",
  },
  endTime: {
    hh: "21",
    mm: "00",
  },
};

const NationalSubscriberModals = ({ data, customModalName, modalName, setModalName, setCustomModalName } : { data:any, customModalName: string, modalName: string, setModalName: Function, setCustomModalName: Function}) => {
    const dispatch = useDispatch()
    const [formData, setFormData] = useState(createStateObject([]))
    const [fields, setFields] = useState<string[]>([])

    useEffect(() => {
        if (modalName && modalsData[modalName].fields) {
          setFormData(createStateObject(modalsData[modalName].fields))
          setFields(modalsData[modalName].fields as string[])
        }
    }, [modalName])

    const handleSubmitModals = (modal:string) => {
        setModalName('')
        if (modalsData[modal].success) {
          dispatch({
            type: "notify",
            payload: {
              error: false,
              message: modalsData[modal].success,
            },
          });
        }
      }

    return (
        <>
            {
                customModalName === 'changeAddressMdn' ? (
                    <AddNewNumberModal show={customModalName === 'changeAddressMdn'} close={() => setCustomModalName('')} endStep={2} />
                ) : ''
            }
            {
                customModalName === 'updateResubmit' && (
                    <ActiviteSubscriberModal show={customModalName === 'updateResubmit'} close={() => setCustomModalName('')} type="updateResubmit" />
                )
            }
            {
                customModalName === 'changeSubscription' && (
                    <ChangeSubscriptionModal show={customModalName === 'changeSubscription'} closeModal={() => setCustomModalName('')} />
                )
            }
            {
                modalName && modalName !== 'updateResubmit' && (
                    <Dialog 
                        headerTitle={modalsData[modalName].title}
                        onClose={() => setModalName('')}
                        open={modalName !== ''}
                        size='sm'
                        headerSubtitle={modalsData[modalName].subTitle + ' ' + data.mdn}
                        headerIcon={modalsData[modalName].icon ? modalsData[modalName].icon : <ExclaimCircle />}
                        confirmButtonText={modalsData[modalName].subBtnText}
                        confirmButtonOnClick={() => handleSubmitModals(modalName)}
                        cancelButtonOnClick={() => setModalName('')}
                        cancelButtonText={modalsData[modalName].closeBtnText}>
                        { modalsData[modalName].content }
                        {
                            ((modalName === 'changeimei') || (modalName === 'changeIccid')) && fields as string[]  ? (
                                <>
                                    {
                                       fields.map((field:string) => (
                                            <Input 
                                                key={`${field}-input`}
                                                label={labels[field]}
                                                value={data[field]}
                                                onChange={(e: any) => {
                                                    handleInputChange(field, e, formData, setFormData);
                                                }}
                                                error={formData.errors[field]}
                                                clear={() => {
                                                    clearInput(field, setFormData);
                                                }}
                                                disabled={(field === 'iccid') || (field === 'imei')}
                                                infoTooltipText
                                            />
                                        ))
                                    }
                                </>
                            ) : modalName === 'suspendSubscription' ? (
                                <DatePicker 
                                    label={'effectiveDate'}
                                    field={'effectiveDate'}
                                    masterFrom={dateInitalDate.start}
                                    masterUntil={dateInitalDate.end}
                                    startTime={dateInitalDate.startTime}
                                    endTime={dateInitalDate.endTime}
                                />
                            ): modalName === 'addTopups' ? (
                                <>
                                    <h5>Top-ups</h5>
                                    <PlansStep data={formData} setData={(val:any) => setFormData(val)} type="topups" />
                                </>
                            ) : modalName === 'changeBill' ? (
                                <>
                                    <Input 
                                        key={`bill-input`}
                                        label={labels['currentCycle']}
                                        value={ formatDate(data.creationDate) }
                                        onChange={(e: any) => {
                                            handleInputChange('currentCycle', e, formData, setFormData);
                                        }}
                                        error={formData.errors['currentCycle']}
                                        clear={() => {
                                            clearInput('currentCycle', setFormData);
                                        }}
                                        disabled
                                    />
                                    <DatePicker 
                                        label={'newCycle'}
                                        field={'newCycle'}
                                        masterFrom={dateInitalDate.start}
                                        masterUntil={dateInitalDate.end}
                                        startTime={dateInitalDate.startTime}
                                        endTime={dateInitalDate.endTime}
                                    />
                                </>
                            ) : modalsData[modalName]?.fields && Object.keys(formData).length > 0 && (
                                <InputFieldsStep data={formData} setData={(val:any) => setFormData(val)} fields={modalsData[modalName]?.fields as string[]} />
                            )
                        }
                </Dialog>
            )
            }
        </>
    )
}

export default NationalSubscriberModals