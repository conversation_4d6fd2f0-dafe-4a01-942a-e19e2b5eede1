import styles from "./rich-text.module.scss";
import { EditorProvider, useCurrentEditor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import {
  Bold,
  InsertAttachment,
  InsertList,
  InsertOrderedList,
  Italic,
  StrikeThrough,
  Underline as InsertUnderline,
} from "../svgs";
import Button from "../Button";
import Underline from "@tiptap/extension-underline";
import Placeholder from "@tiptap/extension-placeholder";

// define your extension array
const extensions = [
  StarterKit,
  Underline,
  Placeholder.configure({
    placeholder: "Add a note...",
  }),
];

const RichTextEditor = () => {
  return (
    <div className={styles.container}>
      <EditorProvider
        extensions={extensions}
        editorContainerProps={{ className: styles.editor }}
        slotAfter={<MenuBar />}
      ></EditorProvider>
    </div>
  );
};

export default RichTextEditor;

const MenuBar = () => {
  const { editor } = useCurrentEditor();
  return (
    <div className={styles.footer}>
      <div className={styles.topDivider} />
      <div className={styles.buttons}>
        <button
          onClick={() => editor?.chain().focus().toggleBulletList().run()}
          className={editor?.isActive("bulletList") ? styles.active : ""}
        >
          <InsertList />
        </button>
        <button
          onClick={() => editor?.chain().focus().toggleOrderedList().run()}
          className={editor?.isActive("orderedList") ? styles.active : ""}
        >
          <InsertOrderedList />
        </button>
        <div className={styles.divider} />
        <button
          onClick={() => editor?.chain().focus().toggleBold().run()}
          disabled={!editor?.can().chain().focus().toggleBold().run()}
          className={editor?.isActive("bold") ? styles.active : ""}
        >
          <Bold />
        </button>
        <button
          onClick={() => editor?.chain().focus().toggleItalic().run()}
          disabled={!editor?.can().chain().focus().toggleItalic().run()}
          className={editor?.isActive("italic") ? styles.active : ""}
        >
          <Italic />
        </button>
        <button
          onClick={() => editor?.chain().focus().toggleUnderline().run()}
          disabled={!editor?.can().chain().focus().toggleUnderline().run()}
          className={editor?.isActive("underline") ? styles.active : ""}
        >
          <InsertUnderline />
        </button>
        <button
          onClick={() => editor?.chain().focus().toggleStrike().run()}
          disabled={!editor?.can().chain().focus().toggleStrike().run()}
          className={editor?.isActive("strike") ? styles.active : ""}
        >
          <StrikeThrough />
        </button>
        <div className={styles.divider} />
        <button>
          <InsertAttachment />
        </button>
      </div>
      <Button>Add Note</Button>
    </div>
  );
};
