import styles from "./voice-mailcustomer.module.scss";
import { Delete, StopWatch } from "../svgs";
import StatusPill from "../StatusPill";

const VoiceMailCustomer = ({ data, setShowVoiceMail }: any) => {
  return (
    <div className={styles.modalContent}>
      <div>
        <div>
          <div>
            <StatusPill status={data.status} />
          </div>
        </div>
        <div
          className={styles.delete}
          onClick={() => {
            setShowVoiceMail(true);
          }}
        >
          <Delete />
        </div>
      </div>
      <div>
        <div>
          <span>
            <div
              className={styles.flag}
              style={{ backgroundImage: `url(${data.country.iconURL})` }} />
          </span>
            <div className={styles.description}>
              <p>Voicemail Name here</p>
              <span>{data.number}</span>
            </div>
        </div>
        <div className={styles.description}>
            <span>{data.time} secs </span>
        </div>
      </div>
    </div>
  );
};

export default VoiceMailCustomer;
