import styles from "./search-select.module.scss";
import { ControlledMenu, useMenuState } from "@szhsin/react-menu";
import "@szhsin/react-menu/dist/index.css";
import "@szhsin/react-menu/dist/transitions/slide.css";
import { ChevronDown } from "../svgs";
import { useEffect, useRef, useState } from "react";
import Checkbox from "../Checkbox";
import Button from "../Button";
import SearchBar from "../SearchBar";
import { submitFilterSelectSearch } from "../utils/searchAndFilter";
import { createPortal } from "react-dom";

const SearchSelect = ({
  label,
  options,
  selected,
  setSelected,
  grid,
  twoColumnGrid,
  noClear,
  search,
  searchPlaceholder = "Search",
}: any) => {
  const ref = useRef(null);

  const [menuProps, toggleMenu] = useMenuState({ transition: true });

  const [stagedChanges, setStagedChanges] = useState([...selected]);

  useEffect(() => {
    setStagedChanges([...selected]);
  }, [selected]);

  const reset = () => {
    toggleMenu(false);
    setQuery("");
    setFilteredOptions(options);
  };

  const [query, setQuery] = useState("");

  const [filteredOptions, setFilteredOptions] = useState([] as any);

  useEffect(() => {
    setFilteredOptions(options);
  }, [options]);

  useEffect(() => {
    if (query === "") {
      submitFilterSelectSearch(options, setFilteredOptions, query, setQuery);
    }
  }, [query]);

  return (
    <div className={`${styles.box} multi-select select`}>
      <div
        ref={ref}
        className={`${styles.menuButton} ${
          menuProps.state === "open" || menuProps.state === "opening"
            ? styles.iconOpen
            : styles.iconClosed
        }`}
        onClick={() => {
          if (menuProps.state === "closing") {
            toggleMenu(false);
          } else {
            toggleMenu(true);
          }
        }}
      >
        {label}
        <ChevronDown />
      </div>
      {createPortal(
        <div className="select multi-select">
          <ControlledMenu
            {...menuProps}
            anchorRef={ref}
            onClose={() => {
              reset();
              setStagedChanges([...selected]);
            }}
            align="center"
            position="auto"
            viewScroll="close"
            onItemClick={(e) => (e.keepOpen = true)}
          >
            {search && (
              <div style={{ marginBottom: 32 }}>
                <SearchBar
                  onSubmit={() => {
                    submitFilterSelectSearch(
                      options,
                      setFilteredOptions,
                      query,
                      setQuery
                    );
                  }}
                  query={query}
                  setQuery={setQuery}
                  placeholder={searchPlaceholder}
                  small
                  grey
                />
              </div>
            )}
            <div
              className={`${styles.container} ${grid && styles.grid} ${
                twoColumnGrid && styles.twoColumnGrid
              } modal-scroll`}
            >
              {filteredOptions?.map((item: any) => (
                <div
                  className={`${styles.menuItem} ${
                    selected === item && styles.selected
                  }`}
                  key={item.key}
                >
                  <div style={{ width: 24 }}>
                    <Checkbox
                      checked={stagedChanges.includes(item.key)}
                      onClick={() => {
                        if (stagedChanges.includes(item.key)) {
                          setStagedChanges(
                            stagedChanges.filter(
                              (change) => change !== item.key
                            )
                          );
                        } else {
                          setStagedChanges([...stagedChanges, item.key]);
                        }
                      }}
                    />
                  </div>
                  {item.label}
                </div>
              ))}
            </div>
            <div className={styles.buttons}>
              {!noClear && (
                <Button
                  color="quaternary"
                  style={{
                    marginRight: "auto",
                    opacity:
                      stagedChanges.length !== 0 || selected.length !== 0
                        ? 1
                        : 0,
                    pointerEvents:
                      stagedChanges.length !== 0 || selected.length !== 0
                        ? "all"
                        : "none",
                  }}
                  onClick={() => {
                    setStagedChanges([]);
                  }}
                >
                  Clear All
                </Button>
              )}
              <Button
                onClick={() => {
                  setStagedChanges(selected);
                  reset();
                }}
                style={{
                  marginRight: 12,
                  marginLeft: noClear ? 0 : 20,
                  minWidth: 0,
                }}
                color="secondary"
              >
                Cancel
              </Button>
              <Button
                style={{ minWidth: 0 }}
                onClick={() => {
                  setSelected(stagedChanges);
                  reset();
                }}
              >
                Apply
              </Button>
            </div>
          </ControlledMenu>
        </div>,
        document.getElementById("root")!
      )}
    </div>
  );
};

export default SearchSelect;
