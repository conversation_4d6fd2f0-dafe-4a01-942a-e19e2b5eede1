import Dialog from "@/components/Dialog";
import { FloppyDisk, PencilCircle } from "@/components/svgs";
import { clearInput, createStateObject, createStateObjectWithInitialState, handleInputChange, labels } from "@/components/utils/InputHandlers";
import { useState } from "react";
import styles from "./edit-fee-modal.module.scss"
import { Input } from "@/components/Input";
import ToggleButtonGroup from "@/components/ToggleButtonGroup";
import SelectInput from "@/components/SelectInput";


type EditFeeModalProps = {
  feeData: any;
  open: boolean;
  onClose: () => void;
}

const EditFeeModal = ({
  open,
  onClose,
  feeData
}: EditFeeModalProps) => {
  const fields = ["type", "name", "amount", "status"];
  const [formData, setFormData] = useState(createStateObjectWithInitialState(createStateObject(fields), {
    type: feeData.type,
    name: feeData.name,
    amount: feeData.amount,
    status: feeData.status,
  }))

  return (
    <Dialog
      open={open}
      onClose={onClose}
      size="sm"
      headerIcon={<PencilCircle />}
      headerTitle="Edit Fee"
      confirmButtonText="Save Changes"
      confirmButtonOnClick={onClose}
      confirmButtonIcon={<FloppyDisk />}
      cancelButtonText="Cancel"
    >
      <div className={styles.formContainer}>
        {fields.map(field => {
          if (["status"].includes(field)) {
            return (
              <ToggleButtonGroup
                selected={formData[field] ? "active" : "inactive"}
                options={[{
                  label: "Active",
                  key: "active"
                }, {
                  label: "Inactive",
                  key: "inactive"
                }]}
                label={labels[field]}
                onChange={(value) => {
                  handleInputChange(
                    field,
                    { target: { value } },
                    formData,
                    setFormData
                  )
                }}
              />
            )
          }

          else if (["type"].includes(field)) {
            return (
              <SelectInput
                key={"fee-" + field}
                options={["Regulatory", "Activation"]}
                label={labels[field]}
                selected={formData[field]}
                onChange={(e: any) => {
                  handleInputChange(field, { target: { value: e } }, formData, setFormData);
                }}
                error={formData.errors[field]}
                infoTooltipText
              />)
          }

          else {
            return (
              <Input
                key={"fee-" + field}
                label={labels[field]}
                value={formData[field]}
                onChange={(e: any) => {
                  handleInputChange(field, e, formData, setFormData)
                }}
                error={formData.errors[field]}
                clear={() => {
                  clearInput(field, setFormData)
                }}
                infoTooltipText
              />
            )
          }
        })}
      </div>
    </Dialog>
  )
}

export default EditFeeModal