import Dialog from "@/components/Dialog";
import { PlusCircle } from "@/components/svgs";
import { clearInput, createStateObject, handleInputChange, labels } from "@/components/utils/InputHandlers";
import { useState } from "react";
import styles from "./create-voip-call-product-modal.module.scss"
import { Input } from "@/components/Input";
import SelectInput from "@/components/SelectInput";
import ToggleButtonGroup from "@/components/ToggleButtonGroup";
import { countryListAlpha2 } from "@/components/utils/countryList";
import { getCountryOptions } from "../utils/getCountryOptions";

type CreateVoipCallProductModalProps = {
  open: boolean;
  onClose: () => void
}

const CreateVoipCallProductModal = ({
  open,
  onClose
}: CreateVoipCallProductModalProps) => {
  const fields = ["country", "validity", "callAllowance", "usdPrice", "gbpPrice", "eurPrice", "status"];
  const [formData, setFormData] = useState(createStateObject(fields))

  return (
    <Dialog
      open={open}
      onClose={onClose}
      size="sm"
      headerIcon={<PlusCircle />}
      headerTitle="Create Call Product"
      confirmButtonText="Create Product"
      confirmButtonOnClick={onClose}
      cancelButtonText="Cancel"
    >
      <div className={styles.formContainer}>
        {fields.map(field => {
          if (field === "country") {
            return (
              <SelectInput
                key={"product-" + field}
                options={getCountryOptions()}
                label={labels[field]}
                selected={formData[field]}
                onChange={(e: any) => {
                  handleInputChange(field, { target: { value: e } }, formData, setFormData);
                }}
                error={formData.errors[field]}
                infoTooltipText
              />
            )
          }
          else if (field === "validity") {
            return (
              <SelectInput
                key={"product-" + field}
                options={selectOptionsByField[field]}
                label={labels[field]}
                value={formData[field]}
                onChange={(e: any) => {
                  handleInputChange(field, e, formData, setFormData);
                }}
                error={formData.errors[field]}
                infoTooltipText
              />
            )
          }
          else if (field === "callAllowance") {
            return (
              <Input
                key={"product-" + field}
                label={labels[field]}
                value={formData[field]}
                onChange={(e: any) => {
                  handleInputChange(field, e, formData, setFormData);
                }}
                error={formData.errors[field]}
                clear={() => {
                  clearInput(field, setFormData)
                }}
                infoTooltipText
                number
              />
            )
          }
          else if (["usdPrice", "gbpPrice", "eurPrice"].includes(field)) {
            if (field === "usdPrice") {
              return (
                <div key={"price-fields"} className={styles.priceFields}>
                  {["usdPrice", "gbpPrice", "eurPrice"].map(priceField => (
                    <Input
                      key={"product-" + priceField}
                      label={labels[priceField]}
                      value={formData[priceField]}
                      onChange={(e: any) => {
                        handleInputChange(priceField, e, formData, setFormData)
                      }}
                      error={formData.errors[priceField]}
                      clear={() => {
                        clearInput(priceField, setFormData)
                      }}
                      infoTooltipText
                      number
                    />
                  ))}
                </div>
              )
            }
          }
          else if (field === "status") {
            return (
              <ToggleButtonGroup
                key={"product-" + field}
                selected={formData[field]}
                options={toggleButtonGroupOptions}
                label={labels[field]}
                onChange={(value) => {
                  handleInputChange(
                    field,
                    { target: { value } },
                    formData,
                    setFormData
                  )
                }}
              />
            )
          }
          return null;
        })}
      </div>
    </Dialog>
  )
}

export default CreateVoipCallProductModal

const selectOptionsByField: Record<string, any> = {
  country: getCountryOptions(),
  validity: ["30 days", "7 days", "5 days"].map(v => ({
    label: v,
    key: v
  }))
}

const toggleButtonGroupOptions = [{
  label: "Active",
  key: "active"
}, {
  label: "Inactive",
  key: "inactive"
}] 