import styles from "./status-switch.module.scss";

const StatusSwitch = ({ status, setStatus, noTransition }: any) => {
  
  return (
    <div>
      <div className={styles.label}>Status</div>
      <div className={styles.container}>
        <div
          onClick={() => {
            setStatus(true);
          }}
          className={`${styles.role} ${status && styles.active}`}
        >
          Active
        </div>
        <div
          onClick={() => {
            setStatus(false);
          }}
          className={`${styles.role} ${!status && styles.active}`}
        >
          Inactive
        </div>
      </div>
    </div>
  );
};

export default StatusSwitch;
