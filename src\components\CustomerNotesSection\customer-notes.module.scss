.panelTopBar {
  width: 100%;
  display: flex;
  align-items: center;
  margin-top: 16px;
  .actions {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
  h4 {
    font-size: 16px;
    font-weight: 700;
    line-height: 22px;
  }
}

.notesContainer {
  margin-top: 16px;
  padding-bottom: 16px;
  padding-right: 16px;
  max-width: 670px;
  width: 100%;
  height: calc(100% - 120px);
}

.note {
  display: grid;
  grid-template-columns: 24px 1fr;
  grid-column-gap: 12px;
  width: 100%;
  font-size: 14px;
  line-height: 18px;
  margin-top: 24px;
  .circle {
    width: 24px;
    height: 24px;
    border-radius: 100px;
    background-color: #2e70e5;
  }
  .top {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 6px;
    color: #525a6b;
  }
  .content {
    color: #061632;
  }
}
