import Button from "../Button";
import styles from "./plan-card.module.scss";

type PlanCardProps = {
  planName?: string;
  price?: string;
  features?: string;
  selected?: boolean;
  onSelect?: () => void;
  standaloneMode?: boolean;
  onEdit?: () => void;
  onRemove?: () => void;
};

const PlanCard = ({
  planName = "Plan Name",
  price = "£10.00",
  features = "10GB | 100mins | 100SMS | 30 Days Validity | £1,99 free global credit",
  standaloneMode,
  onEdit,
  onRemove,
  selected = false,
  onSelect = () => {},
}: PlanCardProps) => {
  return (
    <div
      className={`${styles.planCard} ${selected ? styles.selected : ""}`}
      onClick={onSelect}
    >
      {!standaloneMode && (
        <div className={styles.selectionArea}>
          <div className={styles.radioCircle}></div>
        </div>
      )}

      <div className={styles.planInfo}>
        <div className={styles.mainDetails}>
          <span className={styles.planName}>{planName}</span>
          <span className={styles.planPrice}>{price}</span>
        </div>

        <div className={styles.planFeatures}>{features}</div>

        {standaloneMode && (
          <div className={styles.actions}>
            <Button color="secondary" onClick={onEdit}>
              Edit Product
            </Button>
            <Button color="secondary" onClick={onRemove}>
              Remove
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default PlanCard;
