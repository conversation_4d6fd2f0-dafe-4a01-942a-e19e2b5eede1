@use "../../styles/theme.scss" as *;

.menuButton {
  height: 32px;
  background: #f7f6f6;
  padding: 5.5px 6px 5.5px 12px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  line-height: 21px;
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  transition: background 0.2s ease;
  -webkit-user-select: none; /* Safari */
  -ms-user-select: none; /* IE 10 and IE 11 */
  user-select: none;
  &:hover {
    background: $light-orange;
  }
  svg {
    transition: all 0.2s ease;
  }
  &.iconOpen {
    svg {
      transform: rotate(180deg);
    }
  }
}

.label {
  line-height: 18px;
  font-size: 14px;
  pointer-events: none;
  color: #061632;
  margin-bottom: 4px;
}

.box {
  height: 100%;
}

.menuItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 6px;
  transition: all 0.1s ease;
  color: $black;
  padding: 0;
  line-height: 21px;
  padding: 2.5px 0 2.5px 12px;
  gap: 23px;
  &:hover {
    color: $dark-orange;
    background: none;
  }
  &.selected {
    pointer-events: none;
  }
}
