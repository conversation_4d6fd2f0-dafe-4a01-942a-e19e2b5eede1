import { SwitchTransition, CSSTransition } from "react-transition-group";
import styles from "./mobile-type-switch.module.scss";

const MobileTypeSwitch = ({ status, setStatus }: any) => {
  return (
    <>
      <div className={styles.label}>Select type</div>
      <div className={styles.container}>
        <div
          onClick={() => {
            setStatus("mobile");
          }}
          className={styles.role}
        >
          Mobile
        </div>
        <div
          onClick={() => {
            setStatus("landline");
          }}
          className={styles.role}
        >
          Landline
        </div>
        <div
          className={`${styles.thumb} ${status === "landline" && styles.right}`}
        >
          <SwitchTransition>
            <CSSTransition
              key={status}
              addEndListener={(node, done) =>
                node.addEventListener("transitionend", done, false)
              }
              classNames="fade"
            >
              <div>{status === "mobile" ? <>Mobile</> : <>Landline</>}</div>
            </CSSTransition>
          </SwitchTransition>
        </div>
      </div>
    </>
  );
};

export default MobileTypeSwitch;
