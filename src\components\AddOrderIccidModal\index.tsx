import Dialog from "@/components/Dialog";
import { PlusCircle } from "@/components/svgs";
import styles from "./add-order-iccid-modal.module.scss";
import { useState } from "react";
import {
  clearInput,
  createStateObject,
  handleInputChange,
  labels,
} from "@/components/utils/InputHandlers";
import { Input } from "@/components/Input";
import Button from "@/components/Button";

type AddOrderIccidModalProps = {
  open: boolean;
  onClose: () => void;
  activeOrder: any;
};

const AddOrderIccidModal = ({
  open,
  onClose,
  activeOrder,
}: AddOrderIccidModalProps) => {
  const fields = ["iccid"];
  const [formData, setFormData] = useState(createStateObject(fields));

  return (
    <Dialog
      open={open}
      onClose={onClose}
      size="sm"
      headerIcon={<PlusCircle />}
      headerTitle="Add ICCID"
      confirmButtonText="Add ICCID"
      cancelButtonText="Cancel"
      confirmButtonOnClick={onClose}
    >
      <div className={styles.tableContainer}>
        <table className={styles.table}>
          <tbody>
            <tr>
              <td className={styles.label}>Subscriber</td>
              <td className={styles.value}>{activeOrder.subscriber}</td>
            </tr>
            <tr>
              <td className={styles.label}>Product Name</td>
              <td className={styles.value}>{activeOrder.product}</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div className={styles.formContainer}>
        <Input
          label="ICCID"
          value={formData["ICCID"]}
          onChange={(e: any) => {
            handleInputChange("iccid", e, formData, setFormData);
          }}
          error={formData.errors["iccid"]}
          clear={() => {
            clearInput("iccid", setFormData);
          }}
          infoTooltipText
        />
      </div>
    </Dialog>
  );
};

export default AddOrderIccidModal;
