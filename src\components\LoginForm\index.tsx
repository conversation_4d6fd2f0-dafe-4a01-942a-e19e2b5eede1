import { Input } from "../Input";
import styles from "./login-form.module.scss";
import { useEffect, useState } from "react";
import Button from "../Button";
import { useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import { validateAll } from "indicative/validator";
import $ from "jquery";
import {
  clearInput,
  createStateObject,
  displayErrors,
  getMessages,
  getRules,
  handleInputChange,
  labels,
  placeholders,
} from "../utils/InputHandlers";

const fields = ["email", "password"];
const rules = getRules(fields);
const messages = getMessages(fields);

const LoginForm = ({ handleForcePasswordChange }: any) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  /***********  Init State  ***********/

  const [loginInfo, setLoginInfo] = useState(createStateObject(fields));

  const [loading, setLoading] = useState(false);

  const login = () => {
    const data = {
      email: loginInfo.email.trim(),
      password: loginInfo.password.trim(),
    };

    validateAll(data, rules, messages)
      .then((response) => {
        dispatch({
          type: "set",
          isLoggedIn: true,
        });

        const user = {
          firstName: "Robin",
          lastName: "Billington",
          role: 1,
          roleName: "Supervisor",
          email: "<EMAIL>",
        };

        dispatch({ type: "set", userInfo: user });
        localStorage.setItem("crmUserInfo", JSON.stringify(user));
        navigate("/select-project");
        /*setLoading(true);
        ApiPost("/agent/login", {
          email: data.email,
          password: data.password,
        })
          .then((response) => {
            console.log(response);
            localStorage.setItem("token", response.data.auth);
            console.log(response.data.roleName);
            dispatch({
              type: "set",
              isLoggedIn: true,
            });

            dispatch({
              type: "set",
              loginMessage: response.data.message,
              closeLoginMessage: false,
            });
            ApiGet("/agent/details").then((response) => {
              dispatch({ type: "set", userInfo: response.data });
              localStorage.setItem(
                "crmUserInfo",
                JSON.stringify(response.data)
              );
              navigate("/dashboard");
            });
          })
          .catch((error) => {
            setLoading(false);
            dispatch({
              type: "notify",
              payload: {
                error: true,
                message: error.response.data.message,
              },
            });
          });*/
      })
      .catch((errors) => {
        displayErrors(errors, setLoginInfo);

        if (!data.email) {
          $("#login-email").trigger("focus");
        } else if (!data.password) {
          $("#login-password").trigger("focus");
        }
      });
  };

  useEffect(() => {
    setLoginInfo({
      email: "<EMAIL>",
      password: "Password123!",
      errors: {
        email: "",
        password: "",
      },
    });
  }, []);

  return (
    <div className={styles.form}>
      <h2>Log in</h2>
      <p>Tools for outstanding service are just a step away.</p>
      {fields.map((key: string) => (
        <Input
          label={key === "password" ? 'Your Password' :labels[key]}
          placeholder={placeholders[key]}
          value={loginInfo[key]}
          disabled={loading}
          onChange={(e: any) => {
            handleInputChange(key, e, loginInfo, setLoginInfo);
          }}
          error={loginInfo.errors[key]}
          clear={() => {
            clearInput(key, setLoginInfo);
          }}
          id={"login-" + key}
          onKeyDown={login}
          passwordLink={key === "password"}
          password={key === "password"}
        />
      ))}
      <Button
        style={{ marginTop: 30, height: 48 }}
        loading={loading}
        color="primary"
        onClick={login}>
        Log in
      </Button>
    </div>
  );
};

export default LoginForm;
