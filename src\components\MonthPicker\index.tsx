import { Fade } from "@mui/material";
import { useRef, useState } from "react";
import { ChevronDown } from "../svgs";
import styles from "./month-picker.module.scss";
import { ControlledMenu, useMenuState } from "@szhsin/react-menu";
import $ from "jquery";

export const months = [
  "Jan",
  "Feb",
  "Mar",
  "Apr",
  "May",
  "Jun",
  "Jul",
  "Aug",
  "Sep",
  "Oct",
  "Nov",
  "Dec",
];

const getYears = (future: boolean) => {
  const now = new Date();
  const nowYear = now.getFullYear();
  let yearList = [nowYear.toString()];
  if (future) {
    for (let i = 1; i < 12; i++) {
      yearList.push((nowYear + i).toString());
    }
  } else {
    for (let i = 1; i < 12; i++) {
      yearList.unshift((nowYear - i).toString());
    }
  }
  return yearList;
};

const MonthPicker = ({
  activeMonth,
  setActiveMonth,
  activeYear,
  setActiveYear,
  future,
}: any) => {
  const [selectingYear, setSelectingYear] = useState(true);

  const ref = useRef(null);

  const [menuProps, toggleMenu] = useMenuState({ transition: true });

  const years = getYears(future);

  return (
    <div className={`${styles.box}`}>
      <div
        ref={ref}
        className={`${styles.menuButton} ${
          menuProps.state === "open" || menuProps.state === "opening"
            ? styles.iconOpen
            : styles.iconClosed
        }`}
        onClick={(e: any) => {
          e.stopPropagation();
          if (menuProps.state === "closing") {
            toggleMenu(false);
          } else {
            toggleMenu(true);
          }
        }}
      >
        {months[activeMonth - 1]} {activeYear}
        <ChevronDown />
      </div>
      <ControlledMenu
        {...menuProps}
        anchorRef={ref}
        onClose={(e: any) => {
          console.log(e);
          setTimeout(() => {
            setSelectingYear(true);
          }, 300);
          toggleMenu(false);
        }}
        align="center"
        viewScroll="initial"
        position="initial"
        onItemClick={(e: any) => (e.stopPropagation = true)}
      >
        {selectingYear ? (
          <div className={`${styles.yearMenu} ${styles.menu}`}>
            <div className={styles.topBar}>
              <div className={styles.selectYear}>Year</div>
            </div>
            <div className={styles.months}>
              {years.map((year) => (
                <div
                  className={`${styles.month} ${
                    activeYear == year && styles.active
                  }`}
                  onClick={() => {
                    setActiveYear(parseInt(year));
                    setSelectingYear(false);
                  }}
                  key={year}
                >
                  {year}
                </div>
              ))}
            </div>
          </div>
        ) : (
          <div className={styles.menu}>
            <div className={styles.topBar}>
              <div
                className={styles.selectYear}
                style={{ cursor: "pointer" }}
                onClick={() => {
                  setSelectingYear(true);
                }}
              >
                {activeYear} <ChevronDown />
              </div>
            </div>
            <div className={styles.months}>
              {months.map((month: string, index: number) => (
                <div
                  className={`${styles.month} ${
                    activeMonth - 1 === index && styles.active
                  }`}
                  onClick={(e) => {
                    e.stopPropagation();
                    setActiveMonth(index + 1);
                    $("#close-month-menu").trigger("focus");
                    setSelectingYear(true);
                  }}
                  key={month}
                >
                  {month}
                </div>
              ))}
            </div>
          </div>
        )}
      </ControlledMenu>
    </div>
  );
};

export default MonthPicker;
