import styles from "../DeleteTicketModal/deleteTicketModal.module.scss";
import { WarningCircle } from "../svgs";
import { useDispatch } from "react-redux";
import { formatDateWithTime } from "../utils/formatDate";
import StatusPill from "../StatusPill";
import { labels } from "../utils/InputHandlers";
import Dialog from "../Dialog";

const fields = [
  "type",
  "amount",
  "applyTo",
  "creationDate",
  "expiryDate",
  "promocode",
  "coderesuable",
  "maxuses",
  "status",
];

const DeletePromotionModal = ({
  show,
  setShow,
  promotion,
  repopulate,
}: any) => {
  const dispatch = useDispatch();

  const formatDataItem = (item: any) => {
    if (item === "creationDate" || item === "expiryDate") {
      return formatDateWithTime(promotion[item]);
    } else if (item === "status") {
      return <StatusPill status={promotion[item] === "Active"} />;
    } else if (item === "amount") {
      return `$${promotion[item]}`;
    } else if (item === "promocode") {
      return promotion["code"];
    } else {
      return promotion[item];
    }
  };

  const handleSuccess = () => {
    dispatch({
      type: "notify",
      payload: {
        error: false,
        message: "Promotion has been deleted successfully",
      },
    });
    setShow(false);
    repopulate(15);
  };

  return (
    <Dialog
      confirmButtonText="Yes, Delete Promotion"
      headerTitle="Delete Promotion?"
      headerSubtitle="Once removed, it cannot be recovered."
      confirmButtonVariant="customerActionRed"
      headerIcon={<WarningCircle />}
      open={show === "delete"}
      onClose={() => {
        setShow("");
      }}
      size="sm"
      cancelButtonText="Cancel"
      confirmButtonOnClick={() => handleSuccess()}
    >
      <div className={styles.main}>
        <div className={styles.table}>
          {fields.map((row: any) => (
            <div className={styles.tableRow} key={row.key}>
              <div className={styles.labels}>
                <p>{labels[row]}</p>
              </div>
              <div className={styles.value}>{formatDataItem(row)}</div>
            </div>
          ))}
        </div>
      </div>
    </Dialog>
  );
};

export default DeletePromotionModal;
