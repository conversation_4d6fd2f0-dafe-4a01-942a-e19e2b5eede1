import { useEffect, useState } from "react";
import Button from "@/components/Button";
import { Export } from "@/components/svgs";
import styles from "./report-tables.module.scss";
import UserSkeleton from "@/components/UserSkeleton";
import TableControl from "@/components/TableControl";
import ToggleButtonGroup from "@/components/ToggleButtonGroup";
import { faker } from "@faker-js/faker";
import StatusPill from "@/components/StatusPill";
import { formatDateWithTime } from "@/components/utils/formatDate";
import { useDispatch } from "react-redux";
import AvailabilityPill from "../AvailabilityPill";

const ReportTables = () => {
  const dispatch = useDispatch();
  const [reportFilters, setReportFilters] = useState<any>({
    reportType: undefined,
    reportPeriod: undefined,
  });
  const [showReportView, setShowReportView] = useState(false);

  const handleShowReport = () => {

    if (!reportFilters.reportType && !reportFilters.reportPeriod) {
      dispatch({
        type: "notify",
        payload: {
          error: true,
          message: "Please select the report type and report period",
        },
      });
    } else if (!reportFilters.reportType || !reportFilters.reportPeriod) {
      dispatch({
        type: "notify",
        payload: {
          error: true,
          message: `Please select the ${!reportFilters.reportType ? ' report type' : ' report period'}`,
        },
      });
    } else {
      setShowReportView(true)
    }

  }

  return (
    <>
      {!showReportView ? (
        <>
          {/* Report Filters View */}
          <div className={styles.panel}>
            <div className={styles.reportFiltersHeader}>
              <h2>Reports</h2>
              <p>Select a type and a period and click "Run Report".</p>
            </div>

            {/* Report Filters Form */}
            <div className={styles.reportFiltersForm}>
              <div className={styles.reportFiltersFormRow}>
                <h5>Report Type</h5>
                <ToggleButtonGroup
                  options={reportTypeOptions}
                  onChange={(val: any) => setReportFilters({ ...reportFilters, reportType: val })}
                  selected={reportFilters.reportType}
                />
              </div>
              <div className={styles.reportFiltersFormRow}>
                <h5>Period</h5>
                <ToggleButtonGroup
                  options={reportPeriodOptions}
                  onChange={(val: any) => setReportFilters({ ...reportFilters, reportPeriod: val })}
                  selected={reportFilters.reportPeriod}
                />
              </div>

              <div className={styles.reportFiltersButtonWrapper}>
                <Button onClick={() => handleShowReport()}>
                  Run Report
                </Button>
              </div>
            </div>
          </div>
        </>
      ) : (
        <>
          {/* Report Views */}
          {reportFilters.reportType === "subscriptions" && (
            <SubscriptionsReport onClearReport={() => setShowReportView(false)} />
          )}
          {reportFilters.reportType === "churn" && (
            <ChurnReport onClearReport={() => setShowReportView(false)} />
          )}
          {reportFilters.reportType === "voice" && (
            <VoiceReport onClearReport={() => setShowReportView(false)} />
          )}
          {reportFilters.reportType === "sms" && (
            <SMSReport onClearReport={() => setShowReportView(false)} />
          )}
          {reportFilters.reportType === "numbers" && (
            <NumbersReport onClearReport={() => setShowReportView(false)} />
          )}
          {reportFilters.reportType === "finance" && (
            <FinanceReport onClearReport={() => setShowReportView(false)} />
          )}
        </>
      )}
    </>
  )
};

export default ReportTables;

const reportTypeOptions = [
  {
    label: "Subscriptions",
    key: "subscriptions"
  },
  {
    label: "Churn",
    key: "churn"
  },
  {
    label: "Voice",
    key: "voice"
  },
  {
    label: "SMS",
    key: "sms"
  },
  {
    label: "Numbers",
    key: "numbers"
  },
  {
    label: "Finance",
    key: "finance"
  }
]

const reportPeriodOptions = [
  // last 7 days, last 14 days, last month, custom  
  {
    label: "Last 7 days",
    key: "last7days"
  },
  {
    label: "Last 14 days",
    key: "last14days"
  },
  {
    label: "Last Month",
    key: "lastmonth"
  },
  {
    label: "Custom",
    key: "custom"
  }
]

// add type
type ReportTableProps = {
  title: string;
  getTableData: (itemsPerPage: number) => any[];
  tableFields: any;
  formatDataItem: (item: any, key: string) => React.ReactNode;
  onClearReport: () => void;
}

const ReportTable = ({ title, getTableData, tableFields, formatDataItem, onClearReport }: ReportTableProps) => {
  const [loading, setLoading] = useState(true);
  const [displayData, setDisplayData] = useState<any[]>([]);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [numberOfPages, setNumberOfPages] = useState(10);

  const populate = (itemsPerPage: number) => {
    setLoading(true);
    setTimeout(() => {
      setDisplayData(getTableData(itemsPerPage));
      setLoading(false);
    }, 500);
  };

  useEffect(() => {
    populate(itemsPerPage);
  }, [itemsPerPage, currentPage]);

  return (
    <div className={styles.panel}>
      <div className={styles.panelTopBar}>
        <h4>{title}</h4>
        <div className={styles.actions}>
          <Button color="secondary" onClick={onClearReport}>
            Clear Report
          </Button>
          <Button color="secondary">
            <Export /> Export to CSV

          </Button>
        </div>
      </div>

      <div className={`${styles.tableContainer} table-scroll`}>
        <table>
          <thead>
            <tr>
              {tableFields.map((field: any) => (
                <th key={field.key}>{field.label}</th>
              ))}
            </tr>
          </thead>
          <tbody>
            {!loading ? (
              displayData.map((item: any, i: number) => (
                <tr key={`item-${i}`}>
                  {tableFields.map((field: any) => (
                    <td key={`item-${i}-${field.key}`}>
                      {formatDataItem(item, field.key)}
                    </td>
                  ))}
                </tr>
              ))
            ) : (
              Array.from({ length: 10 }, (_, i) => (
                <UserSkeleton
                  key={`skeleton-${i}`}
                  noOfStandard={tableFields.length}
                />
              ))
            )}
          </tbody>
        </table>
      </div>

      <div style={{ marginTop: "16px" }}>
        <TableControl
          show
          itemsPerPage={itemsPerPage}
          setItemsPerPage={(val: number) => setItemsPerPage(val)}
          currentPage={currentPage}
          setCurrentPage={setCurrentPage}
          numberOfPages={numberOfPages}
          label="items"
          loading={loading}
        />
      </div>
    </div>
  );
};

const SubscriptionsReport = ({ onClearReport }: { onClearReport: () => void }) => {

  const getTableData = (itemsPerPage: number) => {
    return Array.from({ length: itemsPerPage }, (_, i) => ({
      id: i,
      ctn: faker.string.numeric(10),
      ctnStatus: faker.helpers.arrayElement(['Active', 'Cancelled', 'Suspended', 'Ported Out']),
      ban: faker.string.numeric(10),
      activationDate: faker.date.recent(),
      usageKB: 1000000,
      usageGB: 1,
      overageGB: 1,
      intlCalling: faker.helpers.arrayElement(['Disabled', 'Enabled']),
      intlRoaming: faker.helpers.arrayElement(['Disabled', 'Enabled']),
      intlDayPay: faker.helpers.arrayElement(['Disabled', 'Enabled']),
      dataBlock: faker.helpers.arrayElement(['No', 'Yes']),
    }));
  }

  const tableFields = [
    {
      label: "CTN",
      key: "ctn"
    },
    {
      label: "CTN Status",
      key: "ctnStatus"
    },
    {
      label: "BAN",
      key: "ban"
    },
    {
      label: "Activation Date",
      key: "activationDate"
    },
    {
      label: "Usage (KB)",
      key: "usageKB"
    },
    {
      label: "Usage (GB)",
      key: "usageGB"
    },
    {
      label: "Overage (GB)",
      key: "overageGB"
    },
    {
      label: "Intl. Calling",
      key: "intlCalling"
    },
    {
      label: "Intl. Roaming",
      key: "intlRoaming"
    },
    {
      label: "Intl. Day Pay",
      key: "intlDayPay"
    },
    {
      label: "Data Block",
      key: "dataBlock"
    }
  ]

  const formatDataItem = (item: any, key: string) => {
    if (key === "ctnStatus") {
      const statusColorMap = {
        "Active": "active",
        "Cancelled": "failed",
        "Suspended": "suspended",
        "Ported Out": "suspended"
      } as any;

      return <StatusPill status={item[key]} color={statusColorMap[item[key]]} />
    }

    if (key === "activationDate") {
      return formatDateWithTime(item[key]);
    }

    if (key === "intlCalling" || key === "intlRoaming" || key === "intlDayPay") {
      const colorMap = {
        "Disabled": "inactive",
        "Enabled": "active"
      } as any;

      return <StatusPill status={item[key]} color={colorMap[item[key]]} />
    }

    return item[key];
  }

  return (
    <ReportTable
      title="Subscriptions Report"
      getTableData={getTableData}
      tableFields={tableFields}
      formatDataItem={formatDataItem}
      onClearReport={onClearReport}
    />
  )
}

const ChurnReport = ({ onClearReport }: { onClearReport: () => void }) => {
  const getTableData = (itemsPerPage: number) => {
    return Array.from({ length: itemsPerPage }, (_, i) => ({
      id: i,
      ctn: faker.string.numeric(10),
      ctnStatus: faker.helpers.arrayElement(['Active', 'Cancelled', 'Suspended', 'Ported Out']),
      activationDate: faker.date.past(),
      subscriptionCancelled: faker.date.recent(),
      portOutDate: faker.date.recent(),
      iccid: faker.string.numeric(15),
      billerImei: faker.string.numeric(15),
      networkImei: faker.string.numeric(15),
      subscriberName: faker.person.fullName(),
    }));
  }

  const tableFields = [
    {
      label: "CTN",
      key: "ctn"
    },
    {
      label: "CTN Status",
      key: "ctnStatus"
    },
    {
      label: "Activation Date",
      key: "activationDate"
    },
    {
      label: "Subscription Cancelled",
      key: "subscriptionCancelled"
    },
    {
      label: "Port Out Date",
      key: "portOutDate"
    },
    {
      label: "ICCID",
      key: "iccid"
    },
    {
      label: "Biller IMEI",
      key: "billerImei"
    },
    {
      label: "Network IMEI",
      key: "networkImei"
    },
    {
      label: "Subscriber Name",
      key: "subscriberName"
    }
  ]

  const formatDataItem = (item: any, key: string) => {
    if (key === "ctnStatus") {
      const statusColorMap = {
        "Active": "active",
        "Cancelled": "failed",
        "Suspended": "suspended",
        "Ported Out": "suspended"
      } as any;

      return <StatusPill status={item[key]} color={statusColorMap[item[key]]} />
    }

    if (["activationDate", "subscriptionCancelled", "portOutDate"].includes(key)) {
      return formatDateWithTime(item[key]);
    }

    return item[key];
  }

  return (
    <ReportTable
      title="Churn Report"
      getTableData={getTableData}
      tableFields={tableFields}
      formatDataItem={formatDataItem}
      onClearReport={onClearReport}
    />
  )
}

const VoiceReport = ({ onClearReport }: { onClearReport: () => void }) => {
  const getTableData = (itemsPerPage: number) => {
    return Array.from({ length: itemsPerPage }, (_, i) => ({
      id: i,
      userId: faker.string.numeric(6),
      email: faker.internet.email(),
      dateTime: faker.date.recent(),
      type: faker.helpers.arrayElement(['Outbound', 'Inbound']),
      source: `+44${faker.string.numeric(10)}`,
      destination: `+44${faker.string.numeric(10)}`,
      duration: faker.date.soon().toLocaleTimeString('en-GB', {
        hour12: false,
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit"
      }),
      cost: faker.helpers.arrayElement([
        'N/A',
        faker.number.float({ min: 0, max: 10, precision: 0.01 }).toFixed(2)
      ]),
      chargeMethod: faker.helpers.arrayElement(['Combo', 'Credit'])
    }));
  }

  const tableFields = [
    {
      label: "User ID",
      key: "userId"
    },
    {
      label: "Email",
      key: "email"
    },
    {
      label: "Date & Time",
      key: "dateTime"
    },
    {
      label: "Type",
      key: "type"
    },
    {
      label: "Source",
      key: "source"
    },
    {
      label: "Destination",
      key: "destination"
    },
    {
      label: "Duration",
      key: "duration"
    },
    {
      label: "Cost (GBP)",
      key: "cost"
    },
    {
      label: "Charge Method",
      key: "chargeMethod"
    }
  ]

  const formatDataItem = (item: any, key: string) => {
    if (key === "dateTime") {
      return formatDateWithTime(item[key]);
    }

    return item[key];
  }

  return (
    <ReportTable
      title="Voice Report"
      getTableData={getTableData}
      tableFields={tableFields}
      formatDataItem={formatDataItem}
      onClearReport={onClearReport}
    />
  )
}

const SMSReport = ({ onClearReport }: { onClearReport: () => void }) => {
  const getTableData = (itemsPerPage: number) => {
    return Array.from({ length: itemsPerPage }, (_, i) => ({
      id: i,
      userId: faker.string.numeric(6),
      email: faker.internet.email(),
      dateTime: faker.date.recent(),
      source: `+44${faker.string.numeric(10)}`,
      destination: `+44${faker.string.numeric(10)}`,
      cost: faker.helpers.arrayElement([
        'N/A',
        faker.number.float({ min: 0, max: 10, precision: 0.01 }).toFixed(2)
      ]),
      chargeMethod: faker.helpers.arrayElement(['Combo', 'Credit'])
    }));
  }

  const tableFields = [
    {
      label: "User ID",
      key: "userId"
    },
    {
      label: "Email",
      key: "email"
    },
    {
      label: "Date & Time",
      key: "dateTime"
    },
    {
      label: "Source",
      key: "source"
    },
    {
      label: "Destination",
      key: "destination"
    },
    {
      label: "Cost (GBP)",
      key: "cost"
    },
    {
      label: "Charge Method",
      key: "chargeMethod"
    }
  ]

  const formatDataItem = (item: any, key: string) => {
    if (key === "dateTime") {
      return formatDateWithTime(item[key]);
    }

    return item[key];
  }

  return (
    <ReportTable
      title="SMS Report"
      getTableData={getTableData}
      tableFields={tableFields}
      formatDataItem={formatDataItem}
      onClearReport={onClearReport}
    />
  )
}

const NumbersReport = ({ onClearReport }: { onClearReport: () => void }) => {
  const getTableData = (itemsPerPage: number) => {
    return Array.from({ length: itemsPerPage }, (_, i) => {
      const availability = faker.helpers.arrayElement(['Available', 'Pending', 'Registered']);
      return {
        id: i,
        type: faker.helpers.arrayElement(['DID', 'eSIM']),
        msisdn: `+${faker.string.numeric(11)}`,
        imsi: faker.string.numeric(15),
        provisionedTimestamp: availability === 'Available' ? '-' : faker.date.recent(),
        assignmentTimestamp: availability === 'Available' ? '-' : faker.date.recent(),
        availability: availability,
        status: availability === 'Available' ? 'Inactive' : 'Active'
      };
    });
  }

  const tableFields = [
    {
      label: "Type",
      key: "type"
    },
    {
      label: "MSISDN",
      key: "msisdn"
    },
    {
      label: "IMSI",
      key: "imsi"
    },
    {
      label: "Provisioned Timestamp",
      key: "provisionedTimestamp"
    },
    {
      label: "Assignment Timestamp",
      key: "assignmentTimestamp"
    },
    {
      label: "Availability",
      key: "availability"
    },
    {
      label: "Status",
      key: "status"
    }
  ]

  const formatDataItem = (item: any, key: string) => {
    if (key === "availability") {
      const availabilityColorMap = {
        "Available": "active",
        "Pending": "pending",
        "Registered": "confirmed"
      } as any;

      return <AvailabilityPill status={item[key]} color={availabilityColorMap[item[key]]} />
    }

    if (key === "status") {
      const statusColorMap = {
        "Active": "active",
        "Inactive": "inactive"
      } as any;

      return <StatusPill status={item[key]} color={statusColorMap[item[key]]} />
    }

    if (["provisionedTimestamp", "assignmentTimestamp"].includes(key)) {
      return item[key] === '-' ? item[key] : formatDateWithTime(item[key]);
    }

    return item[key];
  }

  return (
    <ReportTable
      title="Numbers Report"
      getTableData={getTableData}
      tableFields={tableFields}
      formatDataItem={formatDataItem}
      onClearReport={onClearReport}
    />
  )
}
const FinanceReport = ({ onClearReport }: { onClearReport: () => void }) => {
  const getTableData = (itemsPerPage: number) => {
    return Array.from({ length: itemsPerPage }, (_, i) => {
      return {
        id: i,
        userId: faker.string.numeric(6),
        email: faker.internet.email(),
        product: faker.helpers.arrayElement(['Combo', 'Credit', 'DID Number', 'SMS', 'Data']),
        paymentType: faker.helpers.arrayElement(['Google Pay', 'Apple Pay', 'Debit Card']),
        currency: 'GBP',
        totalAmount: faker.number.float({ min: 0, max: 1000, precision: 0.01 }).toFixed(2),
        paymentStatus: faker.helpers.arrayElement(['Completed', 'Failed']),
        paymentDate: faker.date.recent(),
      };
    });
  }

  const tableFields = [
    {
      label: "User ID",
      key: "userId"
    },
    {
      label: "Email",
      key: "email"
    },
    {
      label: "Product",
      key: "product"
    },
    {
      label: "Payment Type",
      key: "paymentType"
    },
    {
      label: "Currency",
      key: "currency"
    },
    {
      label: "Total Amount",
      key: "totalAmount"
    },
    {
      label: "Payment Status",
      key: "paymentStatus"
    },
    {
      label: "Payment Date",
      key: "paymentDate"
    }
  ]

  const formatDataItem = (item: any, key: string) => {
    if (key === "paymentStatus") {
      const paymentStatusColorMap = {
        "Completed": "active",
        "Failed": "failed"
      } as any;

      return <StatusPill status={item[key]} color={paymentStatusColorMap[item[key]]} />
    }

    if (key === "paymentDate") {
      return formatDateWithTime(item[key]);
    }

    return item[key];
  }

  return (
    <ReportTable
      title="Finance Report"
      getTableData={getTableData}
      tableFields={tableFields}
      formatDataItem={formatDataItem}
      onClearReport={onClearReport}
    />
  )
}

