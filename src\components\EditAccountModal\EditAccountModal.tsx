import styles from "../AddSubscriberModal/AddSubscriberModal.module.scss";
import Modal from "../Modal";
import { useEffect, useState } from "react";
import {
  createStateObject,
} from "../utils/InputHandlers";
import { FloppyDisk, Pencil } from "../svgs";
import { InputFieldsStep } from "../ModalsSteps/ModalsSteps";

const fields = ["firstName", "lastName", "email", "phoneNumber", "channel"];
const EditAccountModal = ({show, close}: { show: boolean, close: Function }) => {
    const [data, setData] = useState(createStateObject(fields));

    useEffect(() => {
        if (show) {
            setData({
                firstName: 'Mohamed',
                lastName: 'Ehab',
                email: '<EMAIL>',
                phoneNumber: '********',
                channel: 'General',
                errors: {}
            })
        } else {
            setData(createStateObject(fields))
        }
    }, [show])

    return (
        <Modal
            show={show}
            close={() => close(false)}
            title="Edit Account details"
            cancelButton="Cancel"
            icon={<Pencil />}
            scroll={false}
            saveButton={<><FloppyDisk/> Save Changes</>}
            proceed={() => close(false)}>
            <div className={styles.main}>
                <InputFieldsStep data={data} setData={(val:any) => setData(val)} fields={fields} />
            </div>
        </Modal>
    )
}

export default EditAccountModal