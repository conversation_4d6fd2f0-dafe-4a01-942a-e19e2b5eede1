@use "../../styles/theme.scss" as *;

.buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  button {
    margin-left: 8px
  }
}

.main {
  border: 1px solid $lightgrey;
  border-radius: 16px;
  padding: 12px;
  margin: 0px 11px 11px 0px;
  display: flex;
  flex-direction: column;
  width: 100%;
  > p {
    font-size: 14px;
    color: #667085;
    margin-bottom: 10px;
  }
  .subData {
    display: grid;
    grid-template-columns: auto auto;
    gap: 15px;
    p {
      font-size: 12px;
    }
    .title {
      @include greyTitle;
      margin-bottom: 5px;
    }
  }
  &.cancelled {
    .topSection {
      background-color: #ea3d5c;
    }
  }
  &.expired {
    .topSection {
      background-color: #f2a446;
      color: #1a1a1a;
    }
  }
  &.inactive {
    .topSection {
      background-color: #b5b5b5;
      color: #1a1a1a;
    }
  }
}

.topSection {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  width: 100%;
}

.classification {
  padding: 6px 12px;
  font-size: 12px;
  line-height: 15px;
  border-radius: 8px;
  width: auto;
  display: inline-block;
  background: $lightblue;
  color: #0c2c64;
  margin-left: 6px;
}

.country {
  margin-right: 8px;
  .flag {
    background-size: cover;
    background-position: center;
    width: 30px;
    height: 30px;
    border-radius: 1000px;
  }
}

.title {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  .info {
    width: 100%;
    .titleText {
      color: #061632;
      font-size: 14px;
      font-weight: 700;
      margin-bottom: 2px;
      line-height: 18px;
    }
    .planData {
      color: #667085;
      font-size: 12px;
      line-height: 15px;
      display: flex;
      justify-content: space-between;
      div {
        p {
          &:last-of-type {
            color: #000014
          }
        }
        &:last-of-type {
          width: 34%;
        }
      }
    }
  }
}

.bottomSection {
  font-size: 12px;
  line-height: 15px;
  border-radius: 0px 0px 16px 16px;
  margin: auto -12px -12px -12px;
  padding: 10px 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #061632;
  &.cancelled {
    background: #fdecec;
  }
  &.expired {
    background: #f1f2f4;
  }
  &.renew {
    background: $lightblue;
  }
}

.autorenew {
  display: flex;
  align-items: center;
  .autorenewText {
    margin-right: 8px;
  }
}
.subscriberCards {
  display: grid;
  grid-template-columns: auto auto auto;
  gap: 15px;
  .title {
    .planData {
      div {
        &:last-of-type {
          width: 47%;
        }
      }
    }
  }
}
