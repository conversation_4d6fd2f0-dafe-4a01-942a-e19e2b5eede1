import Dialog from "@/components/Dialog";
import { PlusCircle } from "@/components/svgs";
import style from "./add-port-in-details-modal.module.scss";
import { useState } from "react";
import {
  clearInput,
  createStateObject,
  handleInputChange,
  labels,
} from "@/components/utils/InputHandlers";
import { Input } from "@/components/Input";
import Button from "@/components/Button";

type AddPortInDetailsModalProps = {
  open: boolean;
  onClose: () => void;
};

const AddPortInDetailsModal = ({
  open,
  onClose,
}: AddPortInDetailsModalProps) => {
  const fields = ["firstName", "lastName", "billingAccountNumber", "password"];
  const [formData, setFormData] = useState(createStateObject(fields));

  return (
    <Dialog
      open={open}
      onClose={onClose}
      size="sm"
      headerIcon={<PlusCircle />}
      headerTitle="Add Port-in Details"
      headerSubtitle="Porting-in number: ************"
      confirmButtonText="Add Port-in Details"
      cancelButtonText="Cancel"
      confirmButtonOnClick={onClose}
    >
      <h5 className={style.subHeading}>Old Carrier Details</h5>
      <div className={style.formContainer}>
        {fields.map((field) => (
          <Input
            key={"add-portin-" + field}
            label={field === "password" ? "PIN / Password" : labels[field]}
            value={formData[field]}
            onChange={(e: any) => {
              handleInputChange(field, e, formData, setFormData);
            }}
            error={formData.errors[field]}
            clear={() => {
              clearInput(field, setFormData);
            }}
            infoTooltipText
            password={field === "password"}
          />
        ))}
      </div>
    </Dialog>
  );
};

export default AddPortInDetailsModal;
