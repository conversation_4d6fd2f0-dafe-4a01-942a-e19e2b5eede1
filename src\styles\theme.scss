:root {
  
  // variables names come from Figma. Replaces '/' with '-'
  --primary-50: rgba(232, 240, 252, 1);
  --primary-50-half: #F1F6FD;
  --primary-100: rgba(214, 227, 250, 1);
  --primary-200: rgba(173, 200, 245, 1);
  --primary-400: #578DEA;
  --primary-500: rgba(46, 112, 229, 1);
  --primary-800: rgba(87, 141, 234, 1);
  --primary-900: #061632;
  --gray-50: #F1F2F4;
  --gray-100: rgba(223, 226, 231, 1);
  --gray-300: #A3A9B8;
  --gray-400: rgba(131, 140, 160, 1);
  --gray-500: rgba(102, 112, 133, 1);
  --gray-600: rgba(82, 90, 107, 1);
  --gray-800: #282C34;

  --text-primary: rgba(6, 22, 50, 1);
  --text-secondary: rgba(102, 112, 133, 1);

  --button-secondary-background: #D6E3FA;
  --button-secondary-text: #1857C3;
  --button-tertiary-text: #2E70E5;
  
  --textField-border-primary: #DFE2E7;
  --textField-border-error: rgba(240, 71, 71, 1);
  --textField-background-primary: #F1F6FD;
  --textField-text-placeholder: #667085;
  --textField-border-active: #80A9EF;

  --systemStatus-red-50: rgba(253, 236, 236, 1);
  --systemStatus-red-100: #FCD9D9;
  --systemStatus-red-500: rgba(255, 86, 48, 1);
  --systemStatus-red-800: #710909;
  --systemStatus-orange-100: #FFF0CC;
  --systemStatus-orange-800: #664700;
  --systemStatus-green-100: #DCF4E0;
  --systemStatus-green-800: #1A5625;
}

#root {
  --orange: #f47d27;
  --dark-orange: #d15e0b;
  --light-orange: #fde5d4;
  --faded-orange: #f9b686;
  --mid-orange: #fcc9a5;
}

$black: #1a1a1a;
$primary: #000;
$dark-primary: #000;
$light-primary: #545454;
$off-white: #fdfcfb;
$orange: var(--orange);
$dark-orange: var(--dark-orange);
$light-orange: var(--light-orange);
$faded-orange: var(--faded-orange);
$mid-orange: var(--mid-orange);
$error: #ec5b60;
$grey: #74767e;
$light-grey: #e9e9e9;
$placeholder: #45474f;
$disabled: rgba(0, 0, 20, 0.12);
$disabled-text: rgba(26, 26, 26, 0.38);
$open: #cbcaf9;
$pending: #f8ec7e;
$resolved: #037b53;
$closed: #1a1a1a;
$skeleton: #eaeaec;
$input-grey: #eff1f7;
$urgent: #f04337;
$success: #3a8e5c;

$suspended: #f2a446;
$tbs: #fbca90;
$active: #037b53;
$cancelled: #ea3d5c;
$inactive: #b5b5b5;

$avail-available: #DCF4E0;
$avail-pending: #FFF0CC;
$avail-registered: #E8F0FC;

$avail-dark-available: #1A5625;
$avail-dark-pending: #664700;
$avail-dark-registered: #0C2C64;

$delete-text: #000014;

$esim-enable: #037b53;
$esim-registered: #cbcaf9;
$esim-disabled: #f2f2f2;
$esim-installed: #efdcfe;
$esim-deleted: #fee0e5;
$esim-available: #e3fedc;

$esim-enable-text: #fdfcfb;
$esim-registered-text: #302e95;
$esim-disabled-text: #474747;
$esim-installed-text: #9e3dea;
$esim-deleted-text: #ea3d5c;
$esim-available-text: #037b53;

$admin: #FFF0CC;
$agent: #E8F0FC;

$inprogress: #eed922;
$confirmed: #6361dc;
$completed: #037b53;

$esim-enable: #037b53;
$esim-registered: #cbcaf9;
$esim-disabled: #f2f2f2;
$esim-installed: #efdcfe;
$esim-deleted: #fee0e5;
$esim-available: #e3fedc;

$esim-enable-text: #fdfcfb;
$esim-registered-text: #302e95;
$esim-disabled-text: #474747;
$esim-installed-text: #9e3dea;
$esim-deleted-text: #ea3d5c;
$esim-available-text: #037b53;

$portin-pending: #feebd4;
$portin-pending-text: #c4710e;

$support: #f67e93;
$did-request: #6361dc;
$porting: #fbca90;

$dark-dark-purple: #160b2a;

$iccid-required: #FCD9D9;
$iccid-required-text: #710909;
$ready: #DCF4E0;
$ready-text: #1A5625;
$ban-change: #FFF0CC;
$ban-change-text: #664700;
$details-required-text: #710909;
$details-required: #FCD9D9;

$esim: #cbcaf9;
$sim: #fbca90;


/// 
$lightgrey: #DFE2E7;
$lightblue: #E8F0FC;

@mixin greyTitle {
  color: #667085;
  font-size: 12px;
  font-weight: 400;
  margin-top: 0;
  margin-bottom: 5px;
}

@mixin primaryText {
  font-size: 14px;
  color: #061632
}

@mixin closeButton {
  background-color: #E8F0FC;
  color: #2E70E5;
  stroke: #2E70E5;
  padding: 8px;
  border-radius: 50%;
  width: 30px;
  display: flex;
  cursor: pointer;
  justify-content: center;
  height: 30px;
  align-items: center;
  &:hover {
    background-color: #ADC8F5;
  }
  svg {
    width: 16px;
    width: 16px;
  }
}

@mixin circledIcon {
  background-color: #E8F0FC;
  color: #2E70E5;
  stroke: #2E70E5;
  padding: 8px;
  border-radius: 50%;
  width: 32px;
  display: flex;
  justify-content: center;
  height: 32px;
  align-items: center;
}