:root {
  // variables names come from Figma. Replaces '/' with '-'
  --primary-50: rgba(232, 240, 252, 1);
  --primary-50-half: #f1f6fd;
  --primary-100: rgba(214, 227, 250, 1);
  --primary-200: rgba(173, 200, 245, 1);
  --primary-400: #578dea;
  --primary-500: rgba(46, 112, 229, 1);
  --primary-800: rgba(87, 141, 234, 1);
  --primary-900: #061632;
  --gray-50: #f1f2f4;
  --gray-100: rgba(223, 226, 231, 1);
  --gray-300: #a3a9b8;
  --gray-400: rgba(131, 140, 160, 1);
  --gray-500: rgba(102, 112, 133, 1);
  --gray-600: rgba(82, 90, 107, 1);
  --gray-800: #282c34;

  --text-primary: rgba(6, 22, 50, 1);
  --text-secondary: rgba(102, 112, 133, 1);

  --button-secondary-background: #d6e3fa;
  --button-secondary-text: #1857c3;
  --button-tertiary-text: #2e70e5;

  --textField-border-primary: #dfe2e7;
  --textField-border-error: rgba(240, 71, 71, 1);
  --textField-background-primary: #f1f6fd;
  --textField-text-placeholder: #667085;
  --textField-border-active: #80a9ef;

  --systemStatus-red-50: rgba(253, 236, 236, 1);
  --systemStatus-red-100: #fcd9d9;
  --systemStatus-red-500: rgba(255, 86, 48, 1);
  --systemStatus-red-800: #710909;
  --systemStatus-orange-100: #fff0cc;
  --systemStatus-orange-800: #664700;
  --systemStatus-green-100: #dcf4e0;
  --systemStatus-green-800: #1a5625;
}

#root {
  --orange: #f47d27;
  --dark-orange: #d15e0b;
  --light-orange: #fde5d4;
  --faded-orange: #f9b686;
  --mid-orange: #fcc9a5;
}

$black: #1a1a1a;
$primary: #000;
$dark-primary: #000;
$light-primary: #545454;
$off-white: #fdfcfb;
$orange: var(--orange);
$dark-orange: var(--dark-orange);
$light-orange: var(--light-orange);
$faded-orange: var(--faded-orange);
$mid-orange: var(--mid-orange);
$error: #ec5b60;
$grey: #74767e;
$light-grey: #e9e9e9;
$placeholder: #45474f;
$disabled: rgba(0, 0, 20, 0.12);
$disabled-text: rgba(26, 26, 26, 0.38);
$open: #cbcaf9;
$pending: #f8ec7e;
$resolved: #037b53;
$closed: #1a1a1a;
$skeleton: #eaeaec;
$input-grey: #eff1f7;
$urgent: #f04337;
$success: #3a8e5c;

$suspended: #f2a446;
$tbs: #fbca90;
$active: #037b53;
$cancelled: #ea3d5c;
$inactive: #b5b5b5;

$avail-available: #dcf4e0;
$avail-pending: #fff0cc;
$avail-registered: #e8f0fc;

$avail-dark-available: #1a5625;
$avail-dark-pending: #664700;
$avail-dark-registered: #0c2c64;

$delete-text: #000014;

$esim-enable: #037b53;
$esim-registered: #cbcaf9;
$esim-disabled: #f2f2f2;
$esim-installed: #efdcfe;
$esim-deleted: #fee0e5;
$esim-available: #e3fedc;

$esim-enable-text: #fdfcfb;
$esim-registered-text: #302e95;
$esim-disabled-text: #474747;
$esim-installed-text: #9e3dea;
$esim-deleted-text: #ea3d5c;
$esim-available-text: #037b53;

$admin: #fff0cc;
$agent: #e8f0fc;

$inprogress: #eed922;
$confirmed: #6361dc;
$completed: #037b53;

$esim-enable: #037b53;
$esim-registered: #cbcaf9;
$esim-disabled: #f2f2f2;
$esim-installed: #efdcfe;
$esim-deleted: #fee0e5;
$esim-available: #e3fedc;

$esim-enable-text: #fdfcfb;
$esim-registered-text: #302e95;
$esim-disabled-text: #474747;
$esim-installed-text: #9e3dea;
$esim-deleted-text: #ea3d5c;
$esim-available-text: #037b53;

$portin-pending: #feebd4;
$portin-pending-text: #c4710e;

$support: #f67e93;
$did-request: #6361dc;
$porting: #fbca90;

$dark-dark-purple: #160b2a;

$iccid-required: #fcd9d9;
$iccid-required-text: #710909;
$ready: #dcf4e0;
$ready-text: #1a5625;
$ban-change: #fff0cc;
$ban-change-text: #664700;
$details-required-text: #710909;
$details-required: #fcd9d9;

$esim: #cbcaf9;
$sim: #fbca90;

///
$lightgrey: #dfe2e7;
$lightblue: #e8f0fc;

@mixin greyTitle {
  color: #667085;
  font-size: 12px;
  font-weight: 400;
  margin-top: 0;
  margin-bottom: 5px;
}

@mixin primaryText {
  font-size: 14px;
  color: #061632;
}

@mixin closeButton {
  background-color: #e8f0fc;
  color: #2e70e5;
  stroke: #2e70e5;
  padding: 8px;
  border-radius: 50%;
  width: 30px;
  display: flex;
  cursor: pointer;
  justify-content: center;
  height: 30px;
  align-items: center;
  &:hover {
    background-color: #adc8f5;
  }
  svg {
    width: 16px;
    width: 16px;
  }
}

@mixin circledIcon {
  background-color: #e8f0fc;
  color: #2e70e5;
  stroke: #2e70e5;
  padding: 8px;
  border-radius: 50%;
  width: 32px;
  display: flex;
  justify-content: center;
  height: 32px;
  align-items: center;
}
