import { CircularProgress } from "@mui/material";
import styles from "./button.module.scss";

const IconButton = ({
  style,
  onClick,
  loading,
  disabled,
  children,
  noOutline,
  white,
  id = "",
}: any) => {
  return (
    <button
      id={id}
      disabled={loading || disabled}
      className={`${styles.button} ${noOutline && styles.noOutline} ${white && styles.white}`}
      style={style}
      onClick={onClick}
    >
      {loading ? (
        <CircularProgress
          style={{
            width: 20,
            height: 20,
            color: "#2E70E5",
            margin: "0 auto",
          }}
        />
      ) : (
        children
      )}
    </button>
  );
};

export default IconButton;
