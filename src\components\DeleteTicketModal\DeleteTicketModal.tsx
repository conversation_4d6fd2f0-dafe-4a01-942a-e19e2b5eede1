import styles from "./deleteTicketModal.module.scss";
import { WarningCircle } from "../svgs";
import { useDispatch } from "react-redux";
import { ticketsFields } from "../utils/ticketsFields";
import { formatDateWithTime } from "../utils/formatDate";
import StatusBadge, { convertStatus } from "../StatusBadge";
import Priority, { convertPriority } from "../Priority";
import Dialog from "../Dialog";

const DeleteTicketModal = ({ show, setShow, ticket, repopulate }: any) => {
  const dispatch = useDispatch();

  const formatDataItem = (item: any, key: string) => {
    if (key === "creationDate" || key === "dueDate") {
      return formatDateWithTime(item);
    } else if (key === "status") {
      const status = convertStatus(item);
      return <StatusBadge status={status} />;
    } else if (key === "assignee") {
      return item;
    } else if (key === "priority") {
      const priority = convertPriority(item);
      return <Priority priority={priority} />;
    } else if (key === "subject" || key === "description") {
      const sliceText = item.substring(0, 35) + "...";
      return sliceText;
    } else {
      return item;
    }
  };

  const handleSuccess = () => {
    dispatch({
      type: "notify",
      payload: {
        error: false,
        message: "Ticket has been deleted successfully",
      },
    });
    setShow(false);
    repopulate(15);
  };

  return (
    <Dialog
      confirmButtonText="Yes, Delete Ticket"
      headerTitle="Delete Ticket?"
      headerSubtitle="Once removed, it cannot be recovered."
      confirmButtonVariant="customerActionRed"
      headerIcon={<WarningCircle />}
      open={show}
      onClose={() => {
        setShow(false);
      }}
      size="sm"
      confirmButtonOnClick={() => handleSuccess()}
      cancelButtonText="Cancel"
    >
      <div className={styles.tableContainer}>
        <table className={styles.table}>
          <tbody>
            {ticketsFields.map((row: any) => (
              <tr key={row.key}>
                <td className={styles.label}>{row.label}</td>
                <td className={styles.value}>
                  {formatDataItem(ticket[row.key], row.key)}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </Dialog>
  );
};

export default DeleteTicketModal;
