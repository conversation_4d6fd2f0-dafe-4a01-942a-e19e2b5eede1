import { ArrowUp } from "../svgs";
import styles from "./daily-highlight.module.scss";

const DailyHighlight = ({ title, time, number, percentChange, red }: any) => {
  const formatNumber = (x: number) => {
    return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  };

  return (
    <div className={styles.main}>
      <div className={styles.title}>{title}</div>
      <div className={styles.number}>{formatNumber(number)}</div>
    </div>
  );
};

export default DailyHighlight;
