import styles from "./customer-tickets-panel.module.scss";
import { useEffect, useState } from "react";
import { PlusFilled } from "../svgs";
import TicketSkeleton from "../TicketSkeleton";
import CustomerTicket from "../CustomerTickets";
import CreateTicketModal from "../CreateTicketModal";
import { exampleCustomerTickets } from "../utils/exampleCustomerDetails";
import SearchBar from "../SearchBar";
import IconButton from "../IconButton";

const CustomerTicketsPanel = ({ customerEmail }: any) => {
  const [ticketsLoading, setTicketsLoading] = useState(false);
  const [tickets, setTickets] = useState([] as any);
  const [ticketsDetails, setTicketsDetails] = useState(null as any)

  const populate = () => {
    setTickets(exampleCustomerTickets);
    /*ApiGet(`/tickets/by-account/${mid}`)
      .then((response) => {
        console.log("request", response);
        setTicketsLoading(false);
        setTickets(response.data);
      })
      .catch((error) => {
        setTicketsLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: error.response.data.message,
          },
        });
      });*/
  };

  useEffect(populate, []);

  const handleTicketUpdate = (type: string, option: string, ticket: any) => {};

  const [filteredTickets, setFilteredTickets] = useState([] as any);

  const [selectedTickets, setSelectedTickets] = useState([] as any);

  const handleSelectTicket = (ticket: any) => {
    let currentSelected = [...selectedTickets];
    if (currentSelected.includes(ticket.id)) {
      currentSelected = currentSelected.filter(
        (item: any) => item !== ticket.id
      );
    } else {
      currentSelected.push(ticket.id);
    }
    setSelectedTickets(currentSelected);
  };

  /************    BULK EDIT     **********/

  const [showBulkEditModal, setShowBulkEditModal] = useState(false);

  const [bulkEdit, setBulkEdit] = useState({
    priority: "",
    category: "",
    assignee: "",
    status: "",
  } as any);

  const editBulkValues = (type: string, option: string) => {
    if (bulkEdit[type] === option) {
      setBulkEdit({
        ...bulkEdit,
        [type]: "",
      });
    } else {
      setBulkEdit({
        ...bulkEdit,
        [type]: option,
      });
    }
  };

  const cancelBulkEdit = () => {
    setBulkEdit({
      priority: "",
      category: "",
      assignee: "",
      status: "",
    });
    setSelectedTickets([] as any);
  };

  const saveBulkEdit = () => {
    setLoading(true);
    /*setTimeout(() => {
      let currentTickets = [...allTickets];
      selectedTickets.forEach((id: any) => {
        const objIndex = currentTickets.findIndex((obj: any) => obj.id === id);
        if (bulkEdit.priority)
          currentTickets[objIndex].priority = bulkEdit.priority;
        if (bulkEdit.category)
          currentTickets[objIndex].category = bulkEdit.category;
        if (bulkEdit.assignee)
          currentTickets[objIndex].assignee = bulkEdit.assignee;
        if (bulkEdit.status) currentTickets[objIndex].status = bulkEdit.status;
      });
      dispatch({
        type: "set",
        tickets: {
          currentTickets,
        },
      });
      setBulkEdit({
        priority: "",
        category: "",
        assignee: "",
        status: "",
      });
      dispatch({
        type: "notify",
        payload: {
          message: "Your changes have been saved.",
          error: false,
        },
      });
      setLoading(false);
      setShowBulkEditModal(false);
    }, 2000);*/
  };

  const [selectAll, setSelectAll] = useState(false);
  const [selectAllIndeterminate, setSelectAllIndeterminate] = useState(true);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (selectedTickets.length === filteredTickets.length) {
      setSelectAllIndeterminate(false);
      setSelectAll(true);
    } else {
      setSelectAll(false);
      setSelectAllIndeterminate(true);
    }
  }, [selectedTickets]);

  const handleSelectAll = () => {
    if (filteredTickets.length === selectedTickets.length) {
      setSelectedTickets([] as any);
    } else {
      let allTicketIds = [] as any;
      filteredTickets.forEach((ticket: any) => {
        allTicketIds.push(ticket.id);
      });
      setSelectedTickets(allTicketIds);
    }
  };

  /************    PAGINATION     ***********/

  const [ticketsShown, setTicketsShown] = useState(15);

  const [showTicketNumberMenu, setShowTicketNumberMenu] = useState(false);

  const [currentPage, setCurrentPage] = useState(1);

  const [showAddTicket, setShowAddTicket] = useState(false);

  useEffect(() => {
    window.addEventListener("click", (e: any) => {
      let classes = e.target.className;
      if (typeof classes === "string") {
        if (!e.target.className.includes("ticketsPerPageButton")) {
          setShowTicketNumberMenu(false);
        }
      }
    });
  }, []);

  return (
    <div className={styles.container}>
      <CreateTicketModal
        show={showAddTicket}
        setShow={setShowAddTicket}
        customerEmail={customerEmail}
        repopulate={populate}
      />

      <div className={styles.panelTopBar}>
        <h4>Tickets</h4>
        <div className={styles.actions}>
          <SearchBar placeholder="Search" />
          <IconButton style={{ marginLeft: "8px" }}>
            <PlusFilled />
          </IconButton>
        </div>
      </div>
      <div className={`${styles.main} select modal-scroll`}>
        {ticketsLoading ? (
          [0, 1, 2, 3].map((n) => (
            <TicketSkeleton key={"skeleton-ticket-" + n} />
          ))
        ) : tickets.length ? (
          tickets
            .slice((currentPage - 1) * ticketsShown, currentPage * ticketsShown)
            .map((ticket: any) => (
              <CustomerTicket
                setTicketsDetails={setTicketsDetails}
                ticket={ticket}
                handleTicketUpdate={handleTicketUpdate}
                key={"ticket-comp-" + ticket.id}
                handleSelectTicket={handleSelectTicket}
                selectedTickets={selectedTickets}
                disabled={loading}
              />
            ))
        ) : (
          <div className={styles.noneFound}>
            <img src="/none_found.svg" />
            <h3>No tickets found</h3>
          </div>
        )}
      </div>
    </div>
  );
};

export default CustomerTicketsPanel;
