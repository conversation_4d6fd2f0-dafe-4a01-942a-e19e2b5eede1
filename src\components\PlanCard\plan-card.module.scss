.planCard {
  display: flex;
  background-color: var(--textField-background-primary);
  border-radius: 16px;
  border: 1px solid var(--textField-border-primary);
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

.selectionArea {
  display: flex;
  margin-right: 8px;
}

.radioCircle {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  border: 1px solid var(--gray-400);

  .selected & {
    border-color: var(--primary-500);
    border-width: 5px;
  }
}

.planInfo {
  flex: 1;
}

.mainDetails {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.planName {
  font-weight: 700;
  font-size: 14px;
}

.planPrice {
  font-weight: 700;
  font-size: 14px;
}

.planFeatures {
  font-size: 14px;
  line-height: 18px;
}

.actions {
  margin-top: 8px;
  display: flex;
  gap: 8px;
}
