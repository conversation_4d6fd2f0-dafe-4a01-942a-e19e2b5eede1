.container {
  background-color: #f1f6fd;
  border: 1px solid var(--textField-border-primary, #dfe2e7);
  outline: none;
  border-radius: 16px;
  padding: 12px 16px;
  font-size: 14px;
  line-height: 18px;
  min-height: 190px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  ul,
  ol {
    padding-inline-start: 16px;
  }
  :global(.ProseMirror-focused) {
    outline: none !important;
  }
  :global(.ProseMirror) {
    flex-grow: 1;
  }
}

.editor {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  :global(.tiptap) {
    p {
      &:global(.is-editor-empty) {
        &:first-child {
          &::before {
            color: #667085;
            content: attr(data-placeholder);
            float: left;
            height: 0;
            pointer-events: none;
          }
        }
      }
    }
  }
}

.footer {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  width: 100%;
  padding-top: 6px;
  margin-top: 6px;
  .topDivider {
    position: absolute;
    top: 0px;
    width: 489px;
    height: 1px;
    background-color: #dfe2e7;
  }
  .divider {
    margin-right: 3px;
    width: 1px;
    height: 16px;
    background-color: #bfc4ce;
  }
  .buttons {
    display: flex;
    align-items: center;
    margin-right: auto;
    button {
      background: none;
      border: none;
      outline: none;
      padding: 0px;
      cursor: pointer;
      border-radius: 6px;
      margin-right: 3px;
      padding: 3px;
      display: flex;
      align-items: center;
      justify-content: center;
      &.active {
        background-color: #2e70e5;
        &:hover {
          background-color: #6b97e2;
        }
        svg {
          color: #fff;
        }
      }
      svg {
        vertical-align: middle;
        color: #2e70e5;
      }
      &:hover {
        background-color: #ebebeb;
      }
    }
  }
}
