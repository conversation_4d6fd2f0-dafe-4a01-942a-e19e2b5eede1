import { Coin, Gift, PercentCircle } from "@/components/svgs";


export const promotionsFields = [
    {
      label: "Type",
      labelStr: "Type",
      key: "type",
    },
    {
      label: "Name",
      labelStr: "Name",
      key: "name",
    },
    {
      label: "Amount",
      labelStr: "Amount",
      key: "amount",
    },
    {
      label: "Time Used",
      labelStr: "Time Used",
      key: "timesUsed",
    },
    {
      label: "Promo Code",
      labelStr: "Promo Code",
      key: "code",
      icon: Gift
    },
    {
      label: "Date Created",
      labelStr: "Date Created",
      key: "creationDate",
    },
    {
      label: "Expiry date",
      labelStr: "Expiry date",
      key: "expiryDate",
    },
    {
      label: "Status",
      labelStr: "Status",
      key: "status",
    },
  ];

export const promotionsHistoryFields = [
  {
    label: "Date & Time",
    labelStr: "Date & Time",
    key: "date",
  },
  {
    label: "Email Address",
    labelStr: "Email Address",
    key: "email",
  },
  {
    label: "Name",
    labelStr: "Name",
    key: "name",
  },
  {
    label: "Order number",
    labelStr: "Order number",
    key: "orderNo",
  },
]

export const activityFields = [
  {
    label: "Date & Time",
    labelStr: "Date & Time",
    key: "date",
  },
  {
    label: "Email Address",
    labelStr: "Email Address",
    key: "email",
  },
  {
    label: "Activity",
    labelStr: "Activity",
    key: "activity",
  },
]

export const detailsIcons = {
  amount: Coin,
  code: Gift,
}