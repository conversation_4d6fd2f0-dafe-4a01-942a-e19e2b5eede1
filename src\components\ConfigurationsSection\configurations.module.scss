@use "../../styles/theme.scss" as *;

.title {
  font-size: 14px;
  margin-top: 8px;
  margin-bottom: 10px;
  font-weight: 600;
}

.modalText {
  padding-left: 5px;
  p {
    color: #061632;
    font-size: 14px;
    font-weight: 400;
    &:first-of-type {
      font-weight: 700;
      margin-bottom: 5px;
    }
  }
}

.feature {
  display: flex;
  margin-bottom: 8px;
  font-size: 14px;
  align-items: center;
  margin-top: 10px;
  cursor: pointer;
  .circledIcon {
    @include circledIcon;
    margin-right: 8px;
    &.clicked {
      cursor: pointer;
    }
  }
  p {
    width: 217px;
  }
  &:not(:first-of-type) {
    .clicked {
      transition: all 0.5s ease;
      display: none;
    }
    &:hover {
      .clicked {
        transition: all 0.5s ease;
        display: flex;
      }
    }
  }
}
