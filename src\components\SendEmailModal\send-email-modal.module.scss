.container {
  position: absolute;
  bottom: 20px;
  right: 20px;
  width: 100%;
  max-width: 533px;
  height: 428px;
  border: 1px solid #dfe2e7;
  background-color: #fff;
  border-radius: 16px;
  box-shadow: 0px 0px 30px 0px #0000004d;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;

  &.expanded {
    max-width: calc(100% - 32px);
    height: calc(100% - 32px);
  }

  .top {
    display: flex;
    width: 100%;
    align-items: center;
    justify-content: space-between;
    background-color: #2e70e5;
    color: #fff;
    padding: 8px 8px 8px 16px;
    border-radius: 16px 16px 0px 0px;
    .title {
      width: 100%;
      font-size: 14px;
      font-weight: 700;
      line-height: 18px;
    }
    .actions {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      width: 100%;
    }
  }

  .mainEmail {
    padding: 0px 16px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    .row {
      display: flex;
      align-items: center;
      width: 100%;
      font-size: 14px;
      font-weight: 400;
      line-height: 18px;
      padding: 12px 0px;
      color: #525a6b;
      border-bottom: 1px solid #dfe2e7;
      .circle {
        background-color: #80a9ef;
        width: 24px;
        height: 24px;
        border-radius: 50px;
        margin-right: 12px;
      }
      .toEmail {
        background-color: #d6e3fa;
        border-radius: 100px;
        padding: 8px;
        margin-left: 12px;
      }
      input {
        border: none;
        outline: none;
        padding: 0px;
        width: 100%;
        font-size: 14px;
        line-height: 18px;
        color: #525a6b;
        &::placeholder {
          color: #525a6b;
        }
      }
    }
    .body {
      padding: 16px 0px;
      flex-grow: 1;
      display: flex;
      flex-direction: column;
      textarea {
        flex-grow: 1;
        border: none;
        outline: none;
        padding: 0px;
        width: 100%;
        font-size: 14px;
        line-height: 18px;
        color: #525a6b;
        height: 100%;
        resize: none;
        &::placeholder {
          color: #bfc4ce;
        }
      }
    }
  }
  .bottom {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    border-top: 1px solid #dfe2e7;
    padding: 10px 17px;
  }
}
