import { useDispatch, useSelector } from "react-redux";
import styles from "./ticket-summary.module.scss";
import {
  ArrowRight,
  CaretLeft,
  CaretRight,
  Coin,
  Cube,
  Home,
  Plus,
  Spanner,
} from "../svgs";
import { Fade } from "@mui/material";
import { useState } from "react";
import { motion } from "framer-motion";
import TicketType from "../TicketType";
import TicketSelect from "../TicketSelect";
import StatusBadge from "../StatusBadge";
import Assignee from "../Assignee";
import Initials from "../Initials";
import Category from "../Category";
import Priority from "../Priority";
import Button from "../Button";
import formatDate from "../utils/formatDate";
import { SwitchTransition, CSSTransition } from "react-transition-group";
import TicketSidebarTile from "../TicketSidebarTile";
import CorrespondenceTile from "../CorrespondenceTile";
import { Link } from "react-router-dom";

const TicketSummary = ({
  nextTicket,
  prevTicket,
  handleTicketUpdate,
  agents,
}: any) => {
  const dispatch = useDispatch();
  const { ticketOpen, ticket } = useSelector((state: any) => state);

  const [loading, setLoading] = useState(false);

  const closeTicket = () => {
    dispatch({
      type: "set",
      ticketOpen: false,
    });
  };

  const [selection, setSelection] = useState("ticket");

  return (
    <div className={`${styles.main} ${ticketOpen && styles.open}`}>
      <SwitchTransition>
        <CSSTransition
          key={ticket ? ticket.id : "1"}
          addEndListener={(node, done) =>
            node.addEventListener("transitionend", done, false)
          }
          classNames="fade"
        >
          <div className={`${styles.mainGrid} ${styles.support}`}>
            {ticket && (
              <>
                <div>
                  <Fade in={ticketOpen}>
                    <div onClick={closeTicket} className={styles.close}>
                      <CaretRight />
                    </div>
                  </Fade>
                  <div className={styles.prevNext}>
                    <div
                      onClick={prevTicket}
                      style={{ marginRight: 16, cursor: "pointer" }}
                    >
                      <CaretLeft />
                    </div>
                    <div onClick={nextTicket} style={{ cursor: "pointer" }}>
                      <CaretRight />
                    </div>
                  </div>
                  <div className={styles.customerInfo}>
                    <div>
                      <div className={styles.name}>{ticket.name}</div>
                      <div className={styles.email}>{ticket.customerEmail}</div>
                    </div>
                    <Link
                      to={`/customer-details/${ticket.mid}`}
                      className={styles.customerSummary}
                      id="customer-summary"
                    >
                      Customer Summary <ArrowRight />
                    </Link>
                  </div>
                  {/*<div className={styles.selectionWrapper}>
                    <div
                      className={`${styles.selection} ${
                        selection === "ticket" && styles.activeSelection
                      }`}
                      onClick={() => {
                        setSelection("ticket");
                      }}
                    >
                      <span>Ticket Overview</span>
                      {selection === "ticket" && (
                        <motion.div
                          className={styles.background}
                          layoutId="ticket-summary-underline"
                        />
                      )}
                    </div>
                    <div
                      className={`${styles.selection} ${
                        selection === "correpsondance" && styles.activeSelection
                      }`}
                      onClick={() => {
                        setSelection("correpsondance");
                      }}
                    >
                      <span>Correspondence</span>
                      {selection === "correpsondance" && (
                        <motion.div
                          className={styles.background}
                          layoutId="ticket-summary-underline"
                        />
                      )}
                    </div>
                  </div>*/}
                </div>
                <div className={`${styles.contentScroll} modal-scroll`}>
                  {true ? (
                    <SwitchTransition>
                      <CSSTransition
                        key={selection}
                        addEndListener={(node, done) =>
                          node.addEventListener("transitionend", done, false)
                        }
                        classNames="fade"
                      >
                        <div>
                          {selection === "ticket" ? (
                            <div>
                              <TicketSidebarTile
                                ticket={ticket}
                                handleTicketUpdate={handleTicketUpdate}
                                agents={agents}
                              />
                              {/*<div className={styles.notes}>
                                <div className={styles.title}>
                                  <h4>Notes</h4>
                                  <Button
                                    style={{
                                      padding: 0,
                                      height: 24,
                                      fontSize: 14,
                                    }}
                                    color="tertiary"
                                  >
                                    <Plus />
                                    Add Note
                                  </Button>
                                </div>
                                <div className={styles.notesContainer}>
                                  <div className={styles.note}>
                                    <div className={styles.top}>
                                      <div className={styles.by}>
                                        By:{" "}
                                        <span style={{ fontWeight: 600 }}>
                                          Mira Gouse
                                        </span>
                                      </div>
                                      <div className={styles.date}>
                                        MM/DD/YYYY
                                      </div>
                                    </div>
                                    <div className={styles.noteContent}>
                                      Lorem ipsum dolor sit amet consectetur.
                                      Lobortis sed risus in cras. Sit cras
                                      varius vulputate tortor suscipit. Duis
                                      enim bibendum ac nunc. Praesent eget
                                      posuere ac molestie tincidunt faucibus
                                      senectus nunc. Tincidunt senectus nullam
                                      mauris fermentum tortor. Quis felis
                                      egestas porttitor augue nibh malesuada
                                      viverra morbi. Lobortis arcu tellus arcu
                                      ipsum. Elit interdum at in ornare a enim
                                      aliquet dictum. At consequat ut
                                      vestibulum.
                                    </div>
                                  </div>
                                  <div className={styles.note}>
                                    <div className={styles.top}>
                                      <div className={styles.by}>
                                        By:{" "}
                                        <span style={{ fontWeight: 600 }}>
                                          Mira Gouse
                                        </span>
                                      </div>
                                      <div className={styles.date}>
                                        MM/DD/YYYY
                                      </div>
                                    </div>
                                    <div className={styles.noteContent}>
                                      Lorem ipsum dolor sit amet consectetur.
                                      Lobortis sed risus in cras. Sit cras
                                      varius vulputate tortor suscipit. Duis
                                      enim bibendum ac nunc. Praesent eget
                                      posuere ac molestie tincidunt faucibus
                                      senectus nunc. Tincidunt senectus nullam
                                      mauris fermentum tortor. Quis felis
                                      egestas porttitor augue nibh malesuada
                                      viverra morbi. Lobortis arcu tellus arcu
                                      ipsum. Elit interdum at in ornare a enim
                                      aliquet dictum. At consequat ut
                                      vestibulum.
                                    </div>
                                  </div>
                                </div>
                              </div>*/}
                            </div>
                          ) : (
                            <CorrespondenceTile />
                          )}
                        </div>
                      </CSSTransition>
                    </SwitchTransition>
                  ) : ticket.type === "DID Request" ? (
                    <>
                      <TicketSidebarTile ticket={ticket} />
                      <div className={styles.notes}>
                        <div className={styles.title}>
                          <h4>Documents</h4>
                        </div>
                        <div className={styles.docsContainer}>
                          <div className={styles.doc}>
                            <div className={styles.docTitle}>
                              Proof of Address
                            </div>
                            <img
                              src="/bill_example.png"
                              className={styles.docImage}
                            />
                          </div>
                          <div className={styles.doc}>
                            <div className={styles.docTitle}>
                              Identity Document
                            </div>
                            <img
                              src="/id_example.png"
                              className={styles.docImage}
                            />
                          </div>
                        </div>
                      </div>
                      <div
                        style={{
                          display: "flex",
                          justifyContent: "flex-end",
                          marginTop: 24,
                        }}
                      >
                        <Button
                          style={{ minWidth: "initial", marginRight: 12 }}
                          color="secondary"
                        >
                          Deny
                        </Button>
                        <Button style={{ minWidth: "initial" }}>Approve</Button>
                      </div>
                    </>
                  ) : (
                    <>
                      <TicketSidebarTile ticket={ticket} />
                      <div
                        style={{ display: "flex", justifyContent: "flex-end" }}
                      >
                        <Button>Confirm & Forward</Button>
                      </div>
                    </>
                  )}
                </div>
              </>
            )}
          </div>
        </CSSTransition>
      </SwitchTransition>
    </div>
  );
};

export default TicketSummary;
