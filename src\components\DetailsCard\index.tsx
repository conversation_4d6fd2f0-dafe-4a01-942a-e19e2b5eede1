import React from 'react'
import styles from './details-card.module.scss'

type DetailsCardProps = {
  title: string,
  items: {
    title: string,
    value: React.ReactNode,
    icon: React.ReactNode
  }[]
}

const DetailsCard = ({
  items,
  title
}: DetailsCardProps) => {

  return (
    <div className={styles.detailCard}>
      <h4>{title}</h4>
      {items.map(i => (
        <div className={styles.detailCardItem}>
          <div className={styles.cardItemIcon}>
            {i.icon}
          </div>
          <div className={styles.cardItemContent}>
            <p className={styles.itemTitle}>{i.title}</p>
            <p className={styles.itemValue}>{i.value}</p>
          </div>
        </div>
      ))}
    </div>
  )
}

export default DetailsCard