import styles from "./select-input.module.scss";
import { ControlledMenu, MenuItem, useMenuState } from "@szhsin/react-menu";
import "@szhsin/react-menu/dist/index.css";
import "@szhsin/react-menu/dist/transitions/slide.css";
import { Check, ChevronDown, InfoCircle } from "../svgs";
import { useEffect, useRef, useState } from "react";
import { Collapse } from "@mui/material";

const SelectInput = ({
  options,
  selected,
  placeholder = "Choose",
  onChange,
  readonly,
  disabled,
  id = null,
  error,
  label,
  infoTooltipText,
}: any) => {
  const ref = useRef(null);
  const menuRef = useRef<HTMLDivElement>(null);
  const [menuProps, toggleMenu] = useMenuState({ transition: true });
  const [openUpwards, setOpenUpwards] = useState(false);

  const getDisplayValue = (value: any) => {
    if (!value) return placeholder;

    const option = options.find((opt: any) =>
      typeof opt === "string" ? opt === value : opt.key === value
    );

    if (!option) return placeholder;
    return typeof option === "string" ? option : option.label;
  };

  const getOptionValue = (option: any) => {
    return typeof option === "string" ? option : option.key;
  };

  const getOptionDisplay = (option: any) => {
    if (typeof option === "string") {
      return option;
    }
    return option.label;
  };

  useEffect(() => {
    if (menuProps.state === "open" && selected) {
      // Wait for the menu to be fully rendered
      const selectedElement = menuRef.current?.querySelector(
        `.${styles.selected}`
      );
      if (selectedElement) {
        selectedElement.scrollIntoView({ block: "center" });
      }
    }
  }, [menuProps.state, selected]);

  return (
    <div className={styles.box} style={{ maxWidth: "320px" }}>
      <div className={styles.label}>
        <span>{label}</span>
        <span style={{ color: "#838CA0" }}>
          {infoTooltipText && <InfoCircle />}
        </span>
      </div>
      <div
        ref={ref}
        className={`${styles.menuButton} ${selected && styles.selected} ${
          (disabled || readonly) && styles.disabled
        } ${readonly && styles.readonly} ${
          menuProps.state === "open" || menuProps.state === "opening"
            ? styles.iconOpen
            : styles.iconClosed
        } ${error && styles.error} select-input`}
        onClick={() => toggleMenu(true)}
      >
        <span style={{ marginRight: 4 }}>{getDisplayValue(selected)}</span>
        {!readonly && <ChevronDown />}
      </div>
      <Collapse in={error ? true : false}>
        <p className={styles.errorText} id={`${id}-error`}>
          {error || <br />}
        </p>
      </Collapse>
      <ControlledMenu
        {...menuProps}
        ref={menuRef}
        anchorRef={ref}
        onClose={() => toggleMenu(false)}
        align="start"
        viewScroll="initial"
        position="anchor"
        overflow="auto"
        direction={openUpwards ? "top" : "bottom"}
        onItemClick={(e) => (e.keepOpen = true)}
        menuClassName={styles.menu}
      >
        {options.map((item: any) => {
          const isSelected = selected === getOptionValue(item);

          return (
            <MenuItem
              className={`${styles.menuItem} ${isSelected && styles.selected}`}
              onClick={() => {
                onChange(getOptionValue(item));
                toggleMenu(false);
              }}
              key={getOptionValue(item)}
            >
              <span>{getOptionDisplay(item)}</span>
              {isSelected && <Check />}
            </MenuItem>
          );
        })}
      </ControlledMenu>
    </div>
  );
};

export default SelectInput;
