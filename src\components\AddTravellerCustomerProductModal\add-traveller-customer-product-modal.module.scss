.plans {
  margin-right: 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 16px;

  .label {
    font-size: 14px;
  }
}

.planCardPreview {
  padding: 12px;
  border-radius: 16px;
  border: 1px solid var(--gray-100);
  max-width: 320px;

  .productTypeTag {
    font-size: 12px;
    color: var(--primary-900);
    background: var(--primary-50);
    border-radius: 8px;
    padding: 6px 12px;
    width: fit-content;
    margin-bottom: 12px;
    text-transform: capitalize;
  }

  .row {
    display: flex;
    justify-content: space-between;
  }

  .productCountryContainer {
    display: flex;
    gap: 8px;
    align-items: center;

    img {
      border-radius: 1000px;
      width: 30px;
      height: 30px;
    }

    .countryNameAndOffersContainer {
      display: flex;
      flex-direction: column;
      gap: 2px;
    }

    .countryName {
      font-size: 14px;
      font-weight: 700;
      line-height: 18px;
      color: var(--primary-900);
    }

    .offers {
      font-size: 12px;
      color: var(--gray-500);
      line-height: 15px;
    }
  }
  .price {
    font-size: 12px;
    color: var(--gray-500);
    line-height: 15px;
    align-self: flex-end;
  }
}
