import styles from "./AddSubscriberModal.module.scss";
import Modal from "../Modal";
import { useEffect, useRef, useState } from "react";
import { createStateObject } from "../utils/InputHandlers";
import { CheckCircle, PersonCircle, User, XCircle } from "../svgs";
import ActiviteSubscriberModal from "../ActiviteSubscriberModal/ActiviteSubscriberModal";
import { AddressStep, InputFieldsStep } from "../ModalsSteps/ModalsSteps";
import { Collapse } from "@mui/material";
import Dialog from "../Dialog";

const portInFields = ["portInNumber", "zipCode"];
const oldNumberFields = [
  "firstName",
  "lastName",
  "billingAccountNumber",
  "pinPassword",
];
const subscriberFields = [
  "firstName",
  "lastName",
  "streetNumber",
  "streetDirection",
  "streetName",
  "state",
  "city",
  "zipCode",
  "email",
  "contactNumber",
  "channel",
];

const AddSubscriberModal = ({
  show,
  close,
}: {
  show: boolean;
  close: Function;
}) => {
  const [data, setData] = useState(
    createStateObject([
      ...portInFields,
      ...oldNumberFields,
      ...subscriberFields,
    ])
  );
  const [step, setStep] = useState(1);
  const [isPortIn, setIsPortIn] = useState("");
  const [numberIneligible, setNumberIneligible] = useState(false);
  const [numberVerified, setNumberVerified] = useState(false);
  const [cancelBtnText, setCancelBtnText] = useState("");
  const [saveBtnText, setSaveBtnText] = useState("");
  const [showActiviteModal, setShowActiviteModal] = useState(false);

  useEffect(() => {
    handleButtonsText();
  }, [step, isPortIn]);

  const handleButtonsText = () => {
    if (isPortIn === "Yes") {
      if (step === 1) {
        setCancelBtnText("Cancel");
        setSaveBtnText("Check Eligibility");
      } else if (step === 4) {
        setSaveBtnText("Continue");
        setCancelBtnText("Close");
      } else {
        setSaveBtnText("Continue");
        setCancelBtnText("Cancel");
      }
    }
    if (isPortIn === "No") {
      if (step === 2) {
        setSaveBtnText("Subscriber Profile Screen");
        setCancelBtnText("Close");
      } else {
        setSaveBtnText("Add subscriber");
        setCancelBtnText("Activate Subscriber");
      }
    }
    if (isPortIn === "") {
      setSaveBtnText("");
    }
  };

  const handleCloseModal = () => {
    close(false);
    setIsPortIn("");
    setSaveBtnText("");
    setCancelBtnText("");
    setStep(1);
  };

  const handleCancelPortNo = () => {
    handleCloseModal();
    setShowActiviteModal(true);
  };

  const handleNextStep = () => {
    // scroll to top of modal content
    modalContentRef.current?.scrollTo(0, 0);

    if (isPortIn === "Yes") {
      if (step === 1 && !data.portInNumber) {
        setNumberIneligible(true);
      } else if (step === 1 && data.portInNumber && numberVerified) {
        setStep(step + 1);
      } else if (step === 1 && data.portInNumber) {
        setNumberIneligible(false);
        setNumberVerified(true);
        setSaveBtnText("Continue");
      } else if (step <= 4) {
        setStep(step + 1);
        if (step === 4) {
          setShowActiviteModal(true);
          handleCloseModal();
        }
      } else {
        handleCloseModal();
      }
    }
    if (isPortIn === "No") {
      if (step < 2) {
        setStep(step + 1);
      } else {
        handleCloseModal();
      }
    }
  };

  const modalContentRef = useRef<HTMLDivElement | null>(null);

  return (
    <>
      <ActiviteSubscriberModal
        show={showActiviteModal}
        close={(val: boolean) => setShowActiviteModal(val)}
        type="Add Subscriber"
      />
      <Dialog
        /* iconColor={(step === 2 && isPortIn === 'No') && 'success'} */
        modalContentRef={modalContentRef}
        open={show}
        size="sm"
        onClose={() => handleCloseModal()}
        headerTitle={
          step === 2 && isPortIn === "No"
            ? "Subscriber Created!"
            : "Add Subscriber"
        }
        headerIcon={
          step === 2 && isPortIn === "No" ? <CheckCircle /> : <PersonCircle />
        }
        confirmButtonText={saveBtnText}
        cancelButtonText={cancelBtnText}
        confirmButtonOnClick={() => handleNextStep()}
        cancelButtonOnClick={
          step === 1 && isPortIn === "No"
            ? () => handleCancelPortNo()
            : () => handleCloseModal()
        }
      >
        <div className={styles.main}>
          {step < 2 && (
            <div className={styles.choicesStep}>
              <p>Porting In?</p>
              <div className="flex">
                <span
                  className={
                    styles.choicesBg +
                    " " +
                    (isPortIn === "Yes" ? styles.selected : "")
                  }
                  onClick={() => setIsPortIn("Yes")}
                >
                  Yes
                </span>
                <span
                  className={
                    styles.choicesBg +
                    " " +
                    (isPortIn === "No" ? styles.selected : "")
                  }
                  onClick={() => setIsPortIn("No")}
                >
                  No
                </span>
              </div>
            </div>
          )}
          {step === 1 && isPortIn === "Yes" ? (
            <div>
              <h5>Eligibility Check</h5>
              <InputFieldsStep
                data={data}
                setData={(val: any) => setData(val)}
                fields={portInFields}
              />
              <Collapse in={numberIneligible}>
                <div className={styles.notEligible}>
                  <XCircle />
                  <div>
                    <div className={styles.topText}>
                      <p>Number is not eligible for port in</p>
                    </div>
                    <div className={styles.bottomText}>
                      <p>
                        The entered phone number has an outstanding balance with
                        their current service provider.
                      </p>
                    </div>
                  </div>
                </div>
              </Collapse>
              <Collapse style={{ width: "100%" }} in={numberVerified}>
                <div className={styles.eligible}>
                  <CheckCircle />
                  <div>
                    <div className={styles.topText}>
                      <p>Number is eligible for port in</p>
                    </div>
                    <div className={styles.bottomText}>
                      <p>You can continue</p>
                    </div>
                  </div>
                </div>
              </Collapse>
            </div>
          ) : (step === 2 && isPortIn === "Yes") ||
            (step === 1 && isPortIn === "No") ? (
            <div>
              {isPortIn === "Yes" && (
                <>
                  <p>Portin Number: 071234567890</p>
                  <h5>Old Carrier Details</h5>
                  <InputFieldsStep
                    data={data}
                    setData={(val: any) => setData(val)}
                    fields={oldNumberFields}
                  />
                </>
              )}
              {isPortIn === "No" && (
                <>
                  <h5>Subscriber details</h5>
                  <InputFieldsStep
                    data={data}
                    setData={(val: any) => setData(val)}
                    fields={subscriberFields}
                  />
                </>
              )}
            </div>
          ) : step === 3 && isPortIn === "Yes" ? (
            <div>
              <p>Portin Number: 071234567890</p>
              <h5>Old Carrier Address</h5>
              <AddressStep data={data} setData={(val: any) => setData(val)} />
            </div>
          ) : step === 4 && isPortIn === "Yes" ? (
            <>
              <h5>Subscriber details</h5>
              <InputFieldsStep
                data={data}
                setData={(val: any) => setData(val)}
                fields={subscriberFields}
              />
            </>
          ) : step === 2 && isPortIn === "No" ? (
            <div className={styles.successMsg}>
              <p>
                You can now return to Subscriber Management page or see the
                profile screen of the subscriber you just created.
              </p>
            </div>
          ) : null}
        </div>
      </Dialog>
    </>
  );
};

export default AddSubscriberModal;
