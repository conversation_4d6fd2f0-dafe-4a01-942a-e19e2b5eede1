import BlobSwitchBar from "@/components/BlobSwitchBar";
import styles from "./att-tab.module.scss";
import { useMemo, useState } from "react";
import WholeSaleTab from "@/pages/product-management/att-tab/wholesale-tab";
import RetailTab from "@/pages/product-management/att-tab/retail-tab";
import FeesTab from "@/pages/product-management/att-tab/fees-tab";
import Button from "@/components/Button";
import { Plus } from "@/components/svgs";
import CreateFeeModal from "@/components/CreateFeeModal";
import { faker } from "@faker-js/faker";

const ATTProductManagementTab = () => {
  const [activeTabId, setActiveTabId] = useState("wholesale");

  const [showCreateFeeModal, setShowCreateFeeModal] = useState(false);

  const feesData = useMemo(() => {
    // only regenerate fees data when active tab is fees so that
    // alternating between generating 2 / 3 fees works correctly
    return generateFeesData(activeTabId === "fees");
  }, [activeTabId]);
  const hasReachedFeeLimit = feesData.length > 2;

  return (
    <>
      <CreateFeeModal
        open={showCreateFeeModal}
        onClose={() => setShowCreateFeeModal(false)}
      />
      <div className={styles.main}>
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
          }}
        >
          <BlobSwitchBar
            options={[
              {
                label: "Wholesale",
                id: "wholesale",
              },
              {
                label: "Retail",
                id: "retail",
              },
              {
                label: "Fees",
                id: "fees",
              },
            ]}
            selected={activeTabId}
            setSelected={setActiveTabId}
            layoutId="att-product-management-tabs"
          />

          {/* Create Fee button */}
          {activeTabId === "fees" && (
            <div
              style={{
                display: "flex",
                alignItems: "center",
                gap: 8,
                marginTop: 8,
              }}
            >
              {hasReachedFeeLimit && (
                <span
                  style={{
                    maxWidth: 180,
                    color: "var(--text-secondary)",
                    fontSize: 12,
                  }}
                >
                  You've reached the limit of 3 fees. No additional fees can be
                  added.
                </span>
              )}
              <Button
                onClick={() => setShowCreateFeeModal(true)}
                disabled={hasReachedFeeLimit}
              >
                <Plus />
                <span>Create Fee</span>
              </Button>
            </div>
          )}
        </div>

        {activeTabId === "wholesale" && <WholeSaleTab />}
        {activeTabId === "retail" && <RetailTab />}
        {activeTabId === "fees" && <FeesTab feesData={feesData} />}
      </div>
    </>
  );
};

export default ATTProductManagementTab;

const generateFeesData = (isFeesTabActive: boolean) => {
  const fees = [
    {
      id: 1,
      type: "Regulatory",
      name: "feeName",
      amount: 3,
      creationDate: faker.date.between({
        from: new Date(2024, 10, 26),
        to: new Date(2024, 12, 3),
      }),
      status: true,
    },
    {
      id: 2,
      type: "Activation",
      name: "feeName",
      amount: 9.99,
      creationDate: faker.date.between({
        from: new Date(2024, 10, 26),
        to: new Date(2024, 12, 3),
      }),
      status: true,
    },
  ];

  // add a third fee half the time the tab is switched / page reloaded
  // so that user can visualize state for when the fees limit is reached
  if (isFeesTabActive) {
    const addThirdFee = () => {
      fees.push({
        id: 3,
        type: "thirdFeeType",
        name: "feeName",
        amount: 2.48,
        creationDate: faker.date.between({
          from: new Date(2024, 10, 26),
          to: new Date(2024, 12, 3),
        }),
        status: false,
      });
    };

    const key = "addThirdFee";
    const storedValue = sessionStorage.getItem(key);

    if (storedValue === null || storedValue === "false") {
      sessionStorage.setItem(key, "true");
    } else if (storedValue === "true") {
      addThirdFee();
      sessionStorage.setItem(key, "false");
    }
  }

  return fees;
};
