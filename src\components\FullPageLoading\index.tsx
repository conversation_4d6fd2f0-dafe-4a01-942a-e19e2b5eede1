import { CircularProgress, Fade } from "@mui/material";
import styles from "./full-page-loading.module.scss";

const FullPageLoading = ({ loading }: any) => {
  return (
    <Fade in={loading} unmountOnExit>
      <div className={styles.main}>
        <CircularProgress
          style={{
            width: 80,
            height: 80,
            color: "#fde5d4",
          }}
        />
      </div>
    </Fade>
  );
};

export default FullPageLoading;
