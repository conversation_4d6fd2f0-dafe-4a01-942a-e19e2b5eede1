import { CircularProgress } from "@mui/material";
import styles from "./button.module.scss";
import { CSSProperties } from "react";

export type ButtonColors =
  | "primary"
  | "secondary"
  | "tertiary"
  | "quaternary"
  | "search"
  | "export"
  | "blue"
  | "customerActionBlue"
  | "customerActionRed"
  | "delete"
  | "destructive"
  | "transparent";

type ButtonProps = Partial<{
  style: CSSProperties;
  onClick: (e: any) => void;
  loading: boolean;
  disabled: boolean;
  children: React.ReactNode;
  color: ButtonColors;
  id: string;
}>;

const Button = ({
  style,
  onClick,
  loading,
  disabled,
  children,
  color = "primary",
  id = "",
}: ButtonProps) => {
  return (
    <button
      id={id}
      disabled={loading || disabled}
      className={`${styles.button} ${styles[color]}`}
      style={style}
      onClick={onClick}
    >
      {loading && (
        <CircularProgress
          style={{
            width: 20,
            height: 20,
            color:
              color === "primary" || color === "search" ? "#fff" : "#F47D27",
            gridArea: "1 / 1 / 2 / 2",
            margin: "0 auto",
          }}
        />
      )}
      <span
        style={{
          visibility: loading ? "hidden" : "visible",
        }}
        className={styles.content}
      >
        {children}
      </span>
    </button>
  );
};

export default Button;
