@use "../../styles/theme.scss" as *;

.selectContainer {
  width: 100%;
  margin-bottom: 12px;
  position: relative;
}

.label {
  position: absolute;
  z-index: 10;
  background: $off-white;
  transition: all 0.3s ease;
  pointer-events: none;
  color: $placeholder;
  top: -5px;
  left: 12px;
  font-size: 12px;
  line-height: 16px;
  padding: 0 4px;
  &.white {
    background: #fff;
  }
  &.disabled {
    color: $disabled-text;
  }
  &.labelError {
    color: $error !important;
  }
}

.inputContainer {
  width: 100%;
  margin-bottom: 9px;
}

.errorIcon {
  position: absolute;
  right: 15px;
  svg {
    width: 20px;
    height: 20px;
  }
}

.errorText {
  margin: 0px 0px 0px 5px;
  font-size: 12px;
  color: $error;
  text-align: start;
}
