@use "../../styles/theme.scss" as *;

.menuButton {
  background: none;
  border: none;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  -webkit-user-select: none; /* Safari */
  -ms-user-select: none; /* IE 10 and IE 11 */
  user-select: none;
  color: var(--primary-500);
  width: 40px;
  height: 40px;

  button {
    padding: 5px;
  }
  svg {
    transition: all 0.2s ease;
    stroke: #2e70e5;
    width: 24px;
    height: 24px;
  }
  &.iconOpen {
    svg {
      transform: rotate(180deg);
    }
  }
  &.withLabel {
    background: #d6e3fa;
    color: #1857c3;
    padding: 10px 15px;
    border-radius: 20px;
    font-weight: 600;
    width: auto;
    svg {
      margin-right: 5px;
    }
  }
}

.menu ul {
  min-width: initial !important;
  li {
    svg {
      stroke: #2e70e5;
      color: #2e70e5;
      margin-right: 10px;
    }
  }
}

.menuItem {
  min-width: 185px;
  text-align: center;
  transition: all 0.1s ease;
  color: #061632;
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 400;
  line-height: 21px;
  &:last-of-type {
    margin-bottom: 0px;
  }
  &:hover {
    background: var(--primary-50);
  }
  svg {
    color: var(--primary-500);
  }
}
