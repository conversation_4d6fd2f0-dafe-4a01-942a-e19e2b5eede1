import { useEffect, useState } from "react";
import Dialog from "../Dialog";
import { ArrowsClockwise, PencilCircle } from "../svgs";
import { useDispatch } from "react-redux";
import {
  clearInput,
  createStateObject,
  handleInputChange,
  labels,
} from "../utils/InputHandlers";
import { Input } from "../Input";
import { ChoicesStep, PlansStep } from "../ModalsSteps/ModalsSteps";
import DatePicker from "../DatePicker";

const dateInitalDate: { [key: string]: any } = {
  start: null,
  end: null,
  startTime: {
    hh: "09",
    mm: "00",
  },
  endTime: {
    hh: "21",
    mm: "00",
  },
};

const stepOneFields = ["currentSubs", "newSubs"];

const ChangeSubscriptionModal = ({
  show,
  closeModal,
}: {
  show: boolean;
  closeModal: Function;
}) => {
  const dispatch = useDispatch();
  const [data, setData] = useState(createStateObject([]));
  const [step, setStep] = useState(1);

  useEffect(() => {
    setData({
      currentSubs: "Airespring-Custom Usage 1 GB",
      newSubs: "",
      plan: "",
      effectiveDate: "",
      errors: {},
    });
  }, []);

  const handleNextStep = () => {
    if (step === 1) {
      setStep(2);
    } else {
      closeModal();
      dispatch({
        type: "notify",
        payload: {
          error: false,
          message: "Subscription changed Successfully",
        },
      });
    }
  };

  return (
    <Dialog
      size="sm"
      open={show}
      onClose={() => closeModal("")}
      headerTitle="Change Subscription"
      headerIcon={<PencilCircle />}
      headerSubtitle="Number: 071234567890"
      confirmButtonText={step === 1 ? "Continue" : "Apply Change Subscription"}
      confirmButtonIcon={step === 2 && <ArrowsClockwise />}
      cancelButtonText="Cancel"
      cancelButtonOnClick={() => closeModal("")}
      confirmButtonOnClick={() => handleNextStep()}
    >
      {step === 1 && (
        <>
          <Input
            key={`current-input`}
            label={labels["currentSubs"]}
            value={data["currentSubs"]}
            onChange={(e: any) => {
              handleInputChange("currentSubs", e, data, setData);
            }}
            error={data.errors["currentSubs"]}
            clear={() => {
              clearInput("currentSubs", setData);
            }}
            disabled
          />

          <div>
            <h5>New Subscription</h5>
            <ChoicesStep
              fieldName="isTether"
              data={data}
              setData={(val: any) => setData(val)}
              title="Tether Plans?"
              choices={["Yes", "No"]}
            />
            <PlansStep data={data} setData={(val: any) => setData(val)} />
          </div>
        </>
      )}
      {step === 2 && (
        <>
          {stepOneFields.map((field: string) => (
            <Input
              key={`${field}-input`}
              label={labels[field]}
              value={field === "newSubs" ? data["plan"] : data[field]}
              disabled
              editable={field === "newSubs"}
              editOnClick={() => setStep(1)}
            />
          ))}
          <DatePicker
            label={"effectiveDate"}
            field={"effectiveDate"}
            masterFrom={dateInitalDate.start}
            masterUntil={dateInitalDate.end}
            startTime={dateInitalDate.startTime}
            endTime={dateInitalDate.endTime}
          />
        </>
      )}
    </Dialog>
  );
};

export default ChangeSubscriptionModal;
