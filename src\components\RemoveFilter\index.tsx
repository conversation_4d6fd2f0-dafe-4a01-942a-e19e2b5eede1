import styles from "./remove-filter.module.scss";
import { XCircle } from "../svgs";
import { motion, AnimatePresence } from "framer-motion";
import { statuses } from "../StatusPill";
import { countryListAlpha2 } from "../utils/countryList";

const RemoveFilter = ({
  type,
  status = null,
  handleRemoveFilter,
  filter,
  filterObject,
  setFilterObject,
  grey,
  children,
}: any) => {
  console.log(status);
  return (
    <AnimatePresence>
      <motion.div
        layout
        initial="initial"
        animate="in"
        variants={{
          initial: {
            opacity: 0,
          },
          in: {
            opacity: 1,
          },
          out: {
            opacity: 0,
          },
        }}
        transition={{
          type: "tween",
          ease: "easeInOut",
          duration: 0.3,
        }}
        exit={{ opacity: 0 }}
        className={`${styles.main} ${grey && styles.grey} ${
          status === 0
            ? styles.inactive
            : status === 1
              ? styles.active
              : status === 2
                ? styles.open
                : status === 3
                  ? styles.pending
                  : status === 4
                    ? styles.resolved
                    : status === 5
                      ? styles.closed
                      : null
        }`}
      >
        {type === "country" ? (
          <>
            <div
              className={styles.flag}
              style={{
                backgroundImage: `url(https://public-gist-bucket.s3.eu-north-1.amazonaws.com/flag-icons/png/${children}.png)`,
              }}
            />
            {
              countryListAlpha2.filter(
                (countryItem: any) => children === countryItem.code
              )[0]?.name
            }
          </>
        ) : (
          children
        )}
        <div
          className={styles.remove}
          onClick={() => {
            handleRemoveFilter(type, filter, filterObject, setFilterObject);
          }}
        >
          <XCircle />
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

export default RemoveFilter;
