import { useEffect, useState } from "react";
import SettingsSkeleton from "../SettingsSkeleton";
import styles from "./transaction-history.module.scss";
import { transactionFields } from "../utils/transactionFields";
import formatDate from "../utils/formatDate";
import { HandCoins, Funnel } from "../svgs";
import IconButton from "../IconButton";
import RefundTransactionModal from "../RefundTransactionModal";
import StatusPill from "../StatusPill";
import TableControl from "../TableControl";
import { getTransactions } from "../utils/dataCreator";
import PaymentMethodDisplay from "../PaymentMethodDisplay";

const TransactionHistory = () => {
  const [initialLoading, setInitialLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [transactions, setTransactions] = useState([] as any);
  const [isRefundModalOpen, setIsRefundModalOpen] = useState(false);
  const [selectedTransaction, setSelectedTransaction] = useState<any>(null);

  const reformatPrice = (priceStr: string) => {
    const amount = parseFloat(priceStr);
    const roundedAmount = Math.round((amount + Number.EPSILON) * 100) / 100;

    return `${roundedAmount}`;
  };

  const formatDataItem = (item: any, key: string) => {
    if (key === "time") {
      return formatDate(item[key]);
    } else if (key === "period") {
      return `${item[key]} days`;
    } else if (key === "result") {
      return (
        <StatusPill
          status={item[key]}
          color={item[key] === "failed" ? "red" : "green"}
        />
      );
    } else if (key === "price") {
      return reformatPrice(item.price);
    } else {
      return item[key] || "-";
    }
  };

  const itemsPerPage = 10;

  const handleRefund = (transaction: any) => {
    setSelectedTransaction(transaction);
    setIsRefundModalOpen(true);
  };

  useEffect(() => {
    setInitialLoading(true);
    setTimeout(() => {
      setTransactions(getTransactions(itemsPerPage));
      setInitialLoading(false);
    }, 500);
  }, [currentPage]);

  return (
    <div className={styles.container}>
      <div
        className="flex-justify-content-between flex align-items-center"
        style={{
          marginBottom: "15px",
        }}
      >
        <div className={styles.title}>Transaction History</div>
        <IconButton style={{ margin: "0px 8px" }}>
          <Funnel />
        </IconButton>
      </div>
      <div className={`${styles.tableContainer} table-scroll`}>
        <table>
          <thead style={{ backgroundColor: "white" }}>
            <tr>
              {transactionFields?.map((field: any) => (
                <>
                  <th>{field.label}</th>
                </>
              ))}

              <th />
            </tr>
          </thead>
          <tbody>
            {!initialLoading
              ? transactions?.map((item: any) => {
                  return (
                    <tr>
                      {transactionFields.map((field: any) => (
                        <td>
                          <div
                            style={{
                              display: "flex",
                              justifyContent: "flex-start",
                              alignItems: "center",
                            }}
                          >
                            {field.key === "method" ? (
                              <PaymentMethodDisplay
                                type={item.type}
                                lastDigits={item.lastdigits}
                              />
                            ) : (
                              formatDataItem(item, field.key)
                            )}
                          </div>
                        </td>
                      ))}
                      <td className={styles.actionCell}>
                        {item.result !== "Failed" && (
                          <>
                            {item.result === "Success" ? (
                              <div
                                className={styles.actionBox}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleRefund(item);
                                }}
                              >
                                <HandCoins width={16} /> Refund
                              </div>
                            ) : (
                              <div></div>
                            )}
                          </>
                        )}
                      </td>
                    </tr>
                  );
                })
              : Array.from(
                  {
                    length: itemsPerPage,
                  },
                  (v, i) => i
                ).map((i) => (
                  <SettingsSkeleton
                    key={"settings-skeleton-" + i}
                    noOfStandard={9}
                  />
                ))}
          </tbody>
        </table>
      </div>
      <div>
        <TableControl
          show
          itemsPerPage={itemsPerPage}
          setItemsPerPage={() => {}}
          currentPage={currentPage}
          setCurrentPage={setCurrentPage}
          numberOfPages={7}
          label="transactions"
        />
      </div>
      {isRefundModalOpen && (
        <RefundTransactionModal
          open={isRefundModalOpen}
          onClose={() => {
            setIsRefundModalOpen(false);
            setSelectedTransaction(null);
          }}
          transactionData={selectedTransaction}
        />
      )}
    </div>
  );
};

export default TransactionHistory;
