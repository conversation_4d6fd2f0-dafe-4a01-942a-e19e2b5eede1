import { useEffect, useState } from "react"
import { clearInput, createStateObject, handleInputChange, labels } from "../utils/InputHandlers"
import { FloppyDisk, PencilCircle, PlusCircle } from "../svgs"
import { Input } from "../Input"
import { useDispatch } from "react-redux";
import SelectInput from "../SelectInput"
import { ChoicesStep } from "../ModalsSteps/ModalsSteps"
import DatePicker from "../DatePicker"
import Dialog from "../Dialog"

const fields = ["type", "feeDiscount", "name", "discountType", "discountAmount", "applyTo", "startDate", "expiryDate", "promocode", "coderesuable", "maxuses", "status"]

const dateInitalDate: { [key: string]: any } = {
    start: null,
    end: null,
    startTime: {
      hh: "09",
      mm: "00",
    },
    endTime: {
      hh: "21",
      mm: "00",
    },
};

const AddEditPromotionModal = ({ show, setShow, repopulate, promotion} : any) =>  {
    const dispatch = useDispatch();
    const [data, setData] = useState(createStateObject(fields))

    const handleSuccess = () => {
        dispatch({
          type: "notify",
          payload: {
            error: false,
            message: `Promotion has been ${show === 'create' ? 'created' : 'updated'} successfully`,
          },
        });
        setShow('')
        repopulate(15)
    }

    useEffect(() => {
        if (show === 'edit') {
            setData({
                type: promotion.type,
                name: promotion.name,
                feeDiscount: 'Regulatory',
                promocode: promotion.code,
                status: promotion.status,
                discountType: promotion.discountType,
                discountAmount: promotion.amount,
                applyTo: promotion.applyTo,
                coderesuable: promotion.coderesuable,
                maxuses: promotion.maxuses,
                errors: {}
            })
        }
    }, [show])

    return (
        <Dialog
            confirmButtonText={show === 'create' ? 'Create Promotion' : <><FloppyDisk /> Save changes</>}
            headerIcon={show === 'create' ? <PlusCircle /> : <PencilCircle />}
            headerTitle={show === 'create' ? 'Create Promotion' : 'Edit Promotion'}
            open={show !== ''}
            confirmButtonOnClick={() => handleSuccess()}
            cancelButtonText="Cancel"
            size="sm"
            onClose={() => {
              setShow('');
            }}>
            {
                fields.map((field:string) => {
                    if (["type"].includes(field) || (["feeDiscount"].includes(field) && data['type'] === 'Fee Discount')) {
                        return (
                            <SelectInput
                            key={"promotion-" + field}
                            options={selectOptionsByField[field]}
                            label={labels[field]}
                            value={data[field]}
                            selected={data[field]}
                            onChange={(e: any) => {
                                handleInputChange(field, e, data, setData, 'select');
                            }}
                            error={data.errors[field]}
                            infoTooltipText
                        />
                        )
                    } else if (["name", "discountAmount", "promocode"].includes(field) || (["maxuses"].includes(field) && data["coderesuable"] === "Yes")) {
                        return (
                            <Input
                                key={"promotion-" + field}
                                label={labels[field]}
                                value={data[field]}
                                onChange={(e: any) => {
                                    handleInputChange(field, e, data, setData);
                                }}
                                error={data.errors[field]}
                                clear={() => {
                                    clearInput(field, setData);
                                }}
                                infoTooltipText
                            />
                        )
                    } else if (["discountType", "status", "applyTo", "coderesuable"].includes(field)) {
                        return (
                            <ChoicesStep key={"promotion-" + field} fieldName={field} data={data} setData={(val:any) => setData(val)} title={labels[field]} choices={choicesByField[field]} />
                        )
                    } else if (["startDate", "expiryDate"].includes(field)) {
                        return (
                            <DatePicker
                                key={"promotion-" + field}
                                label={field}
                                field={field}
                                masterFrom={dateInitalDate.start}
                                masterUntil={dateInitalDate.end}
                                startTime={dateInitalDate.startTime}
                                endTime={dateInitalDate.endTime}
                            />
                        )
                    }
                })
            }
        </Dialog>
    )
}

export default AddEditPromotionModal

const selectOptionsByField: Record<string, any> = {
    type: ["Fee Discount", "Discount"],
    feeDiscount: ["Regulatory"],
}

const choicesByField: Record<string, any> = {
    status: ["Active", "Inactive"],
    applyTo: ["Total", "Subtotal"],
    coderesuable: ["Yes", "No"],
    discountType: ["Fixed", "Percentage"]
}