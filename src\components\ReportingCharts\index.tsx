import { faker } from "@faker-js/faker";
import styles from "./reporting-charts.module.scss";
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  PointElement,
  LineElement,
  Filler,
} from "chart.js";
import { Bar, Doughnut } from "react-chartjs-2";
import { useEffect, useState } from "react";
import {
  getRegistrations,
  getTransactions,
  sortByDate,
  squashDataPoints,
} from "../utils/chartHelpers";
import {
  formatChartDate,
  formatChartDateLabel,
  formatChartTitle,
  formatReportsDate,
} from "../utils/formatDate";
import { Dollars, Euros, Pounds, Successful, Wallet } from "./icons";
import ChartRangeSelect from "../ChartRangeSelect";
import { XCircle } from "../svgs";

ChartJS.register(
  ArcElement,
  Tooltip,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  PointElement,
  LineElement,
  Filler
);

const formatNumber = (x: number) => {
  return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
};

const constantOptions = {
  responsive: true,
  scales: {
    x: {
      ticks: {
        color: "#667085",
        font: {
          size: 12,
          family: "Roboto",
        },
        autoSkip: true,
        callback(val: any, index: any): string {
          return formatChartDateLabel(
            (this as any).getLabelForValue(val)
          ) as any;
        },
      },
      border: {
        display: false,
      },
      grid: {
        color: "rgba(181, 181, 181, 0)",
      },
    },
    y: {
      ticks: {
        color: "#667085",
        font: {
          size: 12,
          family: "Roboto",
        },
        autoSkip: true,
        beginAtZero: true,
        callback: (value: number) => {
          if (value % 1 === 0) {
            return value;
          }
        },
      },
      border: { dash: [3, 2], display: false },
      grid: {
        color: "#BFC4CE",
        drawTicks: false,
        display: true,
      },
      beginAtZero: true,
    },
  },
  plugins: {
    tooltip: {
      callbacks: {
        title: (tooltipItem: any, data: any) => {
          return formatChartDate(tooltipItem[0].label);
        },
      },
    },
  },
} as any;

const ReportingCharts = () => {
  const [chartTimeRange, setChartTimeRange] = useState({
    start: formatReportsDate(
      new Date(new Date().setDate(new Date().getDate() - 8))
    ),
    end: formatReportsDate(
      new Date(new Date().setDate(new Date().getDate() - 1))
    ),
    key: "week",
  });

  const [users, setUsers] = useState({
    isLoading: true,
    isLoadingTotal: true,
    active: 0,
    total: 0,
  });

  const [registrationData, setRegistrationData] = useState(null as any);
  const [activeUserData, setActiveUserData] = useState(null as any);
  const [paymentChartData, setPaymentChartData] = useState(null as any);

  const [payments, setPayments] = useState(null as any);
  const [paymentData, setPaymentData] = useState([] as any);

  const [dateDisplay, setDateDisplay] = useState("");

  useEffect(() => {
    let startDate = chartTimeRange.start;

    let endDate = chartTimeRange.end;

    setDateDisplay(formatChartTitle(startDate, endDate));

    setUsers({ ...users, isLoading: true });
    const data = getRegistrations(chartTimeRange);
    const squashed = squashDataPoints(data);

    setRegistrationData({
      labels: Object.keys(squashed)
        .sort(sortByDate)
        .map((x, i) => x),
      datasets: [
        {
          label: "# of registrations",
          data: Object.keys(squashed)
            .sort(sortByDate)
            .map((x, i) => squashed[x]),
          borderRadius: 1000,
          backgroundColor: "#2E70E5",
          borderColor: "#2E70E5",
          borderWidth: 1,
          maxBarThickness: 16,
        },
      ],
    });

    setUsers({
      active: faker.number.int(10000),
      total: faker.number.int(10000),
      isLoading: false,
      isLoadingTotal: false,
    });
    const usersData = getRegistrations(chartTimeRange);
    const usersSquashed = squashDataPoints(usersData);
    setActiveUserData({
      labels: Object.keys(usersSquashed)
        .sort(sortByDate)
        .map((x, i) => x),
      datasets: [
        {
          label: "# of users",
          data: Object.keys(usersSquashed)
            .sort(sortByDate)
            .map((x, i) => usersSquashed[x]),
          borderRadius: 1000,
          backgroundColor: "#2E70E5",
          borderColor: "#2E70E5",
          borderWidth: 1,
          maxBarThickness: 16,
        },
      ],
    });

    const transactionData = getRegistrations(chartTimeRange);
    const transactionSquashed = squashDataPoints(transactionData);
    setPaymentChartData({
      labels: Object.keys(transactionSquashed)
        .sort(sortByDate)
        .map((x, i) => x),
      datasets: [
        {
          label: "# of transactions",
          data: Object.keys(transactionSquashed)
            .sort(sortByDate)
            .map((x, i) => transactionSquashed[x]),
          borderRadius: 1000,
          backgroundColor: "#2E70E5",
          borderColor: "#2E70E5",
          borderWidth: 1,
          maxBarThickness: 16,
        },
      ],
    });

    setPaymentData(getTransactions());
  }, [chartTimeRange]);

  const summaryItems = [
    {
      icon: Wallet,
      label: "Total Payments",
      amount: 154320,
    },
    {
      icon: Successful,
      label: "Successful Payments",
      amount: 154241,
    },
    {
      icon: XCircle,
      label: "Failed Payments",
      amount: 79,
    },
    {
      icon: Dollars,
      label: "Total USD Value",
      amount: 462960,
    },
    {
      icon: Pounds,
      label: "Total GBP Value",
      amount: 380160,
    },
    {
      icon: Euros,
      label: "Total EUR Value",
      amount: 450196,
    },
  ];

  return (
    <div className={styles.mainContainer}>
      <div className={styles.topBar}>
        <h3 className={styles.title}>Overview</h3>
        <ChartRangeSelect
          selected={chartTimeRange.key}
          setSelected={(range: any) => setChartTimeRange(range)}
          masterFrom={chartTimeRange.start}
          masterUntil={chartTimeRange.end}
        />
      </div>
      <div className={styles.grid}>
        <div className={styles.chartTile} style={{ gridColumn: "span 3" }}>
          <h4 className={styles.title}>Summary</h4>
          <div className={styles.summaryContainer}>
            <div className={styles.doughnut}>
              <Doughnut
                data={{
                  labels: ["Active Users", "Inactive Users"],
                  datasets: [
                    {
                      label: "# of Users",
                      data: [17905, 5968],
                      backgroundColor: ["#2E70E5", "#E6AA2E"],
                      borderColor: ["#fff", "#fff"],
                      borderRadius: 1000,
                    },
                  ],
                }}
                options={{
                  cutout: 50,
                }}
              />
            </div>
            <div className={styles.legend}>
              <div className={styles.legendItem} style={{ marginBottom: 16 }}>
                <div className={styles.legendTitle} style={{ marginRight: 7 }}>
                  Total Users:
                </div>
                <div className={styles.legendDataSm}>23,873</div>
              </div>
              <div className={styles.legendItem} style={{ marginBottom: 16 }}>
                <Ring />
                <div style={{ marginLeft: 19 }}>
                  <div className={styles.legendTitle}>Active Users</div>
                  <div className={styles.legendDataLg}>17,905</div>
                </div>
              </div>
              <div className={styles.legendItem}>
                <Ring color="#E6AA2E" />
                <div style={{ marginLeft: 19 }}>
                  <div className={styles.legendTitle}>Inactive Users</div>
                  <div className={styles.legendDataLg}>5,968</div>
                </div>
              </div>
            </div>
            <div className={styles.divider} />
            <div className={styles.summaryItemsContainer}>
              {summaryItems.map((item: any) => (
                <SummaryItem
                  Icon={item.icon}
                  label={item.label}
                  amount={item.amount}
                />
              ))}
            </div>
          </div>
        </div>
        <div className={styles.chartTile}>
          <h4 className={styles.title}>Payments</h4>
          {paymentChartData && (
            <Bar data={paymentChartData} options={constantOptions} />
          )}
        </div>
        <div className={styles.chartTile}>
          <h4 className={styles.title}>Registrations</h4>
          {registrationData && (
            <Bar data={registrationData} options={constantOptions} />
          )}
        </div>
        <div className={styles.chartTile}>
          <h4 className={styles.title}>Active Users</h4>
          {activeUserData && (
            <Bar data={activeUserData} options={constantOptions} />
          )}
        </div>
      </div>
    </div>
  );
};

export default ReportingCharts;

const SummaryItem = ({ Icon, label, amount }: any) => (
  <div className={styles.summaryItem}>
    <div className={styles.icon}>
      <div className={styles.svgContainer}>
        <Icon />
      </div>
    </div>
    <div>
      <div className={styles.label}>{label}</div>
      <div className={styles.amount}>{formatNumber(amount)}</div>
    </div>
  </div>
);

const Ring = ({ color = "#2E70E5" }: any) => (
  <svg
    width="27"
    height="28"
    viewBox="0 0 27 28"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle cx="13.5" cy="14" r="10.5" stroke={color} stroke-width="6" />
  </svg>
);
