@use "../../styles/theme.scss" as *;

.pagination {
  margin-top: 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;


  
  @media (min-width: 900px) {
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
  }
}

.ticketsPerPage {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 14px;
  padding-left: 7px;
  position: relative;
  -webkit-user-select: none; /* Safari */
  -ms-user-select: none; /* IE 10 and IE 11 */
  user-select: none;
  order: 2;

  @media (min-width: 900px) {
    margin-right: auto;
    order: 0;
  }

  p {
    color: var(--textField-text-placeholder);
    font-size: 14px;
  }

  select {
    border: 1px solid var(--textField-border-primary);
    border-radius: 8px;
    padding: 8px 12px;
    background: var(--textField-background-primary);
    color: var(--textField-text-placeholder);
  }
}
