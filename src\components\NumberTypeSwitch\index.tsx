import { SwitchTransition, CSSTransition } from "react-transition-group";
import styles from "./number-type-switch.module.scss";

const NumberTypeSwitch = ({ type, setType, selectedPlan }: any) => {
  return (
    <div>
      <div className={styles.label}>Select type</div>
      <div className={styles.container}>
        <div
          onClick={() => {
            if (selectedPlan?.hasMobileNumber) {
              setType("mobile");
            }
          }}
          className={`${styles.role} ${
            !selectedPlan?.hasMobileNumber && styles.greyOut
          }`}
        >
          Mobile
        </div>
        <div
          onClick={() => {
            if (selectedPlan?.hasLocalNumber) {
              setType("landline");
            }
          }}
          className={`${styles.role} ${
            !selectedPlan?.hasLocalNumber && styles.greyOut
          }`}
        >
          Landline
        </div>
        <div
          className={`${styles.thumb} ${type === "landline" && styles.right}`}
        >
          <SwitchTransition>
            <CSSTransition
              key={type}
              addEndListener={(node, done) =>
                node.addEventListener("transitionend", done, false)
              }
              classNames="fade"
            >
              <div>{type === "landline" ? <>Landline</> : <>Mobile</>}</div>
            </CSSTransition>
          </SwitchTransition>
        </div>
      </div>
    </div>
  );
};

export default NumberTypeSwitch;
