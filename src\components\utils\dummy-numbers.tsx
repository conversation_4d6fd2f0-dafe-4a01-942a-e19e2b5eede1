import { faker } from "@faker-js/faker";
import { countryListAlpha2 } from "./countryList";

const createDidNumber = () => {
  let alpha2 = faker.location.countryCode("alpha-2");
  return {
    number: faker.phone.number("+###########"),
    country: countryListAlpha2.filter(
      (country: any) => country.code === alpha2
    )[0]?.name,
    availability: "Available",
    registeredTo: faker.internet.email(),
    type: ["Mobile", "Landline"][faker.number.int(1)],
    period: faker.number.int(60),
    purchasedOn: faker.date.recent({ days: 100 }),
    status: [true, false][faker.number.int(1)],
  };
};

export const getDidNumbers = (number: number) => {
  return Array.from({ length: number }).map(() => createDidNumber());
};

const createEsimNumber = () => {
  let alpha2 = faker.location.countryCode("alpha-2");
  return {
    msisdn: faker.string.numeric(13),
    iccid: faker.string.numeric(18),
    country: countryListAlpha2.filter(
      (country: any) => country.code === alpha2
    )[0]?.name,
    availability: "Available",
    registeredTo: faker.internet.email(),
    period: faker.number.int(60),
    purchasedOn: faker.date.recent({ days: 100 }),
    status: [true, false][faker.number.int(1)],
  };
};

export const getEsimNumbers = (number: number) => {
  return Array.from({ length: number }).map(() => createEsimNumber());
};

const createPortedNumber = () => {
  let alpha2 = faker.location.countryCode("alpha-2");
  return {
    userId: faker.string.numeric(10),
    email: faker.internet.email(),
    msisdn: faker.string.numeric(13),
    pacCode: faker.string.numeric(5),
    country: countryListAlpha2.filter(
      (country: any) => country.code === alpha2
    )[0]?.name,
    city: faker.location.city(),
    status: "COMPLETED",
  };
};

export const getPortedNumbers = (number: number) => {
  return Array.from({ length: number }).map(() => createPortedNumber());
};
