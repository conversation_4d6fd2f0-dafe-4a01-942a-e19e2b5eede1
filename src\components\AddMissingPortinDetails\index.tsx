import { useState } from "react";
import Modal from "../Modal";
import styles from "./add-missing-port.module.scss";
import {
  clearInput,
  createStateObject,
  displayErrors,
  getMessages,
  getRules,
  handleInputChange,
  labels,
  placeholders,
} from "../utils/InputHandlers";
import { useDispatch } from "react-redux";
import { SwitchTransition, CSSTransition } from "react-transition-group";
import { Input } from "../Input";
import { validateAll } from "indicative/validator";
import { ApiPostAuth } from "../../pages/api/api";
import { Collapse } from "@mui/material";
import Button from "../Button";
import { CheckCircle, XCircle } from "../svgs";

const oldNumberFields = ["billingAccountNumber", "billingAccountPassword"];
const oldNumberRules = getRules(oldNumberFields);
const oldNumberMessages = getMessages(oldNumberFields);

const portInFields = ["portInNumber", "zipCode"];
const portInRules = getRules(portInFields);
const portInMessages = getMessages(portInFields);

const AddMissingPortinDetails = ({ show, setShow, repopulate, plan }: any) => {
  const dispatch = useDispatch();

  const [oldNumberData, setOldNumberData] = useState(
    createStateObject(oldNumberFields)
  );

  const [portInData, setPortInData] = useState(createStateObject(portInFields));
  const [activeSection, setActiveSection] = useState("check-eligibility");
  const [ineligibleReason, setIneligibleReason] = useState("");
  const [numberIneligible, setNumberIneligible] = useState(false);
  const [numberVerified, setNumberVerified] = useState(false);
  const [loading, setLoading] = useState(false);

  const handleCheckDone = () => {
    setActiveSection("enter-details");
  };

  const saveChanges = () => {
    validateAll(oldNumberData, oldNumberRules, oldNumberMessages)
      .then((response) => {
        setLoading(true);
        ApiPostAuth("/accounts/portin/temp/update", {
          oldService: {
            billingAccountNumber: oldNumberData.billingAccountNumber,
            billingAccountPassword: oldNumberData.billingAccountPassword,
          },
          accountDetails: plan.attDetails.accountDetails,
          msisdn: plan.attDetails.msisdn,
        })
          .then((response) => {
            repopulate("temp");
            handleCancel();
            dispatch({
              type: "notify",
              payload: {
                error: false,
                message: "Port in updated",
              },
            });
          })
          .catch((error) => {
            setLoading(false);
            dispatch({
              type: "notify",
              payload: {
                error: true,
                message: error.response.data.message,
              },
            });
          });
      })
      .catch((errors: any) => {
        displayErrors(errors, setOldNumberData);
      });
  };

  const reset = () => {
    setActiveSection("check-eligibility");
    setNumberIneligible(false);
    setNumberVerified(false);
    setOldNumberData(createStateObject(oldNumberFields));
    setPortInData(createStateObject(portInFields));
  };

  const handleCancel = () => {
    setShow(false);
    setTimeout(() => {
      reset();
    }, 300);
  };

  const checkPortIn = () => {
    setNumberIneligible(false);
    validateAll(portInData, portInRules, portInMessages)
      .then((response) => {
        setLoading(true);
        setNumberVerified(false);
        ApiPostAuth("/accounts/portin/check", {
          msisdn: portInData.portInNumber,
          zipCode: portInData.zipCode,
        })
          .then((response) => {
            setLoading(false);
            setNumberVerified(true);
          })
          .catch((error) => {
            setLoading(false);
            setNumberIneligible(true);
            if (error?.response?.data?.attResponse?.reasonDescription) {
              setIneligibleReason(
                error?.response?.data?.attResponse?.reasonDescription
              );
            } else if (error?.response?.data?.attResponse?.errorDescription) {
              setIneligibleReason(
                error?.response?.data?.attResponse?.errorDescription
              );
            } else {
              setIneligibleReason(error.response.data.message);
            }
          });
      })
      .catch((errors) => {
        setNumberVerified(false);
        displayErrors(errors, setPortInData);
      });
  };

  return (
    <Modal
      saveButton={
        activeSection === "check-eligibility"
          ? !numberVerified
            ? null
            : "Next"
          : "Save"
      }
      cancelButton="Cancel"
      image="/add_user_graphic.svg"
      imageStyle={activeSection === "enter-details"}
      show={show}
      proceed={
        activeSection === "check-eligibility" ? handleCheckDone : saveChanges
      }
      close={handleCancel}
      loading={loading}
      fullSize
      title={
        activeSection === "check-eligibility"
          ? "Eligibility Check"
          : "Add old carrier details"
      }
      subtitle={
        activeSection === "enter-details"
          ? `Port-in Number: ${portInData.portInNumber}`
          : null
      }
      onCancel={handleCancel}
    >
      <div
        className={styles.main}
        style={activeSection === "check-eligibility" ? { width: "auto" } : {}}
      >
        <SwitchTransition>
          <CSSTransition
            key={activeSection}
            addEndListener={(node, done) =>
              node.addEventListener("transitionend", done, false)
            }
            classNames="fade"
          >
            {activeSection === "check-eligibility" ? (
              <div style={{ width: "350px" }}>
                {portInFields.map((prop: string) => (
                  <Input
                    key={"portin-" + prop}
                    label={labels[prop]}
                    placeholder={placeholders[prop]}
                    value={portInData[prop]}
                    onChange={(e: any) =>
                      handleInputChange(prop, e, portInData, setPortInData)
                    }
                    error={portInData.errors[prop]}
                    clear={() => {
                      clearInput(prop, setPortInData);
                    }}
                    disabled={loading}
                    white
                    onKeyDown={checkPortIn}
                  />
                ))}
                <Collapse in={numberIneligible}>
                  <div className={styles.notEligible}>
                    <XCircle />
                    <div>
                      <div className={styles.topText}>
                        Number is not eligible for port in
                      </div>
                      <div className={styles.bottomText}>
                        {ineligibleReason}
                      </div>
                    </div>
                  </div>
                </Collapse>
                <Collapse style={{ width: "100%" }} in={numberVerified}>
                  <div className={styles.eligible}>
                    <CheckCircle />
                    <div>Number is eligible for port in</div>
                  </div>
                </Collapse>

                <Button
                  style={{ marginTop: 15, width: "100%" }}
                  onClick={checkPortIn}
                  loading={loading}
                >
                  Check Eligibility
                </Button>
              </div>
            ) : (
              <div style={{ width: "100%", maxWidth: 430, margin: "0 auto" }}>
                {oldNumberFields.map((prop: string) => (
                  <Input
                    key={"oldnumber-" + prop}
                    label={labels[prop]}
                    placeholder={placeholders[prop]}
                    value={oldNumberData[prop]}
                    onChange={(e: any) =>
                      handleInputChange(
                        prop,
                        e,
                        oldNumberData,
                        setOldNumberData
                      )
                    }
                    error={oldNumberData.errors[prop]}
                    clear={() => {
                      clearInput(prop, setOldNumberData);
                    }}
                    disabled={loading}
                    white
                  />
                ))}
              </div>
            )}
          </CSSTransition>
        </SwitchTransition>
      </div>
    </Modal>
  );
};

export default AddMissingPortinDetails;
