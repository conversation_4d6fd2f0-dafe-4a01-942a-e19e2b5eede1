import { useContext, useEffect } from "react";
import { formatDateWithTime, formatReportsDate } from "../utils/formatDate";
import { ApiPostAuth } from "../../pages/api/api";
import styles from "../../styles/reporting.module.scss";
import type { NewReport } from "@/types/report";
import { v4 } from "uuid";
import { reportFields } from "../utils/reportFields";
import { ReportContext } from "../ReportContext";
import { reportDispatchActions } from "../utils/reportReducer";
import { padArrayToLength } from "../utils/padArray";
import ESimStatusPill from "../ESimStatusPill";
import ActiveBadge from "../ActiveBadge";
import UserSkeleton from "../UserSkeleton";
import { createReport } from "./createDummyData";

const ReportingVoice = () => {
  function getCurrencySymbol(amt: number, data: any): string {
    const { currencyCode } = data;

    const amtWithSymbol = currencyCode!!
      ? new Intl.NumberFormat("en-US", {
          style: "currency",
          currency: currencyCode,
        }).format(amt)
      : amt + "";

    return amtWithSymbol;
  }
  const {
    state: {
      chartTimeRange,
      filteredReports,
      currentPage,
      reportsPerPage,
      is_loading,
    },
    actions: { dispatch },
  } = useContext(ReportContext);

  useEffect(() => {
    dispatch({ type: reportDispatchActions.IS_LOADING, payload: true });
    const newAllReports = {
      Voice: createReport(reportsPerPage),
    } as NewReport;
    dispatch({
      type: reportDispatchActions.SET_REPORT,
      payload: newAllReports,
    });
  }, [chartTimeRange]);

  return (
    <>
      {!is_loading ? (
        filteredReports.length !== 0 ? (
          padArrayToLength(
            filteredReports.slice(
              (currentPage - 1) * reportsPerPage,
              currentPage * reportsPerPage
            ),
            reportsPerPage,
            null
          ).map((singleProduct: any) => {
            if (singleProduct === null) {
              return (
                <tr
                  key={v4()}
                  style={{
                    visibility: "hidden",
                    pointerEvents: "none",
                  }}
                ></tr>
              );
            } else {
              return (
                <tr key={v4()}>
                  {reportFields["Voice"].map((field: any) => {
                    if (
                      field === "timestamp" ||
                      field === "provisionedTimestamp" ||
                      field === "assignmentTimestamp" ||
                      field === "paymentDate"
                    ) {
                      return (
                        <td key={v4()}>
                          {formatDateWithTime(singleProduct[field])}
                        </td>
                      );
                    } else if (field === "provisionedStatus") {
                      return (
                        <td key={v4()}>
                          <ESimStatusPill status={singleProduct[field]} />
                        </td>
                      );
                    } else if (field === "amount") {
                      return (
                        <td key={v4()}>
                          {getCurrencySymbol(
                            singleProduct[field],
                            singleProduct
                          )}
                        </td>
                      );
                    } else if (field === "paymentStatus") {
                      return (
                        <td key={v4()}>
                          {singleProduct[field] === 1 ? (
                            <span className={`${styles.paymentSuccess}`}>
                              Completed
                            </span>
                          ) : (
                            <span className={`${styles.paymentFailed}`}>
                              Failed
                            </span>
                          )}
                        </td>
                      );
                    } else if (field === "status") {
                      return (
                        <td key={v4()}>
                          <ActiveBadge isActive={singleProduct[field]} />
                        </td>
                      );
                    } else {
                      return <td key={v4()}>{singleProduct[field] || "-"}</td>;
                    }
                  })}
                </tr>
              );
            }
          })
        ) : (
          <tr
            style={{
              background: "none",
            }}
          >
            <td colSpan={100}>
              <div className={styles.noneFound}>
                <img src="/none_found.svg" />
                <h3>We couldn't find anything matching these filters</h3>
              </div>
            </td>
          </tr>
        )
      ) : (
        Array.from({ length: reportsPerPage }, (v, i) => i).map((i) => (
          <UserSkeleton
            key={"user-skeleton-" + i}
            noOfStandard={reportFields["SMS"].length}
          />
        ))
      )}
    </>
  );
};

export default ReportingVoice;
