import styles from "../AddSubscriberModal/AddSubscriberModal.module.scss";
import { useEffect, useState } from "react";
import {
  createStateObject,
  labels,
  clearInput,
  handleInputChange,
} from "../utils/InputHandlers";
import { CheckCircle, PencilCircle, PersonCircle } from "../svgs";
import {
  ImeiField,
  ChoicesStep,
  PlansStep,
  InputFieldsStep,
} from "../ModalsSteps/ModalsSteps";
import Dialog from "../Dialog";
import { Input } from "../Input";

const detailsFields = ["imei", "plan", "carrier", "iccid"];
const iccidFields = ["currentIccid", "newIccid"];

const ActiviteSubscriberModal = ({
  show,
  close,
  type,
}: {
  show: boolean;
  close: Function;
  type: string;
}) => {
  const [data, setData] = useState(
    createStateObject([...detailsFields, "isTether", ...iccidFields])
  );
  const [step, setStep] = useState(1);
  const [cancelBtnText, setCancelBtnText] = useState("Cancel");
  const [saveBtnText, setSaveBtnText] = useState("Continue");
  const [savedOrCreate, setSavedOrCreate] = useState("");

  useEffect(() => {
    handleButtonsText();
  }, [step, data.isTether]);

  useEffect(() => {
    if (type === "updateResubmit") {
      setData({
        imei: "12345678901234567890",
        carrier: "AT&T",
        currentIccid: "12345678901234567890",
        newImei: "",
        newIccid: "",
        iccid: "",
        plan: "",
        errors: {},
      });
    }
  }, []);

  const handleButtonsText = () => {
    if (step === 1) {
      setCancelBtnText("Cancel");
      setSaveBtnText("Continue");
    } else if (step === 2) {
      if (data.isTether === "Yes" || data.isTether === "No") {
        setCancelBtnText("Cancel");
        if (type === "Add Subscriber") {
          setSaveBtnText("Add plan");
        } else {
          setSaveBtnText("Update plan");
        }
      } else {
        setCancelBtnText("");
        setSaveBtnText("");
      }
    } else if (step === 3) {
      setCancelBtnText("Cancel");
      if (type === "Add Subscriber") {
        setSaveBtnText("Send Port-in Request");
      } else {
        setSaveBtnText("Continue");
      }
    } else {
      if (savedOrCreate === "create") {
        setCancelBtnText("Close");
        if (type === "Add Subscriber") {
          setSaveBtnText("Subscriber Profile Screen");
        } else {
          setSaveBtnText("Re-submit");
        }
      } else {
        setCancelBtnText("Close");
        setSaveBtnText("View");
      }
    }
  };

  const handleCloseModal = () => {
    close(false);
    setSaveBtnText("Continue");
    setCancelBtnText("Cancel");
    setSavedOrCreate("");
    setStep(1);
  };

  const handleSaveForLater = () => {
    setSavedOrCreate("save");
    setStep(4);
  };

  const handleHeaderTitle = () => {
    if (type === "Add Subscriber") {
      if (savedOrCreate === "create") {
        return "Port-in Request Sent!";
      } else if (savedOrCreate === "save") {
        return "Saved for Later!";
      } else {
        return "Activate Subscriber";
      }
    } else {
      return "Update & Re-Submit Activation";
    }
  };

  const handleHeaderSubtitle = () => {
    if (type === "Add Subscriber") {
      if (step === 3) {
        return "Enter device IMEI";
      } else {
        return "";
      }
    } else {
      if (step === 1) {
        return "Update IMEI";
      } else if (step === 2) {
        return "Update plan";
      } else if (step === 3) {
        return "Update ICCID";
      } else {
        return "Verify Details & Re-Submit";
      }
    }
  };

  const handleNextStep = () => {
    if (step === 3) {
      setSavedOrCreate("create");
    }
    if (step < 4) {
      setStep(step + 1);
    } else {
      handleCloseModal();
    }
  };

  return (
    <Dialog
      /*iconColor={step === 4 && 'success'}*/
      open={show}
      size="sm"
      onClose={() => handleCloseModal()}
      headerTitle={handleHeaderTitle()}
      headerSubtitle={handleHeaderSubtitle()}
      headerIcon={
        type === "updateResubmit" ? (
          <PencilCircle />
        ) : step === 4 ? (
          <CheckCircle />
        ) : (
          <PersonCircle />
        )
      }
      cancelButtonOnClick={() => handleCloseModal()}
      cancelButtonText={cancelBtnText}
      confirmButtonText={saveBtnText}
      confirmButtonOnClick={() => handleNextStep()}
      customButtonText={
        type === "Add Subscriber" && step === 3 && "Save for later"
      }
      customButtonOnClick={() => handleSaveForLater()}
    >
      <div className={styles.main}>
        {step === 1 ? (
          <>
            <ImeiField
              data={data}
              setData={(val: any) => setData(val)}
              disabled={type === "updateResubmit"}
            />
            {type === "updateResubmit" && (
              <ImeiField
                data={data}
                setData={(val: any) => setData(val)}
                newImei
              />
            )}
          </>
        ) : step === 2 ? (
          <>
            <ChoicesStep
              fieldName="isTether"
              data={data}
              setData={(val: any) => setData(val)}
              title="Tether Plans?"
              choices={["Yes", "No"]}
            />
            {(data.isTether === "Yes" || data.isTether === "No") && (
              <>
                <h5>Select a plan</h5>
                <PlansStep data={data} setData={(val: any) => setData(val)} />
              </>
            )}
          </>
        ) : step === 3 && type === "updateResubmit" ? (
          <>
            {iccidFields.map((field: string) => (
              <Input
                key={`${field}-input`}
                label={labels[field]}
                value={
                  field === "currentIccid"
                    ? "12345678901234567890"
                    : data[field]
                }
                onChange={(e: any) => {
                  handleInputChange(field, e, data, setData);
                }}
                error={data.errors[field]}
                clear={() => {
                  clearInput(field, setData);
                }}
                noClear={field === "currentIccid"}
                infoTooltipText={field === "newIccid"}
                disabled={field === "currentIccid"}
              />
            ))}
          </>
        ) : (step === 3 && type === "Add Subscriber") ||
          (step === 4 && type === "updateResubmit") ? (
          <>
            {detailsFields.map((field: string) => (
              <Input
                key={`${field}-input`}
                label={labels[field]}
                value={field === "carrier" ? "AT&T" : data[field]}
                onChange={(e: any) => {
                  handleInputChange(field, e, data, setData);
                }}
                error={data.errors[field]}
                clear={() => {
                  clearInput(field, setData);
                }}
                noClear={
                  (field !== "iccid" && type === "Add Subscriber") ||
                  type === "updateResubmit"
                }
                infoTooltipText
                disabled={
                  (field !== "iccid" && type === "Add Subscriber") ||
                  type === "updateResubmit"
                }
                editOnClick={() => {
                  setStep(field === "imei" ? 1 : field === "plan" ? 2 : 3);
                }}
                editable={
                  field === "imei" ||
                  field === "plan" ||
                  (field === "iccid" && type === "updateResubmit")
                }
              />
            ))}
          </>
        ) : (
          <div className={styles.successMsg}>
            {savedOrCreate === "create" ? (
              <>
                <p>
                  We have received your request and it’s currently in progress.
                </p>
                <p>
                  You can track the progress of your request by visiting your
                  subscriber profile screen
                </p>
              </>
            ) : (
              ""
            )}
          </div>
        )}
      </div>
    </Dialog>
  );
};

export default ActiviteSubscriberModal;
