import { useState } from "react";
import { useDispatch } from "react-redux";
import Modal from "../Modal";
import styles from "./active-number.module.scss";
import AvailabilityPill from "../AvailabilityPill";
import {
  numbersModalFields,
  eSimModalFields,
} from "../utils/numberModalFields";
import { ApiPatch } from "../../pages/api/api";
import { formatNumber } from "../utils/formatNumber";
import { countryListAlpha2 } from "../utils/countryList";

const CountryDisplay = ({ country }: any) => {
  return (
    <div className={styles.country}>
      <div
        className={styles.flag}
        style={{
          backgroundImage: `url(https://public-gist-bucket.s3.eu-north-1.amazonaws.com/flag-icons/png/${countryListAlpha2.find((item: any) => item.name.toLowerCase() === country.toLowerCase())?.code}.png)`,
        }}
      />
      {country}
    </div>
  );
};

const ActiveNumberModal = ({
  show,
  setShow,
  multiNumber,
  repopulate,
  remove,
  selection,
}: any) => {
  const dispatch = useDispatch();

  const [loading, setLoading] = useState(false);
  const multiNumbers = multiNumber?.map((item: any) => {
    return item.number.replace(/\s+/g, "").replace("+", "");
  });

  const activateNumber = () => {
    setLoading(true);
    ApiPatch(`/agent/activate-number`, {
      didNumbers: multiNumbers,
    })
      .then((res) => {
        dispatch({
          type: "notify",
          payload: {
            error: false,
            message: res.data.message,
          },
        });
        setLoading(false);
        setShow(false);
        repopulate();
      })
      .catch((err) => {
        setLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: err.response.data.message,
          },
        });
      });
  };

  const activateEsim = () => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
      setShow(false);
    }, 2000);
  };

  return (
    <Modal
      saveButton={
        selection === "did-numbers" ? (
          <>Yes, Activate DID Number{multiNumber?.length > 1 && "s"}</>
        ) : (
          <>Yes, Activate eSim{multiNumber?.length > 1 && "s"}</>
        )
      }
      cancelButton="No"
      image="/bulk_edit_confirm_graphic.svg"
      show={show}
      close={() => {
        setShow(false);
      }}
      proceed={selection === "did-numbers" ? activateNumber : activateEsim}
      loading={loading}
    >
      <div className={styles.panel}>
        <h3 style={{ marginBottom: "32px" }}>
          Activate {multiNumber?.length}{" "}
          {selection === "did-numbers" ? "DID Number" : "eSIM"}
          {multiNumber?.length > 1 && "s"}?
        </h3>
        <div className={`${styles.tableContainer} table-scroll`}>
          {multiNumber?.length > 0 && (
            <table>
              <thead>
                <tr>
                  {selection === "did-numbers"
                    ? numbersModalFields.map((field: any) => (
                        <th key={field.key}>{field.label}</th>
                      ))
                    : eSimModalFields.map((field: any) => (
                        <th key={field.key}>{field.label}</th>
                      ))}

                  <th />
                </tr>
              </thead>
              <tbody>
                {multiNumber.map((item: any) => (
                  <tr
                    key={
                      selection === "did-numbers" ? item?.number : item?.msisdn
                    }
                  >
                    <td>
                      {selection === "did-numbers"
                        ? formatNumber(item?.number)
                        : item?.msisdn}
                    </td>
                    <td>
                      <CountryDisplay country={item.country} />
                    </td>
                    <td>
                      <AvailabilityPill status={item.availability} />
                    </td>
                    <td className={styles.remove} onClick={() => remove(item)}>
                      remove
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>
      </div>
    </Modal>
  );
};

export default ActiveNumberModal;
