import styles from "./ticket-sidebar-tile.module.scss";
import { Coin, Cube, Home, Spanner } from "../svgs";
import TicketType from "../TicketType";
import TicketSelect from "../TicketSelect";
import StatusBadge from "../StatusBadge";
import Assignee from "../Assignee";
import Initials from "../Initials";
import Category from "../Category";
import Priority from "../Priority";
import formatDate from "../utils/formatDate";
import { useState } from "react";

const TicketSidebarTile = ({ ticket, handleTicketUpdate, agents }: any) => {
  const [loading, setLoading] = useState(false);

  return (
    <div className={styles.ticketSummary}>
      <div className={styles.top}>
        <TicketType status="Support" />
        <div className={styles.ticketNo}>#{ticket.id}</div>
        <div className={styles.date}>{formatDate(ticket.creationDate)}</div>
        {/*ticket.type !== "Support" && (
          <div
            style={{
              display: "flex",
              alignItems: "center",
              marginLeft: "auto",
            }}
          >
            <TicketSelect
              disabled={loading}
              label="Assignee"
              options={[
                {
                  key: "Robin Billington",
                  label: <Assignee name="Robin Billington" />,
                  displayLabel: <Initials>RB</Initials>,
                },
                {
                  key: "Christina Choong",
                  label: <Assignee name="Christina Choong" />,
                  displayLabel: <Initials>CC</Initials>,
                },
                {
                  key: "Ahmed Houssein",
                  label: <Assignee name="Ahmed Houssein" />,
                  displayLabel: <Initials>AH</Initials>,
                },
                {
                  key: "Malwina Roczniak",
                  label: <Assignee name="Malwina Roczniak" />,
                  displayLabel: <Initials>MR</Initials>,
                },
              ]}
              selected={ticket.assignee}
              onChange={(option: string) => {}}
              white
            />
            <TicketSelect
              disabled={loading}
              label="Status"
              options={[
                {
                  key: "Open",
                  label: <StatusBadge status="Open" />,
                  displayLabel: <StatusBadge status="Open" />,
                },
                {
                  key: "Pending",
                  label: <StatusBadge status="Pending" />,
                  displayLabel: <StatusBadge status="Pending" />,
                },
                {
                  key: "Resolved",
                  label: <StatusBadge status="Resolved" />,
                  displayLabel: <StatusBadge status="Resolved" />,
                },
                {
                  key: "Closed",
                  label: <StatusBadge status="Closed" />,
                  displayLabel: <StatusBadge status="Closed" />,
                },
              ]}
              selected={ticket.status}
              onChange={(option: string) => {}}
              white
            />
          </div>
        )*/}
      </div>
      {true ? (
        <>
          <div className={styles.subject}>{ticket.subject}</div>
          <div className={styles.issueLabel}>Issue:</div>
          <div
            className={styles.issueContent}
            dangerouslySetInnerHTML={{ __html: ticket.description }}
          />
          <div className={styles.dataBar}>
            <TicketSelect
              disabled={loading}
              label="Priority"
              selected={ticket.priority}
              onChange={(option: string) => {
                handleTicketUpdate("priority", option, ticket);
              }}
              options={[
                {
                  key: 4,
                  label: <Priority priority="Urgent" />,
                  displayLabel: <Priority priority="Urgent" />,
                },
                {
                  key: 3,
                  label: <Priority priority="High" />,
                  displayLabel: <Priority priority="High" />,
                },
                {
                  key: 2,
                  label: <Priority priority="Medium" />,
                  displayLabel: <Priority priority="Medium" />,
                },
                {
                  key: 1,
                  label: <Priority priority="Low" />,
                  displayLabel: <Priority priority="Low" />,
                },
              ]}
              white
            />
            <TicketSelect
              disabled={loading}
              label="Category"
              options={[
                {
                  key: "Mobilise",
                  label: <Category category="Mobilise" />,
                  displayLabel: <Home />,
                },
                {
                  key: "Technical Support",
                  label: <Category category="Technical Support" />,
                  displayLabel: <Spanner />,
                },
                {
                  key: "Finance",
                  label: <Category category="Finance" />,
                  displayLabel: <Coin />,
                },
                {
                  key: "Product Management",
                  label: <Category category="Product Management" />,
                  displayLabel: <Cube />,
                },
              ]}
              selected={ticket.category}
              onChange={(option: string) => {
                handleTicketUpdate("category", option, ticket);
              }}
              white
            />
            {/*<TicketSelect
              disabled={loading}
              label="Assignee"
              options={agents.map((agent: any) => ({
                key: agent.freshdeskId,
                label: (
                  <Assignee name={agent.firstName + " " + agent.lastName} />
                ),
                displayLabel: (
                  <Initials>
                    {agent.firstName.slice(0, 1)}
                    {agent.lastName.slice(0, 1)}
                  </Initials>
                ),
              }))}
              selected={ticket.assignee}
              onChange={(option: string) => {
                handleTicketUpdate("responder_id", option, ticket);
              }}
              white
            />*/}
            <TicketSelect
              disabled={loading}
              label="Status"
              options={[
                {
                  key: 2,
                  label: <StatusBadge status="Open" />,
                  displayLabel: <StatusBadge status="Open" />,
                },
                {
                  key: 3,
                  label: <StatusBadge status="Pending" />,
                  displayLabel: <StatusBadge status="Pending" />,
                },
                {
                  key: 4,
                  label: <StatusBadge status="Resolved" />,
                  displayLabel: <StatusBadge status="Resolved" />,
                },
                {
                  key: 5,
                  label: <StatusBadge status="Closed" />,
                  displayLabel: <StatusBadge status="Closed" />,
                },
              ]}
              selected={ticket.status}
              onChange={(option: string) => {
                handleTicketUpdate("status", option, ticket);
              }}
              white
            />
          </div>
        </>
      ) : ticket.type === "DID Request" ? (
        <div>
          <div className={styles.issueLabel}>Address</div>
          <div
            className={styles.issueContent}
            dangerouslySetInnerHTML={{
              __html: ticket.didRequest.address.replaceAll(",", "<br />"),
            }}
          />
        </div>
      ) : (
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            paddingRight: 12,
            paddingTop: 12,
          }}
        >
          <div>
            <div className={styles.issueLabel}>Phone Number</div>
            <div className={styles.issueContent}>
              {ticket.porting.phoneNumber}
            </div>
          </div>
          <div>
            <div className={styles.issueLabel}>Country</div>
            <div className={styles.issueContent}>{ticket.porting.country}</div>
          </div>
          <div>
            <div className={styles.issueLabel}>PAC Code</div>
            <div className={styles.issueContent}>{ticket.porting.pacCode}</div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TicketSidebarTile;
