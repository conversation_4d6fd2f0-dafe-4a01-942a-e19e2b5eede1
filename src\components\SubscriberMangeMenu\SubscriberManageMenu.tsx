import ProductsMenu from "../ProductsMenu";
import { Cog, Pencil, Receipt, Plus, Sparkle, Pause, Delete, ChangeSubscription, PaperPlane, Play } from "../svgs";

const SubscriberManageMenu = ({status, modalName, selectedTab, menuLabel, customModal}: {status: string, modalName:Function, selectedTab:Function, menuLabel?:string, customModal:Function }) => {
    return (
        <ProductsMenu
          data={{
            label: menuLabel ? 'Manage' : '',
            icon: <Cog/>,
            items:
            status === 'Active' ? 
                [
                  {
                    label: 'Edit Subscription Details',
                    onClick: () => modalName('editSubscriptionDetails'),
                    icon: <Pencil />
                  },
                  {
                    label: 'Change MDN',
                    onClick: () => modalName('changemdn'),
                    icon: <Pencil />
                  },
                  {
                    label: 'Change Address & MDN',
                    onClick: () => customModal('changeAddressMdn'),
                    icon: <Pencil />
                  },
                  {
                    label: 'Change ICCID',
                    onClick: () => modalName('changeIccid'),
                    icon: <Pencil />
                  },
                  {
                    label: 'Change IMEI',
                    onClick: () => modalName('changeimei'),
                    icon: <Pencil />
                  },
                  {
                    label: 'Change Subscription',
                    onClick: () => customModal('changeSubscription'),
                    icon: <ChangeSubscription />
                  },
                  {
                    label: 'Change Bill Cycle',
                    onClick: () => modalName('changeBill'),
                    icon: <Receipt />
                  },
                  {
                    label: 'Add Top-ups',
                    onClick: () => modalName('addTopups'),
                    icon: <Plus />
                  },
                  {
                    label: 'Edit Features',
                    onClick: () => selectedTab('configurations'),
                    icon: <Sparkle />
                  },
                  {
                    label: 'Suspend Subscription',
                    onClick: () => modalName('suspendSubscription'),
                    icon: <Pause />
                  },
                  {
                    label: 'Cancel Subscription',
                    onClick: () => modalName('cancelSubscription'),
                    icon: <Delete />
                  },
                ]
                : status === 'Rejected' ? [
                  {
                    label: "Re-submit",
                    onClick: () => modalName('resubmitActivation'),
                    icon: <PaperPlane />,
                  },
                  {
                    label: "Update & Re-submit",
                    onClick: () => customModal('updateResubmit'),
                    icon: <Pencil />,
                  },
                ] : status === 'Ready to Activate' ?
                [
                  {
                    label: "Resume Subscription",
                    onClick: () => modalName('resumeSubscription'),
                    icon: <Play />,
                  },
                  {
                    label: "Cancel Subscription",
                    onClick: () => modalName('cancelSubscription'),
                    icon: <Delete />,
                  },
                ] : [
                  {
                    label: "Activite Subscription",
                    onClick: () => modalName('activiteSubscription'),
                    icon: <Play />,
                  },
                  {
                    label: "Remove Subscription",
                    onClick: () => modalName('removeSubscription'),
                    icon: <Delete />,
                  },
                ],
        }}
      />
    )
}

export default SubscriberManageMenu