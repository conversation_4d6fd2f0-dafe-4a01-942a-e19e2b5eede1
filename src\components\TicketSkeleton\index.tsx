import styles from "./ticket-skeleton.module.scss";
import Checkbox from "../Checkbox";
import { ChevronDownLg } from "../svgs";
import Shimmer from "../Shimmer";

const TicketSkeleton = () => {
  return (
    <div className={styles.main}>
      <div className={styles.summary}>
        <div style={{ width: 24 }}>
          <Checkbox disabled />
        </div>
        <div className={styles.overview}>
          <div className={styles.top}>
            <div className={styles.id}>
              <Shimmer />
            </div>
            <div className={styles.date}>
              <Shimmer />
            </div>
          </div>
          <div className={styles.bottom}>
            <div className={styles.subject}>
              <Shimmer />
            </div>
            <div className={styles.email}>
              <Shimmer />
            </div>
          </div>
        </div>
        <div className={styles.dataBar}>
          <div className={`${styles.box1} ${styles.box}`}>
            <Shimmer />
          </div>
          <div className={`${styles.box2} ${styles.box}`}>
            <Shimmer />
          </div>
          <div className={`${styles.box3} ${styles.box}`}>
            <Shimmer />
          </div>
          <div className={`${styles.box4} ${styles.box}`}>
            <Shimmer />
          </div>
        </div>
        <div className={styles.expand}>
          <ChevronDownLg />
        </div>
      </div>
    </div>
  );
};

export default TicketSkeleton;
