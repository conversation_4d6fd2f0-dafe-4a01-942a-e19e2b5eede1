import styles from "./range-search.module.scss";
import { ControlledMenu, MenuItem, useMenuState } from "@szhsin/react-menu";
import "@szhsin/react-menu/dist/index.css";
import "@szhsin/react-menu/dist/transitions/slide.css";
import { ChevronDown } from "../svgs";
import { useEffect, useRef, useState } from "react";
import Checkbox from "../Checkbox";
import Button from "../Button";
import { Fade } from "@mui/material";
import SearchBar from "../SearchBar";
import { submitSearch } from "../utils/searchAndFilter";
import RoleSwitch from "../RoleSwitch";
import { Input } from "../Input";
import { handleInputChange, clearInput } from "../utils/InputHandlers";
import RangeSwitch from "../RangeSwitch";
import { data, event } from "jquery";

const RangeSearch = ({
  label,
  options,
  selected,
  setSelected,
  grid,
  twoColumnGrid,
  noClear,
  search,
  searchPlaceholder = "Min Price",
}: any) => {
  console.log("options", options);
  const ref = useRef(null);

  const [menuProps, toggleMenu] = useMenuState({ transition: true });

  const [stagedChanges, setStagedChanges] = useState([...selected]);
  console.log({ stagedChanges });
  const [Role, setRole] = useState("specific");

  useEffect(() => {
    setStagedChanges([...selected]);
  }, [selected]);

  const reset = () => {
    toggleMenu(false);
    setQuery("");
    setFilteredOptions(options);
  };

  const [query, setQuery] = useState("");

  const [filteredOptions, setFilteredOptions] = useState([] as any);

  console.log("FIltered Options", filteredOptions);
  useEffect(() => {
    setFilteredOptions(options);
  }, [options]);

  // useEffect(() => {
  //   if (query === "") {
  //     submitSearch(options, setFilteredOptions, query, setQuery);
  //   }
  // }, [query]);
  // console.log("ROLE", Role);

  return (
    <div className={`${styles.box} multi-select select`}>
      <div
        ref={ref}
        className={`${styles.menuButton} ${
          menuProps.state === "open" || menuProps.state === "opening"
            ? styles.iconOpen
            : styles.iconClosed
        }`}
        onClick={() => {
          if (menuProps.state === "closing") {
            toggleMenu(false);
          } else {
            toggleMenu(true);
          }
        }}
      >
        {label}
        <ChevronDown />
      </div>
      <ControlledMenu
        {...menuProps}
        anchorRef={ref}
        onClose={() => {
          reset();
          setStagedChanges([...selected]);
        }}
        align="center"
        position="auto"
        viewScroll="close"
        onItemClick={(e) => (e.keepOpen = true)}
      >
        <RangeSwitch role={Role} setRole={setRole} />

        {Role === "specific" ? (
          <div style={{ marginBottom: 32 }}>
            <Input
              label="Min Price"
              placeholder={searchPlaceholder}
              value={stagedChanges[0]}
              onChange={(e: any) => {
                setStagedChanges((prevValue) => {
                  let newValue = prevValue;
                  newValue[0] = e.target.value;
                  return [...newValue];
                });
              }}
              white
            />
          </div>
        ) : (
          <div style={{ display: "flex", maxWidth: "100", gap: "12px" }}>
            <div style={{ marginBottom: "12px" }}>
              <Input
                label="Min Price"
                placeholder={searchPlaceholder}
                value={stagedChanges[0]}
                onChange={(e: any) => {
                  setStagedChanges((prevValue) => {
                    let newValue = prevValue;
                    newValue[0] = e.target.value;
                    return [...newValue];
                  });
                }}
                white
              />
            </div>
            <div style={{ marginBottom: "12px" }}>
              <Input
                label="Max Price"
                placeholder={searchPlaceholder}
                value={stagedChanges[1]}
                onChange={(e: any) => {
                  setStagedChanges((prevValue) => {
                    let newValue = prevValue;
                    newValue[1] = e.target.value;
                    return [...newValue];
                  });
                }}
              />
            </div>
          </div>
        )}
        {/* <div
          className={`${styles.container} ${grid && styles.grid} ${
            twoColumnGrid && styles.twoColumnGrid
          } modal-scroll`}
        >
          {filteredOptions?.map((item: any) => (
            <div
              className={`${styles.menuItem} ${
                selected === item && styles.selected
              }`}
              key={item.key}
            >
              <div style={{ width: 24 }}>
                <Checkbox
                  checked={stagedChanges.includes(item.key)}
                  onClick={() => {
                    if (stagedChanges.includes(item.key)) {
                      setStagedChanges(
                        stagedChanges.filter((change) => change !== item.key)
                      );
                    } else {
                      setStagedChanges([...stagedChanges, item.key]);
                    }
                  }}
                />
              </div>
              {item.label}
            </div>
          ))}
        </div> */}
        <div className={styles.buttons}>
          {!noClear && (
            <Button
              color="quaternary"
              style={{
                marginRight: "auto",
                opacity:
                  stagedChanges.length !== 0 || selected.length !== 0 ? 1 : 0,
                pointerEvents:
                  stagedChanges.length !== 0 || selected.length !== 0
                    ? "all"
                    : "none",
              }}
              onClick={() => {
                setStagedChanges(["", ""]);
              }}
            >
              Clear All
            </Button>
          )}
          <Button
            onClick={() => {
              setStagedChanges(selected);
              reset();
            }}
            style={{
              marginRight: 12,
              marginLeft: noClear ? 0 : 20,
              minWidth: 0,
            }}
            color="secondary"
          >
            Cancel
          </Button>
          <Button
            style={{ minWidth: 0 }}
            onClick={() => {
              setSelected(stagedChanges);
              reset();
            }}
          >
            Apply
          </Button>
        </div>
      </ControlledMenu>
    </div>
  );
};

export default RangeSearch;
