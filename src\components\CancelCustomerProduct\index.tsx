import styles from "./cancel-customer-product.module.scss";
import Modal from "../Modal";
import { Delete } from "../svgs";
import { useState } from "react";
import { useDispatch } from "react-redux";
import { formatDateWords } from "../utils/formatDate";
import { ApiDelete } from "../../pages/api/api";
import { useParams } from "react-router-dom";
import { parsePhoneNumber } from "libphonenumber-js";

const CancelCustomerProduct = ({
  show,
  setShow,
  data,
  repopulate,
  isNumber,
}: any) => {
  const dispatch = useDispatch();

  const [loading, setLoading] = useState(false);

  const { mid } = useParams();

  const cancelProduct = () => {
    setLoading(true);
    ApiDelete(
      isNumber ? "/customer/subscription" : "/agent/plan",
      isNumber
        ? {
            accountMid: mid,
            subscriptionId: data.subscriptionId,
          }
        : {
            mid: mid,
            subscriptionId: data.subscriptionId,
          }
    )
      .then((response) => {
        setLoading(false);
        setShow(false);
        dispatch({
          type: "notify",
          payload: {
            error: false,
            message: response.data.message,
          },
        });
        repopulate();
      })
      .catch((error: any) => {
        setLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: error.response.data.message,
          },
        });
      });
  };

  return (
    <Modal
      saveButton={
        isNumber ? (
          <>
            <Delete />
            Yes, Delete DID Number
          </>
        ) : (
          "Yes, Cancel Product"
        )
      }
      cancelButton="No"
      image="/delete_user_graphic.svg"
      show={show}
      close={() => {
        setShow(false);
      }}
      proceed={cancelProduct}
      loading={loading}
    >
      <div className={styles.main}>
        <h3>
          {isNumber
            ? "Are you sure you want to delete this DID Number?"
            : "Are you sure you want to cancel this product?"}
        </h3>
        <div className={styles.productDetails}>
          <table>
            {isNumber && (
              <tr>
                <th>Status:</th>
                <td>{data.activated ? "Active" : "Inactive"}</td>
              </tr>
            )}
            <tr>
              <th>{data.activated ? "Type:" : "Product Type:"}</th>
              <td>
                {isNumber
                  ? data.classification.replace("Numbers", "")
                  : data.classification}
              </td>
            </tr>
            {isNumber && (
              <tr>
                <th>Country:</th>
                <td>{data.countries[0]?.countryName}</td>
              </tr>
            )}
            {isNumber && (
              <tr>
                <th>Number:</th>
                <td>
                  {(() => {
                    let phone = parsePhoneNumber(
                      data.didNumber.slice(0, 1) === "+"
                        ? data.didNumber
                        : "+" + data.didNumber
                    );
                    if (phone) {
                      return phone.formatInternational();
                    } else {
                      return data.didNumber;
                    }
                  })()}
                </td>
              </tr>
            )}
            {data.initialBytes > 0 && (
              <tr>
                <th>Data:</th>
                <td>{data.initialBytes / (1024 * 1024 * 1024)} GB</td>
              </tr>
            )}
            {data.initialMinutes > 0 && (
              <tr>
                <th>Mins:</th>
                <td>{data.initialMinutes}</td>
              </tr>
            )}
            {data.initialMessages > 0 && (
              <tr>
                <th>SMS:</th>
                <td>{data.initialMessages}</td>
              </tr>
            )}
            <tr>
              <th>Validity:</th>
              <td>
                {formatDateWords(data.startDate, true)} -{" "}
                {formatDateWords(data.endDate, true)}
              </td>
            </tr>
            <tr>
              <th>Auto-Renew:</th>
              <td>{data.autorenew ? "On" : "Off"}</td>
            </tr>
          </table>
        </div>
      </div>
    </Modal>
  );
};

export default CancelCustomerProduct;
