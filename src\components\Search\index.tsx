import styles from "./search-select.module.scss";
import { ControlledMenu, MenuItem, useMenuState } from "@szhsin/react-menu";
import "@szhsin/react-menu/dist/index.css";
import "@szhsin/react-menu/dist/transitions/slide.css";
import { ChevronDown } from "../svgs";
import { useEffect, useRef, useState } from "react";
import Checkbox from "../Checkbox";
import Button from "../Button";
import { Fade } from "@mui/material";
import SearchBar from "../SearchBar";
import {
  submitFilterSelectSearch,
  submitSearch,
} from "../utils/searchAndFilter";
import { useParams } from "react-router-dom";
import { useSelector } from "react-redux";

const Search = ({
  label,
  data,
  setFilteredData,
  setQueryDisplay,
  placeholder,
}: any) => {
  const ref = useRef(null);

  const [menuProps, toggleMenu] = useMenuState({ transition: true });

  const reset = () => {
    toggleMenu(false);
  };

  const [searchQuery, setSearchQuery] = useState("");

  useEffect(() => {
    if (searchQuery === "") {
      submitSearch(data, setFilteredData, searchQuery, setQueryDisplay);
    }
  }, [searchQuery]);

  return (
    <div className={`${styles.box} multi-select select`}>
      <div
        ref={ref}
        className={`${styles.menuButton} ${
          menuProps.state === "open" || menuProps.state === "opening"
            ? styles.iconOpen
            : styles.iconClosed
        }`}
        onClick={() => {
          if (menuProps.state === "closing") {
            toggleMenu(false);
          } else {
            toggleMenu(true);
          }
        }}
      >
        {label}
        <ChevronDown />
      </div>
      <ControlledMenu
        {...menuProps}
        anchorRef={ref}
        onClose={() => {
          toggleMenu(false);
        }}
        align="center"
        position="auto"
        viewScroll="close"
        onItemClick={(e) => (e.keepOpen = true)}
      >
        <SearchBar
          query={searchQuery}
          setQuery={setSearchQuery}
          onSubmit={() => {
            submitSearch(data, setFilteredData, searchQuery, setQueryDisplay);
          }}
          placeholder={placeholder}
          small
          grey
        />
      </ControlledMenu>
    </div>
  );
};

export default Search;
