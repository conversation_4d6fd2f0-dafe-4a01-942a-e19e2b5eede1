import styles from "./add-notes.module.scss";
import Modal from "../Modal";
import { useState } from "react";
import TextArea from "../TextArea";
import { clearInput, handleInputChange } from "../utils/InputHandlers";
import { ApiPostAuth } from "src/pages/api/api";
import { useParams } from "react-router-dom";
import { useDispatch } from "react-redux";
import getErrorMessage from "@/utils/get-error-message";

const AddNotesModal = ({ show, setShow }: any) => {
  const { mid } = useParams();
  const dispatch = useDispatch();

  const [loading, setLoading] = useState(false);
  const [data, setData] = useState({ note: "", errors: { note: "" } });

  const createNote = () => {
    setLoading(true);
    if (!data.note) {
      setData({ ...data, errors: { note: "Note cannot be empty" } });
      return;
    }

    setData({ ...data, errors: { note: "" } });
    ApiPostAuth(`/customer/${mid}/addnote`, { note: data.note })
      .then((response) => {
        setData({ ...data, note: "" });
        setShow(false);
        dispatch({
          type: "notify",
          payload: {
            error: false,
            message: response.data.message,
          },
        });
      })
      .catch((error: unknown) => {
        const message = getErrorMessage(error);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message,
          },
        });
      })
      .finally(() => {
        setLoading(false);
      });
  };

  return (
    <Modal
      saveButton="Add Note to Account"
      image="/bulk_edit_confirm_graphic.svg"
      show={show}
      setShow={setShow}
      proceed={createNote}
      close={setShow}
      onClose={() => setShow(false)}
      loading={loading}
    >
      <div className={styles.main}>
        <h3>Add Note</h3>
        <TextArea
          small
          placeholder="Note"
          label="Note"
          value={data.note}
          error={data.errors.note}
          clear={() => {
            clearInput("note", setData);
          }}
          onChange={(e: any) => {
            handleInputChange("note", e, data, setData);
          }}
        />
      </div>
    </Modal>
  );
};

export default AddNotesModal;
