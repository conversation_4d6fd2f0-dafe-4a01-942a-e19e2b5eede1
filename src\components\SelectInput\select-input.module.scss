@use "../../styles/theme.scss" as *;

.menuButton {
  height: 48px;
  width: 100%;
  border-radius: 16px;
  border: none;
  box-shadow: 0px 0px 0px 1px var(--textField-border-primary);
  padding-left: 16px;
  padding-right: 12px;
  margin-bottom: 16px;
  transition: border 0.1s ease;
  font-size: 14px;
  color: var(--textField-text-placeholder);
  background: var(--textField-background-primary);
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: background 0.2s ease;
  -webkit-user-select: none; /* Safari */
  -ms-user-select: none; /* IE 10 and IE 11 */
  user-select: none;
  &.selected {
    color: $black;
  }
  &:focus {
    box-shadow: 0px 0px 0px 2px var(--primary-400);
  }
  &.error {
    border: 2px solid $error !important;
  }
  &.disabled {
    border: 1px solid $disabled;
    color: $disabled-text;
    background: none;
    pointer-events: none;
    &.readonly {
      color: $black;
      border: 1px solid #cacaca;
    }
  }
  svg {
    transition: all 0.2s ease;
  }
  &.iconOpen {
    border: 2px solid var(--primary-400) !important;
    svg {
      transform: rotate(180deg);
    }
  }
}

.label {
  display: flex;
  gap: 6px;
  align-items: center;
  line-height: 18px;
  font-size: 14px;
  pointer-events: none;
  color: #061632;
  margin-bottom: 4px;
}

.menuItem {
  transition: all 0.1s ease;
  color: var(--primary-900);
  padding: 4px 12px !important;
  font-size: 14px;
  line-height: 18px;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  &:hover {
    background: var(--primary-100);
  }
}

.errorText {
  margin: 0px 0px 0px 5px;
  font-size: 12px;
  color: $error;
}

.menu {
  width: 320px !important;
  border-radius: 16px !important;
  background: #fff !important;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.15) !important;
  overflow: hidden;
}
