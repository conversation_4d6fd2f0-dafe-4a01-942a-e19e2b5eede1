import Dialog from "@/components/Dialog";
import { WarningCircle } from "@/components/svgs";
import styles from "./delete-fee-modal.module.scss";
import StatusPill from "@/components/StatusPill";

type DeleteFeeModalProps = {
  onClose: () => void;
  open: boolean;
  feeData: any;
};

const DeleteFeeModal = ({ onClose, open, feeData }: DeleteFeeModalProps) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      size="sm"
      headerIcon={<WarningCircle />}
      headerTitle="Delete Fee?"
      headerSubtitle="Once removed, it cannot be recovered"
      confirmButtonText="Yes, Delete Fee"
      confirmButtonOnClick={onClose}
      confirmButtonVariant="destructive"
      cancelButtonText="Cancel"
    >
      <div className={styles.tableContainer}>
        <table className={styles.table}>
          <tbody>
            <tr>
              <td className={styles.label}>Type</td>
              <td className={styles.value}>{feeData.type}</td>
            </tr>
            <tr>
              <td className={styles.label}>Name</td>
              <td className={styles.value}>{feeData.name}</td>
            </tr>
            <tr>
              <td className={styles.label}>Amount</td>
              <td className={styles.value}>{feeData.amount}</td>
            </tr>
            <tr>
              <td className={styles.label}>Status</td>
              <td className={styles.value}>
                {
                  <StatusPill
                    status={feeData.status ? "Active" : "Inactive"}
                    color={feeData.status ? "active" : "inactive"}
                  />
                }
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </Dialog>
  );
};

export default DeleteFeeModal;
