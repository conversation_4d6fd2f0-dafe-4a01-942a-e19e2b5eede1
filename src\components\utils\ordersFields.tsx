export const ordersFields = [
  {
    label: "Order Number",
    labelStr: "Order Number",
    key: "orderNumber",
  },
  {
    label: "Date & Time",
    labelStr: "Date & Time",
    key: "dateAndTime",
  },
  {
    label: "Subscriber",
    labelStr: "Subscriber",
    key: "subscriberName",
  },
  {
    label: "SIM Type",
    labelStr: "SIM Type",
    key: "simType",
  },
  {
    label: "Product",
    labelStr: "Product",
    key: "offerName",
  },
  {
    label: "IMEI",
    labelStr: "IMEI",
    key: "imei",
  },
  {
    label: "ICCID",
    labelStr: "ICCID",
    key: "iccid",
  },
  {
    label: "Status",
    labelStr: "Status",
    key: "status",
  },
];

export const createOrder = () => {
  const iccid = ["12345678912345", null][Math.floor(Math.random() * 2)];
  return {
    orderNumber: "123456",
    dateAndTime: new Date(),
    subscriberName: "<PERSON>" + " " + "<PERSON>",
    simType: ["eSIM", "SIM"][Math.floor(Math.random() * 2)],
    product: { offerName: "MVNO Throttle Data" },
    imei: "123456789123456789",
    iccid: iccid,
    status:
      iccid === null
        ? "ICCIDREQUIRED"
        : ["BANCHANGE", "READY"][Math.floor(Math.random() * 2)],
  };
};

export const getOrders = (size: number) => {
  let orders = [] as any;
  Array.from({ length: size }).forEach((i: any) => {
    orders.push(createOrder());
  });
  return orders;
};
