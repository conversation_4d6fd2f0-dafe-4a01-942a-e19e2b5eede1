export const LogoSmall = () => (
  <svg
    width="40"
    height="44"
    viewBox="0 0 40 44"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M31.1946 8H25.5425V36H31.1946V8Z" fill="#F47D27" />
    <path d="M31.1954 19.0227H8.8042V24.9755H31.1954V19.0227Z" fill="#F47D27" />
    <path d="M14.4924 8H8.81396V13.759H14.4924V8Z" fill="#F47D27" />
    <path
      d="M14.4924 30.2401H8.81396V35.9991H14.4924V30.2401Z"
      fill="#F47D27"
    />
  </svg>
);

export const LogoFull = () => (
  <svg
    width="124"
    height="44"
    viewBox="0 0 124 44"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M34.3909 8H28.7388V36H34.3909V8Z" fill="#F47D27" />
    <path d="M34.3912 19.0226H12V24.9753H34.3912V19.0226Z" fill="#F47D27" />
    <path d="M17.6887 8H12.0103V13.759H17.6887V8Z" fill="#F47D27" />
    <path
      d="M17.6887 30.2401H12.0103V35.9991H17.6887V30.2401Z"
      fill="#F47D27"
    />
    <path
      d="M55.4242 8.35244H39.6094V35.7259H55.4242V31.2615H45.0762V24.0168H54.2528V19.6699H45.0762V12.8168H55.4242V8.35244Z"
      fill="#F47D27"
    />
    <path
      d="M74.8405 16.8895C74.8405 19.3566 73.4737 20.9622 70.4279 20.9622H65.8592V12.9343H70.4279C73.4737 12.9343 74.8405 14.4615 74.8405 16.8895ZM60.3923 8.39161V35.7259H65.8592V25.0741H68.4364L74.45 35.7259H80.7759L74.2547 24.6434C78.6282 23.4685 80.4635 20.1007 80.4635 16.772C80.4635 12.1902 77.1834 8.39161 70.6232 8.39161H60.3923Z"
      fill="#F47D27"
    />
    <path
      d="M112 21.9804C112 13.7958 105.831 8 98.06 8C90.3673 8 84.0804 13.7958 84.0804 21.9804C84.0804 30.2042 90.3673 36 98.06 36C105.792 36 112 30.2042 112 21.9804ZM89.7035 21.9804C89.7035 16.4196 93.1008 12.9343 98.06 12.9343C102.98 12.9343 106.377 16.4196 106.377 21.9804C106.377 27.5413 102.98 31.1049 98.06 31.1049C93.1008 31.1049 89.7035 27.5413 89.7035 21.9804Z"
      fill="#F47D27"
    />
  </svg>
);

export const Stack = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_6629_55963)">
      <path
        d="M2 11L8 14.5L14 11"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M2 8L8 11.5L14 8"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M2 5L8 8.5L14 5L8 1.5L2 5Z"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_6629_55963">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const ArrowBack = ({ color = "currentColor" }: any) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M20.25 12H3.75"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10.5 5.25L3.75 12L10.5 18.75"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const LineChart = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_7146_123812)">
      <path
        d="M14 13H2V3"
        stroke="#061632"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M14 6L10 9.5L6 6.5L2 10"
        stroke="#061632"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_7146_123812">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const ChevronRight = () => (
  <svg
    width="24"
    height="25"
    viewBox="0 0 24 25"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_5823_38777)">
      <path
        d="M9 5L16.5 12.5L9 20"
        stroke="currentColor"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_5823_38777">
        <rect
          width="24"
          height="24"
          fill="white"
          transform="translate(0 0.5)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const SortIcon = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_5774_57630)">
      <path
        d="M10.5 16.5L7.5 19.5L4.5 16.5"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M7.5 4.5V19.5"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M13.5 7.5L16.5 4.5L19.5 7.5"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M16.5 19.5V4.5"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_5774_57630">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const ChevronDown = ({ color = "currentColor" }: any) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M17 10L12 15L7 10"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const ChevronDownLg = ({ color = "currentColor" }: any) => (
  <svg
    width="24"
    height="25"
    viewBox="0 0 24 25"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M19.5 9.5L12 17L4.5 9.5"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const LogOut = ({ color = "currentColor" }: any) => (
  <svg
    width="17"
    height="16"
    viewBox="0 0 17 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_7772_7636)">
      <path
        d="M2 14H15"
        stroke="#1857C3"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M4 14V2.5C4 2.36739 4.05268 2.24021 4.14645 2.14645C4.24021 2.05268 4.36739 2 4.5 2H12.5C12.6326 2 12.7598 2.05268 12.8536 2.14645C12.9473 2.24021 13 2.36739 13 2.5V14"
        stroke="#1857C3"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M10.5 2C10.6326 2 10.7598 2.05268 10.8536 2.14645C10.9473 2.24021 11 2.36739 11 2.5V14"
        stroke="#1857C3"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M8.75 9C9.16421 9 9.5 8.66421 9.5 8.25C9.5 7.83579 9.16421 7.5 8.75 7.5C8.33579 7.5 8 7.83579 8 8.25C8 8.66421 8.33579 9 8.75 9Z"
        fill="#1857C3"
      />
    </g>
    <defs>
      <clipPath id="clip0_7772_7636">
        <rect width="16" height="16" fill="white" transform="translate(0.5)" />
      </clipPath>
    </defs>
  </svg>
);

export const User = ({ color = "currentColor", ...props }: any) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M12 15C15.3137 15 18 12.3137 18 9C18 5.68629 15.3137 3 12 3C8.68629 3 6 5.68629 6 9C6 12.3137 8.68629 15 12 15Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M2.90625 20.2501C3.82775 18.6537 5.15328 17.328 6.74958 16.4062C8.34588 15.4845 10.1567 14.9993 12 14.9993C13.8433 14.9993 15.6541 15.4845 17.2504 16.4062C18.8467 17.328 20.1722 18.6537 21.0938 20.2501"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Ticketing = ({ color = "currentColor" }: any) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M9 5.25V18.75"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M2.25001 15.675C2.24906 15.5023 2.3085 15.3346 2.41807 15.2011C2.52763 15.0676 2.68042 14.9765 2.85001 14.9437C3.52456 14.8005 4.12946 14.43 4.56349 13.8941C4.99752 13.3583 5.23435 12.6896 5.23435 12C5.23435 11.3104 4.99752 10.6417 4.56349 10.1059C4.12946 9.57002 3.52456 9.19948 2.85001 9.05625C2.68042 9.02346 2.52763 8.93242 2.41807 8.79889C2.3085 8.66535 2.24906 8.49773 2.25001 8.325V6C2.25001 5.80109 2.32903 5.61032 2.46968 5.46967C2.61033 5.32902 2.8011 5.25 3.00001 5.25H21C21.1989 5.25 21.3897 5.32902 21.5303 5.46967C21.671 5.61032 21.75 5.80109 21.75 6V8.325C21.751 8.49773 21.6915 8.66535 21.582 8.79889C21.4724 8.93242 21.3196 9.02346 21.15 9.05625C20.4755 9.19948 19.8706 9.57002 19.4365 10.1059C19.0025 10.6417 18.7657 11.3104 18.7657 12C18.7657 12.6896 19.0025 13.3583 19.4365 13.8941C19.8706 14.43 20.4755 14.8005 21.15 14.9437C21.3196 14.9765 21.4724 15.0676 21.582 15.2011C21.6915 15.3346 21.751 15.5023 21.75 15.675V18C21.75 18.1989 21.671 18.3897 21.5303 18.5303C21.3897 18.671 21.1989 18.75 21 18.75H3.00001C2.8011 18.75 2.61033 18.671 2.46968 18.5303C2.32903 18.3897 2.25001 18.1989 2.25001 18V15.675Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Chip = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_6697_35911)">
      <path
        d="M11.875 8.125H8.125V11.875H11.875V8.125Z"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M15.625 3.75H4.375C4.02982 3.75 3.75 4.02982 3.75 4.375V15.625C3.75 15.9702 4.02982 16.25 4.375 16.25H15.625C15.9702 16.25 16.25 15.9702 16.25 15.625V4.375C16.25 4.02982 15.9702 3.75 15.625 3.75Z"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M16.25 8.125H18.125"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M16.25 11.875H18.125"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M1.875 8.125H3.75"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M1.875 11.875H3.75"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M11.875 16.25V18.125"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M8.125 16.25V18.125"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M11.875 1.875V3.75"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M8.125 1.875V3.75"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_6697_35911">
        <rect width="20" height="20" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Users = ({ color = "currentColor" }: any) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M8.25 15C10.9424 15 13.125 12.8174 13.125 10.125C13.125 7.43261 10.9424 5.25 8.25 5.25C5.55761 5.25 3.375 7.43261 3.375 10.125C3.375 12.8174 5.55761 15 8.25 15Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M14.5686 5.42813C14.9996 5.31124 15.444 5.25136 15.8905 5.25C17.1834 5.25 18.4234 5.76361 19.3376 6.67785C20.2519 7.59209 20.7655 8.83207 20.7655 10.125C20.7655 11.4179 20.2519 12.6579 19.3376 13.5721C18.4234 14.4864 17.1834 15 15.8905 15"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M1.5 18.5061C2.2612 17.4229 3.27191 16.5388 4.44676 15.9285C5.6216 15.3181 6.92608 14.9995 8.25 14.9995C9.57392 14.9995 10.8784 15.3181 12.0532 15.9285C13.2281 16.5388 14.2388 17.4229 15 18.5061"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M15.8906 15C17.2147 14.9992 18.5194 15.3174 19.6944 15.9277C20.8693 16.5381 21.8799 17.4225 22.6406 18.5063"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Sliders = ({ color = "currentColor" }: any) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M13.875 16.125H3.75"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M20.25 16.125H17.625"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M15.75 18C16.7855 18 17.625 17.1605 17.625 16.125C17.625 15.0895 16.7855 14.25 15.75 14.25C14.7145 14.25 13.875 15.0895 13.875 16.125C13.875 17.1605 14.7145 18 15.75 18Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M7.875 7.875H3.75"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M20.25 7.875H11.625"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9.75 9.75C10.7855 9.75 11.625 8.91053 11.625 7.875C11.625 6.83947 10.7855 6 9.75 6C8.71447 6 7.875 6.83947 7.875 7.875C7.875 8.91053 8.71447 9.75 9.75 9.75Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Close = ({ color = "currentColor" }: any) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M18.75 5.25L5.25 18.75"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M18.75 18.75L5.25 5.25"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Database = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_8232_56542)">
      <path
        d="M8 8C11.0376 8 13.5 6.65685 13.5 5C13.5 3.34315 11.0376 2 8 2C4.96243 2 2.5 3.34315 2.5 5C2.5 6.65685 4.96243 8 8 8Z"
        stroke="#061632"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M2.5 5V8C2.5 9.65688 4.9625 11 8 11C11.0375 11 13.5 9.65688 13.5 8V5"
        stroke="#061632"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M2.5 8V11C2.5 12.6569 4.9625 14 8 14C11.0375 14 13.5 12.6569 13.5 11V8"
        stroke="#061632"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_8232_56542">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Dashboard = ({ color = "currentColor" }: any) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_6119_1447)">
      <path
        d="M6.5 3H3.5C3.22386 3 3 3.22386 3 3.5V6.5C3 6.77614 3.22386 7 3.5 7H6.5C6.77614 7 7 6.77614 7 6.5V3.5C7 3.22386 6.77614 3 6.5 3Z"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.5 3H9.5C9.22386 3 9 3.22386 9 3.5V6.5C9 6.77614 9.22386 7 9.5 7H12.5C12.7761 7 13 6.77614 13 6.5V3.5C13 3.22386 12.7761 3 12.5 3Z"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.5 9H3.5C3.22386 9 3 9.22386 3 9.5V12.5C3 12.7761 3.22386 13 3.5 13H6.5C6.77614 13 7 12.7761 7 12.5V9.5C7 9.22386 6.77614 9 6.5 9Z"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.5 9H9.5C9.22386 9 9 9.22386 9 9.5V12.5C9 12.7761 9.22386 13 9.5 13H12.5C12.7761 13 13 12.7761 13 12.5V9.5C13 9.22386 12.7761 9 12.5 9Z"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_6119_1447">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const CaretRight = ({ color = "currentColor" }: any) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M9 4.5L16.5 12L9 19.5"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const CaretLeft = ({ color = "currentColor" }: any) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M15 19.5L7.5 12L15 4.5"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const ArrowLeft = ({ color = "currentColor" }: any) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M20.25 12H3.75"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10.5 5.25L3.75 12L10.5 18.75"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const MagnifyingGlass = ({ color = "currentColor" }: any) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_6119_21299)">
      <path
        d="M10.5 18C14.6421 18 18 14.6421 18 10.5C18 6.35786 14.6421 3 10.5 3C6.35786 3 3 6.35786 3 10.5C3 14.6421 6.35786 18 10.5 18Z"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M15.8037 15.8035L21.0003 21"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_6119_21299">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Tick = ({ color = "currentColor" }: any) => (
  <svg
    width="24"
    height="25"
    viewBox="0 0 24 25"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M20.25 7.25L9.75 17.75L4.5 12.5"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Export = ({ color = "currentColor" }: any) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M8.0625 5.4375L12 1.5L15.9375 5.4375"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12 12V1.5"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M16.5 9H18.75C18.9489 9 19.1397 9.07902 19.2803 9.21967C19.421 9.36032 19.5 9.55109 19.5 9.75V19.5C19.5 19.6989 19.421 19.8897 19.2803 20.0303C19.1397 20.171 18.9489 20.25 18.75 20.25H5.25C5.05109 20.25 4.86032 20.171 4.71967 20.0303C4.57902 19.8897 4.5 19.6989 4.5 19.5V9.75C4.5 9.55109 4.57902 9.36032 4.71967 9.21967C4.86032 9.07902 5.05109 9 5.25 9H7.5"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Home = ({ color = "currentColor" }: any) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M20.0062 10.275L12.5062 3.45933C12.368 3.33284 12.1874 3.2627 12 3.2627C11.8126 3.2627 11.632 3.33284 11.4937 3.45933L3.99375 10.275C3.91828 10.3462 3.85783 10.4319 3.81597 10.5269C3.77411 10.6219 3.75168 10.7243 3.75 10.8281V19.5C3.75 19.6989 3.82902 19.8896 3.96967 20.0303C4.11032 20.1709 4.30109 20.25 4.5 20.25H19.5C19.6989 20.25 19.8897 20.1709 20.0303 20.0303C20.171 19.8896 20.25 19.6989 20.25 19.5V10.8281C20.2483 10.7243 20.2259 10.6219 20.184 10.5269C20.1422 10.4319 20.0817 10.3462 20.0062 10.275V10.275Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Spanner = ({ color = "currentColor" }: any) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M20.5219 6.6563C21.0138 7.80501 21.1343 9.07881 20.8663 10.2994C20.5983 11.5199 19.9554 12.6261 19.0275 13.4631C18.0996 14.3001 16.9331 14.8259 15.6915 14.9671C14.4499 15.1082 13.1952 14.8575 12.1031 14.25V14.25L6.84375 20.3438C6.42106 20.7665 5.84777 21.0039 5.25 21.0039C4.65222 21.0039 4.07894 20.7665 3.65625 20.3438C3.23356 19.9211 2.99609 19.3478 2.99609 18.75C2.99609 18.1523 3.23356 17.579 3.65625 17.1563L9.75 11.8969C9.14258 10.8049 8.89188 9.55017 9.03299 8.30854C9.1741 7.06691 9.69996 5.90049 10.5369 4.97257C11.3739 4.04466 12.4801 3.40171 13.7007 3.13374C14.9212 2.86578 16.195 2.98621 17.3437 3.47817L13.4062 7.4063L13.9406 10.0594L16.5937 10.5938L20.5219 6.6563Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Coin = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12 14.25C17.3848 14.25 21.75 12.2353 21.75 9.75C21.75 7.26472 17.3848 5.25 12 5.25C6.61522 5.25 2.25 7.26472 2.25 9.75C2.25 12.2353 6.61522 14.25 12 14.25Z"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12 14.25V18.75"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M2.25 9.75V14.25C2.25 16.5 6 18.75 12 18.75C18 18.75 21.75 16.5 21.75 14.25V9.75"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M18 13.3218V17.8218"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6 13.3218V17.8218"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Cube = ({ color = "currentColor" }: any) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M21 16.6217V7.378C20.9993 7.24448 20.9634 7.1135 20.8959 6.99829C20.8284 6.88307 20.7317 6.78768 20.6156 6.72175L12.3656 2.08112C12.2545 2.01694 12.1284 1.98315 12 1.98315C11.8716 1.98315 11.7455 2.01694 11.6344 2.08112L3.38437 6.72175C3.26827 6.78768 3.1716 6.88307 3.10411 6.99829C3.03663 7.1135 3.00072 7.24448 3 7.378V16.6217C3.00072 16.7553 3.03663 16.8862 3.10411 17.0015C3.1716 17.1167 3.26827 17.2121 3.38437 17.278L11.6344 21.9186C11.7455 21.9828 11.8716 22.0166 12 22.0166C12.1284 22.0166 12.2545 21.9828 12.3656 21.9186L20.6156 17.278C20.7317 17.2121 20.8284 17.1167 20.8959 17.0015C20.9634 16.8862 20.9993 16.7553 21 16.6217V16.6217Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M20.8973 6.99365L12.0848 11.9999L3.10352 6.99365"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12.0844 12L12 22.0125"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const ArrowRight = ({ color = "currentColor", ...props }: any) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M3.75 12H20.25"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M13.5 5.25L20.25 12L13.5 18.75"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const XCircle = ({ color = "currentColor" }: any) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M15 9L9 15"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M15 15L9 9"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const FloppyDisk = ({ color = "currentColor" }: any) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M20.25 8.55938V19.5C20.25 19.6989 20.171 19.8897 20.0303 20.0303C19.8897 20.171 19.6989 20.25 19.5 20.25H4.5C4.30109 20.25 4.11032 20.171 3.96967 20.0303C3.82902 19.8897 3.75 19.6989 3.75 19.5V4.5C3.75 4.30109 3.82902 4.11033 3.96967 3.96967C4.11032 3.82902 4.30109 3.75 4.5 3.75H15.4406C15.538 3.74966 15.6345 3.76853 15.7246 3.80553C15.8147 3.84253 15.8966 3.89694 15.9656 3.96563L20.0344 8.03438C20.1031 8.10341 20.1575 8.18532 20.1945 8.27541C20.2315 8.36549 20.2503 8.46199 20.25 8.55938V8.55938Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M7.5 20.25V14.25C7.5 14.0511 7.57902 13.8603 7.71967 13.7197C7.86032 13.579 8.05109 13.5 8.25 13.5H15.75C15.9489 13.5 16.1397 13.579 16.2803 13.7197C16.421 13.8603 16.5 14.0511 16.5 14.25V20.25"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M14.25 6.75H9"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Plus = ({ color = "currentColor" }: any) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M3.75 12H20.25"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12 3.75V20.25"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const SendPassword = ({ color = "currentColor" }: any) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M20.5908 16.1565C19.7352 18.0233 18.2668 19.5414 16.4295 20.4587C14.5922 21.376 12.4965 21.6374 10.4901 21.1995C8.48381 20.7616 6.68757 19.6507 5.39957 18.0513C4.11157 16.4519 3.40929 14.4601 3.40929 12.4065C3.40929 10.3529 4.11157 8.36113 5.39957 6.7617C6.68757 5.16226 8.48381 4.0514 10.4901 3.6135C12.4965 3.1756 14.5922 3.43701 16.4295 4.35432C18.2668 5.27164 19.7352 6.78969 20.5908 8.6565"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M18 9H21V6"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M15.124 10.4375H8.87402C8.70143 10.4375 8.56152 10.5774 8.56152 10.75V15.125C8.56152 15.2976 8.70143 15.4375 8.87402 15.4375H15.124C15.2966 15.4375 15.4365 15.2976 15.4365 15.125V10.75C15.4365 10.5774 15.2966 10.4375 15.124 10.4375Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10.5928 10.4375V9.03125C10.5928 8.65829 10.7409 8.3006 11.0047 8.03688C11.2684 7.77316 11.6261 7.625 11.999 7.625C12.372 7.625 12.7297 7.77316 12.9934 8.03688C13.2571 8.3006 13.4053 8.65829 13.4053 9.03125V10.4375"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Delete = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_6119_7978)">
      <path
        d="M20.25 5.25H3.75"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.25 2.25H15.75"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M18.75 5.25V19.5C18.75 19.6989 18.671 19.8897 18.5303 20.0303C18.3897 20.171 18.1989 20.25 18 20.25H6C5.80109 20.25 5.61032 20.171 5.46967 20.0303C5.32902 19.8897 5.25 19.6989 5.25 19.5V5.25"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_6119_7978">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const AddUser = ({ color = "currentColor" }: any) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M18.75 12.75H23.25"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M21 10.5V15"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10.125 15C13.2316 15 15.75 12.4816 15.75 9.375C15.75 6.2684 13.2316 3.75 10.125 3.75C7.0184 3.75 4.5 6.2684 4.5 9.375C4.5 12.4816 7.0184 15 10.125 15Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M2.08203 18.7501C3.06758 17.5755 4.29844 16.6309 5.68809 15.9829C7.07774 15.3348 8.59246 14.999 10.1258 14.999C11.6591 14.999 13.1738 15.3348 14.5635 15.9829C15.9531 16.6309 17.184 17.5755 18.1695 18.7501"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Clear = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21Z"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M15 9L9 15"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M15 15L9 9"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Eye = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10 4.375C3.75 4.375 1.25 10 1.25 10C1.25 10 3.75 15.625 10 15.625C16.25 15.625 18.75 10 18.75 10C18.75 10 16.25 4.375 10 4.375Z"
      stroke="#160B2A"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10 13.125C11.7259 13.125 13.125 11.7259 13.125 10C13.125 8.27411 11.7259 6.875 10 6.875C8.27411 6.875 6.875 8.27411 6.875 10C6.875 11.7259 8.27411 13.125 10 13.125Z"
      stroke="#160B2A"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const EyeSlash = () => (
  <svg
    width="21"
    height="21"
    viewBox="0 0 21 21"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M4.72461 3.16016L17.2246 16.9102"
      stroke="#160B2A"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M13.0758 12.3475C12.5024 12.8729 11.752 13.163 10.9743 13.16C10.3435 13.16 9.7276 12.969 9.2074 12.6124C8.68721 12.2557 8.28713 11.7499 8.05974 11.1616C7.83236 10.5733 7.78833 9.92994 7.93343 9.31612C8.07853 8.70231 8.40597 8.14677 8.87271 7.72253"
      stroke="#160B2A"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6.75586 5.39465C3.56836 7.00403 2.22461 10.0353 2.22461 10.0353C2.22461 10.0353 4.72461 15.6603 10.9746 15.6603C12.4391 15.6722 13.8854 15.3348 15.1934 14.6759"
      stroke="#160B2A"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M17.2715 13.2461C18.9746 11.7227 19.7246 10.0352 19.7246 10.0352C19.7246 10.0352 17.2246 4.41018 10.9746 4.41018C10.4328 4.40911 9.89182 4.45353 9.35742 4.54299"
      stroke="#160B2A"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M11.5605 6.96484C12.2251 7.09076 12.8307 7.42943 13.2859 7.92972C13.7411 8.43002 14.0212 9.06481 14.084 9.73828"
      stroke="#160B2A"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const ClockTimer = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12 7.5L12 12L15.75 14.25"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6.75 9.75L3 9.75L3 6"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6.3375 18C7.51684 19.1128 8.99798 19.8535 10.5958 20.1294C12.1937 20.4052 13.8374 20.2041 15.3217 19.5512C16.8059 18.8982 18.0648 17.8224 18.9411 16.458C19.8173 15.0937 20.2721 13.5014 20.2486 11.8801C20.2251 10.2587 19.7244 8.68026 18.8089 7.3419C17.8934 6.00354 16.6039 4.96463 15.1014 4.35498C13.5988 3.74532 11.95 3.59195 10.3608 3.91404C8.77157 4.23612 7.31253 5.01938 6.16594 6.16594C5.0625 7.28344 4.15125 8.33719 3 9.75"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const StopWatch = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="25"
    viewBox="0 0 24 25"
    fill="none"
  >
    <g clip-path="url(#clip0_8255_22935)">
      <path
        d="M12 21.5C16.5563 21.5 20.25 17.8063 20.25 13.25C20.25 8.69365 16.5563 5 12 5C7.44365 5 3.75 8.69365 3.75 13.25C3.75 17.8063 7.44365 21.5 12 21.5Z"
        stroke="#1A1A1A"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12 13.25L15.75 9.5"
        stroke="#1A1A1A"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9.75 2H14.25"
        stroke="#1A1A1A"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_8255_22935">
        <rect
          width="24"
          height="24"
          fill="white"
          transform="translate(0 0.5)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const Pause = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M18.75 3.75H15C14.5858 3.75 14.25 4.08579 14.25 4.5V19.5C14.25 19.9142 14.5858 20.25 15 20.25H18.75C19.1642 20.25 19.5 19.9142 19.5 19.5V4.5C19.5 4.08579 19.1642 3.75 18.75 3.75Z"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9 3.75H5.25C4.83579 3.75 4.5 4.08579 4.5 4.5V19.5C4.5 19.9142 4.83579 20.25 5.25 20.25H9C9.41421 20.25 9.75 19.9142 9.75 19.5V4.5C9.75 4.08579 9.41421 3.75 9 3.75Z"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Play = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M6.75 3.73876V20.2613C6.75245 20.3931 6.78962 20.522 6.85776 20.6349C6.9259 20.7478 7.0226 20.8407 7.13812 20.9043C7.25364 20.9679 7.38388 20.9999 7.51572 20.9972C7.64756 20.9944 7.77634 20.9569 7.88906 20.8884L21.3966 12.6272C21.5045 12.5619 21.5937 12.4699 21.6556 12.36C21.7175 12.2501 21.7501 12.1261 21.7501 12C21.7501 11.8739 21.7175 11.7499 21.6556 11.64C21.5937 11.5302 21.5045 11.4381 21.3966 11.3728L7.88906 3.11157C7.77634 3.04314 7.64756 3.00564 7.51572 3.00285C7.38388 3.00007 7.25364 3.03209 7.13812 3.0957C7.0226 3.1593 6.9259 3.25224 6.85776 3.36514C6.78962 3.47804 6.75245 3.60691 6.75 3.73876Z"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Calendar = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
  >
    <g clip-path="url(#clip0_8051_67350)">
      <path
        d="M19.5 3.75H4.5C4.08579 3.75 3.75 4.08579 3.75 4.5V19.5C3.75 19.9142 4.08579 20.25 4.5 20.25H19.5C19.9142 20.25 20.25 19.9142 20.25 19.5V4.5C20.25 4.08579 19.9142 3.75 19.5 3.75Z"
        stroke="#0C2C64"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M16.5 2.25V5.25"
        stroke="#0C2C64"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M7.5 2.25V5.25"
        stroke="#0C2C64"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M3.75 8.25H20.25"
        stroke="#0C2C64"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_8051_67350">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const CaretUp = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M4.5 15L12 7.5L19.5 15"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const CaretDown = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M19.5 9L12 16.5L4.5 9"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Power = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_292_1359)">
      <path
        d="M10 3.75V10"
        stroke="#474747"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13.75 4.375C15.6312 5.60156 16.875 7.58672 16.875 10C16.875 11.8234 16.1507 13.572 14.8614 14.8614C13.572 16.1507 11.8234 16.875 10 16.875C8.17664 16.875 6.42795 16.1507 5.13864 14.8614C3.84933 13.572 3.125 11.8234 3.125 10C3.125 7.58672 4.36875 5.60156 6.25 4.375"
        stroke="#474747"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_292_1359">
        <rect width="20" height="20" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Mobile = () => (
  <svg
    width="21"
    height="20"
    viewBox="0 0 21 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_292_965)">
      <path
        d="M15.666 16.875L15.666 3.125C15.666 2.43464 15.1064 1.875 14.416 1.875L6.91602 1.875C6.22566 1.875 5.66602 2.43464 5.66602 3.125L5.66602 16.875C5.66602 17.5654 6.22566 18.125 6.91602 18.125H14.416C15.1064 18.125 15.666 17.5654 15.666 16.875Z"
        stroke="#474747"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10.666 5.625C11.1838 5.625 11.6035 5.20527 11.6035 4.6875C11.6035 4.16973 11.1838 3.75 10.666 3.75C10.1482 3.75 9.72852 4.16973 9.72852 4.6875C9.72852 5.20527 10.1482 5.625 10.666 5.625Z"
        fill="#474747"
      />
    </g>
    <defs>
      <clipPath id="clip0_292_965">
        <rect
          width="20"
          height="20"
          fill="white"
          transform="translate(0.666016)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const ArrowsClockwise = () => (
  <svg
    width="21"
    height="20"
    viewBox="0 0 21 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_292_1192)">
      <path
        d="M5.33398 5C5.33398 5 7.20898 3.125 10.334 3.125C14.709 3.125 17.209 7.5 17.209 7.5"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M15.334 15C15.334 15 13.459 16.875 10.334 16.875C5.95898 16.875 3.45898 12.5 3.45898 12.5"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13.459 7.5H17.209V3.75"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7.20898 12.5H3.45898V16.25"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_292_1192">
        <rect
          width="20"
          height="20"
          fill="white"
          transform="translate(0.333984)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const SimCard = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_292_1900)">
      <path
        d="M15.625 17.5H4.375C4.20924 17.5 4.05027 17.4342 3.93306 17.3169C3.81585 17.1997 3.75 17.0408 3.75 16.875V3.125C3.75 2.95924 3.81585 2.80027 3.93306 2.68306C4.05027 2.56585 4.20924 2.5 4.375 2.5H11.875L16.25 6.875V16.875C16.25 17.0408 16.1842 17.1997 16.0669 17.3169C15.9497 17.4342 15.7908 17.5 15.625 17.5Z"
        stroke="#474747"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13.75 9.375H6.25V15H13.75V9.375Z"
        stroke="#474747"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_292_1900">
        <rect width="20" height="20" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const ChangeSubscription = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_6542_40446)">
      <path
        d="M6.25 12.5H16.25C16.4158 12.5 16.5747 12.4342 16.6919 12.3169C16.8092 12.1997 16.875 12.0408 16.875 11.875V3.75C16.875 3.58424 16.8092 3.42527 16.6919 3.30806C16.5747 3.19085 16.4158 3.125 16.25 3.125H7.5C7.33424 3.125 7.17527 3.19085 7.05806 3.30806C6.94085 3.42527 6.875 3.58424 6.875 3.75V4.375"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M8.125 10.625L6.25 12.5L8.125 14.375"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M13.75 7.5H3.75C3.58424 7.5 3.42527 7.56585 3.30806 7.68306C3.19085 7.80027 3.125 7.95924 3.125 8.125V16.25C3.125 16.4158 3.19085 16.5747 3.30806 16.6919C3.42527 16.8092 3.58424 16.875 3.75 16.875H12.5C12.6658 16.875 12.8247 16.8092 12.9419 16.6919C13.0592 16.5747 13.125 16.4158 13.125 16.25V15.625"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M11.875 9.375L13.75 7.5L11.875 5.625"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_6542_40446">
        <rect width="20" height="20" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Receipt = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 21 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_292_4401)">
      <path
        d="M6.91602 8.125H14.416"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.91602 10.625H14.416"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M3.16602 16.25V4.375C3.16602 4.20924 3.23186 4.05027 3.34907 3.93306C3.46628 3.81585 3.62526 3.75 3.79102 3.75H17.541C17.7068 3.75 17.8657 3.81585 17.983 3.93306C18.1002 4.05027 18.166 4.20924 18.166 4.375V16.25L15.666 15L13.166 16.25L10.666 15L8.16602 16.25L5.66602 15L3.16602 16.25Z"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_292_4401">
        <rect
          width="20"
          height="20"
          fill="white"
          transform="translate(0.666016)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const ArrowsLeftRight = () => (
  <svg
    width="21"
    height="20"
    viewBox="0 0 21 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_292_1940)">
      <path
        d="M14.084 11.25L16.584 13.75L14.084 16.25"
        stroke="#474747"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M4.08398 13.75H16.584"
        stroke="#474747"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.58398 8.75L4.08398 6.25L6.58398 3.75"
        stroke="#474747"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16.584 6.25H4.08398"
        stroke="#474747"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_292_1940">
        <rect
          width="20"
          height="20"
          fill="white"
          transform="translate(0.333984)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const X = (props: any) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clip-path="url(#clip0_6283_24398)">
      <path
        d="M12.5 3.5L3.5 12.5"
        stroke="currentColor"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M12.5 12.5L3.5 3.5"
        stroke="currentColor"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_6283_24398">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const MasterCard = () => (
  <svg
    width="26"
    height="17"
    viewBox="0 0 26 17"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M16.5149 1.86255H9.48438V14.5974H16.5149V1.86255Z"
      fill="#FF5F00"
    />
    <path
      d="M9.93135 8.22986C9.93024 7.0034 10.206 5.79276 10.7376 4.68959C11.2693 3.58642 12.043 2.61964 13.0002 1.86243C11.8149 0.923313 10.3914 0.339291 8.89233 0.177115C7.3933 0.0149386 5.87925 0.281146 4.52322 0.945322C3.16719 1.6095 2.0239 2.64484 1.22404 3.93301C0.424169 5.22117 0 6.71019 0 8.22986C0 9.74953 0.424169 11.2385 1.22404 12.5267C2.0239 13.8149 3.16719 14.8502 4.52322 15.5144C5.87925 16.1786 7.3933 16.4448 8.89233 16.2826C10.3914 16.1204 11.8149 15.5364 13.0002 14.5973C12.043 13.8401 11.2693 12.8733 10.7377 11.7701C10.206 10.667 9.93025 9.45632 9.93135 8.22986Z"
      fill="#EB001B"
    />
    <path
      d="M25.9998 8.22986C25.9999 9.74951 25.5757 11.2385 24.7759 12.5267C23.9761 13.8148 22.8328 14.8502 21.4768 15.5144C20.1208 16.1786 18.6068 16.4448 17.1078 16.2826C15.6088 16.1204 14.1853 15.5364 13 14.5973C13.9563 13.8393 14.7294 12.8724 15.261 11.7694C15.7926 10.6664 16.0688 9.45612 16.0688 8.22986C16.0688 7.00359 15.7926 5.79333 15.261 4.69034C14.7294 3.58734 13.9563 2.6204 13 1.86243C14.1853 0.92331 15.6088 0.339287 17.1078 0.177113C18.6068 0.0149386 20.1208 0.281157 21.4768 0.94534C22.8328 1.60952 23.9761 2.64487 24.7759 3.93303C25.5757 5.2212 25.9999 6.71021 25.9998 8.22986Z"
      fill="#F79E1B"
    />
    <path
      d="M25.2336 13.2484V12.9877H25.3379V12.9346H25.0723V12.9877H25.1766V13.2484H25.2336ZM25.7493 13.2484V12.9341H25.6679L25.5742 13.1503L25.4805 12.9341H25.3991V13.2484H25.4566V13.0113L25.5444 13.2158H25.604L25.6918 13.0108V13.2484H25.7493Z"
      fill="#F79E1B"
    />
  </svg>
);

export const Visa = () => (
  <svg
    width="38"
    height="22"
    viewBox="0 0 38 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M15.3831 6.42358L13.5586 15.8193H16.4766L18.3026 6.42358H15.3831Z"
      fill="#1431C2"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M11.0181 6.42556L8.1521 12.8422L7.84654 11.8733C7.28133 10.7059 5.67676 9.0294 3.79297 7.97283L6.41357 15.8193L9.50982 15.8147L14.1181 6.42358L11.0181 6.42556Z"
      fill="#1431C2"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M6.73265 7.19176C6.566 6.61579 6.08304 6.44413 5.48357 6.42358H1.03882L1.00195 6.61182C4.46087 7.36608 6.7496 9.18346 7.69931 11.3687L6.73265 7.19176Z"
      fill="#1431C2"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M24.1475 8.17782C25.0906 8.16439 25.7743 8.35111 26.3052 8.54455L26.5654 8.65537L26.9554 6.57859C26.3845 6.38448 25.4896 6.17627 24.3729 6.17627C21.5242 6.17627 19.5163 7.47593 19.5007 9.33845C19.4821 10.7147 20.931 11.4831 22.0254 11.9418C23.1487 12.412 23.5254 12.7109 23.5202 13.1307C23.5113 13.7721 22.6245 14.0663 21.7963 14.0663C20.6418 14.0663 20.0286 13.9219 19.0818 13.5645L18.7103 13.4114L18.3047 15.556C18.9794 15.824 20.2244 16.0544 21.5167 16.0665C24.5471 16.0665 26.5165 14.7823 26.5373 12.7921C26.5499 11.7034 25.781 10.8725 24.1149 10.1908C23.1065 9.74615 22.4896 9.45062 22.4955 9.00195C22.4955 8.60365 23.019 8.17782 24.1475 8.17782Z"
      fill="#1431C2"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M32.329 6.42358H34.6109L37.0006 15.8193H34.2607C34.2607 15.8193 33.9888 14.7392 33.9007 14.4111C33.6735 14.4111 32.7225 14.4099 31.8272 14.4089C31.0308 14.408 30.2784 14.4071 30.1186 14.4071C30.0039 14.6608 29.498 15.8193 29.498 15.8193H26.3965L30.7817 7.20382C31.0932 6.59097 31.6203 6.42358 32.329 6.42358ZM32.1455 9.85547C32.1455 9.85547 31.2119 11.9431 30.9688 12.4828H33.4224L32.738 9.76848L32.5389 8.95794C32.4643 9.13104 32.3647 9.3557 32.2843 9.53705C32.2003 9.72644 32.1373 9.86859 32.1455 9.85547Z"
      fill="#1431C2"
    />
  </svg>
);

export const MapPin = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_310_10132)">
      <path
        d="M12 12.75C13.6569 12.75 15 11.4069 15 9.75C15 8.09315 13.6569 6.75 12 6.75C10.3431 6.75 9 8.09315 9 9.75C9 11.4069 10.3431 12.75 12 12.75Z"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M19.5 9.75C19.5 16.5 12 21.75 12 21.75C12 21.75 4.5 16.5 4.5 9.75C4.5 7.76088 5.29018 5.85322 6.6967 4.4467C8.10322 3.04018 10.0109 2.25 12 2.25C13.9891 2.25 15.8968 3.04018 17.3033 4.4467C18.7098 5.85322 19.5 7.76088 19.5 9.75Z"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_310_10132">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Envelope = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_5811_36796)">
      <path
        d="M3 9V18.75C3 18.9489 3.07902 19.1397 3.21967 19.2803C3.36032 19.421 3.55109 19.5 3.75 19.5H20.25C20.4489 19.5 20.6397 19.421 20.7803 19.2803C20.921 19.1397 21 18.9489 21 18.75V9L12 3L3 9Z"
        stroke="currentColor"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M21 9L13.6369 14.25H10.3641L3 9"
        stroke="currentColor"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_5811_36796">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const QrCode = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_6119_36041)">
      <path
        d="M9.75 4.5H5.25C4.83579 4.5 4.5 4.83579 4.5 5.25V9.75C4.5 10.1642 4.83579 10.5 5.25 10.5H9.75C10.1642 10.5 10.5 10.1642 10.5 9.75V5.25C10.5 4.83579 10.1642 4.5 9.75 4.5Z"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9.75 13.5H5.25C4.83579 13.5 4.5 13.8358 4.5 14.25V18.75C4.5 19.1642 4.83579 19.5 5.25 19.5H9.75C10.1642 19.5 10.5 19.1642 10.5 18.75V14.25C10.5 13.8358 10.1642 13.5 9.75 13.5Z"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M18.75 4.5H14.25C13.8358 4.5 13.5 4.83579 13.5 5.25V9.75C13.5 10.1642 13.8358 10.5 14.25 10.5H18.75C19.1642 10.5 19.5 10.1642 19.5 9.75V5.25C19.5 4.83579 19.1642 4.5 18.75 4.5Z"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13.5 13.5V16.5"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13.5 19.5H16.5V13.5"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16.5 15H19.5"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M19.5 18V19.5"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_6119_36041">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const ChainLink = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_6119_7411)">
      <path
        d="M12.3642 18L11.4323 18.9318C10.5861 19.7644 9.44516 20.2288 8.25804 20.224C7.07093 20.2192 5.93381 19.7455 5.09439 18.906C4.25496 18.0666 3.78124 16.9295 3.7764 15.7424C3.77157 14.5553 4.23602 13.4143 5.06858 12.5681L7.32889 10.3125C8.13966 9.50005 9.23021 9.02814 10.3774 8.99329C11.5247 8.95843 12.6419 9.36327 13.5005 10.125"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11.6363 5.99997L12.5681 5.06809C13.4144 4.23553 14.5553 3.77108 15.7424 3.77592C16.9295 3.78075 18.0666 4.25447 18.9061 5.0939C19.7455 5.93332 20.2192 7.07044 20.224 8.25756C20.2289 9.44467 19.7644 10.5856 18.9319 11.4318L16.6716 13.6922C15.8602 14.5039 14.7693 14.975 13.6221 15.0089C12.4749 15.0429 11.358 14.6373 10.5 13.875"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_6119_7411">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Door = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_6119_33905)">
      <path
        d="M2.25 21H21.75"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M5.25 21V3.75C5.25 3.55109 5.32902 3.36032 5.46967 3.21967C5.61032 3.07902 5.80109 3 6 3H18C18.1989 3 18.3897 3.07902 18.5303 3.21967C18.671 3.36032 18.75 3.55109 18.75 3.75V21"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M15 3C15.1989 3 15.3897 3.07902 15.5303 3.21967C15.671 3.36032 15.75 3.55109 15.75 3.75V21"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.375 13.5C12.9963 13.5 13.5 12.9963 13.5 12.375C13.5 11.7537 12.9963 11.25 12.375 11.25C11.7537 11.25 11.25 11.7537 11.25 12.375C11.25 12.9963 11.7537 13.5 12.375 13.5Z"
        fill="currentColor"
      />
    </g>
    <defs>
      <clipPath id="clip0_6119_33905">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Phone = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_8258_7581)">
      <path
        d="M15 3.75C16.2614 4.08218 17.4121 4.7432 18.3344 5.66557C19.2568 6.58793 19.9178 7.73858 20.25 9"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M14.25 6.75C15.7988 7.16438 16.8356 8.20125 17.25 9.75"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M15.4116 13.6256C15.5154 13.5565 15.6349 13.5144 15.7591 13.5031C15.8834 13.4918 16.0085 13.5117 16.1231 13.5609L20.5444 15.5419C20.6934 15.6055 20.8177 15.7158 20.8989 15.856C20.98 15.9963 21.0135 16.1591 20.9944 16.32C20.8487 17.4085 20.3127 18.407 19.486 19.1299C18.6593 19.8528 17.5982 20.2508 16.5 20.25C13.1185 20.25 9.87548 18.9067 7.48439 16.5156C5.0933 14.1245 3.75 10.8815 3.75 7.49998C3.74916 6.4018 4.1472 5.34068 4.87009 4.51398C5.59298 3.68728 6.59152 3.15126 7.68 3.0056C7.84091 2.98649 8.00368 3.02 8.14395 3.10112C8.28422 3.18224 8.39444 3.30661 8.45813 3.4556L10.4391 7.8806C10.4877 7.99426 10.5076 8.11818 10.4968 8.24134C10.486 8.36451 10.4449 8.48309 10.3772 8.58654L8.37375 10.9687C8.30269 11.076 8.26066 11.1998 8.25179 11.3281C8.24291 11.4565 8.26749 11.5849 8.32313 11.7009C9.09844 13.2881 10.7391 14.909 12.3309 15.6769C12.4475 15.7322 12.5766 15.7563 12.7053 15.7466C12.834 15.7369 12.958 15.6938 13.065 15.6215L15.4116 13.6256Z"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_8258_7581">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Sparkle = () => (
  <svg
    width="25"
    height="24"
    viewBox="0 0 25 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_736_6850)">
      <path
        d="M8.07851 15.9816L3.23539 14.2004C3.09298 14.1478 2.9701 14.0529 2.88332 13.9283C2.79653 13.8038 2.75 13.6556 2.75 13.5038C2.75 13.352 2.79653 13.2039 2.88332 13.0793C2.9701 12.9548 3.09298 12.8598 3.23539 12.8073L8.07851 11.026C8.17935 10.9891 8.27093 10.9306 8.34687 10.8547C8.42281 10.7787 8.48127 10.6872 8.5182 10.5863L10.2994 5.7432C10.352 5.60079 10.447 5.47791 10.5715 5.39113C10.6961 5.30434 10.8442 5.25781 10.996 5.25781C11.1478 5.25781 11.296 5.30434 11.4205 5.39113C11.5451 5.47791 11.64 5.60079 11.6926 5.7432L13.4738 10.5863C13.5108 10.6872 13.5692 10.7787 13.6452 10.8547C13.7211 10.9306 13.8127 10.9891 13.9135 11.026L18.7566 12.8073C18.899 12.8598 19.0219 12.9548 19.1087 13.0793C19.1955 13.2039 19.242 13.352 19.242 13.5038C19.242 13.6556 19.1955 13.8038 19.1087 13.9283C19.0219 14.0529 18.899 14.1478 18.7566 14.2004L13.9135 15.9816C13.8127 16.0186 13.7211 16.077 13.6452 16.153C13.5692 16.2289 13.5108 16.3205 13.4738 16.4213L11.6926 21.2645C11.64 21.4069 11.5451 21.5297 11.4205 21.6165C11.296 21.7033 11.1478 21.7498 10.996 21.7498C10.8442 21.7498 10.6961 21.7033 10.5715 21.6165C10.447 21.5297 10.352 21.4069 10.2994 21.2645L8.5182 16.4213C8.48127 16.3205 8.42281 16.2289 8.34687 16.153C8.27093 16.077 8.17935 16.0186 8.07851 15.9816Z"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M17 1.5V6"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M21.5 6.75V9.75"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M14.75 3.75H19.25"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M20 8.25H23"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_736_6850">
        <rect width="24" height="24" fill="white" transform="translate(0.5)" />
      </clipPath>
    </defs>
  </svg>
);

export const MagicWand = () => (
  <svg
    width="25"
    height="24"
    viewBox="0 0 25 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_736_3152)">
      <path
        d="M20.75 12V16.5"
        stroke="#FDFCFB"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M18.5 14.25H23"
        stroke="#FDFCFB"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8 3.75V8.25"
        stroke="#FDFCFB"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M5.75 6H10.25"
        stroke="#FDFCFB"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16.25 17.25V20.25"
        stroke="#FDFCFB"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M14.75 18.75H17.75"
        stroke="#FDFCFB"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M14 7.5L17 10.5"
        stroke="#FDFCFB"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M17.5305 3.96951L4.46978 17.0302C4.17689 17.3231 4.17689 17.798 4.46978 18.0909L6.4088 20.0299C6.70169 20.3228 7.17657 20.3228 7.46946 20.0299L20.5302 6.96919C20.8231 6.6763 20.8231 6.20143 20.5302 5.90853L18.5911 3.96951C18.2983 3.67662 17.8234 3.67662 17.5305 3.96951Z"
        stroke="#FDFCFB"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_736_3152">
        <rect width="24" height="24" fill="white" transform="translate(0.5)" />
      </clipPath>
    </defs>
  </svg>
);

export const CheckCircle = () => (
  <svg
    width="48"
    height="48"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect width="48" height="48" rx="24" fill="#EBF9EE" />
    <g clip-path="url(#clip0_5972_165597)">
      <path
        d="M20.25 24.75L22.5 27L27.75 21.75"
        stroke="#1A5625"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M24 33C28.9706 33 33 28.9706 33 24C33 19.0294 28.9706 15 24 15C19.0294 15 15 19.0294 15 24C15 28.9706 19.0294 33 24 33Z"
        stroke="#1A5625"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_5972_165597">
        <rect
          width="24"
          height="24"
          fill="white"
          transform="translate(12 12)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const Info = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_435_14716)">
      <path
        d="M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21Z"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11.25 11.25C11.4489 11.25 11.6397 11.329 11.7803 11.4697C11.921 11.6103 12 11.8011 12 12V15.75C12 15.9489 12.079 16.1397 12.2197 16.2803C12.3603 16.421 12.5511 16.5 12.75 16.5"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11.625 9C12.2463 9 12.75 8.49632 12.75 7.875C12.75 7.25368 12.2463 6.75 11.625 6.75C11.0037 6.75 10.5 7.25368 10.5 7.875C10.5 8.49632 11.0037 9 11.625 9Z"
        fill="currentColor"
      />
    </g>
    <defs>
      <clipPath id="clip0_435_14716">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Hash = (props: any) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clip-path="url(#clip0_5745_26726)">
      <path
        d="M4.5 9H21"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16.5 3.75L13.5 20.25"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10.5 3.75L7.5 20.25"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M3 15H19.5"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_5745_26726">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Reporting = () => (
  <svg
    width="24"
    height="25"
    viewBox="0 0 24 25"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M15.75 21.5H5.25C5.05109 21.5 4.86032 21.421 4.71967 21.2803C4.57902 21.1397 4.5 20.9489 4.5 20.75V7.25C4.5 7.05109 4.57902 6.86032 4.71967 6.71967C4.86032 6.57902 5.05109 6.5 5.25 6.5H12.75L16.5 10.25V20.75C16.5 20.9489 16.421 21.1397 16.2803 21.2803C16.1397 21.421 15.9489 21.5 15.75 21.5Z"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M7.5 6.5V4.25C7.5 4.05109 7.57902 3.86032 7.71967 3.71967C7.86032 3.57902 8.05109 3.5 8.25 3.5H15.75L19.5 7.25V17.75C19.5 17.9489 19.421 18.1397 19.2803 18.2803C19.1397 18.421 18.9489 18.5 18.75 18.5H16.5"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M8.25 14.75H12.75"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M8.25 17.75H12.75"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Settings = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    stroke="#1857C3"
  >
    <g clip-path="url(#clip0_8232_56782)">
      <path
        d="M8 10.5C9.38071 10.5 10.5 9.38071 10.5 8C10.5 6.61929 9.38071 5.5 8 5.5C6.61929 5.5 5.5 6.61929 5.5 8C5.5 9.38071 6.61929 10.5 8 10.5Z"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M2.59031 11.1306C2.31405 10.6547 2.10239 10.1442 1.96094 9.61246L3.00969 8.29996C2.99781 8.0993 2.99781 7.89812 3.00969 7.69746L1.96156 6.38496C2.10277 5.85313 2.314 5.34242 2.58969 4.86621L4.25906 4.67871C4.39237 4.52852 4.5345 4.38639 4.68469 4.25309L4.87219 2.58434C5.34771 2.30996 5.85759 2.09999 6.38844 1.95996L7.70094 3.00871C7.9016 2.99684 8.10278 2.99684 8.30344 3.00871L9.61594 1.96059C10.1478 2.1018 10.6585 2.31302 11.1347 2.58871L11.3222 4.25809C11.4724 4.39139 11.6145 4.53352 11.7478 4.68371L13.4166 4.87121C13.6928 5.34706 13.9045 5.85759 14.0459 6.38934L12.9972 7.70184C13.0091 7.90249 13.0091 8.10368 12.9972 8.30434L14.0453 9.61684C13.9051 10.1485 13.6949 10.6592 13.4203 11.1356L11.7509 11.3231C11.6176 11.4733 11.4755 11.6154 11.3253 11.7487L11.1378 13.4175C10.662 13.6937 10.1514 13.9054 9.61969 14.0468L8.30719 12.9981C8.10653 13.01 7.90535 13.01 7.70469 12.9981L6.39219 14.0462C5.86052 13.906 5.34981 13.6958 4.87344 13.4212L4.68594 11.7518C4.53575 11.6185 4.39362 11.4764 4.26031 11.3262L2.59031 11.1306Z"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_8232_56782">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const WarningCross = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_5774_88432)">
      <path
        d="M15.4172 3H8.58281C8.48429 2.99992 8.38672 3.01926 8.29568 3.0569C8.20463 3.09454 8.12189 3.14975 8.05219 3.21938L3.21938 8.05219C3.14975 8.12189 3.09454 8.20463 3.0569 8.29568C3.01926 8.38672 2.99992 8.48429 3 8.58281V15.4172C2.99992 15.5157 3.01926 15.6133 3.0569 15.7043C3.09454 15.7954 3.14975 15.8781 3.21938 15.9478L8.05219 20.7806C8.12189 20.8503 8.20463 20.9055 8.29568 20.9431C8.38672 20.9807 8.48429 21.0001 8.58281 21H15.4172C15.5157 21.0001 15.6133 20.9807 15.7043 20.9431C15.7954 20.9055 15.8781 20.8503 15.9478 20.7806L20.7806 15.9478C20.8503 15.8781 20.9055 15.7954 20.9431 15.7043C20.9807 15.6133 21.0001 15.5157 21 15.4172V8.58281C21.0001 8.48429 20.9807 8.38672 20.9431 8.29568C20.9055 8.20463 20.8503 8.12189 20.7806 8.05219L15.9478 3.21938C15.8781 3.14975 15.7954 3.09454 15.7043 3.0569C15.6133 3.01926 15.5157 2.99992 15.4172 3Z"
        stroke="#F04747"
        stroke-width="1.5"
        stroke-miterlimit="10"
      />
      <path
        d="M15 9L9 15"
        stroke="#F04747"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M9 9L15 15"
        stroke="#F04747"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_5774_88432">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const LogIn = ({ color = "currentColor" }: any) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
  >
    <g clip-path="url(#clip0_8238_25190)">
      <path
        d="M2.25 12H12.75"
        stroke="#1A1A1A"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9 8.25L12.75 12L9 15.75"
        stroke="#1A1A1A"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.75 3.75H18C18.1989 3.75 18.3897 3.82902 18.5303 3.96967C18.671 4.11032 18.75 4.30109 18.75 4.5V19.5C18.75 19.6989 18.671 19.8897 18.5303 20.0303C18.3897 20.171 18.1989 20.25 18 20.25H12.75"
        stroke="#1A1A1A"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_8238_25190">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const SmallX = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_9856_30337)">
      <path
        d="M12.5 3.5L3.5 12.5"
        stroke="#FDFCFB"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.5 12.5L3.5 3.5"
        stroke="#FDFCFB"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_9856_30337">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const PaperPlane = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_4617_70716)">
      <path
        d="M6.70555 11.7365C6.76464 11.8994 6.76464 12.078 6.70555 12.2409L3.7993 20.745C3.74734 20.8885 3.74044 21.0445 3.7795 21.1921C3.81857 21.3397 3.90175 21.4718 4.01793 21.5709C4.1341 21.67 4.27774 21.7312 4.42966 21.7464C4.58157 21.7617 4.73451 21.7302 4.86805 21.6562L20.618 12.6478C20.7353 12.5829 20.833 12.4878 20.901 12.3724C20.9691 12.257 21.005 12.1255 21.005 11.9915C21.005 11.8575 20.9691 11.726 20.901 11.6106C20.833 11.4952 20.7353 11.4001 20.618 11.3353L4.86805 2.34933C4.73484 2.27483 4.58207 2.24274 4.43014 2.25735C4.27821 2.27197 4.13435 2.33259 4.01779 2.43112C3.90122 2.52965 3.81749 2.6614 3.77778 2.80878C3.73807 2.95615 3.74427 3.11213 3.79555 3.25589L6.70555 11.7365Z"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.75 12H6.75"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_4617_70716">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Bell = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_7247_96362)">
      <path
        d="M6 14H10"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M3.5009 6.5C3.5009 5.30653 3.975 4.16193 4.81892 3.31802C5.66283 2.47411 6.80742 2 8.0009 2C9.19437 2 10.339 2.47411 11.1829 3.31802C12.0268 4.16193 12.5009 5.30653 12.5009 6.5C12.5009 8.73875 13.0196 10.5375 13.4321 11.25C13.476 11.3259 13.4991 11.4119 13.4991 11.4996C13.4992 11.5872 13.4763 11.6733 13.4326 11.7492C13.3889 11.8252 13.326 11.8883 13.2503 11.9324C13.1745 11.9764 13.0885 11.9997 13.0009 12H3.0009C2.91339 11.9995 2.82754 11.976 2.75195 11.9319C2.67636 11.8878 2.61367 11.8246 2.57014 11.7487C2.52661 11.6728 2.50377 11.5868 2.50391 11.4992C2.50404 11.4117 2.52714 11.3258 2.5709 11.25C2.98277 10.5375 3.5009 8.73813 3.5009 6.5Z"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_7247_96362">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const AToZ = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M6.74893 7.81168H4.41826L3.98893 9.00001H3.25293L5.18493 3.68701H5.98993L7.91426 9.00001H7.17826L6.74893 7.81168ZM6.5496 7.24434L5.5836 4.54568L4.6176 7.24434H6.5496Z"
      fill="currentColor"
    />
    <path
      d="M4.68054 19.3942H7.28721V19.9999H3.85254V19.4479L6.44387 15.2619H3.88321V14.6562H7.27187V15.2083L4.68054 19.3942Z"
      fill="currentColor"
    />
    <g clip-path="url(#clip0_6460_25602)">
      <path
        d="M16 4L16 19"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.625 15.75L16 19.125L19.375 15.75"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_6460_25602">
        <rect width="12" height="18" fill="white" transform="translate(10 3)" />
      </clipPath>
    </defs>
  </svg>
);

export const ZToA = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M4.68054 8.39425H7.28721V8.99992H3.85254V8.44792L6.44387 4.26192H3.88321V3.65625H7.27187V4.20825L4.68054 8.39425Z"
      fill="currentColor"
    />
    <path
      d="M6.74893 18.8117H4.41826L3.98893 20H3.25293L5.18493 14.687H5.98993L7.91426 20H7.17826L6.74893 18.8117ZM6.5496 18.2443L5.5836 15.5457L4.6176 18.2443H6.5496Z"
      fill="currentColor"
    />
    <g clip-path="url(#clip0_6460_25617)">
      <path
        d="M16 4L16 19"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.625 15.75L16 19.125L19.375 15.75"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_6460_25617">
        <rect width="12" height="18" fill="white" transform="translate(10 3)" />
      </clipPath>
    </defs>
  </svg>
);

export const Signal = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_6160_4854)">
      <path
        d="M15 6.75V18.75"
        stroke="#061632"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M18.75 3V18.75"
        stroke="#061632"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11.25 10.5V18.75"
        stroke="#061632"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7.5 14.25V18.75"
        stroke="#061632"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M3.75 18V18.75"
        stroke="#061632"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_6160_4854">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const SMS = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12.3749 20.25H4.47182C4.37667 20.2513 4.28223 20.2334 4.19408 20.1976C4.10593 20.1618 4.02585 20.1087 3.95856 20.0414C3.89128 19.9741 3.83815 19.894 3.80232 19.8059C3.76649 19.7177 3.74868 19.6233 3.74994 19.5281V11.625C3.74994 9.33751 4.65864 7.14371 6.27614 5.5262C7.89365 3.9087 10.0874 3 12.3749 3V3C13.5076 3 14.6292 3.22309 15.6756 3.65654C16.722 4.08999 17.6728 4.7253 18.4737 5.5262C19.2746 6.32711 19.91 7.27792 20.3434 8.32436C20.7768 9.37079 20.9999 10.4923 20.9999 11.625V11.625C20.9999 12.7577 20.7768 13.8792 20.3434 14.9256C19.91 15.9721 19.2746 16.9229 18.4737 17.7238C17.6728 18.5247 16.722 19.16 15.6756 19.5935C14.6292 20.0269 13.5076 20.25 12.3749 20.25V20.25Z"
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9.375 10.5H15"
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9.375 13.5H15"
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Gift = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M20.25 7.5H3.75C3.33579 7.5 3 7.83579 3 8.25V11.25C3 11.6642 3.33579 12 3.75 12H20.25C20.6642 12 21 11.6642 21 11.25V8.25C21 7.83579 20.6642 7.5 20.25 7.5Z"
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M19.5 12V18.75C19.5 18.9489 19.421 19.1397 19.2803 19.2803C19.1397 19.421 18.9489 19.5 18.75 19.5H5.25C5.05109 19.5 4.86032 19.421 4.71967 19.2803C4.57902 19.1397 4.5 18.9489 4.5 18.75V12"
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12 7.5V19.5"
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M16.2469 6.44091C15.1781 7.50029 12 7.50029 12 7.50029C12 7.50029 12 4.32216 13.0594 3.25341C13.4821 2.83073 14.0554 2.59326 14.6531 2.59326C15.2509 2.59326 15.8242 2.83073 16.2469 3.25341C16.6696 3.6761 16.907 4.24939 16.907 4.84716C16.907 5.44494 16.6696 6.01823 16.2469 6.44091V6.44091Z"
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M7.75293 6.44091C8.82168 7.50029 11.9998 7.50029 11.9998 7.50029C11.9998 7.50029 11.9998 4.32216 10.9404 3.25341C10.5177 2.83073 9.94445 2.59326 9.34668 2.59326C8.7489 2.59326 8.17561 2.83073 7.75293 3.25341C7.33024 3.6761 7.09277 4.24939 7.09277 4.84716C7.09277 5.44494 7.33024 6.01823 7.75293 6.44091V6.44091Z"
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Funnel = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_6734_27033)">
      <path
        d="M3.19493 5.75438C3.09734 5.64697 3.033 5.51357 3.00972 5.37033C2.98643 5.22709 3.00521 5.08018 3.06376 4.94739C3.12231 4.81461 3.21812 4.70167 3.33958 4.62226C3.46105 4.54284 3.60294 4.50038 3.74805 4.5H20.2481C20.3933 4.50001 20.5355 4.54222 20.6572 4.62149C20.779 4.70076 20.8751 4.81368 20.9338 4.94654C20.9926 5.07939 21.0116 5.22646 20.9883 5.36987C20.9651 5.51328 20.9008 5.64686 20.8031 5.75438L14.4524 12.5334C14.3223 12.6725 14.2499 12.8558 14.2499 13.0463V18.2484C14.25 18.3719 14.2196 18.4936 14.1614 18.6025C14.1031 18.7114 14.0189 18.8043 13.9162 18.8728L10.9162 20.8725C10.8033 20.9483 10.672 20.992 10.5362 20.9989C10.4004 21.0058 10.2653 20.9757 10.1453 20.9118C10.0254 20.8478 9.92501 20.7525 9.85503 20.6359C9.78506 20.5193 9.74808 20.386 9.74805 20.25V13.0463C9.74811 12.8558 9.67572 12.6725 9.54555 12.5334L3.19493 5.75438Z"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_6734_27033">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Note = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
  >
    <g clip-path="url(#clip0_8031_25180)">
      <path
        d="M14.6897 20.25H4.5C4.30109 20.25 4.11032 20.171 3.96967 20.0303C3.82902 19.8897 3.75 19.6989 3.75 19.5V4.5C3.75 4.30109 3.82902 4.11032 3.96967 3.96967C4.11032 3.82902 4.30109 3.75 4.5 3.75H19.5C19.6989 3.75 19.8897 3.82902 20.0303 3.96967C20.171 4.11032 20.25 4.30109 20.25 4.5V14.6897C20.2499 14.8883 20.171 15.0788 20.0306 15.2194L15.2194 20.0306C15.0788 20.171 14.8883 20.2499 14.6897 20.25Z"
        stroke="#1A1A1A"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M20.1825 14.9991H15V20.1816"
        stroke="#1A1A1A"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_8031_25180">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);
export const HandCoins = (props: any) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    {...props}
  >
    <g clip-path="url(#clip0_8173_14441)">
      <path
        d="M19.125 10.5C20.5747 10.5 21.75 9.32475 21.75 7.875C21.75 6.42525 20.5747 5.25 19.125 5.25C17.6753 5.25 16.5 6.42525 16.5 7.875C16.5 9.32475 17.6753 10.5 19.125 10.5Z"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M4.5 19.5H1.5C1.30109 19.5 1.11032 19.421 0.96967 19.2803C0.829018 19.1397 0.75 18.9489 0.75 18.75V15C0.75 14.8011 0.829018 14.6103 0.96967 14.4697C1.11032 14.329 1.30109 14.25 1.5 14.25H4.5"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10.5 15H13.5L19.7812 13.5553C20.0122 13.492 20.2546 13.4829 20.4897 13.5285C20.7247 13.5741 20.9461 13.6734 21.1365 13.8185C21.327 13.9636 21.4814 14.1507 21.5878 14.3652C21.6942 14.5797 21.7497 14.8159 21.75 15.0553C21.7501 15.3444 21.6697 15.6279 21.5176 15.8738C21.3656 16.1197 21.148 16.3184 20.8894 16.4475L17.25 18L11.25 19.5H4.5V14.25L6.84375 11.9063C7.05324 11.6975 7.30182 11.5321 7.5753 11.4195C7.84877 11.3069 8.14175 11.2493 8.4375 11.25H13.125C13.6223 11.25 14.0992 11.4476 14.4508 11.7992C14.8025 12.1508 15 12.6277 15 13.125C15 13.6223 14.8025 14.0992 14.4508 14.4508C14.0992 14.8025 13.6223 15 13.125 15H10.5Z"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16.5002 7.99603C15.9908 8.23699 15.4173 8.30758 14.8646 8.19736C14.3119 8.08715 13.8093 7.80198 13.4313 7.38404C13.0532 6.96609 12.8197 6.43756 12.7652 5.87662C12.7108 5.31568 12.8383 4.75211 13.129 4.26926C13.4196 3.78642 13.8579 3.40993 14.3791 3.19549C14.9003 2.98105 15.4767 2.94005 16.023 3.07854C16.5693 3.21703 17.0565 3.52766 17.4126 3.96449C17.7687 4.40132 17.9747 4.94116 18.0002 5.50416"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_8173_14441">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);
export const Chat = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_6160_4870)">
      <path
        d="M7.49356 19.7917C9.38406 20.8858 11.608 21.2551 13.7506 20.8307C15.8933 20.4062 17.8084 19.217 19.1391 17.4848C20.4697 15.7526 21.125 13.5956 20.9827 11.4159C20.8404 9.2363 19.9103 7.18277 18.3657 5.63825C16.8212 4.09372 14.7677 3.16361 12.588 3.02132C10.4084 2.87904 8.25138 3.53429 6.51916 4.86492C4.78695 6.19555 3.59777 8.11072 3.17333 10.2534C2.74889 12.396 3.11817 14.6199 4.21231 16.5104L3.0395 20.012C2.99543 20.1441 2.98903 20.286 3.02103 20.4215C3.05302 20.5571 3.12215 20.6811 3.22065 20.7796C3.31915 20.8781 3.44314 20.9472 3.57871 20.9792C3.71429 21.0112 3.8561 21.0048 3.98825 20.9607L7.49356 19.7917Z"
        stroke="#061632"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_6160_4870">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const DotsThree = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
  >
    <g clip-path="url(#clip0_8380_17313)">
      <path
        d="M12 14.25C13.2426 14.25 14.25 13.2426 14.25 12C14.25 10.7574 13.2426 9.75 12 9.75C10.7574 9.75 9.75 10.7574 9.75 12C9.75 13.2426 10.7574 14.25 12 14.25Z"
        fill="currentColor"
      />
      <path
        d="M4.5 14.25C5.74264 14.25 6.75 13.2426 6.75 12C6.75 10.7574 5.74264 9.75 4.5 9.75C3.25736 9.75 2.25 10.7574 2.25 12C2.25 13.2426 3.25736 14.25 4.5 14.25Z"
        fill="currentColor"
      />
      <path
        d="M19.5 14.25C20.7426 14.25 21.75 13.2426 21.75 12C21.75 10.7574 20.7426 9.75 19.5 9.75C18.2574 9.75 17.25 10.7574 17.25 12C17.25 13.2426 18.2574 14.25 19.5 14.25Z"
        fill="currentColor"
      />
    </g>
    <defs>
      <clipPath id="clip0_8380_17313">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const CloudArrowUp = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="25"
    height="24"
    viewBox="0 0 25 24"
    fill="none"
  >
    <path
      d="M20.25 5.75H3.75"
      stroke="#160B2A"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9.75 10.25V16.25"
      stroke="#160B2A"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M14.25 10.25V16.25"
      stroke="#160B2A"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M18.75 5.75V20C18.75 20.1989 18.671 20.3897 18.5303 20.5303C18.3897 20.671 18.1989 20.75 18 20.75H6C5.80109 20.75 5.61032 20.671 5.46967 20.5303C5.32902 20.3897 5.25 20.1989 5.25 20V5.75"
      stroke="#160B2A"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M15.75 5.75V4.25C15.75 3.85218 15.592 3.47064 15.3107 3.18934C15.0294 2.90804 14.6478 2.75 14.25 2.75H9.75C9.35218 2.75 8.97064 2.90804 8.68934 3.18934C8.40804 3.47064 8.25 3.85218 8.25 4.25V5.75"
      stroke="#160B2A"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const CaretRightSmall = () => (
  <svg
    width="12"
    height="12"
    viewBox="0 0 12 12"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M4.5 2.25L8.25 6L4.5 9.75"
      stroke="#1A1A1A"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const CaretLeftSmall = () => (
  <svg
    width="12"
    height="12"
    viewBox="0 0 12 12"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M7.5 9.75L3.75 6L7.5 2.25"
      stroke="#1A1A1A"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const ArrowUp = () => (
  <svg
    width="21"
    height="20"
    viewBox="0 0 21 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_12079_38516)">
      <path
        d="M10.1014 16.875V3.125"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M4.47644 8.75L10.1014 3.125L15.7264 8.75"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_12079_38516">
        <rect
          width="20"
          height="20"
          fill="white"
          transform="translate(0.10144)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const Box = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_10353_18191)">
      <path
        d="M12 12.1022V21.7472"
        stroke="#1A1A1A"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M3.06641 7.2113L12.0008 12.1013L20.9352 7.2113"
        stroke="#1A1A1A"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M20.61 17.1413L12.36 21.6581C12.2496 21.7185 12.1258 21.7502 12 21.7502C11.8742 21.7502 11.7504 21.7185 11.64 21.6581L3.39 17.1413C3.2722 17.0768 3.17386 16.9819 3.10526 16.8665C3.03666 16.751 3.0003 16.6193 3 16.485V7.51688C3.0003 7.3826 3.03666 7.25086 3.10526 7.13543C3.17386 7.01999 3.2722 6.92509 3.39 6.86063L11.64 2.34376C11.7504 2.28336 11.8742 2.25171 12 2.25171C12.1258 2.25171 12.2496 2.28336 12.36 2.34376L20.61 6.86063C20.7278 6.92509 20.8261 7.01999 20.8947 7.13543C20.9633 7.25086 20.9997 7.3826 21 7.51688V16.4831C21 16.6177 20.9638 16.7499 20.8952 16.8657C20.8266 16.9815 20.7281 17.0766 20.61 17.1413Z"
        stroke="#1A1A1A"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7.64453 4.52905L16.4983 9.37499V14.25"
        stroke="#1A1A1A"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_10353_18191">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Storefront = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_15028_41795)">
      <path
        d="M4.5 13.0865V20.25H19.5V13.0865"
        stroke="black"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M5.0625 3.75H18.9375C19.1004 3.75003 19.2589 3.80311 19.389 3.90122C19.519 3.99932 19.6136 4.13711 19.6584 4.29375L21 9H3L4.34438 4.29375C4.38904 4.13757 4.48321 4.00012 4.61272 3.90206C4.74222 3.804 4.90006 3.75064 5.0625 3.75Z"
        stroke="black"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9 9V10.5C9 11.2956 8.68393 12.0587 8.12132 12.6213C7.55871 13.1839 6.79565 13.5 6 13.5C5.20435 13.5 4.44129 13.1839 3.87868 12.6213C3.31607 12.0587 3 11.2956 3 10.5V9"
        stroke="black"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M15 9V10.5C15 11.2956 14.6839 12.0587 14.1213 12.6213C13.5587 13.1839 12.7956 13.5 12 13.5C11.2044 13.5 10.4413 13.1839 9.87868 12.6213C9.31607 12.0587 9 11.2956 9 10.5V9"
        stroke="black"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M21 9V10.5C21 11.2956 20.6839 12.0587 20.1213 12.6213C19.5587 13.1839 18.7956 13.5 18 13.5C17.2044 13.5 16.4413 13.1839 15.8787 12.6213C15.3161 12.0587 15 11.2956 15 10.5V9"
        stroke="black"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_15028_41795">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Subscribers = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_6119_1471)">
      <path
        d="M12 7.5C12.5822 7.49956 13.1566 7.63492 13.6773 7.8953C14.1981 8.15569 14.651 8.53394 15 9"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M1 9C1.349 8.53394 1.80187 8.15569 2.32265 7.8953C2.84343 7.63492 3.41775 7.49956 4 7.5"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8 11.5C9.38071 11.5 10.5 10.3807 10.5 9C10.5 7.61929 9.38071 6.5 8 6.5C6.61929 6.5 5.5 7.61929 5.5 9C5.5 10.3807 6.61929 11.5 8 11.5Z"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M4.5 13.5C4.85893 12.8909 5.3706 12.386 5.98444 12.0353C6.59827 11.6845 7.29302 11.5 8 11.5C8.70698 11.5 9.40173 11.6845 10.0156 12.0353C10.6294 12.386 11.1411 12.8909 11.5 13.5"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10.0625 5C10.156 4.63785 10.3492 4.30918 10.6201 4.05131C10.891 3.79345 11.2288 3.61672 11.5952 3.5412C11.9615 3.46567 12.3416 3.49439 12.6925 3.62407C13.0433 3.75376 13.3507 3.97922 13.5798 4.27486C13.8089 4.57049 13.9506 4.92445 13.9886 5.29653C14.0267 5.66862 13.9596 6.04392 13.7951 6.37981C13.6305 6.7157 13.3751 6.99872 13.0578 7.19673C12.7405 7.39475 12.374 7.49981 12 7.5"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M3.99997 7.5C3.62594 7.49981 3.25947 7.39475 2.94216 7.19673C2.62485 6.99872 2.36942 6.7157 2.20489 6.37981C2.04035 6.04392 1.9733 5.66862 2.01135 5.29653C2.0494 4.92445 2.19103 4.57049 2.42015 4.27486C2.64926 3.97922 2.95669 3.75376 3.30751 3.62407C3.65833 3.49439 4.03849 3.46567 4.40481 3.5412C4.77113 3.61672 5.10894 3.79345 5.37986 4.05131C5.65078 4.30918 5.84396 4.63785 5.93747 5"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_6119_1471">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Tag = (props: any) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clip-path="url(#clip0_6119_1493)">
      <path
        d="M2.64625 8.64625C2.55266 8.55255 2.50006 8.42556 2.5 8.29313V2.5H8.29313C8.42556 2.50006 8.55255 2.55266 8.64625 2.64625L14.8538 8.85375C14.9474 8.94751 15.0001 9.07464 15.0001 9.20719C15.0001 9.33974 14.9474 9.46687 14.8538 9.56063L9.5625 14.8538C9.46874 14.9474 9.34161 15.0001 9.20906 15.0001C9.07651 15.0001 8.94938 14.9474 8.85563 14.8538L2.64625 8.64625Z"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M5.5 5.25C5.5 5.38807 5.38807 5.5 5.25 5.5C5.11193 5.5 5 5.38807 5 5.25C5 5.11193 5.11193 5 5.25 5C5.38807 5 5.5 5.11193 5.5 5.25Z"
        fill="currentColor"
        stroke="currentColor"
      />
    </g>
    <defs>
      <clipPath id="clip0_6119_1493">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const ClockArrowsBack = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_6119_1501)">
      <path
        d="M8 5V8L10.5 9.5"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M4.5 6.5H2V4"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M4.225 12C5.01123 12.7419 5.99865 13.2356 7.06389 13.4196C8.12914 13.6035 9.22496 13.4694 10.2144 13.0341C11.2039 12.5988 12.0432 11.8816 12.6274 10.972C13.2116 10.0625 13.5148 9.00092 13.4991 7.92003C13.4834 6.83914 13.1496 5.78684 12.5393 4.8946C11.929 4.00236 11.0693 3.30975 10.0676 2.90331C9.06588 2.49687 7.96664 2.39463 6.90718 2.60935C5.84771 2.82408 4.87502 3.34625 4.11062 4.11062C3.375 4.85562 2.7675 5.55812 2 6.5"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_6119_1501">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const PieChart = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_6119_1510)">
      <path
        d="M8 14C11.3137 14 14 11.3137 14 8C14 4.68629 11.3137 2 8 2C4.68629 2 2 4.68629 2 8C2 11.3137 4.68629 14 8 14Z"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8 8V2"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13.1962 5L2.80371 11"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_6119_1510">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const UserStar = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_6119_1529)">
      <path
        d="M6.75 10C8.82107 10 10.5 8.32107 10.5 6.25C10.5 4.17893 8.82107 2.5 6.75 2.5C4.67893 2.5 3 4.17893 3 6.25C3 8.32107 4.67893 10 6.75 10Z"
        stroke="currentColor"
        strokeMiterlimit="10"
      />
      <path
        d="M1.5 12.5C2.78437 10.9719 4.5975 10 6.75 10C8.9025 10 10.7156 10.9719 12 12.5"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13.5 9.5C14.0523 9.5 14.5 9.05228 14.5 8.5C14.5 7.94772 14.0523 7.5 13.5 7.5C12.9477 7.5 12.5 7.94772 12.5 8.5C12.5 9.05228 12.9477 9.5 13.5 9.5Z"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13.5 7.5V6.75"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.6337 8L11.9844 7.625"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.6337 9L11.9844 9.375"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13.5 9.5V10.25"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M14.3662 9L15.0156 9.375"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M14.3662 8L15.0156 7.625"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_6119_1529">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const LifeRing = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_6119_1544)">
      <path
        d="M8 14C11.3137 14 14 11.3137 14 8C14 4.68629 11.3137 2 8 2C4.68629 2 2 4.68629 2 8C2 11.3137 4.68629 14 8 14Z"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8 10.5C9.38071 10.5 10.5 9.38071 10.5 8C10.5 6.61929 9.38071 5.5 8 5.5C6.61929 5.5 5.5 6.61929 5.5 8C5.5 9.38071 6.61929 10.5 8 10.5Z"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.23232 6.23257L3.75732 3.75757"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9.76758 6.23257L12.2426 3.75757"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9.76758 9.76758L12.2426 12.2426"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.23232 9.76758L3.75732 12.2426"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_6119_1544">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const BellNotif = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_6119_1556)">
      <path
        d="M6 14H10"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M3.49992 6.5C3.49992 5.30653 3.97403 4.16193 4.81794 3.31802C5.66186 2.47411 6.80645 2 7.99992 2C9.1934 2 10.338 2.47411 11.1819 3.31802C12.0258 4.16193 12.4999 5.30653 12.4999 6.5C12.4999 8.73875 13.0187 10.5375 13.4312 11.25C13.475 11.3259 13.4981 11.4119 13.4982 11.4996C13.4982 11.5872 13.4753 11.6733 13.4316 11.7492C13.3879 11.8252 13.3251 11.8883 13.2493 11.9324C13.1735 11.9764 13.0875 11.9997 12.9999 12H2.99992C2.91241 11.9995 2.82657 11.976 2.75098 11.9319C2.67539 11.8878 2.61269 11.8246 2.56916 11.7487C2.52564 11.6728 2.5028 11.5868 2.50293 11.4992C2.50306 11.4117 2.52616 11.3258 2.56992 11.25C2.9818 10.5375 3.49992 8.73813 3.49992 6.5Z"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_6119_1556">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const PlusFilled = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect width="24" height="24" rx="12" fill="currentColor" />
    <g clip-path="url(#clip0_5774_56763)">
      <path
        d="M6.5 12H17.5"
        stroke="#F1F2F4"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12 6.5V17.5"
        stroke="#F1F2F4"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_5774_56763">
        <rect width="16" height="16" fill="white" transform="translate(4 4)" />
      </clipPath>
    </defs>
  </svg>
);

export const Cog = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_6160_4823)">
      <path
        d="M12 15.75C14.0711 15.75 15.75 14.0711 15.75 12C15.75 9.92893 14.0711 8.25 12 8.25C9.92893 8.25 8.25 9.92893 8.25 12C8.25 14.0711 9.92893 15.75 12 15.75Z"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M3.88449 16.6959C3.47009 15.9821 3.15261 15.2163 2.94043 14.4187L4.51355 12.4499C4.49574 12.149 4.49574 11.8472 4.51355 11.5462L2.94137 9.57744C3.15319 8.7797 3.47002 8.01363 3.88355 7.29932L6.38762 7.01807C6.58758 6.79278 6.80077 6.57959 7.02605 6.37963L7.3073 3.8765C8.02059 3.46494 8.78541 3.14999 9.58168 2.93994L11.5504 4.51307C11.8514 4.49525 12.1532 4.49525 12.4542 4.51307L14.4229 2.94088C15.2207 3.1527 15.9867 3.46953 16.7011 3.88307L16.9823 6.38713C17.2076 6.58709 17.4208 6.80028 17.6207 7.02557L20.1239 7.30682C20.5383 8.0206 20.8557 8.78639 21.0679 9.584L19.4948 11.5528C19.5126 11.8537 19.5126 12.1555 19.4948 12.4565L21.067 14.4253C20.8567 15.2228 20.5414 15.9888 20.1295 16.7034L17.6254 16.9846C17.4255 17.2099 17.2123 17.4231 16.987 17.6231L16.7057 20.1262C15.992 20.5406 15.2262 20.8581 14.4286 21.0703L12.4598 19.4971C12.1588 19.5149 11.857 19.5149 11.5561 19.4971L9.5873 21.0693C8.7898 20.859 8.02374 20.5437 7.30918 20.1318L7.02793 17.6278C6.80264 17.4278 6.58945 17.2146 6.38949 16.9893L3.88449 16.6959Z"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_6160_4823">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const FullScreen = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_6160_36040)">
      <path
        d="M15 4.5H19.5V9"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13.5 10.5L19.5 4.5"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9 19.5H4.5V15"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10.5 13.5L4.5 19.5"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_6160_36040">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Minimize = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_5811_36800)">
      <path
        d="M13.5 6V10.5H18"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M19.5 4.5L13.5 10.5"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6 13.5H10.5V18"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M4.5 19.5L10.5 13.5"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_5811_36800">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Paperclip = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_5811_36798)">
      <path
        d="M15 7.5001L7.18965 15.4398C6.9152 15.7225 6.76301 16.1019 6.76597 16.4959C6.76894 16.8899 6.92682 17.2669 7.20551 17.5455C7.48419 17.824 7.86128 17.9818 8.2553 17.9846C8.64932 17.9873 9.02861 17.835 9.31122 17.5604L18.6215 8.12166C19.1842 7.55899 19.5003 6.79584 19.5003 6.0001C19.5003 5.20436 19.1842 4.44121 18.6215 3.87854C18.0589 3.31586 17.2957 2.99976 16.5 2.99976C15.7042 2.99976 14.9411 3.31586 14.3784 3.87854L5.06809 13.3182C4.23553 14.1645 3.77108 15.3054 3.77592 16.4925C3.78075 17.6796 4.25447 18.8167 5.0939 19.6562C5.93332 20.4956 7.07044 20.9693 8.25756 20.9741C9.44467 20.979 10.5856 20.5145 11.4318 19.682L19.125 12.0001"
        stroke="currentColor"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_5811_36798">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const AttachmentImage = () => (
  <svg
    width="41"
    height="25"
    viewBox="0 0 41 25"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M26.0327 4.65135C27.3171 4.65135 28.3584 3.61011 28.3584 2.32567C28.3584 1.04124 27.3171 0 26.0327 0C24.7483 0 23.707 1.04124 23.707 2.32567C23.707 3.61011 24.7483 4.65135 26.0327 4.65135Z"
      fill="#80A9EF"
    />
    <path
      d="M24.3477 14.7296L29.3265 9.75649C29.6173 9.46595 30.0115 9.30273 30.4225 9.30273C30.8335 9.30273 31.2277 9.46595 31.5185 9.75649L39.2106 17.4545"
      stroke="#80A9EF"
      stroke-width="3.1009"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M2 15.6375L12.5314 5.10414C12.6754 4.95998 12.8464 4.84563 13.0346 4.7676C13.2229 4.68958 13.4246 4.64941 13.6284 4.64941C13.8321 4.64941 14.0339 4.68958 14.2221 4.7676C14.4103 4.84563 14.5813 4.95998 14.7253 5.10414L32.8752 23.256"
      stroke="#80A9EF"
      stroke-width="3.1009"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);

export const InsertList = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_5823_41211)">
      <path
        d="M5.5 4H13.5"
        stroke="currentColor"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M5.5 8H13.5"
        stroke="currentColor"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M5.5 12H13.5"
        stroke="currentColor"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M2.75 4.75C3.16421 4.75 3.5 4.41421 3.5 4C3.5 3.58579 3.16421 3.25 2.75 3.25C2.33579 3.25 2 3.58579 2 4C2 4.41421 2.33579 4.75 2.75 4.75Z"
        fill="currentColor"
      />
      <path
        d="M2.75 8.75C3.16421 8.75 3.5 8.41421 3.5 8C3.5 7.58579 3.16421 7.25 2.75 7.25C2.33579 7.25 2 7.58579 2 8C2 8.41421 2.33579 8.75 2.75 8.75Z"
        fill="currentColor"
      />
      <path
        d="M2.75 12.75C3.16421 12.75 3.5 12.4142 3.5 12C3.5 11.5858 3.16421 11.25 2.75 11.25C2.33579 11.25 2 11.5858 2 12C2 12.4142 2.33579 12.75 2.75 12.75Z"
        fill="currentColor"
      />
    </g>
    <defs>
      <clipPath id="clip0_5823_41211">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const InsertOrderedList = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_5823_41212)">
      <path
        d="M6.5 8H13.5"
        stroke="currentColor"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M6.5 4H13.5"
        stroke="currentColor"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M6.5 12H13.5"
        stroke="currentColor"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M3.5 6.5V2.5L2.5 3"
        stroke="currentColor"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M4.5 13H2.5L4.2925 10.6018C4.37381 10.4976 4.4332 10.378 4.46712 10.2503C4.50103 10.1225 4.50876 9.98918 4.48985 9.85835C4.47093 9.72752 4.42576 9.60187 4.35705 9.48894C4.28834 9.37601 4.1975 9.27814 4.09 9.20121C3.87093 9.04129 3.59843 8.97246 3.32969 9.00917C3.06096 9.04587 2.8169 9.18527 2.64875 9.39808C2.5855 9.47939 2.53531 9.57006 2.5 9.66684"
        stroke="currentColor"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_5823_41212">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Bold = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_5823_41215)">
      <path
        d="M5 7.5H10C10.663 7.5 11.2989 7.76339 11.7678 8.23223C12.2366 8.70107 12.5 9.33696 12.5 10C12.5 10.663 12.2366 11.2989 11.7678 11.7678C11.2989 12.2366 10.663 12.5 10 12.5H5V3H9.25C9.84674 3 10.419 3.23705 10.841 3.65901C11.2629 4.08097 11.5 4.65326 11.5 5.25C11.5 5.84674 11.2629 6.41903 10.841 6.84099C10.419 7.26295 9.84674 7.5 9.25 7.5"
        stroke="currentColor"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_5823_41215">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Italic = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_5823_41216)">
      <path
        d="M9.5 3.5L6.5 12.5"
        stroke="currentColor"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M4 12.5H9"
        stroke="currentColor"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M7 3.5H12"
        stroke="currentColor"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_5823_41216">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Underline = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_5823_41217)">
      <path
        d="M4 14H12"
        stroke="currentColor"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M11.5 3.5V8.5C11.5 9.42826 11.1313 10.3185 10.4749 10.9749C9.8185 11.6313 8.92826 12 8 12C7.07174 12 6.1815 11.6313 5.52513 10.9749C4.86875 10.3185 4.5 9.42826 4.5 8.5V3.5"
        stroke="currentColor"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_5823_41217">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const StrikeThrough = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_5823_41218)">
      <path
        d="M2.5 8H13.5"
        stroke="currentColor"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M4.77062 6C4.71897 5.8384 4.69323 5.66965 4.69437 5.5C4.69437 4.11937 6.06937 3 8 3C9.4375 3 10.515 3.61687 11 4.5"
        stroke="currentColor"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M4.5 10.5C4.5 11.8806 6.06687 13 8 13C9.93313 13 11.5 11.8806 11.5 10.5C11.5 9.01437 10.1487 8.4375 8.65 8"
        stroke="currentColor"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_5823_41218">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const InsertAttachment = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_5823_41220)">
      <path
        d="M9.99981 4.99998L4.79294 10.2931C4.60997 10.4816 4.50851 10.7345 4.51049 10.9972C4.51246 11.2599 4.61772 11.5112 4.80351 11.6969C4.9893 11.8826 5.24069 11.9878 5.50337 11.9896C5.76605 11.9915 6.01891 11.8899 6.20731 11.7069L12.4142 5.41436C12.7893 5.03924 13 4.53048 13 3.99998C13 3.46949 12.7893 2.96072 12.4142 2.58561C12.0391 2.21049 11.5303 1.99976 10.9998 1.99976C10.4693 1.99976 9.96055 2.21049 9.58544 2.58561L3.37856 8.87873C2.82352 9.44289 2.51389 10.2035 2.51711 10.9949C2.52034 11.7863 2.83615 12.5444 3.39577 13.104C3.95539 13.6636 4.71346 13.9795 5.50487 13.9827C6.29628 13.9859 7.05691 13.6763 7.62106 13.1212L12.7498 7.99998"
        stroke="currentColor"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_5823_41220">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Reply = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_6283_2725)">
      <path
        d="M7.5 14.25L3 9.75L7.5 5.25"
        stroke="#1857C3"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M21 18.75C21 16.3631 20.0518 14.0739 18.364 12.386C16.6761 10.6982 14.3869 9.75 12 9.75H3"
        stroke="#1857C3"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_6283_2725">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const BoltinCircle = () => (
  <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="48" height="48" rx="24" fill="#E8F0FC"/>
    <g clip-path="url(#clip0_6023_28893)">
    <path d="M24 27.75C26.0711 27.75 27.75 26.0711 27.75 24C27.75 21.9289 26.0711 20.25 24 20.25C21.9289 20.25 20.25 21.9289 20.25 24C20.25 26.0711 21.9289 27.75 24 27.75Z" stroke="#2E70E5" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M32.61 29.1413L24.36 33.6581C24.2496 33.7185 24.1258 33.7502 24 33.7502C23.8742 33.7502 23.7504 33.7185 23.64 33.6581L15.39 29.1413C15.2722 29.0768 15.1739 28.9819 15.1053 28.8665C15.0367 28.751 15.0003 28.6193 15 28.485V19.5169C15.0003 19.3826 15.0367 19.2509 15.1053 19.1354C15.1739 19.02 15.2722 18.9251 15.39 18.8606L23.64 14.3438C23.7504 14.2834 23.8742 14.2517 24 14.2517C24.1258 14.2517 24.2496 14.2834 24.36 14.3438L32.61 18.8606C32.7278 18.9251 32.8261 19.02 32.8947 19.1354C32.9633 19.2509 32.9997 19.3826 33 19.5169V28.4831C33 28.6177 32.9638 28.7499 32.8952 28.8657C32.8266 28.9815 32.7281 29.0766 32.61 29.1413Z" stroke="#2E70E5" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
    </g>
    <defs>
    <clipPath id="clip0_6023_28893">
      <rect width="24" height="24" fill="white" transform="translate(12 12)"/>
    </clipPath>
    </defs>
  </svg>
)

export const SpeedometerCircle = () => (
  <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="48" height="48" rx="24" fill="#E8F0FC"/>
  <g clip-path="url(#clip0_6023_28016)">
  <path d="M23.25 27L32.25 18" stroke="#2E70E5" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
  <path d="M17.2912 26.9999C17.2638 26.7508 17.25 26.5005 17.25 26.2499C17.2509 25.1787 17.5065 24.123 17.9958 23.17C18.4851 22.2171 19.194 21.3941 20.064 20.7691C20.934 20.1441 21.9401 19.7349 22.9994 19.5754C24.0587 19.4158 25.1407 19.5104 26.1562 19.8515" stroke="#2E70E5" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
  <path d="M32.6835 21.8099C33.2849 22.9898 33.6405 24.2796 33.7288 25.601C33.8171 26.9224 33.6362 28.2479 33.1972 29.4974C33.146 29.6447 33.0501 29.7723 32.9229 29.8626C32.7957 29.9528 32.6435 30.0011 32.4875 30.0008H15.5122C15.356 30.0002 15.2038 29.951 15.0767 29.8602C14.9496 29.7693 14.8538 29.6413 14.8025 29.4937C14.4276 28.4274 14.2407 27.3042 14.2504 26.174C14.2916 20.8124 18.725 16.4521 24.0941 16.4999C25.6074 16.512 27.0969 16.8774 28.4441 17.5668" stroke="#2E70E5" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
  </g>
  <defs>
  <clipPath id="clip0_6023_28016">
  <rect width="24" height="24" fill="white" transform="translate(12 12)"/>
  </clipPath>
  </defs>
  </svg>
)

export const Boltin = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_6530_24417)">
      <path
        d="M8 10.5C9.38071 10.5 10.5 9.38071 10.5 8C10.5 6.61929 9.38071 5.5 8 5.5C6.61929 5.5 5.5 6.61929 5.5 8C5.5 9.38071 6.61929 10.5 8 10.5Z"
        stroke="#2E70E5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M13.74 11.4276L8.24 14.4388C8.16641 14.4791 8.08388 14.5002 8 14.5002C7.91612 14.5002 7.83359 14.4791 7.76 14.4388L2.26 11.4276C2.18147 11.3846 2.11591 11.3213 2.07017 11.2444C2.02444 11.1674 2.0002 11.0796 2 10.9901V5.01134C2.0002 4.92181 2.02444 4.83399 2.07017 4.75703C2.11591 4.68007 2.18147 4.61681 2.26 4.57384L7.76 1.56259C7.83359 1.52232 7.91612 1.50122 8 1.50122C8.08388 1.50122 8.16641 1.52232 8.24 1.56259L13.74 4.57384C13.8185 4.61681 13.8841 4.68007 13.9298 4.75703C13.9756 4.83399 13.9998 4.92181 14 5.01134V10.9888C14 11.0786 13.9759 11.1667 13.9301 11.2439C13.8844 11.3211 13.8187 11.3845 13.74 11.4276Z"
        stroke="#2E70E5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_6530_24417">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Speedometer = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_6530_24468)">
      <path
        d="M7.5 10L13.5 4"
        stroke="#2E70E5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M3.5275 9.99994C3.50919 9.8339 3.50001 9.66698 3.5 9.49994C3.5006 8.78578 3.67103 8.08201 3.99722 7.44669C4.3234 6.81138 4.796 6.26275 5.37599 5.84607C5.95599 5.42938 6.62676 5.1566 7.33295 5.05024C8.03914 4.94387 8.7605 5.00696 9.4375 5.23431"
        stroke="#2E70E5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M13.789 6.54002C14.1899 7.32664 14.427 8.18646 14.4858 9.06739C14.5447 9.94832 14.4242 10.832 14.1315 11.665C14.0973 11.7632 14.0334 11.8483 13.9486 11.9085C13.8638 11.9686 13.7623 12.0008 13.6584 12.0006H2.34149C2.23732 12.0002 2.13587 11.9674 2.05112 11.9069C1.96638 11.8463 1.90252 11.7609 1.86836 11.6625C1.61838 10.9517 1.49382 10.2029 1.50024 9.44939C1.52774 5.87502 4.48336 2.96814 8.06274 3.00002C9.07159 3.00811 10.0646 3.25166 10.9627 3.71127"
        stroke="#2E70E5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_6530_24468">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const SparklesCircle = () => {
  return (
    <svg
      width="48"
      height="48"
      viewBox="0 0 48 48"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="48" height="48" rx="24" fill="#D6E3FA" />
      <g clip-path="url(#clip0_6849_31956)">
        <path
          d="M19.5785 27.9816L14.7354 26.2004C14.593 26.1478 14.4701 26.0529 14.3833 25.9283C14.2965 25.8038 14.25 25.6556 14.25 25.5038C14.25 25.352 14.2965 25.2039 14.3833 25.0793C14.4701 24.9548 14.593 24.8598 14.7354 24.8073L19.5785 23.026C19.6794 22.9891 19.7709 22.9306 19.8469 22.8547C19.9228 22.7787 19.9813 22.6872 20.0182 22.5863L21.7994 17.7432C21.852 17.6008 21.947 17.4779 22.0715 17.3911C22.1961 17.3043 22.3442 17.2578 22.496 17.2578C22.6478 17.2578 22.796 17.3043 22.9205 17.3911C23.0451 17.4779 23.14 17.6008 23.1926 17.7432L24.9738 22.5863C25.0108 22.6872 25.0692 22.7787 25.1452 22.8547C25.2211 22.9306 25.3127 22.9891 25.4135 23.026L30.2566 24.8073C30.399 24.8598 30.5219 24.9548 30.6087 25.0793C30.6955 25.2039 30.742 25.352 30.742 25.5038C30.742 25.6556 30.6955 25.8038 30.6087 25.9283C30.5219 26.0529 30.399 26.1478 30.2566 26.2004L25.4135 27.9816C25.3127 28.0186 25.2211 28.077 25.1452 28.153C25.0692 28.2289 25.0108 28.3205 24.9738 28.4213L23.1926 33.2645C23.14 33.4069 23.0451 33.5297 22.9205 33.6165C22.796 33.7033 22.6478 33.7498 22.496 33.7498C22.3442 33.7498 22.1961 33.7033 22.0715 33.6165C21.947 33.5297 21.852 33.4069 21.7994 33.2645L20.0182 28.4213C19.9813 28.3205 19.9228 28.2289 19.8469 28.153C19.7709 28.077 19.6794 28.0186 19.5785 27.9816Z"
          stroke="#1857C3"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M28.5 13.5V18"
          stroke="#1857C3"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M33 18.75V21.75"
          stroke="#1857C3"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M26.25 15.75H30.75"
          stroke="#1857C3"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M31.5 20.25H34.5"
          stroke="#1857C3"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_6849_31956">
          <rect
            width="24"
            height="24"
            fill="white"
            transform="translate(12 12)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export const InfoCircle = () => {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clip-path="url(#clip0_6971_25964)">
        <path
          d="M8 14C11.3137 14 14 11.3137 14 8C14 4.68629 11.3137 2 8 2C4.68629 2 2 4.68629 2 8C2 11.3137 4.68629 14 8 14Z"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M7.5 7.5C7.63261 7.5 7.75979 7.55268 7.85355 7.64645C7.94732 7.74021 8 7.86739 8 8V10.5C8 10.6326 8.05268 10.7598 8.14645 10.8536C8.24021 10.9473 8.36739 11 8.5 11"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M7.75 6C8.16421 6 8.5 5.66421 8.5 5.25C8.5 4.83579 8.16421 4.5 7.75 4.5C7.33579 4.5 7 4.83579 7 5.25C7 5.66421 7.33579 6 7.75 6Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_6971_25964">
          <rect width="16" height="16" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export const CloseCircle = () => {
  return (
    <svg
      width="40"
      height="40"
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect x="5" y="5" width="30" height="30" rx="15" fill="#E8F0FC" />
      <g clip-path="url(#clip0_6797_56854)">
        <path
          d="M24.5 15.5L15.5 24.5"
          stroke="#2E70E5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M24.5 24.5L15.5 15.5"
          stroke="#2E70E5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_6797_56854">
          <rect
            width="16"
            height="16"
            fill="white"
            transform="translate(12 12)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export const CallCircle = () => {
  return (
    <svg
      width="32"
      height="33"
      viewBox="0 0 32 33"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect y="0.5" width="32" height="32" rx="16" fill="#E8F0FC" />
      <g clip-path="url(#clip0_7129_51413)">
        <path
          d="M18.2744 17.5837C18.3436 17.5377 18.4233 17.5096 18.5061 17.5021C18.5889 17.4945 18.6723 17.5078 18.7488 17.5406L21.6963 18.8612C21.7956 18.9037 21.8785 18.9772 21.9326 19.0707C21.9867 19.1642 22.009 19.2727 21.9963 19.38C21.8991 20.1056 21.5418 20.7713 20.9907 21.2533C20.4395 21.7352 19.7321 22.0005 19 22C16.7457 22 14.5837 21.1045 12.9896 19.5104C11.3955 17.9163 10.5 15.7543 10.5 13.5C10.4994 12.7679 10.7648 12.0605 11.2467 11.5093C11.7287 10.9582 12.3943 10.6008 13.12 10.5037C13.2273 10.491 13.3358 10.5133 13.4293 10.5674C13.5228 10.6215 13.5963 10.7044 13.6388 10.8037L14.9594 13.7537C14.9918 13.8295 15.005 13.9121 14.9978 13.9942C14.9906 14.0763 14.9633 14.1554 14.9181 14.2244L13.5825 15.8125C13.5351 15.884 13.5071 15.9665 13.5012 16.0521C13.4953 16.1377 13.5117 16.2233 13.5488 16.3006C14.0656 17.3587 15.1594 18.4394 16.2206 18.9512C16.2984 18.9882 16.3844 19.0042 16.4702 18.9977C16.556 18.9912 16.6387 18.9625 16.71 18.9144L18.2744 17.5837Z"
          stroke="#2E70E5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_7129_51413">
          <rect
            width="16"
            height="16"
            fill="white"
            transform="translate(8 8.5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export const SimCardCircle = () => (
  <svg
    width={32}
    height={33}
    viewBox="0 0 32 33"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect y={0.5} width={32} height={32} rx={16} fill="#E8F0FC" />
    <g clipPath="url(#clip0_6071_61241)">
      <path
        d="M20.5 22.5H11.5C11.3674 22.5 11.2402 22.4473 11.1464 22.3536C11.0527 22.2598 11 22.1326 11 22V11C11 10.8674 11.0527 10.7402 11.1464 10.6464C11.2402 10.5527 11.3674 10.5 11.5 10.5H17.5L21 14V22C21 22.1326 20.9473 22.2598 20.8536 22.3536C20.7598 22.4473 20.6326 22.5 20.5 22.5Z"
        stroke="#2E70E5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M19 16H13V20.5H19V16Z"
        stroke="#2E70E5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M15 18V20.5"
        stroke="#2E70E5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M17 18V20.5"
        stroke="#2E70E5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_6071_61241">
        <rect
          width={16}
          height={16}
          fill="white"
          transform="translate(8 8.5)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const LocationPinCircle = () => {
  return (
    <svg
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="32" height="32" rx="16" fill="#E8F0FC" />
      <g clip-path="url(#clip0_7134_51421)">
        <path
          d="M16 16.5C17.1046 16.5 18 15.6046 18 14.5C18 13.3954 17.1046 12.5 16 12.5C14.8954 12.5 14 13.3954 14 14.5C14 15.6046 14.8954 16.5 16 16.5Z"
          stroke="#2E70E5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M21 14.5C21 19 16 22.5 16 22.5C16 22.5 11 19 11 14.5C11 13.1739 11.5268 11.9021 12.4645 10.9645C13.4021 10.0268 14.6739 9.5 16 9.5C17.3261 9.5 18.5979 10.0268 19.5355 10.9645C20.4732 11.9021 21 13.1739 21 14.5Z"
          stroke="#2E70E5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_7134_51421">
          <rect
            width="16"
            height="16"
            fill="white"
            transform="translate(8 8)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export const TagCircle = () => {
  return (
    <svg
      width="32"
      height="33"
      viewBox="0 0 32 33"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect y="0.5" width="32" height="32" rx="16" fill="#E8F0FC" />
      <g clip-path="url(#clip0_6071_61247)">
        <path
          d="M10.6463 17.1462C10.5527 17.0526 10.5001 16.9256 10.5 16.7931V11H16.2931C16.4256 11.0001 16.5526 11.0527 16.6462 11.1463L22.8538 17.3538C22.9474 17.4475 23.0001 17.5746 23.0001 17.7072C23.0001 17.8397 22.9474 17.9669 22.8538 18.0606L17.5625 23.3538C17.4687 23.4474 17.3416 23.5001 17.2091 23.5001C17.0765 23.5001 16.9494 23.4474 16.8556 23.3538L10.6463 17.1462Z"
          stroke="#2E70E5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M13.25 14.5C13.6642 14.5 14 14.1642 14 13.75C14 13.3358 13.6642 13 13.25 13C12.8358 13 12.5 13.3358 12.5 13.75C12.5 14.1642 12.8358 14.5 13.25 14.5Z"
          fill="#2E70E5"
        />
      </g>
      <defs>
        <clipPath id="clip0_6071_61247">
          <rect
            width="16"
            height="16"
            fill="white"
            transform="translate(8 8.5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export const BarCodeCircle = () => {
  return (
    <svg
      width="32"
      height="33"
      viewBox="0 0 32 33"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect y="0.5" width="32" height="32" rx="16" fill="#E8F0FC" />
      <g clip-path="url(#clip0_6071_61253)">
        <path
          d="M19.5 11.5H22V14"
          stroke="#2E70E5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M12.5 21.5H10V19"
          stroke="#2E70E5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M22 19V21.5H19.5"
          stroke="#2E70E5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M10 14V11.5H12.5"
          stroke="#2E70E5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M13 14V19"
          stroke="#2E70E5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M19 14V19"
          stroke="#2E70E5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M17 14V19"
          stroke="#2E70E5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M15 14V19"
          stroke="#2E70E5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_6071_61253">
          <rect
            width="16"
            height="16"
            fill="white"
            transform="translate(8 8.5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export const CoinCircle = () => {
  return (
    <svg
      width="32"
      height="33"
      viewBox="0 0 32 33"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect y="0.5" width="32" height="32" rx="16" fill="#E8F0FC" />
      <g clip-path="url(#clip0_6076_63654)">
        <path
          d="M16 18C19.5899 18 22.5 16.6569 22.5 15C22.5 13.3431 19.5899 12 16 12C12.4101 12 9.5 13.3431 9.5 15C9.5 16.6569 12.4101 18 16 18Z"
          stroke="#2E70E5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M16 18V21"
          stroke="#2E70E5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M9.5 15V18C9.5 19.5 12 21 16 21C20 21 22.5 19.5 22.5 18V15"
          stroke="#2E70E5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M20 17.3818V20.3818"
          stroke="#2E70E5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M12 17.3818V20.3818"
          stroke="#2E70E5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_6076_63654">
          <rect
            width="16"
            height="16"
            fill="white"
            transform="translate(8 8.5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export const Promotions = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_7335_15573)">
      <path
        d="M12.5 3.5L3.5 12.5"
        stroke="#061632"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M4.75 6.5C5.7165 6.5 6.5 5.7165 6.5 4.75C6.5 3.7835 5.7165 3 4.75 3C3.7835 3 3 3.7835 3 4.75C3 5.7165 3.7835 6.5 4.75 6.5Z"
        stroke="#061632"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M11.25 13C12.2165 13 13 12.2165 13 11.25C13 10.2835 12.2165 9.5 11.25 9.5C10.2835 9.5 9.5 10.2835 9.5 11.25C9.5 12.2165 10.2835 13 11.25 13Z"
        stroke="#061632"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_7335_15573">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const PercentCircle = () => {
  return (
    <svg
      width="32"
      height="33"
      viewBox="0 0 32 33"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect y="0.5" width="32" height="32" rx="16" fill="#E8F0FC" />
      <g clip-path="url(#clip0_6076_63660)">
        <path
          d="M11.4037 21.0962C10.8287 20.5212 11.21 19.3131 10.9175 18.6056C10.6138 17.875 9.5 17.2812 9.5 16.5C9.5 15.7188 10.6138 15.125 10.9175 14.3944C11.21 13.6875 10.8287 12.4787 11.4037 11.9037C11.9787 11.3287 13.1875 11.71 13.8944 11.4175C14.6281 11.1138 15.2188 10 16 10C16.7812 10 17.375 11.1138 18.1056 11.4175C18.8131 11.71 20.0212 11.3287 20.5962 11.9037C21.1712 12.4787 20.79 13.6869 21.0825 14.3944C21.3863 15.1281 22.5 15.7188 22.5 16.5C22.5 17.2812 21.3863 17.875 21.0825 18.6056C20.79 19.3131 21.1712 20.5212 20.5962 21.0962C20.0212 21.6712 18.8131 21.29 18.1056 21.5825C17.375 21.8863 16.7812 23 16 23C15.2188 23 14.625 21.8863 13.8944 21.5825C13.1875 21.29 11.9787 21.6712 11.4037 21.0962Z"
          stroke="#2E70E5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M14 15.5C14.5523 15.5 15 15.0523 15 14.5C15 13.9477 14.5523 13.5 14 13.5C13.4477 13.5 13 13.9477 13 14.5C13 15.0523 13.4477 15.5 14 15.5Z"
          stroke="#2E70E5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M18 19.5C18.5523 19.5 19 19.0523 19 18.5C19 17.9477 18.5523 17.5 18 17.5C17.4477 17.5 17 17.9477 17 18.5C17 19.0523 17.4477 19.5 18 19.5Z"
          stroke="#2E70E5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M13.5 19L18.5 14"
          stroke="#2E70E5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_6076_63660">
          <rect
            width="16"
            height="16"
            fill="white"
            transform="translate(8 8.5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export const ScaleCircle = () => {
  return (
    <svg
      width="32"
      height="33"
      viewBox="0 0 32 33"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect y="0.5" width="32" height="32" rx="16" fill="#E8F0FC" />
      <g clip-path="url(#clip0_6076_63666)">
        <path
          d="M16 11V22"
          stroke="#2E70E5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M14.5 22H17.5"
          stroke="#2E70E5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M11.5 14L20.5 12"
          stroke="#2E70E5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M9.5 19C9.5 20.1044 10.75 20.5 11.5 20.5C12.25 20.5 13.5 20.1044 13.5 19L11.5 14L9.5 19Z"
          stroke="#2E70E5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M18.5 17C18.5 18.1044 19.75 18.5 20.5 18.5C21.25 18.5 22.5 18.1044 22.5 17L20.5 12L18.5 17Z"
          stroke="#2E70E5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_6076_63666">
          <rect
            width="16"
            height="16"
            fill="white"
            transform="translate(8 8.5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export const LightningCircle = () => {
  return (
    <svg
      width="32"
      height="33"
      viewBox="0 0 32 33"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect y="0.5" width="32" height="32" rx="16" fill="#E8F0FC" />
      <g clip-path="url(#clip0_6505_84973)">
        <path
          d="M18 9.5L17 14.5L21 16L14 23.5L15 18.5L11 17L18 9.5Z"
          stroke="#2E70E5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_6505_84973">
          <rect
            width="16"
            height="16"
            fill="white"
            transform="translate(8 8.5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export const CardCircle = () => {
  return (
    <svg
      width="32"
      height="33"
      viewBox="0 0 32 33"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect y="0.5" width="32" height="32" rx="16" fill="#E8F0FC" />
      <g clip-path="url(#clip0_6489_84912)">
        <path
          d="M22 12H10C9.72386 12 9.5 12.2239 9.5 12.5V20.5C9.5 20.7761 9.72386 21 10 21H22C22.2761 21 22.5 20.7761 22.5 20.5V12.5C22.5 12.2239 22.2761 12 22 12Z"
          stroke="#2E70E5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M18.5 19H20.5"
          stroke="#2E70E5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M15.5 19H16.5"
          stroke="#2E70E5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M9.5 14.5H22.5"
          stroke="#2E70E5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_6489_84912">
          <rect
            width="16"
            height="16"
            fill="white"
            transform="translate(8 8.5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export const WalletCircle = () => {
  return (
    <svg
      width="32"
      height="33"
      viewBox="0 0 32 33"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect y="0.5" width="32" height="32" rx="16" fill="#E8F0FC" />
      <g clip-path="url(#clip0_6505_84989)">
        <path
          d="M10.5 12.5V20.5C10.5 20.7652 10.6054 21.0196 10.7929 21.2071C10.9804 21.3946 11.2348 21.5 11.5 21.5H21.5C21.6326 21.5 21.7598 21.4473 21.8536 21.3536C21.9473 21.2598 22 21.1326 22 21V14C22 13.8674 21.9473 13.7402 21.8536 13.6464C21.7598 13.5527 21.6326 13.5 21.5 13.5H11.5C11.2348 13.5 10.9804 13.3946 10.7929 13.2071C10.6054 13.0196 10.5 12.7652 10.5 12.5ZM10.5 12.5C10.5 12.2348 10.6054 11.9804 10.7929 11.7929C10.9804 11.6054 11.2348 11.5 11.5 11.5H20"
          stroke="#2E70E5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M19.25 18C19.6642 18 20 17.6642 20 17.25C20 16.8358 19.6642 16.5 19.25 16.5C18.8358 16.5 18.5 16.8358 18.5 17.25C18.5 17.6642 18.8358 18 19.25 18Z"
          fill="#2E70E5"
        />
      </g>
      <defs>
        <clipPath id="clip0_6505_84989">
          <rect
            width="16"
            height="16"
            fill="white"
            transform="translate(8 8.5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export const PlusCircle = () => {
  return (
    <svg
      width="48"
      height="48"
      viewBox="0 0 48 48"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="48" height="48" rx="24" fill="#E8F0FC" />
      <g clip-path="url(#clip0_6797_56842)">
        <path
          d="M15.75 24H32.25"
          stroke="#2E70E5"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M24 15.75V32.25"
          stroke="#2E70E5"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_6797_56842">
          <rect
            width="24"
            height="24"
            fill="white"
            transform="translate(12 12)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export const InfoCircle2 = () => {
  return (
    <svg
      width="16"
      height="16"
      version="1.1"
      id="Layer_1"
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 32 32"
      enable-background="new 0 0 32 32"
    >
      <rect x="15" y="14" width="2" height="8" fill="currentColor" />
      <rect x="15" y="10" width="2" height="2" fill="currentColor" />
      <circle
        fill="none"
        stroke="currentColor"
        stroke-width="2"
        stroke-miterlimit="10"
        cx="16"
        cy="16"
        r="12"
      />
    </svg>
  );
};
export const Download = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_7210_18574)">
      <path
        d="M12 13.5V3"
        stroke="#1857C3"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M20.25 13.5V19.5H3.75V13.5"
        stroke="#1857C3"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M15.75 9.75L12 13.5L8.25 9.75"
        stroke="#1857C3"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_7210_18574">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const ScrewDriver = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_7146_123794)">
      <path
        d="M6.50016 7.93363C6.10046 7.21029 5.93461 6.38089 6.02543 5.55947C6.11625 4.73804 6.45926 3.96491 7.00733 3.34635C7.55539 2.72779 8.28159 2.29417 9.08611 2.10511C9.89062 1.91604 10.734 1.98081 11.5002 2.2905L9.00016 4.99988L9.35391 6.64613L11.0002 6.99988L13.7095 4.49988C14.0192 5.26609 14.084 6.10942 13.8949 6.91393C13.7059 7.71844 13.2723 8.44465 12.6537 8.99271C12.0351 9.54078 11.262 9.88379 10.4406 9.97461C9.61915 10.0654 8.78975 9.89958 8.06641 9.49988L4.56266 13.5624C4.28087 13.8442 3.89868 14.0025 3.50016 14.0025C3.10165 14.0025 2.71945 13.8442 2.43766 13.5624C2.15587 13.2806 1.99756 12.8984 1.99756 12.4999C1.99756 12.1014 2.15587 11.7192 2.43766 11.4374L6.50016 7.93363Z"
        stroke="#061632"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_7146_123794">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Pencil = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_6680_61906)">
      <path
        d="M8.68969 20.2501H4.5C4.30109 20.2501 4.11032 20.1711 3.96967 20.0305C3.82902 19.8898 3.75 19.699 3.75 19.5001V15.3104C3.75009 15.1118 3.82899 14.9213 3.96938 14.7807L15.5306 3.2195C15.6713 3.07895 15.862 3 16.0608 3C16.2596 3 16.4503 3.07895 16.5909 3.2195L20.7806 7.40637C20.9212 7.54701 21.0001 7.7377 21.0001 7.93653C21.0001 8.13535 20.9212 8.32605 20.7806 8.46668L9.21937 20.0307C9.07883 20.1711 8.88834 20.25 8.68969 20.2501Z"
        stroke="currentColor"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M12.75 6L18 11.25"
        stroke="currentColor"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_6680_61906">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const PencilCircle = () => (
  <svg
    width="48"
    height="48"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect width="48" height="48" rx="24" fill="#E8F0FC" />
    <g clip-path="url(#clip0_6489_83387)">
      <path
        d="M20.6897 32.2501H16.5C16.3011 32.2501 16.1103 32.1711 15.9697 32.0305C15.829 31.8898 15.75 31.699 15.75 31.5001V27.3104C15.7501 27.1118 15.829 26.9213 15.9694 26.7807L27.5306 15.2195C27.6713 15.079 27.862 15 28.0608 15C28.2596 15 28.4503 15.079 28.5909 15.2195L32.7806 19.4064C32.9212 19.547 33.0001 19.7377 33.0001 19.9365C33.0001 20.1354 32.9212 20.326 32.7806 20.4667L21.2194 32.0307C21.0788 32.1711 20.8883 32.25 20.6897 32.2501Z"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M24.75 18L30 23.25"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_6489_83387">
        <rect
          width="24"
          height="24"
          fill="white"
          transform="translate(12 12)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const WarningCircle = () => (
  <svg
    width="48"
    height="48"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect width="48" height="48" rx="24" fill="#FFF7E5" />
    <g clip-path="url(#clip0_6489_79727)">
      <path
        d="M25.3496 15.7706L33.5489 30.0084C34.1236 31.0116 33.3811 32.25 32.198 32.25H15.7993C14.6161 32.25 13.8736 31.0116 14.4483 30.0084L22.6477 15.7706C23.2383 14.7431 24.7589 14.7431 25.3496 15.7706Z"
        stroke="#664700"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M24 25.5V21.75"
        stroke="#664700"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M24 30C24.6213 30 25.125 29.4963 25.125 28.875C25.125 28.2537 24.6213 27.75 24 27.75C23.3787 27.75 22.875 28.2537 22.875 28.875C22.875 29.4963 23.3787 30 24 30Z"
        fill="#664700"
      />
    </g>
    <defs>
      <clipPath id="clip0_6489_79727">
        <rect
          width="24"
          height="24"
          fill="white"
          transform="translate(12 12)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const RippedTicketCircle = () => (
  <svg
    width="32"
    height="33"
    viewBox="0 0 32 33"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect y="0.5" width="32" height="32" rx="16" fill="#E8F0FC" />
    <g clip-path="url(#clip0_7503_59623)">
      <path
        d="M13 15H19"
        stroke="#2E70E5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M13 17H19"
        stroke="#2E70E5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M10 21.5V12C10 11.8674 10.0527 11.7402 10.1464 11.6464C10.2402 11.5527 10.3674 11.5 10.5 11.5H21.5C21.6326 11.5 21.7598 11.5527 21.8536 11.6464C21.9473 11.7402 22 11.8674 22 12V21.5L20 20.5L18 21.5L16 20.5L14 21.5L12 20.5L10 21.5Z"
        stroke="#2E70E5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_7503_59623">
        <rect
          width="16"
          height="16"
          fill="white"
          transform="translate(8 8.5)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const InputError = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_3873_12690)">
      <path
        d="M8 14C11.3137 14 14 11.3137 14 8C14 4.68629 11.3137 2 8 2C4.68629 2 2 4.68629 2 8C2 11.3137 4.68629 14 8 14Z"
        stroke="#FF5630"
        stroke-width="1.5"
        stroke-miterlimit="10"
      />
      <path
        d="M8 8.5V5"
        stroke="#FF5630"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M8 11.5C8.41421 11.5 8.75 11.1642 8.75 10.75C8.75 10.3358 8.41421 10 8 10C7.58579 10 7.25 10.3358 7.25 10.75C7.25 11.1642 7.58579 11.5 8 11.5Z"
        fill="#FF5630"
      />
    </g>
    <defs>
      <clipPath id="clip0_3873_12690">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const PlayFilled = () => (
  <svg
    width="43"
    height="52"
    viewBox="0 0 43 52"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M0.125 2.59335V49.4071C0.131939 49.7807 0.237251 50.1458 0.430317 50.4657C0.623383 50.7856 0.897377 51.0489 1.22467 51.2291C1.55197 51.4093 1.921 51.5001 2.29455 51.4922C2.66809 51.4843 3.03296 51.378 3.35234 51.1841L41.6236 27.7773C41.9293 27.5922 42.1821 27.3315 42.3576 27.0202C42.533 26.7089 42.6252 26.3576 42.6252 26.0002C42.6252 25.6429 42.533 25.2916 42.3576 24.9803C42.1821 24.669 41.9293 24.4082 41.6236 24.2232L3.35234 0.816317C3.03296 0.622431 2.66809 0.516184 2.29455 0.508287C1.921 0.500391 1.55197 0.591125 1.22467 0.77134C0.897377 0.951555 0.623383 1.21488 0.430317 1.53476C0.237251 1.85465 0.131939 2.21978 0.125 2.59335Z"
      fill="#2E70E5"
    />
  </svg>
);

export const LockCircle = () => (
  <svg
    width={48}
    height={48}
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect width={48} height={48} rx={24} fill="#E8F0FC" />
    <g clipPath="url(#clip0_8043_199737)">
      <path
        d="M31.5 20.25H16.5C16.0858 20.25 15.75 20.5858 15.75 21V31.5C15.75 31.9142 16.0858 32.25 16.5 32.25H31.5C31.9142 32.25 32.25 31.9142 32.25 31.5V21C32.25 20.5858 31.9142 20.25 31.5 20.25Z"
        stroke="#2E70E5"
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M24 27.375C24.6213 27.375 25.125 26.8713 25.125 26.25C25.125 25.6287 24.6213 25.125 24 25.125C23.3787 25.125 22.875 25.6287 22.875 26.25C22.875 26.8713 23.3787 27.375 24 27.375Z"
        fill="#2E70E5"
      />
      <path
        d="M20.25 20.25V17.25C20.25 16.2554 20.6451 15.3016 21.3483 14.5983C22.0516 13.8951 23.0054 13.5 24 13.5C24.9946 13.5 25.9484 13.8951 26.6517 14.5983C27.3549 15.3016 27.75 16.2554 27.75 17.25V20.25"
        stroke="#2E70E5"
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_8043_199737">
        <rect
          width={24}
          height={24}
          fill="white"
          transform="translate(12 12)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const PeopleCircle = () => (
  <svg
    width="48"
    height="48"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect width="48" height="48" rx="24" fill="#D6E3FA" />
    <g clip-path="url(#clip0_8119_4682)">
      <path
        d="M30 23.25C30.8734 23.2493 31.7349 23.4524 32.516 23.843C33.2972 24.2335 33.9765 24.8009 34.5 25.5"
        stroke="#1857C3"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M13.5 25.5C14.0235 24.8009 14.7028 24.2335 15.484 23.843C16.2651 23.4524 17.1266 23.2493 18 23.25"
        stroke="#1857C3"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M24 29.25C26.0711 29.25 27.75 27.5711 27.75 25.5C27.75 23.4289 26.0711 21.75 24 21.75C21.9289 21.75 20.25 23.4289 20.25 25.5C20.25 27.5711 21.9289 29.25 24 29.25Z"
        stroke="#1857C3"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M18.75 32.25C19.2884 31.3364 20.0559 30.579 20.9767 30.0529C21.8974 29.5267 22.9395 29.25 24 29.25C25.0605 29.25 26.1026 29.5267 27.0233 30.0529C27.9441 30.579 28.7116 31.3364 29.25 32.25"
        stroke="#1857C3"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M27.0938 19.5C27.234 18.9568 27.5238 18.4638 27.9302 18.077C28.3366 17.6902 28.8433 17.4251 29.3927 17.3118C29.9422 17.1985 30.5125 17.2416 31.0387 17.4361C31.5649 17.6306 32.0261 17.9688 32.3697 18.4123C32.7134 18.8557 32.9258 19.3867 32.9829 19.9448C33.04 20.5029 32.9394 21.0659 32.6926 21.5697C32.4458 22.0735 32.0627 22.4981 31.5867 22.7951C31.1108 23.0921 30.561 23.2497 30 23.25"
        stroke="#1857C3"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M18.0004 23.25C17.4394 23.2497 16.8897 23.0921 16.4137 22.7951C15.9378 22.4981 15.5546 22.0735 15.3078 21.5697C15.061 21.0659 14.9604 20.5029 15.0175 19.9448C15.0746 19.3867 15.287 18.8557 15.6307 18.4123C15.9744 17.9688 16.4355 17.6306 16.9618 17.4361C17.488 17.2416 18.0582 17.1985 18.6077 17.3118C19.1572 17.4251 19.6639 17.6902 20.0703 18.077C20.4767 18.4638 20.7664 18.9568 20.9067 19.5"
        stroke="#1857C3"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_8119_4682">
        <rect
          width="24"
          height="24"
          fill="white"
          transform="translate(12 12)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const RefreshCircle = () => (
  <svg
    width="48"
    height="48"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect width="48" height="48" rx="24" fill="#D6E3FA" />
    <g clip-path="url(#clip0_8119_16588)">
      <path
        d="M18 18C18 18 20.25 15.75 24 15.75C29.25 15.75 32.25 21 32.25 21"
        stroke="#1857C3"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M30 30C30 30 27.75 32.25 24 32.25C18.75 32.25 15.75 27 15.75 27"
        stroke="#1857C3"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M27.75 21H32.25V16.5"
        stroke="#1857C3"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M20.25 27H15.75V31.5"
        stroke="#1857C3"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_8119_16588">
        <rect
          width="24"
          height="24"
          fill="white"
          transform="translate(12 12)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const OpenMail = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_8003_113603)">
      <path
        d="M2 6V12.5C2 12.6326 2.05268 12.7598 2.14645 12.8536C2.24021 12.9473 2.36739 13 2.5 13H13.5C13.6326 13 13.7598 12.9473 13.8536 12.8536C13.9473 12.7598 14 12.6326 14 12.5V6L8 2L2 6Z"
        stroke="#2E70E5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M6.9093 9.5L2.1543 12.8588"
        stroke="currentColor"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M13.8458 12.8588L9.09082 9.5"
        stroke="currentColor"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M14 6L9.09125 9.5H6.90938L2 6"
        stroke="currentColor"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_8003_113603">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const PhoneOutlined = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_8003_113552)">
      <path
        d="M10.2744 9.08374C10.3436 9.03766 10.4233 9.00959 10.5061 9.00206C10.5889 8.99453 10.6723 9.00778 10.7488 9.04061L13.6963 10.3612C13.7956 10.4037 13.8785 10.4772 13.9326 10.5707C13.9867 10.6642 14.009 10.7727 13.9963 10.88C13.8991 11.6056 13.5418 12.2713 12.9907 12.7533C12.4395 13.2352 11.7321 13.5005 11 13.5C8.74566 13.5 6.58365 12.6045 4.98959 11.0104C3.39553 9.41633 2.5 7.25433 2.5 4.99999C2.49944 4.26786 2.7648 3.56045 3.24673 3.00932C3.72865 2.45818 4.39435 2.10084 5.12 2.00374C5.22727 1.99099 5.33578 2.01333 5.4293 2.06741C5.52281 2.12149 5.5963 2.2044 5.63875 2.30374L6.95938 5.25374C6.99182 5.3295 7.00504 5.41212 6.99784 5.49423C6.99064 5.57634 6.96326 5.65539 6.91813 5.72436L5.5825 7.31249C5.53512 7.38398 5.50711 7.46654 5.50119 7.55209C5.49528 7.63765 5.51166 7.72328 5.54875 7.80061C6.06563 8.85874 7.15938 9.93936 8.22063 10.4512C8.29836 10.4882 8.38439 10.5042 8.47021 10.4977C8.55602 10.4912 8.63867 10.4625 8.71 10.4144L10.2744 9.08374Z"
        stroke="currentColor"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_8003_113552">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const LocationPin = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_8003_113558)">
      <path
        d="M8 8.5C9.10457 8.5 10 7.60457 10 6.5C10 5.39543 9.10457 4.5 8 4.5C6.89543 4.5 6 5.39543 6 6.5C6 7.60457 6.89543 8.5 8 8.5Z"
        stroke="currentColor"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M13 6.5C13 11 8 14.5 8 14.5C8 14.5 3 11 3 6.5C3 5.17392 3.52678 3.90215 4.46447 2.96447C5.40215 2.02678 6.67392 1.5 8 1.5C9.32608 1.5 10.5979 2.02678 11.5355 2.96447C12.4732 3.90215 13 5.17392 13 6.5Z"
        stroke="currentColor"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_8003_113558">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const ExclaimCircle = () => (
  <svg
    width="48"
    height="48"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect width="48" height="48" rx="24" fill="#E8F0FC" />
    <g clip-path="url(#clip0_7989_99342)">
      <path
        d="M24 24.75V19.5"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M24 29.25C24.6213 29.25 25.125 28.7463 25.125 28.125C25.125 27.5037 24.6213 27 24 27C23.3787 27 22.875 27.5037 22.875 28.125C22.875 28.7463 23.3787 29.25 24 29.25Z"
        fill="#2E70E5"
      />
      <path
        d="M23.4718 14.4686L14.4674 23.473C14.1764 23.764 14.1764 24.236 14.4674 24.527L23.4718 33.5314C23.7628 33.8224 24.2348 33.8224 24.5258 33.5314L33.5302 24.527C33.8212 24.236 33.8212 23.764 33.5302 23.473L24.5258 14.4686C24.2348 14.1776 23.7628 14.1776 23.4718 14.4686Z"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_7989_99342">
        <rect
          width="24"
          height="24"
          fill="white"
          transform="translate(12 12)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const PersonCircle = () => (
  <svg
    width="48"
    height="48"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect width="48" height="48" rx="24" fill="#E8F0FC" />
    <g clip-path="url(#clip0_8010_183904)">
      <path
        d="M24 27C27.3137 27 30 24.3137 30 21C30 17.6863 27.3137 15 24 15C20.6863 15 18 17.6863 18 21C18 24.3137 20.6863 27 24 27Z"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M15 32.25C16.8159 29.1122 20.1141 27 24 27C27.8859 27 31.1841 29.1122 33 32.25"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_8010_183904">
        <rect
          width="24"
          height="24"
          fill="white"
          transform="translate(12 12)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const Gear = () => (
  <svg
    width="25"
    height="24"
    viewBox="0 0 25 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_8010_183424)">
      <path
        d="M12.7148 15.75C14.7859 15.75 16.4648 14.0711 16.4648 12C16.4648 9.92893 14.7859 8.25 12.7148 8.25C10.6438 8.25 8.96484 9.92893 8.96484 12C8.96484 14.0711 10.6438 15.75 12.7148 15.75Z"
        stroke="currentColor"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M4.59934 16.6959C4.18494 15.9821 3.86746 15.2163 3.65527 14.4187L5.2284 12.4499C5.21059 12.149 5.21059 11.8472 5.2284 11.5462L3.65621 9.57744C3.86803 8.7797 4.18486 8.01363 4.5984 7.29932L7.10246 7.01807C7.30242 6.79278 7.51561 6.57959 7.7409 6.37963L8.02215 3.8765C8.73543 3.46494 9.50025 3.14999 10.2965 2.93994L12.2653 4.51307C12.5663 4.49525 12.868 4.49525 13.169 4.51307L15.1378 2.94088C15.9355 3.1527 16.7016 3.46953 17.4159 3.88307L17.6971 6.38713C17.9224 6.58709 18.1356 6.80028 18.3356 7.02557L20.8387 7.30682C21.2531 8.0206 21.5706 8.78639 21.7828 9.584L20.2096 11.5528C20.2275 11.8537 20.2275 12.1555 20.2096 12.4565L21.7818 14.4253C21.5715 15.2228 21.2562 15.9888 20.8443 16.7034L18.3403 16.9846C18.1403 17.2099 17.9271 17.4231 17.7018 17.6231L17.4206 20.1262C16.7068 20.5406 15.941 20.8581 15.1434 21.0703L13.1746 19.4971C12.8737 19.5149 12.5719 19.5149 12.2709 19.4971L10.3021 21.0693C9.50464 20.859 8.73858 20.5437 8.02402 20.1318L7.74277 17.6278C7.51749 17.4278 7.3043 17.2146 7.10434 16.9893L4.59934 16.6959Z"
        stroke="currentColor"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_8010_183424">
        <rect
          width="24"
          height="24"
          fill="white"
          transform="translate(0.714844)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const Minus = (props: any) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clip-path="url(#clip0_8258_49640)">
      <path
        d="M6.875 10H13.125"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M10 17.5C14.1421 17.5 17.5 14.1421 17.5 10C17.5 5.85786 14.1421 2.5 10 2.5C5.85786 2.5 2.5 5.85786 2.5 10C2.5 14.1421 5.85786 17.5 10 17.5Z"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-miterlimit="10"
      />
    </g>
    <defs>
      <clipPath id="clip0_8258_49640">
        <rect width="20" height="20" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const HandCoinsCircle = () => (
  <svg
    width="48"
    height="48"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect width="48" height="48" rx="24" fill="#E8F0FC" />
    <g clip-path="url(#clip0_8033_188471)">
      <path
        d="M31.125 22.5C32.5747 22.5 33.75 21.3247 33.75 19.875C33.75 18.4253 32.5747 17.25 31.125 17.25C29.6753 17.25 28.5 18.4253 28.5 19.875C28.5 21.3247 29.6753 22.5 31.125 22.5Z"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M16.5 31.5H13.5C13.3011 31.5 13.1103 31.421 12.9697 31.2803C12.829 31.1397 12.75 30.9489 12.75 30.75V27C12.75 26.8011 12.829 26.6103 12.9697 26.4697C13.1103 26.329 13.3011 26.25 13.5 26.25H16.5"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M22.5 27H25.5L31.7812 25.5553C32.0122 25.492 32.2546 25.4829 32.4897 25.5285C32.7247 25.5741 32.9461 25.6734 33.1365 25.8185C33.327 25.9636 33.4814 26.1507 33.5878 26.3652C33.6942 26.5797 33.7497 26.8159 33.75 27.0553C33.7501 27.3444 33.6697 27.6279 33.5176 27.8738C33.3656 28.1197 33.148 28.3184 32.8894 28.4475L29.25 30L23.25 31.5H16.5V26.25L18.8438 23.9063C19.0532 23.6975 19.3018 23.5321 19.5753 23.4195C19.8488 23.3069 20.1418 23.2493 20.4375 23.25H25.125C25.6223 23.25 26.0992 23.4476 26.4508 23.7992C26.8025 24.1508 27 24.6277 27 25.125C27 25.6223 26.8025 26.0992 26.4508 26.4508C26.0992 26.8025 25.6223 27 25.125 27H22.5Z"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M28.5002 19.996C27.9908 20.237 27.4173 20.3076 26.8646 20.1974C26.3119 20.0871 25.8093 19.802 25.4313 19.384C25.0532 18.9661 24.8197 18.4376 24.7652 17.8766C24.7108 17.3157 24.8383 16.7521 25.129 16.2693C25.4196 15.7864 25.8579 15.4099 26.3791 15.1955C26.9003 14.9811 27.4767 14.94 28.023 15.0785C28.5693 15.217 29.0565 15.5277 29.4126 15.9645C29.7687 16.4013 29.9747 16.9412 30.0002 17.5042"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_8033_188471">
        <rect
          width="24"
          height="24"
          fill="white"
          transform="translate(12 12)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const MinusCircle = () => (
  <svg
    width="48"
    height="48"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect width="48" height="48" rx="24" fill="#E8F0FC" />
    <g clip-path="url(#clip0_8033_188632)">
      <path
        d="M20.25 24H27.75"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M24 33C28.9706 33 33 28.9706 33 24C33 19.0294 28.9706 15 24 15C19.0294 15 15 19.0294 15 24C15 28.9706 19.0294 33 24 33Z"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-miterlimit="10"
      />
    </g>
    <defs>
      <clipPath id="clip0_8033_188632">
        <rect
          width="24"
          height="24"
          fill="white"
          transform="translate(12 12)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const Plugs = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_8341_9843)">
      <path
        d="M11.25 11.25L9.375 13.125"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M8.75 8.75L6.875 10.625"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M5 8.75L11.25 15"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M4.53594 15.4641L1.875 18.125"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M10.3126 14.0625L8.04698 16.3281C7.69536 16.6797 7.21846 16.8773 6.7212 16.8773C6.22394 16.8773 5.74705 16.6797 5.39542 16.3281L3.67198 14.6023C3.3211 14.2508 3.12402 13.7744 3.12402 13.2777C3.12402 12.781 3.3211 12.3047 3.67198 11.9531L5.93761 9.6875"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M8.75 5L15 11.25"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M15.4639 4.53594L18.1248 1.875"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M14.0625 10.3125L16.3281 8.04686C16.6797 7.69523 16.8773 7.21834 16.8773 6.72108C16.8773 6.22382 16.6797 5.74693 16.3281 5.3953L14.6023 3.67186C14.2508 3.32097 13.7744 3.1239 13.2777 3.1239C12.781 3.1239 12.3047 3.32097 11.9531 3.67186L9.6875 5.93749"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_8341_9843">
        <rect width="20" height="20" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const PlugsConnected = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_8341_17545)">
      <path
        d="M10.076 5.54908L5.54946 10.0757C4.81722 10.8079 4.81722 11.9951 5.54946 12.7273L7.27303 14.4509C8.00526 15.1831 9.19245 15.1831 9.92468 14.4509L14.4513 9.9243C15.1835 9.19207 15.1835 8.00488 14.4513 7.27265L12.7277 5.54908C11.9955 4.81684 10.8083 4.81684 10.076 5.54908Z"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M6.875 6.875L13.125 13.125"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M18.1248 1.875L13.5889 6.41094"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M6.41094 13.5891L1.875 18.125"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M7.5 2.5L8.125 4.0625"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M2.5 7.5L4.0625 8.125"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M15.9375 11.875L17.5 12.5"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M11.875 15.9375L12.5 17.5"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_8341_17545">
        <rect width="20" height="20" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Check = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
  >
    <g clip-path="url(#clip0_8075_126050)">
      <path
        d="M2.5 9L6 12.5L14 4.5"
        stroke="#2E70E5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_8075_126050">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const UploadSimple = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_8619_77875)">
      <path
        d="M12 14.25V3.75"
        stroke="#1857C3"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M20.25 14.25V19.5C20.25 19.6989 20.171 19.8897 20.0303 20.0303C19.8897 20.171 19.6989 20.25 19.5 20.25H4.5C4.30109 20.25 4.11032 20.171 3.96967 20.0303C3.82902 19.8897 3.75 19.6989 3.75 19.5V14.25"
        stroke="#1857C3"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M8.25 7.5L12 3.75L15.75 7.5"
        stroke="#2E70E5"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_8619_77875">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const CreditCard = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_6864_91003)">
      <path
        d="M14 3.5H2C1.72386 3.5 1.5 3.72386 1.5 4V12C1.5 12.2761 1.72386 12.5 2 12.5H14C14.2761 12.5 14.5 12.2761 14.5 12V4C14.5 3.72386 14.2761 3.5 14 3.5Z"
        stroke="#2E70E5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M10.5 10.5H12.5"
        stroke="#2E70E5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M7.5 10.5H8.5"
        stroke="#2E70E5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M1.5 6H14.5"
        stroke="#2E70E5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_6864_91003">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const PercentSign = () => (
  <svg
    width="16"
    height="17"
    viewBox="0 0 16 17"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_6864_90979)">
      <path
        d="M12.5 4L3.5 13"
        stroke="#2E70E5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M4.75 7C5.7165 7 6.5 6.2165 6.5 5.25C6.5 4.2835 5.7165 3.5 4.75 3.5C3.7835 3.5 3 4.2835 3 5.25C3 6.2165 3.7835 7 4.75 7Z"
        stroke="#2E70E5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M11.25 13.5C12.2165 13.5 13 12.7165 13 11.75C13 10.7835 12.2165 10 11.25 10C10.2835 10 9.5 10.7835 9.5 11.75C9.5 12.7165 10.2835 13.5 11.25 13.5Z"
        stroke="#2E70E5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_6864_90979">
        <rect
          width="16"
          height="16"
          fill="white"
          transform="translate(0 0.5)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const CalendarTick = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_6864_91009)">
      <path
        d="M13 2.5H3C2.72386 2.5 2.5 2.72386 2.5 3V13C2.5 13.2761 2.72386 13.5 3 13.5H13C13.2761 13.5 13.5 13.2761 13.5 13V3C13.5 2.72386 13.2761 2.5 13 2.5Z"
        stroke="#2E70E5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M11 1.5V3.5"
        stroke="#2E70E5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M5 1.5V3.5"
        stroke="#2E70E5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M2.5 5.5H13.5"
        stroke="#2E70E5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M5.75 9.5L7.25 11L10.25 8"
        stroke="#2E70E5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_6864_91009">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Calendar12 = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_6864_91015)">
      <path
        d="M13 2.5H3C2.72386 2.5 2.5 2.72386 2.5 3V13C2.5 13.2761 2.72386 13.5 3 13.5H13C13.2761 13.5 13.5 13.2761 13.5 13V3C13.5 2.72386 13.2761 2.5 13 2.5Z"
        stroke="#2E70E5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M11 1.5V3.5"
        stroke="#2E70E5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M5 1.5V3.5"
        stroke="#2E70E5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M2.5 5.5H13.5"
        stroke="#2E70E5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M5.5 8L6.5 7.5V11.5"
        stroke="#2E70E5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M8.63375 7.99997C8.70217 7.88216 8.79395 7.77957 8.90345 7.6985C9.01294 7.61743 9.13785 7.55959 9.2705 7.52853C9.40316 7.49747 9.54076 7.49384 9.67486 7.51788C9.80897 7.54191 9.93675 7.59308 10.0504 7.66827C10.164 7.74346 10.261 7.84107 10.3356 7.95512C10.4101 8.06916 10.4605 8.19724 10.4838 8.33148C10.5071 8.46572 10.5026 8.6033 10.4708 8.73577C10.439 8.86824 10.3804 8.99281 10.2987 9.10184L8.5 11.5H10.5"
        stroke="#2E70E5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_6864_91015">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const SwapIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clip-path="url(#clip0_1701_12163)">
      <path d="M7.5 15H19.5C19.6989 15 19.8897 14.921 20.0303 14.7803C20.171 14.6397 20.25 14.4489 20.25 14.25V4.5C20.25 4.30109 20.171 4.11032 20.0303 3.96967C19.8897 3.82902 19.6989 3.75 19.5 3.75H9C8.80109 3.75 8.61032 3.82902 8.46967 3.96967C8.32902 4.11032 8.25 4.30109 8.25 4.5V5.25" stroke="#2E70E5" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
      <path d="M9.75 12.75L7.5 15L9.75 17.25" stroke="#2E70E5" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
      <path d="M16.5 9H4.5C4.30109 9 4.11032 9.07902 3.96967 9.21967C3.82902 9.36032 3.75 9.55109 3.75 9.75V19.5C3.75 19.6989 3.82902 19.8897 3.96967 20.0303C4.11032 20.171 4.30109 20.25 4.5 20.25H15C15.1989 20.25 15.3897 20.171 15.5303 20.0303C15.671 19.8897 15.75 19.6989 15.75 19.5V18.75" stroke="#2E70E5" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
      <path d="M14.25 11.25L16.5 9L14.25 6.75" stroke="#2E70E5" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
    </g>
    <defs>
      <clipPath id="clip0_1701_12163">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const ReceiptIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clip-path="url(#clip0_1701_11901)">
      <path d="M7.5 9.75H16.5" stroke="#2E70E5" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
      <path d="M7.5 12.75H16.5" stroke="#2E70E5" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
      <path d="M3 19.5V5.25C3 5.05109 3.07902 4.86032 3.21967 4.71967C3.36032 4.57902 3.55109 4.5 3.75 4.5H20.25C20.4489 4.5 20.6397 4.57902 20.7803 4.71967C20.921 4.86032 21 5.05109 21 5.25V19.5L18 18L15 19.5L12 18L9 19.5L6 18L3 19.5Z" stroke="#2E70E5" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
    </g>
    <defs>
      <clipPath id="clip0_1701_11901">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
)
