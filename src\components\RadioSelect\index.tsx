import styles from "./radio-select.module.scss";
import { ControlledMenu, MenuItem, useMenuState } from "@szhsin/react-menu";
import "@szhsin/react-menu/dist/index.css";
import "@szhsin/react-menu/dist/transitions/slide.css";
import { useRef } from "react";
import Radio from "../Radio";

const RadioSelect = ({ label, options, selected, onChange, disabled }: any) => {
  const ref = useRef(null);

  const [menuProps, toggleMenu] = useMenuState({ transition: true });

  return (
    <div className={`${styles.box} select`}>
      <div
        ref={ref}
        className={`${styles.menuButton} ${disabled && styles.disabled}`}
        onClick={(e) => {
          e.stopPropagation();
          toggleMenu(true);
        }}
      >
        {label}
      </div>
      <ControlledMenu
        {...menuProps}
        anchorRef={ref}
        onClose={() => toggleMenu(false)}
        align="center"
        viewScroll="auto"
        position="auto"
      >
        {options.map((item: any) => (
          <MenuItem className={styles.menuItem} key={item.key}>
            <Radio
              checked={selected === item.key}
              onClick={(e: any) => {
                e.stopPropagation();
                onChange(item.key);
                toggleMenu(false);
              }}
            />
            <span
              style={{ marginLeft: 12 }}
              onClick={(e: any) => {
                e.stopPropagation();
                onChange(item.key);
                toggleMenu(false);
              }}
            >
              {item.label}
            </span>
          </MenuItem>
        ))}
      </ControlledMenu>
    </div>
  );
};

export default RadioSelect;
