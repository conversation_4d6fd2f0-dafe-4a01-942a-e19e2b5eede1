import { createStore } from "redux";
import { v4 as uuidv4 } from "uuid";
import {
  getCustomers,
  getProviders,
  getTickets,
  getESims,
  getRates,
  getESimProvision,
  getActivityLog,
  getNumbers,
  getCustomerTickets,
  getCustomerTransactions,
  getCustomerActivity,
  getVoiceMailCustomer,
  getNumberESims,
} from "../components/utils/dataCreator";

const initialState = {
  isLoggedIn: false,
  userInfo: localStorage.getItem("crmUserInfo")
    ? JSON.parse(localStorage.getItem("crmUserInfo") || "")
    : null,
  notifications: [],
  sidebarOpen: false,
  ticketOpen: false,
  ticket: null,
  tickets: getTickets(15),
  eSims: getESims(),
  eSimProvision: getESimProvision(),
  rates: getRates(),
  customers: getCustomers(50),
  providers: [] as any,
  activityLog: getActivityLog(100),
  numbers: getNumbers(),
  customerTickets: getCustomerTickets(),
  transactionHistory: getCustomerTransactions(),
  customerActivity: getCustomerActivity(),
  voiceMail: getVoiceMailCustomer(),
  numberEsims: getNumberESims(),
  loginMessage: null,
  closeLoginMessage: true,
  resetMessage: null,
  closeResetMessage: true,
  productType: localStorage.getItem("productType") || "",
};

const changeState = (state = initialState, { type, payload, ...rest }: any) => {
  switch (type) {
    case "set":
      return { ...state, ...rest };
    case "notify":
      let current = [...state.notifications] as any;
      current.push({ ...payload, id: uuidv4() });
      return { ...state, notifications: current };
    case "closeNotification":
      let currentOpen = [...state.notifications] as any;
      currentOpen = currentOpen.filter((item: any) => item.id !== payload);
      return { ...state, notifications: currentOpen };
    case "deleteNumber":
      const filtered = state.numbers.filter(
        (singleNumber: any) => singleNumber.id !== payload.id
      );
      return { ...state, numbers: filtered };
    default:
      return state;
  }
};

const store = createStore(changeState);
export default store;
