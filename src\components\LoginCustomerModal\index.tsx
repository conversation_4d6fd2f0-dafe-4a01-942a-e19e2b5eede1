import styles from "./login-customer.module.scss";
import Modal from "../Modal";
import { Delete } from "../svgs";
import { useState } from "react";
import { useDispatch } from "react-redux";
import { ApiPostAuth } from "src/pages/api/api";

const LoginCustomerModal = ({ show, setShow, user, mid }: any) => {
  const dispatch = useDispatch();

  const [loading, setLoading] = useState(false);

  const deleteUser = () => {
    setLoading(true);
    ApiPostAuth("/customer/access/account", {
      mid: mid,
    })
      .then((response) => {
        setLoading(false);
        window.open(
          `https://gist.mobilisedev.co.uk/crm-login?auth=${response.data.token}`,
          "_blank",
        );
      })
      .catch((error) => {
        setLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: error.response.data.message,
          },
        });
      });
  };

  return (
    <Modal
      saveButton={<>Log into account</>}
      image="/bulk_edit_confirm_graphic.svg"
      show={show}
      close={() => {
        setShow(false);
      }}
      proceed={deleteUser}
      loading={loading}
    >
      <div className={styles.main}>
        <h3>Log into {user}'s account?</h3>
      </div>
    </Modal>
  );
};

export default LoginCustomerModal;
