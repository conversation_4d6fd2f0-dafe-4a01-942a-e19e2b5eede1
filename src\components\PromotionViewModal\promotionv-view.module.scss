@use "../../styles/theme.scss" as *;
@import "../../styles/table-mixin.module.scss";
@include table;

.tableContainer {
  overflow: auto;
  padding-bottom: 5px;
}

.container {
  position: fixed;
  top: 0px;
  left: 0px;
  z-index: 999;
  width: 100%;
  height: 100vh;
  background: rgba(12, 44, 100, 0.7);
  padding: 32px;
  align-items: center;
  justify-content: center;
  display: flex;
}

.modal {
  width: 100%;
  max-width: 1302px;
  max-height: 100%;
  background: #fff;
  border-radius: 24px;
  padding: 20px;
  position: relative;
  .overview {
    .head {
      align-items: center;
      p {
        font-size: 14px;
        color: #667085;
      }
    }
    .detailsContainer {
      margin-top: 10px;
      width: 25%;
      border: 1px solid #dfe2e7;
      border-radius: 12px;
      padding: 10px 15px;
      .title {
        font-size: 14px;
        font-weight: 700;
        color: #061632;
      }
      .details {
        margin-top: 10px;
        .feature {
          display: flex;
          align-items: end;
          margin-bottom: 7px;
          .icon {
            @include circledIcon;
            margin-right: 8px;
            svg {
              width: 16px;
              height: 16px;
            }
          }
          .data {
            .title {
              font-size: 12px;
              font-weight: 400;
              line-height: 15px;
              color: #667085;
              margin-bottom: 2px;
            }
            .value {
              font-size: 14px;
              line-height: 18px;
              color: #061632;
            }
          }
        }
      }
    }
  }
  .close {
    @include circledIcon;
    position: absolute;
    top: 10px;
    right: 20px;
    cursor: pointer;
    svg {
      width: 20px;
    }
  }
  .title {
    font-size: 20px;
    font-weight: 700;
    line-height: 28px;
    margin-bottom: 6px;
  }
  .menuBar {
    display: flex;
    justify-content: space-between;
    .actionButtons {
      display: flex;
      align-items: center;
      button {
        justify-content: center;
        flex-basis: fit-content;
      }
    }
  }
}
