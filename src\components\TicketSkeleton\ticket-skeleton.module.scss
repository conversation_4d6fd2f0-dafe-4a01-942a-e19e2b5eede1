@use "../../styles/theme.scss" as *;

.main {
  width: 100%;
  padding: 22.5px 32px;
  border-radius: 12px;
  background: #f7f6f6;
  position: relative;
  overflow: hidden;
}

.summary {
  display: flex;
  align-items: center;
}

.overview {
  padding-left: 24px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-right: 5px;
  .top {
    display: flex;
    align-items: center;
    gap: 12px;
    .id {
      height: 20px;
      width: 40px;
      background-color: $skeleton;
      border-radius: 4px;
      position: relative;
      overflow: hidden;
    }
    .date {
      height: 20px;
      width: 80px;
      background-color: $skeleton;
      border-radius: 4px;
      position: relative;
      overflow: hidden;
    }
  }
  .bottom {
    display: flex;
    align-items: center;
    gap: 12px;
    .subject {
      height: 20px;
      width: 270px;
      background-color: $skeleton;
      border-radius: 4px;
      position: relative;
      overflow: hidden;
    }
    .email {
      height: 20px;
      width: 192px;
      background-color: $skeleton;
      border-radius: 4px;
      position: relative;
      overflow: hidden;
    }
  }
}

.expand {
  margin-left: 25px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  &.rotate {
    transform: rotate(180deg);
  }
}

.dataBar {
  display: flex;
  align-items: center;
  margin-left: auto;
  gap: 12px;
  .box {
    height: 41px;
    background-color: $skeleton;
    border-radius: 4px;
    position: relative;
    overflow: hidden;
  }
  .box1 {
    width: 76px;
  }
  .box2 {
    width: 44px;
  }
  .box3 {
    width: 41px;
  }
  .box4 {
    width: 100px;
  }
}
