import { getTickets } from "@/components/utils/dataCreator";
import { useEffect, useState } from "react";
import UserSkeleton from "@/components/UserSkeleton";
import TableControl from "@/components/TableControl";
import styles from "../../styles/tickets.module.scss";
import { formatDateWithTime } from "@/components/utils/formatDate";
import { ticketsFields } from "../utils/ticketsFields";
import Button from "../Button";
import { Delete, Export, Pencil, Settings } from "../svgs";
import Priority, { convertPriority } from "../Priority";
import StatusBadge, { convertStatus } from "../StatusBadge";
import ProductsMenu from "../ProductsMenu";
import DeleteTicketModal from "../DeleteTicketModal/DeleteTicketModal";
import CustomerTicket from "../CustomerTickets";
import CreateTicketModal from "../CreateTicketModal";
import Dialog from "../Dialog";

const TicketsTable = ({ tab }: { tab: string }) => {
  const [loading, setLoading] = useState(false);
  const [tickets, setTickets] = useState([]);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [numberOfPages, setNumberOfPages] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showTicket, setShowTicket] = useState(false);
  const [activeTicket, setActiveTicket] = useState(null as any);

  const populate = (itemsPerPage: number) => {
    setLoading(true);
    setTimeout(() => {
      setTickets(getTickets(itemsPerPage));
      setLoading(false);
    }, 500);
  };

  useEffect(() => {
    populate(15);
  }, []);

  const handleActions = (ticket: any, action: "delete" | "edit" | "view") => {
    setActiveTicket(ticket);
    if (action === "delete") {
      setShowDeleteModal(true);
    } else if (action === "edit") {
      setShowEditModal(true);
    } else {
      setShowTicket(true);
    }
  };

  const formatDataItem = (item: any, key: string) => {
    if (key === "creationDate" || key === "dueDate") {
      return formatDateWithTime(item[key]);
    } else if (key === "status") {
      const status = convertStatus(item[key]);
      return <StatusBadge status={status} />;
    } else if (key === "assignee") {
      if (tab === "unassigned") {
        return "-";
      } else {
        return item[key];
      }
    } else if (key === "priority") {
      const priority = convertPriority(item[key]);
      return <Priority priority={priority} />;
    } else if (key === "description") {
      return (
        <div style={{ width: "100%", maxWidth: "200px" }}>{item[key]}</div>
      );
    } else {
      return item[key];
    }
  };

  return (
    <div className={styles.panel}>
      <div className={styles.panelTopBar}>
        <h4>My Tickets</h4>
        <div className={styles.actions}>
          <Button color="secondary">
            <Export /> Export to CSV
          </Button>
        </div>
      </div>
      {activeTicket && showEditModal && (
        <CreateTicketModal
          show={showEditModal}
          setShow={() => setShowEditModal(false)}
          ticket={activeTicket}
          repopulate={populate}
        />
      )}
      {activeTicket && showDeleteModal && (
        <DeleteTicketModal
          show={showDeleteModal}
          setShow={() => setShowDeleteModal(false)}
          ticket={activeTicket}
          repopulate={populate}
        />
      )}
      {activeTicket && showTicket && (
        <Dialog
          size="lg"
          open={showTicket}
          heightInPx={600}
          onClose={() => setShowTicket(false)}
        >
          <CustomerTicket
            ticket={activeTicket}
            key={"ticket-comp-" + activeTicket?.id}
            disabled={loading}
            size
            ticketsStyle
            closeFn={() => setShowTicket(false)}
          />
        </Dialog>
      )}
      <div className={`${styles.tableContainer} table-scroll`}>
        <table>
          <thead>
            <tr>
              {ticketsFields.map((field: any) => (
                <th
                  style={{
                    width: field.width,
                    minWidth: field.width || "135px",
                  }}
                >
                  {field.label}
                </th>
              ))}
              <th style={{ width: "97px" }}></th>
            </tr>
          </thead>
          <tbody>
            {!loading
              ? tickets.map((item: any, i: number) => {
                  return (
                    <tr key={`ticket-${item.id}`} style={{ cursor: "pointer" }}>
                      {ticketsFields.map((field: any) => (
                        <td key={`ticket-${item.id}-${field.key}`}>
                          {formatDataItem(item, field.key)}
                        </td>
                      ))}
                      <td style={{ paddingInline: 0 }}>
                        <span className={styles.viewRowBtn + " ticketsMenu"}>
                          <ProductsMenu
                            data={{
                              icon: <Settings />,
                              items: [
                                {
                                  label: "Edit Ticket",
                                  icon: <Pencil />,
                                  onClick: () => handleActions(item, "edit"),
                                },
                                {
                                  label: "Delete Ticket",
                                  icon: <Delete />,
                                  onClick: () => handleActions(item, "delete"),
                                },
                              ],
                            }}
                            portal
                          />
                          <span
                            className={styles.rowActionBtn}
                            onClick={() => handleActions(item, "view")}
                          >
                            View
                          </span>
                        </span>
                      </td>
                    </tr>
                  );
                })
              : Array.from({ length: itemsPerPage }, (v, i) => i).map((i) => (
                  <UserSkeleton key={"ticket-skeleton-" + i} noOfStandard={8} />
                ))}
          </tbody>
        </table>
      </div>
      <div style={{ marginTop: "16px" }}>
        <TableControl
          show
          itemsPerPage={itemsPerPage}
          setItemsPerPage={(val: any) => setItemsPerPage(val)}
          currentPage={currentPage}
          setCurrentPage={setCurrentPage}
          numberOfPages={numberOfPages}
          label="tickets"
          loading={loading}
        />
      </div>
    </div>
  );
};

export default TicketsTable;
