@import "./mixins.module.scss";
@import "./table-mixin.module.scss";

.main {
  padding: 16px;
}

.panel {
  @include panel;
  @include table;

  margin-top: 16px;
}

.actions {
  display: flex;
  justify-content: end;
}

.tableContainer {
  overflow: auto;
  padding-bottom: 5px;
  margin-top: 20px;
  border-radius: 16px;
}

.fields {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-top: 20px;
}

.panelTopBar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;

  h4 {
    margin: 0;
    font-size: 18px;
    font-weight: 700;
  }
}
