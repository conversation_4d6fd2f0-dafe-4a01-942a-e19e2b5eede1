import { checkName, isNumeric } from "./CardDetailsCheckers";

const rules = {
  email: "required|email",
  password:
    "regex:^(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[!@#$.%^&*]).*$|required|min:6",
  confirmPassword:
    "regex:^(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[!@#$.%^&*]).*$|required|min:6",
  newPassword:
    "regex:^(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[!@#$.%^&*]).*$|required|min:6",
  confirmNewPassword:
    "regex:^(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[!@#$.%^&*]).*$|required|min:6|same:newPassword",
  currentPassword:
    "regex:^(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[!@#$.%^&*]).*$|required|min:6",
  name: "required|min:2",
  imei: "required|min:15",
  sim: "required|min:18",
  iccid: "required|min:18",
  firstName: "required",
  lastName: "required",
  role: "required",
  address: "required",
  streetNumber: "required",
  streetName: "required",
  city: "required|min:2",
  state: "required|min:2",
  zipCode: "required|min:4",
  phoneNumber: "required|min:7",
  notificationHeading: "required",
  notificationBody: "required",
  creditAmount: "required|number",
  creditReason: "required",
  debitAmount: "required|number",
  debitReason: "required",
  subject: "required",
  body: "required",
  note: "required",
  prefix: "required",
  mode: "required",
  inboundIP: "required",
  outboundIP: "required",
  allowCodecs: "required",
  refundAmount: "required",
  refundReason: "required",
  usd: "required",
  gbp: "required",
  eur: "required",
  callAllowance: "required",
  minutesAllowance: "required",
  dataAllowance: "required",
  smsAllowance: "required",
  country: "required",
  validity: "required",
  channelName: "Name",
  channelDescription: "Description",
  channelEmail: "Email Address",
  channelPhoneNumber: "Phone Number",
} as any;

const messages = {
  "email.required":
    "Please enter a valid email address. Example: <EMAIL>",
  "email.email":
    "Please enter a valid email address. Example: <EMAIL>",
  "currentPassword.required": "Please enter your current password",
  "currentPassword.min": "Please enter a valid password",
  "currentPassword.regex":
    "Invalid password format. Passwords should be at least 6 characters long and contain one capital letter and one number and one symbol",
  "password.min": "Please enter a valid password",
  "password.required": "Please enter your password",
  "confirmPassword.min": "Please enter a valid password",
  "confirmPassword.required": "Please confirm your password",
  "confirmPassword.regex":
    "Invalid password format. Passwords should be at least 6 characters long and contain one capital letter and one number and one symbol",
  "newPassword.required": "Please enter a password",
  "newPassword.regex":
    "Invalid password format. Passwords should be at least 6 characters long and contain one capital letter and one number and one symbol",
  "newPassword.min": "Please enter a valid password",
  "confirmNewPassword.min": "Please enter a valid password",
  "confirmNewPassword.required": "Please confirm your password",
  "confirmNewPassword.regex":
    "Invalid password format. Passwords should be at least 6 characters long and contain one capital letter and one number and one symbol",
  "confirmNewPassword.same": "Passwords do not match",
  "name.required": "Please enter name",
  "name.min": "Please enter name",
  "imei.required": "Please enter IMEI",
  "imei.min": "IMEI should be 15 digits",
  "sim.required": "Please enter SIM number",
  "sim.min": "SIM number should be 18-22 digits",
  "iccid.required": "Please enter ICCID number",
  "iccid.min": "ICCID number should be 18-22 digits",
  "firstName.required": "Please enter a first name",
  "lastName.required": "Please enter a last name",
  "role.required": "Please select a role",
  "address.required": "Please enter your address",
  "streetNumber.required": "Please enter street number",
  "streetName.required": "Please enter street name",
  "city.required": "Please enter city",
  "city.min": "Please enter city",
  "zipCode.required": "Please enter post code",
  "zipCode.min": "Please enter a valid post code",
  "state.required": "Please enter state",
  "state.min": "Enter a valid state",
  "phoneNumber.required": "Please enter a phone number",
  "phoneNumber.min": "Please enter a valid phone number",
  "notificationHeading.required": "Please enter a heading",
  "notificationBody.required": "Please enter a body",
  "creditAmount.required": "Please enter a credit amount",
  "creditAmount.number": "Please enter a valid credit amount",
  "creditReason.required": "Please enter a credit reason",
  "debitAmount.required": "Please enter a debit amount",
  "debitAmount.number": "Please enter a valid debit amount",
  "debitReason.required": "Please enter a debit reason",
  "subject.required": "Please add a subject",
  "body.required": "Please add a body for your email",
  "note.required": "Please add something to your note",
  "prefix.required": "Please add a prefix",
  "mode.required": "Please add a mode",
  "inboundIP.required": "Please add an In Bound IP",
  "outboundIP.required": "Please add an Out Bound IP",
  "allowCodecs.required": "Please add allowed codecs",
  "refundAmount.required": "Please enter a refund amount",
  "refundReason.required": "Please enter a refund reason",
  "usd.required": "Required",
  "gbp.required": "Required",
  "eur.required": "Required",
  "callAllowance.required": "Please add a call allowance",
  "minutesAllowance.required": "Please add a call allowance",
  "dataAllowance.required": "Please add a data allowance",
  "smsAllowance.required": "Please add a SMS allowance",
  "country.required": "Please select a country",
  "validity.required": "Please add a validity",
  "channelName.required": "Please add channel name",
  "channelDescription.required": "Please add channel description",
  "channelEmail.required": "Please add an email address",
  "channelEmail.email": "Please enter a valid email address",
  "channelPhoneNumber.required": "Please add a phone number",
  "channel.required": "Please select a channel",
} as any;

export const labels = {
  sim: "SIM Number",
  message: "Message",
  subject: "Subject",
  country: "Country",
  firstName: "First Name",
  lastName: "Last name",
  buyRate: "Buy Rate",
  sellRate: "Sell Rate",
  mode: "Mode",
  inBoundIp: "Inbound IP",
  outBoundIp: "Outbound IP",
  didNumber: "DID Number",
  numberType: "Number Type",
  rateType: "Rate Type",
  type: "Type",
  prefix: "Prefix",
  name: "Name",
  validity: "Validity (days)",
  billingAccountNumber: 'Billing Account Number',
  pinPassword: 'PIN/Password',
  voiceAllowance: "Voice Allowance",
  usd: "USD Price",
  gbp: "GBP Price",
  eur: "EUR Price",
  role: "Role",
  msisdn: "MSISDN",
  availability: 'Availability',
  currentCycle: 'Current Bill Cycle',
  currentSubs: 'Current Subscription',
  newSubs: 'New Subscription',
  newCycle: 'New Bill Cycle',
  currency: "Currency",
  email: "Email Address",
  password: "Password",
  confirmPassword: "Confirm Password",
  currentPassword: "Current Password",
  currentIccid: "Current ICCID",
  newPassword: "New Password",
  confirmNewPassword: "Confirm New Password",
  address: "Street Address",
  streetNumber: "Street Number",
  streetName: "Street Name",
  streetDirection: "Street direction",
  city: "City",
  state: "State",
  zipCode: "Zip Code",
  phoneNumber: "Phone Number",
  imei: "IMEI",
  creationDate: "Created on",
  startDate: "Start date",
  expiryDate: "Expiry date",
  newImei: "New IMEI",
  portInNumber: "Port-in Number",
  iccid: "ICCID",
  newIccid: "New ICCID",
  mdn: "MDN",
  ban: "BAN",
  timesUsed: "Times Used",
  plan: 'Plan',
  accountType: 'Account type',
  carrier: 'Carrier',
  notificationHeading: "Heading",
  notificationBody: "Body",
  refundAmount: "Refund Amount",
  refundReason: "Refund Reason",
  title: "Title",
  body: "Body",
  customerName: "Customer Name",
  assignee: 'Assignee',
  addressLine: "Address Line",
  addressLine2: "Address Line 2 (optional)",
  creditAmount: "Credit Amount",
  creditReason: "Credit Reason",
  debitAmount: "Debit Amount",
  debitReason: "Debit Reason",
  callAllowance: "Call Allowance (Mins)",
  minutesAllowance: "Call Allowance (Mins)",
  dataAllowance: "Data Allowance (GB)",
  smsAllowance: "SMS Allowance (SMS)",
  channel: "Channel",
  channelName: "Name",
  channelDescription: "Description",
  channelEmail: "Email Address",
  channelPhoneNumber: "Phone Number",
  orderNumber: "Order Number",
  subscriber: "Subscriber",
  simType: "SIM Type",
  effectiveDate: "Effective date",
  status: "Status",
  priority: "Priority",
  category: "Category",
  productName: "Product Name",
  provider: "Provider",
  family: "Family",
  sizeGB: "Size (GB)",
  billing: "Billing",
  wholesaleName: "Wholesale Name",
  wholesalePrice: "Wholesale Price",
  talkAndText: "Talk & Text",
  approach: "Approach",
  addToRetail: "Add to Retail",
  retailName: "Retail Name",
  description: "Description",
  retailPrice: "Retail Price",
  price: "Price",
  discountType: "Discount Type",
  discountAmount: "Discount Amount",
  applyTo: "Applies to",
  visibleOnRetail: "Visible on Retail",
  amount: "Amount",
  promocode: "Promocode",
  feeDiscount: "Fee to discount",
  coderesuable: "Code Reusable?",
  maxuses: "Max Code Uses",
  usdPrice: "USD Price",
  euroPrice: "EUR Price",
  eurPrice: "EUR Price",
  gbpPrice: "GBP Price",
  dueDate: "Due date",
  user: "User",
  ipAddress: "IP Address",
  dateTime: "Date & Time",
  activity: "Activity Type",
  simNo: "Sim No.",
  contactNumber: "Contact Number",
  reason: "Reason",
  allowCodecs: "Allow Codecs",
  providerName: "Provider Name",
} as any;

export const streetDirectionValues = ['S', 'N', 'E', 'W', 'NE', 'NW', 'SE', 'SW']

export const serviceTypesValues = ['National', 'International']

export const placeholders = {
  subject: "Enter subject",
  message: "Enter message",
  email: "Enter user's email address",
  sellRate: "Enter sell rate",
  buyRate: "Enter buy rate",
  mode: "Enter mode",
  inboundIP: "Enter inbound IP",
  outboundIP: "Enter outbound IP",
  prefix: "Enter prefix",
  name: "Enter name",
  validity: "Enter Validity",
  voiceAllowance: "Enter Voice Allowance",
  usd: "USD ",
  gbp: " GBP",
  eur: " EUR",
  password: "Enter your password",
  confirmPassword: "Confirm your password",
  newPassword: "New Password",
  confirmNewPassword: "Confirm New Password",
  imei: "Network recognized device IMEI",
  portInNumber: "Enter port in number",
  sim: "SIM Number",
  iccid: "ICCID",
  firstName: "Enter first name",
  lastName: "Enter last name",
  role: "Role",
  address: "Street Address",
  streetNumber: "Street Number",
  streetName: "Street Name",
  city: "City",
  state: "State",
  zipCode: "Zip Code",
  phoneNumber: "Phone Number",
  notificationHeading: "Enter a heading",
  notificationBody: "Enter a body",
  creditAmount: "Credit Amount",
  creditReason: "Credit Reason",
  debitAmount: "Debit Amount",
  debitReason: "Debit Reason",
  priority: "Priority",
  category: "Category",
  status: "Status",
  callAllowance: "Call Allowance (Mins)",
  minutesAllowance: "Call Allowance (Mins)",
  dataAllowance: "Data Allowance (GB)",
  smsAllowance: "SMS Allowance (SMS)",
  channelName: "Name",
  channelDescription: "Description",
  channelEmail: "Email Address",
  channelPhoneNumber: "Phone Number",
} as any;

export const getRules = (fields: any) => {
  let obj = {} as any;
  fields.forEach((field: string) => {
    obj[field] = rules[field];
  });
  return obj;
};

export const getMessages = (fields: any) => {
  let messageKeys = Object.keys(messages);
  messageKeys = messageKeys.filter((key: any) =>
    fields.some((field: any) => key.includes(field))
  );
  let obj = {} as any;
  messageKeys.forEach((key: string) => {
    obj[key] = messages[key];
  });
  return obj;
};

export const createStateObject = (arr: any, init = "") => {
  let obj = {} as any;
  let errObj = {} as any;
  for (let i = 0; i < arr.length; i++) {
    obj[arr[i]] = init;
    errObj[arr[i]] = "";
  }
  let objWithErrors = {
    ...obj,
    errors: {
      ...errObj,
    },
  };
  return objWithErrors;
};

export const createStateObjectWithInitialState = (stateObj: any, initialState: any) => {
  const result = {} as any

  Object.entries(stateObj).map(([field, value]) => {
    if (field === 'errors') {
      // preserve errors field
      result[field] = value
    } else {
      result[field] = initialState[field] || value
    }
  })

  return result
}

export const clearInput = (prop: any, setState: any) => {
  setState((prev: any) => {
    return {
      ...prev,
      [prop]: "",
      errors: {
        ...prev.errors,
        [prop]: "",
      },
    };
  });
};

export const displayErrors = (errors: any, setState: any) => {
  let formattedErrors = {} as any;
  errors.forEach((error: any) => {
    formattedErrors[error.field] = error.message;
  });
  setState((prev: any) => {
    return {
      ...prev,
      errors: formattedErrors,
    };
  });
};

// Handles change in card information inputs
export const handleInputChange = (
  prop: string,
  e: any,
  state: any,
  setState: any,
  type?: 'select' | 'input'
) => {
  const set = (value: any) => {
    setState((prev: any) => {
      return {
        ...prev,
        [prop]: value,
        errors: {
          ...prev.errors,
          [prop]: "",
        },
      };
    });
  };

  if (type === 'select') {
    set(e)
  } else if (prop === "name" || prop === "firstName" || prop === "lastName") {
    if (checkName(e.target.value) && e.target.value.length <= 60) {
      set(e.target.value);
    }
  } else if (prop === 'streetDirection' || prop === 'plan' || prop === 'serviceType') {
    set(e.target.innerText);
  } else if (prop === "imei") {
    if (
      (e.target.value.length <= 15 && isNumeric(e.target.value)) ||
      e.target.value === ""
    ) {
      set(e.target.value);
    }
  } else if (prop === "sim") {
    if (
      (e.target.value.length <= 22 && isNumeric(e.target.value)) ||
      e.target.value === ""
    ) {
      set(e.target.value);
    }
  } else if (prop === "notificationHeading") {
    if (e.target.value.length <= 65) {
      set(e.target.value);
    }
  } else if (prop === "notificationBody") {
    if (e.target.value.length <= 240) {
      set(e.target.value);
    }
  } else {
    set(e.target.value);
  }
};
