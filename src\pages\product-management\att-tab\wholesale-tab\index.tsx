import CollapsiblePanel from "@/components/CollapsiblePanel";
import styles from "./wholesale-tab.module.scss";
import InsightList from "@/components/InsightList";
import { useCallback, useEffect, useRef, useState } from "react";
import InsightCard from "@/components/InsightCard";
import Button from "@/components/Button";
import {
  Delete,
  Export,
  MagnifyingGlass,
  Pencil,
  Plus,
} from "@/components/svgs";
import Tag from "@/components/Tag";
import { Input } from "@/components/Input";
import {
  clearInput,
  createStateObject,
  handleInputChange,
  labels,
} from "@/components/utils/InputHandlers";
import SelectInput from "@/components/SelectInput";
import { getProducts } from "@/components/utils/dataCreator";
import { getProductFields } from "@/components/utils/productFields";
import UserSkeleton from "@/components/UserSkeleton";
import TableControl from "@/components/TableControl";
import { formatDateWords } from "@/components/utils/formatDate";
import CreateProductModal from "@/components/CreateProductModal";
import EditWholesaleProductModal from "@/components/EditWholesaleProductModal";
import DeleteProductModal from "@/components/DeleteProductModal";
import CheckboxDropdownInput from "@/components/CheckboxDropdownInput";

const WholeSaleTab = () => {
  const searchOrdersFields = ["family", "name", "sizeGB", "billing"];
  const [data, setData] = useState(createStateObject(searchOrdersFields));

  const overviewPanelRef = useRef<any>(null);
  const searchPanelRef = useRef<any>(null);
  const [showSearchResults, setShowSearchResults] = useState(false);

  const [loading, setLoading] = useState(true);
  const [products, setProducts] = useState<any[]>([]);

  const populate = (itemsPerPage: number) => {
    setLoading(true);

    setTimeout(() => {
      setProducts(getProducts(itemsPerPage, "wholesale"));
      setLoading(false);
    }, 500);
  };

  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [numberOfPages, setNumberOfPages] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  useEffect(() => {
    populate(itemsPerPage);
  }, [itemsPerPage, currentPage]);

  const formatDataItem = (item: any, key: string) => {
    if (!item[key]) {
      return "-";
    }

    if (key === "creationDate") {
      return formatDateWords(item[key]);
    } else {
      return item[key];
    }
  };

  const [showCreateProductModal, setShowCreateProductModal] = useState(false);
  const [activelyEditedProduct, setActivelyEditedProduct] = useState(null);
  const [activelyDeletedProduct, setActivelyDeletedProduct] = useState(null);

  const renderCreateProductButton = useCallback(
    () => (
      <Button
        onClick={(e) => {
          e.stopPropagation();
          setShowCreateProductModal(true);
        }}
      >
        <Plus />
        Create Product
      </Button>
    ),
    []
  );

  return (
    <div>
      <CreateProductModal
        open={showCreateProductModal}
        onClose={() => setShowCreateProductModal(false)}
      />
      {!!activelyEditedProduct && (
        <EditWholesaleProductModal
          onClose={() => setActivelyEditedProduct(null)}
          open={!!activelyEditedProduct}
          productData={activelyEditedProduct}
        />
      )}
      {!!activelyDeletedProduct && (
        <DeleteProductModal
          onClose={() => setActivelyDeletedProduct(null)}
          open={!!activelyDeletedProduct}
          productData={activelyDeletedProduct}
          type="wholesale"
        />
      )}
      {/* Overview */}
      <CollapsiblePanel
        title="Overview"
        summaryWhenClosed={
          <div className={styles.overviewSummaryWrapper}>
            <InsightList insights={overviewStats} />
            {renderCreateProductButton()}
          </div>
        }
        headerWhenOpen={
          <div className={styles.overviewHeaderOpenWrapper}>
            {renderCreateProductButton()}
          </div>
        }
        ref={overviewPanelRef}
      >
        <div className={styles.overview}>
          {overviewStats.map((stat, index) => (
            <InsightCard
              key={index}
              title={stat.title}
              value={stat.value.toString()}
            />
          ))}
        </div>
      </CollapsiblePanel>

      {/* Search */}
      <div style={{ marginTop: 16 }}>
        <CollapsiblePanel
          title="Search Wholesale Products"
          summaryWhenClosed={
            <div
              style={{
                display: "flex",
                flex: 1,
                justifyContent: "space-between",
                marginLeft: 16,
                marginRight: 8,
              }}
            >
              <Tag text="3 filters applied" />
              <Button color="secondary">Clear Filters</Button>
            </div>
          }
          ref={searchPanelRef}
        >
          <div className={styles.searchPanelForm}>
            <div className={styles.fields}>
              {searchOrdersFields.map((prop) => {
                if (["name"].includes(prop)) {
                  return (
                    <Input
                      key={"att-" + prop}
                      label={labels[prop]}
                      value={data[prop]}
                      onChange={(e: any) => {
                        handleInputChange(prop, e, data, setData);
                      }}
                      error={data.errors[prop]}
                      clear={() => {
                        clearInput(prop, setData);
                      }}
                      infoTooltipText
                    />
                  );
                } else if (["family", "sizeGB", "billing"].includes(prop)) {
                  return (
                    <CheckboxDropdownInput
                      key={"promotions-" + prop}
                      options={selectOptionsByField[prop]}
                      label={labels[prop]}
                      selected={data[prop]}
                      onChange={(values) => {
                        handleInputChange(
                          prop,
                          values,
                          data,
                          setData,
                          "select"
                        );
                      }}
                      error={data.errors[prop]}
                      infoTooltipText
                    />
                  );
                }
              })}
            </div>
            <div className={styles.actions}>
              <Button
                color="blue"
                onClick={() => {
                  searchPanelRef.current.close();
                  overviewPanelRef.current.close();
                  setShowSearchResults(true);
                }}
              >
                <MagnifyingGlass /> Search
              </Button>
            </div>
          </div>
        </CollapsiblePanel>
      </div>

      {/* Table */}
      {showSearchResults && (
        <div className={styles.panel}>
          <div className={styles.panelTopBar}>
            <h4>Products</h4>
            <div className={styles.actions}>
              <Button color="secondary">
                <Export /> Export to CSV
              </Button>
            </div>
          </div>

          <div className={`${styles.tableContainer} table-scroll`}>
            <table>
              <thead>
                <tr>
                  {getProductFields("wholesale").map((field: any) => (
                    <th>{field.label}</th>
                  ))}
                  <th></th>
                </tr>
              </thead>
              <tbody>
                {!loading
                  ? products.map((item: any, i: number) => {
                      return (
                        <tr key={`product-${i}`}>
                          {getProductFields("wholesale").map((field: any) => (
                            <td key={`product-${i}-${field.key}`}>
                              <div
                                style={{
                                  display: "flex",
                                  justifyContent: "flex-start",
                                }}
                              >
                                {formatDataItem(item, field.key)}
                              </div>
                            </td>
                          ))}
                          <td style={{ width: 100 }}>
                            <div className={styles.tableRowActions}>
                              <span
                                onClick={() => setActivelyEditedProduct(item)}
                              >
                                <Pencil />
                              </span>
                              <span
                                onClick={() => setActivelyDeletedProduct(item)}
                              >
                                <Delete />
                              </span>
                            </div>
                          </td>
                        </tr>
                      );
                    })
                  : Array.from({ length: itemsPerPage }, (v, i) => i).map(
                      (i) => (
                        <UserSkeleton
                          key={"order-skeleton-" + i}
                          noOfStandard={11}
                        />
                      )
                    )}
              </tbody>
            </table>
          </div>
          <div style={{ marginTop: "16px" }}>
            <TableControl
              show
              itemsPerPage={itemsPerPage}
              setItemsPerPage={(val: any) => setItemsPerPage(val)}
              currentPage={currentPage}
              setCurrentPage={setCurrentPage}
              numberOfPages={numberOfPages}
              label="orders"
              loading={loading}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default WholeSaleTab;

const overviewStats = [
  { title: "Total Products", value: "63,629" },
  { title: "Data Only", value: "63,629" },
  { title: "Data & Voice", value: "63,629" },
  { title: "Families", value: "63,629" },
];

const selectOptionsByField: Record<string, any> = {
  family: [
    { label: "Velocity suite", value: "Velocity suite" },
    { label: "Raging Thunder", value: "Raging Thunder" },
  ],
  sizeGB: [
    { label: "1gb", value: "1gb" },
    { label: "2gb", value: "2gb" },
  ],
  billing: [
    { label: "postpaid", value: "postpaid" },
    { label: "prepaid", value: "prepaid" },
  ],
};
