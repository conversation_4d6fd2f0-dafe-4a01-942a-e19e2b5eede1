import styles from "./select.module.scss";
import Select from "react-select";

const SelectDropdown = ({
  value,
  error,
  onChange,
  options,
  disabled,
  placeholder,
  dropDownMaxHeight,
  white,
}: any) => {
  const root = getComputedStyle(document.getElementById("root")!);
  const customStyles = {
    control: (baseStyles: any, state: any) => ({
      ...baseStyles,
      height: 56,
      marginBottom: 0,
      borderRadius: state.isFocused ? "8px 8px 0 0" : 8,
      borderColor: error
        ? "#f04337"
        : disabled
          ? "rgba(0, 0, 20, 0.12)"
          : state.isFocused
            ? root.getPropertyValue("--orange")
            : "#74767e",
      boxShadow: error
        ? "0px 0px 0px 1px #f04337"
        : state.isFocused
          ? `0px 0px 0px 1px ${root.getPropertyValue("--orange")}`
          : "none",
      color: "#000",
      background: "none",
      "&:hover": {
        borderColor: state.isFocused
          ? root.getPropertyValue("--orange")
          : error
            ? "#ec5b60"
            : "#000",
      },
    }),
    valueContainer: (baseStyles: any, state: any) => ({
      ...baseStyles,
      padding: "0px 12px",
      color: error ? "#f04337" : disabled ? "rgba(26, 26, 26, 0.38)" : "#000",
    }),
    menu: (baseStyles: any, state: any) => ({
      ...baseStyles,
      borderRadius: "0px 0px 8px 8px",
      border: "none",
      marginTop: 0,
      zIndex: 3000,
    }),
    menuList: (baseStyles: any, state: any) => ({
      ...baseStyles,
      padding: 0,
      maxHeight: dropDownMaxHeight,
      "::-webkit-scrollbar": {
        width: "10px",
        height: "0px",
        borderRadius: "9999px",
      },
      "::-webkit-scrollbar-track": {
        background: "#fde5d4",
        borderRadius: "9999px",
      },
      "::-webkit-scrollbar-thumb": {
        background: "#f47d27",
        borderRadius: "9999px",
      },
      "::-webkit-scrollbar-thumb:hover": {
        background: "#f47d27",
      },
    }),
    option: (baseStyles: any, state: any) => ({
      ...baseStyles,
      height: 56,
      padding: "0 16px",
      lineHeight: "56px",
      color: state.isSelected ? "#fff" : "#000",
      background: state.isSelected ? root.getPropertyValue("--orange") : "#fff",
      display: "flex",
      alignItems: "center",
      "&:hover": {
        background: state.isSelected
          ? root.getPropertyValue("--orange")
          : root.getPropertyValue("--light-orange"),
      },
    }),
    indicatorSeparator: (baseStyles: any, state: any) => ({
      ...baseStyles,
      display: "none",
    }),
    indicatorsContainer: (baseStyles: any, state: any) => ({
      ...baseStyles,
      padding: "8px",
    }),
  };

  return (
    <div className={styles.selectContainer}>
      {value && (
        <div
          className={`${styles.label} ${white && styles.white} ${disabled && styles.disabled}`}
        >
          {placeholder}
        </div>
      )}
      <div style={{ position: "relative" }}>
        <Select
          styles={customStyles}
          value={value}
          onChange={onChange}
          options={options}
          placeholder={placeholder}
          isDisabled={disabled}
        />
        {error && (
          <img
            src="/input_error.svg"
            className={styles.errorIcon}
            onMouseDown={(e) => {
              e.preventDefault();
            }}
            style={{ right: 45, top: 15 }}
          />
        )}
      </div>
      {error && <p className={styles.errorText}>{error || <br />}</p>}
    </div>
  );
};

export default SelectDropdown;
