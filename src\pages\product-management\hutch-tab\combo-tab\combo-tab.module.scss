@use "./../../../../styles/theme.scss" as *;
@import "./../../../../styles/mixins.module.scss";
@import "./../../../../styles/table-mixin.module.scss";

.overview {
  @include overviewCardsWrapper;
}

.overviewSummaryWrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-left: 22px;
  margin-right: 12px;
}

.overviewHeaderOpenWrapper {
  display: flex;
  justify-content: flex-end;
  margin-right: 12px;
}

.searchPanelForm {
  @include searchPanelForm;
}

.panel {
  @include panel;
  @include table;

  margin-top: 16px;
}
.tableContainer {
  overflow: auto;
  padding-bottom: 5px;
  margin-top: 20px;
  border-radius: 16px;

  tr:first-of-type .tableRowActions {
    opacity: 1;
  }

  tr:hover .tableRowActions {
    opacity: 100 !important;
  }

  .tableRowActions {
    opacity: 0;
    display: flex;
    align-items: center;
    color: var(--primary-500);

    span {
      width: 40px;
      height: 40px;
      display: flex;
      justify-content: center;
      align-items: center;
      color: var(--primary-500);
      cursor: pointer;

      &:hover {
        color: var(--primary-800);
      }
    }
  }
}
