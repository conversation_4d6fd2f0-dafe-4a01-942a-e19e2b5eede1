import { useState } from "react";
import Modal from "../Modal";
import styles from "./send-notification-modal.module.scss";
import { PaperPlane } from "../svgs";
import {
  createStateObject,
  getMessages,
  getRules,
  labels,
  placeholders,
  clearInput,
  displayErrors,
  handleInputChange,
} from "../utils/InputHandlers";
import { Input } from "../Input";
import { validateAll } from "indicative/validator";
import TextArea from "../TextArea";
import { ApiPostAuth } from "../../pages/api/api";
import { useDispatch } from "react-redux";

const fields = ["notificationHeading", "notificationBody"];
const rules = getRules(fields);
const messages = getMessages(fields);

const SendNotificationModal = ({ show, setShow }: any) => {
  const [loading, setLoading] = useState(false);

  const dispatch = useDispatch();

  const [data, setData] = useState(createStateObject(fields));

  const sendNotification = () => {
    validateAll(data, rules, messages)
      .then((response) => {
        setLoading(true);
        ApiPostAuth("/agent/send-notification", {
          subject: data.notificationHeading,
          messageBody: data.notificationBody,
        })
          .then((response) => {
            setShow(false);
            dispatch({
              type: "notify",
              payload: {
                error: false,
                message: response.data.message,
              },
            });
            setTimeout(() => {
              setData(createStateObject(fields));
            }, 300);
          })
          .catch((error: any) => {
            setLoading(false);
            dispatch({
              type: "notify",
              payload: {
                error: true,
                message: error.response.data.message,
              },
            });
          });
      })
      .catch((errors) => {
        displayErrors(errors, setData);
      });
  };

  const maxHeadingLength = 65;
  const maxBodyLength = 240;

  return (
    <Modal
      show={show}
      close={() => {
        setShow(false);
      }}
      image="/bulk_edit_confirm_graphic.svg"
      loading={loading}
      saveButton={
        <>
          <PaperPlane /> Send Notification
        </>
      }
      fullSize
      proceed={sendNotification}
    >
      <div className={styles.main}>
        <h3>Send Notification</h3>
        <Input
          label={labels.notificationHeading}
          placeholder={placeholders.notificationHeading}
          value={data.notificationHeading}
          onChange={(e: any) => {
            handleInputChange("notificationHeading", e, data, setData);
          }}
          error={data.errors.notificationHeading}
          onKeyDown={sendNotification}
          clear={() => {
            clearInput("notificationHeading", setData);
          }}
          disabled={loading}
          white
        />
        <div className={styles.charsLeft}>
          Characters left: {maxHeadingLength - data.notificationHeading.length}
        </div>
        <TextArea
          label={labels.notificationBody}
          placeholder={placeholders.notificationBody}
          value={data.notificationBody}
          onChange={(e: any) => {
            handleInputChange("notificationBody", e, data, setData);
          }}
          error={data.errors.notificationBody}
          onKeyDown={sendNotification}
          clear={() => {
            clearInput("notificationBody", setData);
          }}
          disabled={loading}
          white
          textarea
        />
        <div className={styles.charsLeft}>
          Characters left: {maxBodyLength - data.notificationBody.length}
        </div>
      </div>
    </Modal>
  );
};

export default SendNotificationModal;
