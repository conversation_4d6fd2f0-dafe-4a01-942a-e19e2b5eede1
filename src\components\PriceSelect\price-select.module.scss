@use "../../styles/theme.scss" as *;
@import "../../styles/mixins.module.scss";

.menuButton {
  @include tableHeadFilterButton;
  align-items: flex-start;
}

.box {
  height: 100%;
}

.menuItem {
  display: flex;
  align-items: center;
  border-radius: 6px;
  transition: all 0.1s ease;
  color: $black;
  padding: 0;
  line-height: 21px;
  font-size: 14px;
  padding: 0;
  gap: 11px;
  br {
    display: none;
  }
  cursor: auto;
  &:hover {
    background: none;
  }
}

.container {
  display: grid;
  grid-template-columns: 1fr;
  grid-row-gap: 18px;
  max-height: 318px;
  overflow: auto;
  grid-column-gap: 32px;
  padding-right: 12px;
  white-space: initial;
}

.grid {
  display: grid;
  grid-column-gap: 16px;
  &.range {
    grid-template-columns: 0.5fr 0.5fr;
  }
}

.buttons {
  display: flex;
  width: 100%;
  justify-content: flex-end;
  margin-top: 32px;
}
