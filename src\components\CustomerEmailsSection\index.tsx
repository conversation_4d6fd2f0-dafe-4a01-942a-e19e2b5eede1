import { faker } from "@faker-js/faker";
import { useSearchParams } from "react-router-dom";
import SearchBar from "../SearchBar";
import styles from "./customer-emails.module.scss";
import RichTextEditor from "../RichTextEditor";
import { formatDateWithTime } from "../utils/formatDate";
import IconButton from "../IconButton";
import {
  ChevronDown,
  ChevronDownLg,
  PlusFilled,
  Reply as ReplyIcon,
} from "../svgs";
import { useState } from "react";
import { Collapse } from "@mui/material";
import Button from "../Button";
import SendEmailModal from "../SendEmailModal";
import { AnimatePresence } from "framer-motion";

const CustomerEmailsSection = ({ customer }: any) => {
  const [searchParams, setSearchParams] = useSearchParams();
  const showComposeEmail = searchParams.get("compose") === "true";
  const setShowComposeEmail = (value: boolean) => {
    if (value === true) {
      searchParams.set("compose", "true");
      setSearchParams(searchParams);
    } else {
      searchParams.delete("compose");
      setSearchParams(searchParams);
    }
  };

  const exampleEmail = () => ({
    subject: "Can I have multiple eSIMs on one account?",
    emails: [
      {
        from: "Emma Lemon",
        customer: false,
        content: (
          <>
            Hi Sophie,
            <br />
            <br />
            Thank you for reaching out.
            <br />
            <br />
            I've checked with the support team, and it's confirmed that multiple
            eSIMs can be managed under one account. <br />
            <br />
            Please find below the instructions on setup.
            <br />
            <br />
            Kind regards, <br />
            <br />
            Emma Lemon
          </>
        ),
        date: faker.date.recent({ days: 100 }),
      },
      {
        from: customer?.firstName + " " + customer?.lastName,
        customer: true,
        content: (
          <>
            Hi, <br />
            <br />
            I'd like to have separate eSIMs for personal and work use on my
            account. Is this possible? If so, could you explain how to set it up
            and manage them effectively? <br />
            <br />
            Thanks in advance for your help!
            <br />
            <br />
            Sophie Apple
          </>
        ),
        date: faker.date.recent({ days: 100 }),
      },
    ],
    date: faker.date.recent({ days: 100 }),
  });

  const emails = Array.from({ length: 2 }).map(() => exampleEmail());

  return (
    <>
      <AnimatePresence>
        {showComposeEmail && (
          <SendEmailModal
            setShow={() =>
              setSearchParams((params) => {
                params.delete("compose");
                return params;
              })
            }
          />
        )}
      </AnimatePresence>
      <div className={styles.panelTopBar}>
        <h4>Emails</h4>
        <div className={styles.actions}>
          <SearchBar placeholder="Search" />
          <IconButton
            style={{ marginLeft: "8px" }}
            onClick={() => {
              setSearchParams((params) => {
                params.set("compose", "true");
                return params;
              });
            }}
          >
            <PlusFilled />
          </IconButton>
        </div>
      </div>
      <div className={`${styles.emailsContainer} modal-scroll`}>
        {emails.map((email) => (
          <EmailChain email={email} setShowComposeEmail={setShowComposeEmail} />
        ))}
      </div>
    </>
  );
};

export default CustomerEmailsSection;

const EmailChain = ({ email, setShowComposeEmail }: any) => {
  const [expanded, setExpanded] = useState(true);

  return (
    <div className={styles.emailContainer}>
      <div className={styles.emailSubject}>
        <div className={styles.subjectText}>{email.subject}</div>
        <div className={styles.dateContainer}>
          <div className={styles.date}>{formatDateWithTime(email.date)}</div>
          <div
            className={`${styles.mainExpand} ${expanded ? styles.open : ""}`}
            onClick={() => {
              setExpanded((prev: boolean) => !prev);
            }}
          >
            <ChevronDownLg />
          </div>
        </div>
      </div>
      <Collapse in={expanded}>
        <div className={styles.repliesContainer}>
          {email.emails.map((reply: any) => (
            <Reply reply={reply} setShowComposeEmail={setShowComposeEmail} />
          ))}
        </div>
      </Collapse>
    </div>
  );
};

const Reply = ({ reply, setShowComposeEmail }: any) => {
  const [expanded, setExpanded] = useState(true);

  return (
    <div className={styles.replyContainer}>
      <div className={styles.replyTop}>
        <div className={styles.left}>
          {!reply.customer && <div className={styles.circle} />}
          {reply.customer && <div className={styles.name}>From</div>}
          <div className={styles.name}>{reply.from}</div>
          <div className={styles.date}>{formatDateWithTime(reply.date)}</div>
        </div>
        <div className={styles.right}>
          <Button
            style={{ padding: "0px 16px", height: 36 }}
            color="secondary"
            onClick={() => setShowComposeEmail(true)}
          >
            <ReplyIcon />
            Reply
          </Button>
          <div
            className={`${styles.mainExpand} ${expanded ? styles.open : ""}`}
            onClick={() => {
              setExpanded((prev: boolean) => !prev);
            }}
          >
            <ChevronDownLg />
          </div>
        </div>
      </div>
      <Collapse in={expanded}>
        <div className={styles.replyContent}>{reply.content}</div>
      </Collapse>
    </div>
  );
};
