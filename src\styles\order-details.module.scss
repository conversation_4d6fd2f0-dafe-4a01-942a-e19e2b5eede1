@use "./theme.scss" as *;

.main {
  padding: 50px 40px;
  &.open {
    .subscriberTile {
      @media (max-width: 1420px) {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr;
      }
    }
    .userDetailsSection {
      @media (max-width: 1420px) {
        border-bottom: 1px solid #e0dcdc;
        border-right: none;
        padding-right: 0px;
        display: flex;
        .buttons {
          margin-bottom: 0px;
          margin-left: 75px;
        }
      }
    }
    .planDetailsSection {
      @media (max-width: 1420px) {
        padding-top: 32px;
        padding-left: 0px;
      }
    }
  }
  &.closed {
    .subscriberTile {
      @media (max-width: 1200px) {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr;
      }
    }
    .userDetailsSection {
      @media (max-width: 1200px) {
        border-bottom: 1px solid #e0dcdc;
        border-right: none;
        padding-right: 0px;
        display: flex;
        .buttons {
          margin-bottom: 0px;
          margin-left: 75px;
        }
      }
    }
    .planDetailsSection {
      @media (max-width: 1200px) {
        padding-top: 32px;
        padding-left: 0px;
      }
    }
  }
}

.topBar {
  display: flex;
  margin-bottom: 22px;
  width: 100%;
  align-items: center;
  justify-content: space-between;
}

.backLink {
  display: flex;
  justify-content: flex-start;
}

.mainTile {
  width: 100%;
  background: #fff;
  border-radius: 24px;
  padding: 50px;
  //height: calc(100vh - 178px);
  .topBar {
    display: flex;
    justify-content: flex-start;
    width: 100%;
    align-items: center;
    .orders {
      display: flex;
      flex-direction: column;
      margin-right: 16px;
      h3 {
        font-size: 20px;
        line-height: 30px;
        font-weight: 700;
        margin-bottom: 12px;
      }
      p {
        font-size: 14px;
        line-height: 21px;
      }
    }
    .btn {
      display: flex;
      gap: 16px;
      align-items: center;
      margin-left: auto;
    }
  }

  .orderTile {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    border-radius: 24px;
    border: 1px solid #eae9e8;
    background: var(--Primary-Pallete-Off-white, #fdfcfb);
    h4 {
      font-size: 20px;
      line-height: 30px;
      font-weight: 700;
    }
    .heading {
      display: flex;
      width: 100%;
      align-items: center;
      padding: 24px 24px 12px 24px;
      justify-content: space-between;
      align-self: stretch;
      border-bottom: 1px solid rgba(0, 0, 0, 0.12);
    }
    .innerTile {
      display: flex;
      padding: 24px;
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;
      align-self: stretch;
      height: 100%;
      font-size: 14px;
      line-height: 21px;
      .in {
        display: flex;
        padding: 24px;
        height: 100%;
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
        align-self: stretch;
        border-radius: 8px;
        background: #f7f6f6;
        table {
          tr {
            display: flex;
            justify-content: space-between;
            width: 100%;
            td {
              width: 50%;
              padding: 0 50px 0 0;
            }
          }
        }
      }
    }
  }
}

.orderPanel {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-column-gap: 24px;
  align-items: baseline;
  height: 100%;
  padding-top: 24px;
  align-items: stretch;
}
