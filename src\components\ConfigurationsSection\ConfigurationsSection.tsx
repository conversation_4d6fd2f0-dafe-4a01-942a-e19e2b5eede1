import { useEffect, useState } from "react"
import { features } from "../utils/dataCreator"
import styles from "../TravellerProductContainer/customer-product.module.scss";
import configStyles from "./configurations.module.scss"
import sizeStyles from "../CustomerTickets/customer-ticket.module.scss";
import { Boltin, BoltinCircle, Delete, ExclaimCircle, Plus, Sparkle, Speedometer, SpeedometerCircle } from "../svgs";
import Toggle from "../Toggle";
import Button from "../Button";
import Checkbox from "../Checkbox";
import Dialog from "../Dialog";

const ConfigurationsSection = () => {
    const [feats, setFeats] = useState([] as any)
    const [throttles, setThrottles] = useState([] as any)
    const [boltins, setBoltin] = useState([] as any)
    const [showModal, setShowModal] = useState(false)
    const [modalName, setModalName] = useState('')
    const [saveBtnText, setSaveBtnText] = useState('')
    const [modalTitle, setModalTitle] = useState('')
    const [selected, setSelected] = useState('')
    const [checkedList, setCheckedList] = useState([] as any)

    useEffect(() => {
        setFeats(features.filter((item:any) => item.classificationName === 'feature'))
        setThrottles(features.filter((item:any) => item.classificationName === 'throttle'))
        setBoltin(features.filter((item:any) => item.classificationName === 'bolton'))
    }, [])

    const changeStatus = (i:number) =>  {
        let selectedFeature = feats[i]
        if (selectedFeature.status === "Active") {
            selectedFeature.status = "Inactive"
        } else {
            selectedFeature.status = "Active"
        }
        let updatedFeatures = [...feats]
        updatedFeatures[i] = feats[i]
        setFeats(updatedFeatures)
    }

    const handleCloseModal = () => {
        setCheckedList([])
        setShowModal(false)
    }

    const handleSelectedModal = (type: string, name:string) => {
        setSelected(name)
        if (type === 'removethrottle') {
            setSaveBtnText('Yes, Remove')
            setModalTitle('Remove Throttle?')
        } else if (type === 'removeboltin') {
            setSaveBtnText('Yes, Remove')
            setModalTitle('Remove Bolt-on?')
        } else if (type === 'addboltons') {
            setSaveBtnText('Add Bolt-Ons')
            setModalTitle('Add Bolt-Ons')
        } else {
            setSaveBtnText('Add Throttles')
            setModalTitle('Add Throttles')
        }
        setShowModal(true)
        setModalName(type)
    }

    return (
        <div className={`${styles.plansContainer}`}>
            <Dialog
                open={showModal}
                onClose={() => handleCloseModal()}
                headerTitle={modalTitle}
                headerIcon={modalName === 'removethrottle' || modalName === 'removeboltin' ? <ExclaimCircle /> : modalName === 'addboltons' ? <BoltinCircle /> : <SpeedometerCircle />}
                headerSubtitle={modalName === 'removethrottle' || modalName === 'removeboltin' ? selected : ''}
                confirmButtonText={saveBtnText}
                size="sm"
                cancelButtonText='Cancel'>
                    {
                        modalName === 'removeboltin' && (
                            <p>APEX World Connect Advantage includes: from the U.S. unlimited calling to Mexico and Canada, unlimited calling to over 85 countries, and discounted calling to over 140 countries. Visit att.com/worldconnect for details</p>
                        )
                    }
                    {
                        modalName === 'removethrottle' && (
                            <p>When added to select plans this SOC throttles data usage from the device to 128Kbps.  Does not treat tethering usage.  Compatible with APEX Exclusive.</p>
                        )
                    }
                    {
                        modalName === 'addboltons' && (
                            <>
                               {
                                    boltins.map((item:any, index:number) => {
                                        if (index < 3) {
                                            return (
                                                <div className="flex align-items-basline">
                                                    <Checkbox 
                                                        id={item.name} 
                                                        checked={checkedList.includes(item.name)}
                                                        onClick={() => {
                                                            if (checkedList.includes(item.name)) {
                                                              setCheckedList(
                                                                checkedList.filter(
                                                                  (change: any) => change !== item.name
                                                                )
                                                              );
                                                            } else {
                                                              setCheckedList([...checkedList, item.name]);
                                                            }
                                                          }}/> 
                                                    <div className={configStyles.modalText}>
                                                        <p>{ item.name }</p>
                                                        <p>{ item.description }</p>
                                                    </div>
                                                </div>
                                            )
                                        }
                                        })
                                }  
                            </>
                        )
                    }
                    {
                        modalName === 'addthrottle' && (
                            <>
                                {
                                    throttles.map((item:any, index:number) => {
                                        if (index < 3) {
                                            return (
                                                <div className="flex align-items-basline">
                                                    <Checkbox
                                                        size={45} 
                                                        id={item.name} 
                                                        checked={checkedList.includes(item.name)}
                                                        onClick={() => {
                                                            if (checkedList.includes(item.name)) {
                                                              setCheckedList(
                                                                checkedList.filter(
                                                                  (change: any) => change !== item.name
                                                                )
                                                              );
                                                            } else {
                                                              setCheckedList([...checkedList, item.name]);
                                                            }
                                                          }}/> 
                                                    <div className={configStyles.modalText}>
                                                        <p>{item.name}</p>
                                                        <p>When added to select plans this SOC throttles data usage from the device to 128Kbps.  Does not treat tethering usage.  Compatible with APEX Exclusive.</p>
                                                    </div>
                                                </div>
                                            )
                                        }
                                        })
                                }  
                            </>
                        )
                    }
                </Dialog>
            {
                feats.length > 0 && (
                    <div className={sizeStyles.main}>
                        <p className={configStyles.title}>Features</p>
                        {
                            feats.map((feat:any, index:number) => (
                                <div key={index} className={configStyles.feature}>
                                    <div className={configStyles.circledIcon}>
                                        <Sparkle />
                                    </div>
                                    <p>{feat.name}</p>
                                    <Toggle on={feat.status === "Active"} onChange={() => changeStatus(index)} />
                                </div>
                            ))
                        }
                    </div>
                )
            }
            {
                boltins.length > 0 && (
                    <div className={sizeStyles.main}>
                        <div className="flex flex-justify-content-between">
                            <p className={configStyles.title}>Bolt-ons</p>
                            <Button style={{ height: '36px', padding: '0 17px' }} color="secondary" onClick={() => handleSelectedModal('addboltons', '')}><Plus /> Add</Button>
                        </div>
                        {
                            boltins.map((bolt:any, index:number) => (
                                <div key={index} className={configStyles.feature}>
                                    <div className={configStyles.circledIcon}>
                                        <Boltin />
                                    </div>
                                    <p>{bolt.name}</p>
                                    <div className={`${configStyles.circledIcon} ${configStyles.clicked}`} onClick={() => handleSelectedModal('removeboltin', bolt.name)}>
                                        <Delete />
                                    </div>
                                </div>
                            ))
                        }
                    </div>
                )
            }
            {
                throttles.length > 0 && (
                    <div className={sizeStyles.main}>
                        <div className="flex flex-justify-content-between">
                            <p className={configStyles.title}>Throttles</p>
                            <Button style={{ height: '36px', padding: '0 17px' }} color="secondary" onClick={() => handleSelectedModal('addthrottle', '')}><Plus /> Add</Button>
                        </div>
                        {
                            throttles.map((throttle:any, index:number) => (
                                <div key={index} className={configStyles.feature}>
                                    <div className={configStyles.circledIcon}>
                                        <Speedometer />
                                    </div>
                                    <p>{throttle.name}</p>
                                    <div className={`${configStyles.circledIcon} ${configStyles.clicked}`} onClick={() => handleSelectedModal('removethrottle', throttle.name)}>
                                        <Delete />
                                    </div>
                                </div>
                            ))
                        }
                    </div>
                )
            }
        </div>
    )
}

export default ConfigurationsSection