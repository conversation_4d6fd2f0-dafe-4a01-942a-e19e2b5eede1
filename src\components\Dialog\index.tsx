import { Dialog as DialogPrimitive, styled } from "@mui/material";
import { CloseCircle } from "@/components/svgs";
import React, { useCallback } from "react";
import Button, { ButtonColors } from "@/components/Button";

type DialogProps = {
  children?: React.ReactNode;
  open: boolean;
  size: keyof typeof sizes;
  scroll?: boolean;
  heightInPx?: number;
  onClose: () => void;
  headerIcon?: React.ReactNode;
  headerTitle?: string;
  headerSubtitle?: React.ReactNode;
  showFooter?: boolean;
  confirmButtonText?: React.ReactNode;
  confirmButtonOnClick?: () => void;
  confirmButtonIcon?: React.ReactNode;
  confirmButtonVariant?: ButtonColors;
  cancelButtonText?: React.ReactNode;
  cancelButtonOnClick?: () => void;
  customButtonText?: React.ReactNode;
  customButtonOnClick?: () => void;

  /** Reference to the modal content element for scroll to top */
  modalContentRef?: React.RefObject<HTMLDivElement>;
};

const Dialog = ({
  children,
  size,
  open,
  onClose,
  heightInPx,
  headerIcon,
  headerTitle,
  headerSubtitle,
  showFooter = true,
  confirmButtonText,
  confirmButtonIcon,
  confirmButtonOnClick,
  confirmButtonVariant,
  cancelButtonText,
  cancelButtonOnClick,
  customButtonText,
  customButtonOnClick,
  scroll = true,
  modalContentRef,
}: DialogProps) => {
  const renderDialogHeader = useCallback(() => {
    if (!headerIcon && !headerTitle && !headerSubtitle) {
      return null;
    }

    return (
      <div className="dialog-header">
        {headerIcon && <div className="header-icon">{headerIcon}</div>}
        {headerTitle && <h4 className="header-title">{headerTitle}</h4>}
        {headerSubtitle && <p className="header-subtitle">{headerSubtitle}</p>}
      </div>
    );
  }, [headerIcon, headerTitle, headerSubtitle]);

  const renderDialogFooter = useCallback(() => {
    if (!confirmButtonText && !cancelButtonText) {
      return null;
    }

    return (
      <div className="dialog-footer">
        {confirmButtonText && (
          <Button onClick={confirmButtonOnClick} color={confirmButtonVariant}>
            {confirmButtonIcon || null}
            {confirmButtonText}
          </Button>
        )}
        {customButtonText && (
          <Button onClick={customButtonOnClick} color="secondary">
            {customButtonText}
          </Button>
        )}
        {cancelButtonText && (
          <Button onClick={cancelButtonOnClick || onClose} color="secondary">
            {cancelButtonText}
          </Button>
        )}
      </div>
    );
  }, [
    confirmButtonText,
    confirmButtonOnClick,
    confirmButtonVariant,
    confirmButtonIcon,
    cancelButtonText,
    cancelButtonOnClick,
    onClose,
  ]);

  return (
    <StyledDialog
      open={open}
      onClose={onClose}
      style={sizes[size] as any}
      // @ts-expect-error
      size={size}
      height={heightInPx}
    >
      <button className="dialog-close" onClick={onClose}>
        <CloseCircle />
      </button>

      {renderDialogHeader()}

      <div
        className={`dialog-content ${scroll ? "modal-scroll" : ""}`}
        ref={modalContentRef}
      >
        {children}
      </div>

      {showFooter && renderDialogFooter()}
    </StyledDialog>
  );
};

export default Dialog;

const sizes = {
  sm: {
    "--dialog-width": "500px",
  },
  md: {
    "--dialog-width": "1049px",
  },
  lg: {
    "--dialog-width": "1300px",
  },
};
// todo: move...
const buttonResetStyle = {
  all: "unset",
  appearance: "none",
  background: "none",
  border: "none",
  padding: 0,
  margin: 0,
  color: "inherit",
  font: "inherit",
  textAlign: "inherit",
  lineHeight: "inherit",
  cursor: "pointer",
};

const StyledDialog = styled(DialogPrimitive)((props) => ({
  ".MuiBackdrop-root": {
    background: "rgba(12, 44, 100, 0.6)",
  },

  ".MuiPaper-root": {
    width: "var(--dialog-width)",
    maxWidth: "100%",
    // @ts-expect-error
    height: props.height,
    maxHeight: "620px",
    borderRadius: "32px",
    padding: "32px",
    position: "relative",
    overflowY: "inherit",
  },

  ".dialog-close": {
    ...buttonResetStyle,
    position: "absolute",
    top: "16px",
    right: "16px",
    appearance: "none",

    "&:focus": {
      outline: "2px solid var(--primary-400)",
    },
  },

  "h4.header-title": {
    fontSize: "20px",
    fontWeight: 700,
    color: "var(--text-primary)",
    marginTop: "16px",
  },

  "p.header-subtitle": {
    fontSize: "14px",
    color: "var(--text-secondary)",
    marginTop: "8px",
  },

  ".dialog-header": {
    paddingBottom: "16px",
  },

  // todo: fix scrollbar position for bigger modals
  ".dialog-content": {
    paddingInline: "2px",
    marginRight: "-24px",
    // @ts-expect-error
    ...(props.size === "md"
      ? {
          width: "100%",
        }
      : {}),
  },

  ".dialog-footer": {
    paddingTop: "16px",
    display: "flex",
    gap: "8px",
  },
}));
