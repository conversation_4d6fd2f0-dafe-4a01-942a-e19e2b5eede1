import { Link } from "react-router-dom";
import Button from "../../components/Button";
import styles from "../../styles/404.module.scss";

const NotFound = () => {
  return (
    <div className={styles.main}>
      <div className={styles.logos}>
        <img src="/Logo.png" className={styles.logo} />
      </div>
      <div className={styles.error}>
        <h5>404</h5>
        <div className={styles.notFound}>Page not found</div>
        <div className={styles.text}>
          The page you are looking for doesn't exist.
        </div>
        <Link to="/" style={{ textDecoration: "none" }}>
          <Button>Back to home</Button>
        </Link>
      </div>
    </div>
  );
};

export default NotFound;
