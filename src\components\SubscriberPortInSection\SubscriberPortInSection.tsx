import sizeStyles from "../CustomerTickets/customer-ticket.module.scss";
import StatusPill from "../StatusPill";
import styles from "../TravellerProductContainer/customer-product.module.scss";
import { ExclaimCircle, InfoCircle, MapPin, Phone } from "../svgs";
import Button from "../Button";
import { useEffect, useState } from "react";
import { getPortedNumbers } from "../utils/dataCreator";
import UpdateAddPortInSubscriptionModal from "../UpdateAddPortInSubscriptionModal/UpdateAddPortInSubscriptionModal";
import Dialog from "../Dialog";

const SubscriberPortInSection = () => {
  const [numbers, setNumbers] = useState([] as any);
  const [selected, setSelected] = useState<null | number>(null);
  const [showModal, setShowModal] = useState(false);
  const [modalType, setModalType] = useState("");
  const [showResubmitModal, setShowResubmitModal] = useState(false);

  useEffect(() => {
    setNumbers(getPortedNumbers(5));
  }, []);

  const handleSelectedPortIn = (index: number, type: string) => {
    setSelected(index);
    setShowModal(true);
    setModalType(type);
  };

  const handleCloseModal = (type: string) => {
    if (modalType === "failed") {
      setShowModal(false);
      setModalType("");
      setShowResubmitModal(true);
    }
  };

  return (
    <div className={`${styles.plansContainer}`}>
      <Dialog
        open={showModal}
        size="sm"
        onClose={() => setShowModal}
        headerTitle={
          modalType === "close"
            ? "Close View?"
            : modalType === "cancel"
              ? "Cancel Port-in Request?"
              : "Port-In Failed"
        }
        headerSubtitle={`Porting Number: ${numbers[0]?.phone}`}
        headerIcon={modalType === "failed" ? <InfoCircle /> : <ExclaimCircle />}
        confirmButtonText={
          modalType === "close"
            ? "Yes, Close view"
            : modalType === "cancel"
              ? "Yes, Cancel request"
              : "Update Port-in"
        }
        confirmButtonOnClick={
          modalType === "failed"
            ? () => handleCloseModal("failed")
            : () => setShowModal(false)
        }
        cancelButtonText={
          modalType === "close"
            ? "Cancel"
            : modalType === "cancel"
              ? "No"
              : "Close window"
        }
      >
        {modalType === "failed" && (
          <>
            <h5>Fail Reason:</h5>
            <p>Message here</p>
            <h5>Common fail reasons:</h5>
            <ol>
              <li>Account ID from the other carrier is correct</li>
              <li>PIN (password) is not correct.</li>
              <li>Device is locked.</li>
              <li>Line has not been released by the other carrier.</li>
              <li>Subscriber has a combined account with another carrier.</li>
              <li>
                Subscriber is still paying for the device and must pay the
                remaining balance, so the other carrier can release the account.
              </li>
            </ol>
          </>
        )}
      </Dialog>
      <UpdateAddPortInSubscriptionModal
        show={showResubmitModal}
        close={(val: boolean) => setShowResubmitModal(val)}
        title="Update Port In"
      />
      {numbers.map((item: any, index: number) => (
        <div
          key={index}
          className={sizeStyles.main + " " + sizeStyles.portInSection}
        >
          <StatusPill
            status={item.status}
            icon={item.status === "Failed"}
            click={
              item.status === "Failed"
                ? () => handleSelectedPortIn(index, "failed")
                : null
            }
          />
          <p className={sizeStyles.title}>Porting In</p>
          <div className={sizeStyles.portInDetails}>
            <div>
              <Phone />
              <div>
                <p className={sizeStyles.title}>Phone Number</p>
                <p className={sizeStyles.text}>{item.phone}</p>
              </div>
            </div>
            <div>
              <MapPin />
              <div>
                <p className={sizeStyles.title}>Post Code</p>
                <p className={sizeStyles.text}>{item.zipNo}</p>
              </div>
            </div>
          </div>
          {item.status === "Completed" || item.status === "Cancelled" ? (
            <Button
              color="customerActionBlue"
              style={{ justifyContent: "center" }}
              onClick={() => handleSelectedPortIn(index, "close")}
            >
              Close View
            </Button>
          ) : item.status === "Failed" ? (
            <div className="flex align-items-center">
              <Button
                color="customerActionBlue"
                onClick={() => handleSelectedPortIn(index, "cancel")}
                style={{
                  justifyContent: "center",
                  width: "55%",
                  marginRight: "10px",
                }}
              >
                Cancel Port in
              </Button>
              <Button
                color="blue"
                onClick={() => setShowResubmitModal(true)}
                style={{ justifyContent: "center" }}
              >
                Re-submit
              </Button>
            </div>
          ) : (
            <Button
              color="customerActionBlue"
              onClick={() => handleSelectedPortIn(index, "cancel")}
              style={{ justifyContent: "center" }}
            >
              Cancel Port in
            </Button>
          )}
        </div>
      ))}
    </div>
  );
};

export default SubscriberPortInSection;
