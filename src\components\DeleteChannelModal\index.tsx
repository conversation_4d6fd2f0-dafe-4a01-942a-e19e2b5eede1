import styles from "./delete-channel-modal.module.scss";
import Modal from "../Modal";
import { WarningCircle } from "../svgs";
import { useState } from "react";
import { useDispatch } from "react-redux";
import { ApiDelete } from "../../pages/api/api";
import StatusPill from "../StatusPill";
import Dialog from "../Dialog";

const DeleteChannelModal = ({ show, setShow, channel, repopulate }: any) => {
  const dispatch = useDispatch();

  const [loading, setLoading] = useState(false);

  const handleSuccess = () => {
    setLoading(false);
    setShow(false);
    dispatch({
      type: "notify",
      payload: {
        error: false,
        message: `Channel: ${channel?.name} has been deleted successfully`,
      },
    });
    repopulate();
  };

  const deleteUser = () => {
    setLoading(true);
    ApiDelete(`/channels/${channel.id}`, {})
      .then((response) => {
        setLoading(false);
        setShow(false);
        dispatch({
          type: "notify",
          payload: {
            error: false,
            message: response.data.message,
          },
        });
        repopulate();
      })
      .catch((error) => {
        setLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: error.response.data.message,
          },
        });
      });
  };

  return (
    <Dialog
      confirmButtonText="Yes, Delete Channel"
      headerTitle="Delete Channel?"
      headerSubtitle="Once removed, it cannot be recovered."
      headerIcon={<WarningCircle />}
      open={show}
      onClose={() => {
        setShow(false);
      }}
      size="sm"
      confirmButtonVariant="customerActionRed"
      cancelButtonText="Cancel"
      confirmButtonOnClick={() => handleSuccess()}
    >
      <div className={styles.main}>
        <div className={styles.table}>
          <div className={styles.tableRow}>
            <div className={styles.labels}>{channel?.name && <p>Name</p>}</div>
            <div className={styles.value}>{channel?.name}</div>
          </div>
          <div className={styles.tableRow}>
            <div className={styles.labels}>
              {channel?.description && <p>Description</p>}
            </div>
            <div className={styles.value}>{channel?.description}</div>
          </div>
          <div className={styles.tableRow}>
            <div className={styles.labels}>
              {channel?.subscribers && <p>Subscribers</p>}
            </div>
            <div className={styles.value}>{channel?.subscribers}</div>
          </div>
          <div className={styles.tableRow}>
            <div className={styles.labels}>
              {channel?.subscriptions && <p>Subscriptions</p>}
            </div>
            <div className={styles.value}>{channel?.subscriptions}</div>
          </div>
          <div className={styles.tableRow}>
            <div className={styles.labels}>
              {channel?.status && <p>Status</p>}
            </div>
            <div className={styles.value}>
              <StatusPill status={channel?.status === "Active"} />
            </div>
          </div>
        </div>
      </div>
    </Dialog>
  );
};

export default DeleteChannelModal;
