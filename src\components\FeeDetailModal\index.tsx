import Dialog from "@/components/Dialog";
import styles from './fee-detail-modal.module.scss';
import { useEffect, useState } from "react";
import BlobSwitchBar from "@/components/BlobSwitchBar";
import Button from "@/components/Button";
import { ArrowRight, CoinCircle, Delete, Export, RippedTicketCircle } from "@/components/svgs";
import StatusPill from "@/components/StatusPill";
import { formatDateWithTime } from "@/components/utils/formatDate";
import DetailsCard from "@/components/DetailsCard";
import { faker } from "@faker-js/faker";
import TableControl from "@/components/TableControl";
import UserSkeleton from "@/components/UserSkeleton";


type FeeDetailModal = {
  feeData: any;
  open: boolean;
  onClose: () => void;
  onEditFee: () => void;
  onDeleteFee: () => void;
}

const FeeDetailModal = (props: FeeDetailModal) => {
  const { feeData, onClose, open, onEditFee, onDeleteFee } = props

  const [activeTab, setActiveTab] = useState("overview")
  const tabs = [
    {
      id: "overview",
      label: "Overview"
    },
    {
      id: "activityLog",
      label: "Activity Log"
    }
  ]

  const [loading, setLoading] = useState(true);
  const [activities, setActivities] = useState<any[]>([]);

  const populate = (itemsPerPage: number) => {
    setLoading(true);

    setTimeout(() => {
      setActivities(generateActivityLogData(itemsPerPage))
      setLoading(false)
    }, 500)
  }

  const [itemsPerPage, setItemsPerPage] = useState(8);
  const [numberOfPages, setNumberOfPages] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  useEffect(() => {
    populate(itemsPerPage)
  }, [itemsPerPage, currentPage])

  const formatDataItem = (item: any, field: string) => {
    if (field === "dateTime") {
      return formatDateWithTime(item[field])
    }

    else if (field === "activity") {
      const activity = item[field]

      return (
        <div className={styles.activityCell}>
          <span className={styles.activityName}>{activity.name}: </span>
          <span>{activity.oldValue}</span><ArrowRight width="16" height="16" /><span>{activity.newValue}</span>
        </div>
      )
    }

    else {
      return item[field]
    }
  }

  return (
    <Dialog
      open={open}
      onClose={onClose}
      size="md"
      heightInPx={600}
    >
      <h3 className={styles.title}>{feeData.name}</h3>

      <div className={styles.tabsAndActionsContainer}>
        <BlobSwitchBar
          selected={activeTab}
          setSelected={setActiveTab}
          layoutId="order-details-modal-tabs"
          options={tabs}
        />

        {/* Action Buttons */}
        <div className={styles.actions}>
          {activeTab === "overview" && (
            <>
              {<Button color="secondary" onClick={onEditFee}>Edit Fee</Button>}
              {<Button color="destructive" onClick={onDeleteFee}><span style={{ color: "var(--systemStatus-red-800)" }}><Delete /></span> Delete Fee</Button>}
            </>
          )}
          {activeTab === "activityLog" && (
            <Button color="secondary"><Export /> Export to CSV</Button>
          )}
        </div>
      </div>

      {/* Overview Tab */}
      {activeTab === "overview" && (
        <>
          <div className={styles.statusAndDateContainer}>
            <StatusPill status={feeData.status ? "Active" : "Inactive"} color={feeData.status ? "active" : "inactive"} />
            <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
              <p className={styles.dateTime}>
                Creation Date: {formatDateWithTime(feeData.createdAt)}
              </p>
              <p className={styles.dateTime}>
                Last Modified: {formatDateWithTime(feeData.createdAt)}
              </p>
            </div>
          </div>

          <div className={styles.detailsCardContainer}>
            <DetailsCard
              title="Fee Details"
              items={[
                {
                  title: 'Fee Type',
                  value: feeData.type,
                  icon: <RippedTicketCircle />
                },
                {
                  title: 'Amount',
                  value: `$${feeData.amount.toFixed(2)}`,
                  icon: <CoinCircle />
                }
              ]}
            />
          </div>
        </>
      )}

      {/* Acvitity Log Tab */}
      {activeTab === "activityLog" && (
        <div className={styles.activityLogTabContainer}>
          <div className={styles.activityLogTableContainer}>
            <table>
              <thead>
                <tr>
                  <th>Date & Time</th>
                  <th>Email Address</th>
                  <th>Activity</th>
                </tr>
              </thead>
              <tbody>
                {!loading ?
                  activities.map((item: any, i: number) => {
                    return (
                      <tr key={`activity-${item.id}`}>
                        {activityLogFields.map((field: any) => (
                          <td>{formatDataItem(item, field.key)}</td>
                        ))}
                      </tr>
                    )
                  }) : Array.from({ length: itemsPerPage }, (v, i) => i).map(
                    (i) => (
                      <UserSkeleton
                        key={"order-skeleton-" + i}
                        noOfStandard={3}
                      />
                    )
                  )}
              </tbody>
            </table>
          </div>

          {/* todo: flush to the bottom */}
          <div style={{ marginTop: "auto" }}>
            <TableControl
              show
              itemsPerPage={itemsPerPage}
              setItemsPerPage={setItemsPerPage}
              currentPage={currentPage}
              setCurrentPage={setCurrentPage}
              numberOfPages={numberOfPages}
            />
          </div>
        </div>
      )}


    </Dialog>
  )
}

export default FeeDetailModal

function generateActivityLogData(rowCount: number) {
  const activities = [
    { name: 'Status', oldValue: 'Inactive', newValue: 'Active' },
    { name: 'Status', oldValue: 'Active', newValue: 'Inactive' },
    { name: 'Amount', oldValue: '$3.99', newValue: '$4.99' },
    { name: 'Name', oldValue: 'previousFeeName', newValue: 'newFeeName' },
  ];

  const data = [];

  for (let i = 0; i < rowCount; i++) {
    const dateTime = faker.date.recent()
    const email = faker.internet.email();
    const activity = faker.helpers.arrayElement(activities);

    data.push({
      dateTime,
      email,
      activity,
      id: i
    });
  }

  return data;
}

const activityLogFields = [
  {
    label: 'Date & Time',
    key: 'dateTime',
  },
  {
    label: 'Email Address',
    key: 'email'
  },
  {
    label: 'Activity',
    key: 'activity'
  }
]