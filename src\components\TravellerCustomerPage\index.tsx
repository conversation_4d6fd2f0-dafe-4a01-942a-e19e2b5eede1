import { Customer } from "@/types/customer";
import DotStatus from "../DotStatus";
import styles from "./traveller-customer.module.scss";
import SwitchBar from "../SwitchBar";
import { useState } from "react";
import Button from "../Button";
import {
  ChainLink,
  Delete,
  Door,
  Envelope,
  QrCode,
  Close,
  User,
  OpenMail,
  PhoneOutlined,
  LocationPin,
  Hash,
  Pencil,
  ExclaimCircle,
  WarningCircle,
} from "../svgs";
import { getCurrencySymbol } from "../utils/getCurrencySymbol";
import BlobSwitchBar from "../BlobSwitchBar";
import TravellerProductContainer from "../TravellerProductContainer";
import { SwitchTransition, CSSTransition } from "react-transition-group";
import CustomerTicketsPanel from "../CustomerTicketsPanel";
import { motion } from "framer-motion";
import CustomerNotesSection from "../CustomerNotesSection";
import CustomerEmailsSection from "../CustomerEmailsSection";
import TransactionHistory from "../TransactionHistory";
import VoiceMailCustomer from "../VoiceMailCustomer";
import { useSelector } from "react-redux";
import DeleteVoiceMailModal from "../DeleteVoiceMailModal";
import CustomerActivityDisplay from "../CustomerActivity";
import EditAccountDetailsModal from "../EditAccountDetailsModal";
import Toggle from "../Toggle";
import Dialog from "../Dialog";
import { useSearchParams } from "react-router-dom";

type PropTypes = {
  show: boolean;
  customer: Customer;
  close: Function;
};

const TravellerCustomerPage = ({ show, customer, close }: PropTypes) => {
  const { voiceMail } = useSelector((state: any) => state);
  const [selected, setSelected] = useState("manage");
  const [searchParams, setSearchParams] = useSearchParams();
  const activeTab = searchParams.get("tab") || "products";
  const [showVoiceMail, setShowVoiceMail] = useState(false);

  const tabs = [
    {
      id: "products",
      label: "Products",
    },
    {
      id: "tickets",
      label: "Tickets",
    },
    {
      id: "notes",
      label: "Notes",
    },
    {
      id: "emails",
      label: "Emails",
    },
    {
      id: "transactions",
      label: "Transaction History",
    },
    {
      id: "voicemail",
      label: "Voicemail",
    },
    {
      id: "logs",
      label: "Call & SMS Log",
    },
  ];

  return (
    <motion.div
      className={styles.container}
      key="customer-modal"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      {showVoiceMail && (
        <DeleteVoiceMailModal
          onClose={() => setShowVoiceMail(false)}
          open={showVoiceMail}
          phoneNumber={customer?.phoneNumber}
        />
      )}
      <div className={styles.modal}>
        <LeftColumn
          customer={customer}
          selected={selected}
          setSelected={setSelected}
        />
        <div className={styles.mainSection}>
          <BlobSwitchBar
            options={tabs}
            selected={activeTab}
            setSelected={(value: any) => setSearchParams({ tab: value })}
            layoutId="customer-main-selection"
          />
          <div
            className={styles.close}
            onClick={() => {
              close(false);
              // remove tab search param
              if (searchParams.has("tab")) {
                searchParams.delete("tab");
                setSearchParams(searchParams);
              }
            }}
          >
            <Close />
          </div>
          {/* This is problematic for when user opens email tab from the 'send support email' button and tries to switch tabs back so I've commented it out */}
          {/* <SwitchTransition> */}
          {/* <CSSTransition
              key={activeTab}
              addEndListener={(node, done) =>
                node.addEventListener("transitionend", done, false)
              }
              classNames="fade"
            >
              <> */}
          <div className={styles.tabsContentContainer}>
            {activeTab === "products" && <TravellerProductContainer />}
            {activeTab === "tickets" && <CustomerTicketsPanel />}
            {activeTab === "notes" && <CustomerNotesSection />}
            {activeTab === "emails" && (
              <CustomerEmailsSection customer={customer} />
            )}
            {activeTab === "transactions" && <TransactionHistory />}
            {activeTab === "voicemail" && (
              <div className={styles.grid_3}>
                {voiceMail.map((item: any) => {
                  return (
                    <VoiceMailCustomer
                      data={item}
                      setShowVoiceMail={setShowVoiceMail}
                    />
                  );
                })}
              </div>
            )}
            {activeTab === "logs" && <CustomerActivityDisplay />}
            {/* </>
            </CSSTransition> */}
            {/* </SwitchTransition> */}
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default TravellerCustomerPage;

type LeftColumnProps = {
  customer: Customer;
  selected: string;
  setSelected: (value: string) => void;
};

const LeftColumn = ({ customer, selected, setSelected }: LeftColumnProps) => {
  const [accountEnabled, setAccountEnabled] = useState(true);

  const [showEditAccountModal, setShowEditAccountModal] = useState(false);
  const [showSendQRCodeEmailModal, setShowSendQRCodeEmailModal] =
    useState(false);
  const [showPasswordResetModal, setShowPasswordResetModal] = useState(false);
  const [showLogIntoAccountModal, setShowLogIntoAccountModal] = useState(false);
  const [showDeleteAccountModal, setShowDeleteAccountModal] = useState(false);
  const [showDisableAccountModal, setShowDisableAccountModal] = useState(false);
  const [showEnableAccountModal, setShowEnableAccountModal] = useState(false);

  const [searchParams, setSearchParams] = useSearchParams();
  const composeEmail = () => {
    searchParams.set("tab", "emails");
    searchParams.set("compose", "true");
    setSearchParams(searchParams);
  };

  return (
    <>
      {/* Modals */}
      <EditAccountDetailsModal
        open={showEditAccountModal}
        onClose={() => setShowEditAccountModal(false)}
        accountData={customer}
      />
      <SendQRCodeEmailModal
        open={showSendQRCodeEmailModal}
        onClose={() => setShowSendQRCodeEmailModal(false)}
        email={customer.email}
      />
      <PasswordResetLinkModal
        open={showPasswordResetModal}
        onClose={() => setShowPasswordResetModal(false)}
        email={customer.email}
      />
      <LogIntoAccountModal
        open={showLogIntoAccountModal}
        onClose={() => setShowLogIntoAccountModal(false)}
        customerName={`${customer.firstName} ${customer.lastName}`}
      />
      <DeleteAccountModal
        open={showDeleteAccountModal}
        onClose={() => setShowDeleteAccountModal(false)}
        email={customer.email}
      />
      <DisableAccountModal
        open={showDisableAccountModal}
        onClose={() => {
          setAccountEnabled(false);
          setShowDisableAccountModal(false);
        }}
        email={customer.email}
      />
      <EnableAccountModal
        open={showEnableAccountModal}
        onClose={() => {
          setAccountEnabled(true);
          setShowEnableAccountModal(false);
        }}
        email={customer.email}
      />

      <div className={styles.leftColumn}>
        <img className={styles.profilePicture} src={customer?.profilePicture} />
        <div className={styles.name}>
          {customer?.firstName} {customer?.lastName}
        </div>
        <DotStatus status={true} />
        <div className={styles.creditBalance}>
          <div className={styles.textContainer}>
            <div className={styles.text}>Credit Balance</div>
          </div>
          <div className={styles.creditAmount}>
            {getCurrencySymbol(customer?.currency)}
            {customer?.credit.toFixed(2)}
          </div>
        </div>
        <SwitchBar
          options={[
            {
              id: "manage",
              label: "Manage",
            },
            {
              id: "account",
              label: "Account Info",
            },
          ]}
          selected={selected}
          setSelected={setSelected}
          layoutId="customer-sidebar"
        />
        <div className={styles.tabsContainer}>
          {selected === "manage" && (
            <div className={styles.manageTab}>
              <Button color="customerActionBlue" onClick={composeEmail}>
                <Envelope />
                Send Support Email
              </Button>
              <Button
                color="customerActionBlue"
                onClick={() => setShowSendQRCodeEmailModal(true)}
              >
                <QrCode />
                Send QR Code Email
              </Button>
              <Button
                color="customerActionBlue"
                onClick={() => setShowPasswordResetModal(true)}
              >
                <ChainLink />
                Send Password Reset Link
              </Button>
              <Button
                color="customerActionBlue"
                onClick={() => setShowLogIntoAccountModal(true)}
              >
                <Door />
                Log into Account
              </Button>
              <div className={styles.toggleContainer}>
                <div className={styles.title}>Account</div>
                <div className={styles.toggle}>
                  <span>{accountEnabled ? "Enabled" : "Disabled"}</span>
                  <Toggle
                    on={accountEnabled}
                    onChange={() => {
                      if (accountEnabled) {
                        setShowDisableAccountModal(true);
                      } else {
                        setShowEnableAccountModal(true);
                      }
                    }}
                  />
                </div>
              </div>
              <Button
                color="customerActionRed"
                onClick={() => setShowDeleteAccountModal(true)}
              >
                <Delete />
                Delete Account
              </Button>
            </div>
          )}

          {selected === "account" && (
            <div className={`${styles.accountTab} container-scroll`}>
              <Button
                color="customerActionBlue"
                onClick={() => setShowEditAccountModal(true)}
              >
                <Pencil />
                Edit Account Details
              </Button>
              <div className={styles.accountDetails}>
                <div className={styles.detailItem}>
                  <div className={styles.icon}>
                    <User />
                  </div>
                  <div className={styles.textContainer}>
                    <div className={styles.text}>
                      <div className={styles.label}>First Name</div>
                      <div className={styles.value}>{customer.firstName}</div>
                    </div>
                    <div className={styles.text}>
                      <div className={styles.label}>Last Name</div>
                      <div className={styles.value}>{customer.lastName}</div>
                    </div>
                  </div>
                </div>

                <div className={styles.detailItem}>
                  <div className={styles.icon}>
                    <OpenMail />
                  </div>
                  <div className={styles.textContainer}>
                    <div className={styles.text}>
                      <div className={styles.label}>Email Address</div>
                      <div className={styles.value}>{customer.email}</div>
                    </div>
                  </div>
                </div>

                <div className={styles.detailItem}>
                  <div className={styles.icon}>
                    <PhoneOutlined />
                  </div>
                  <div className={styles.textContainer}>
                    <div className={styles.text}>
                      <div className={styles.label}>Contact Number</div>
                      <div className={styles.value}>{customer.phoneNumber}</div>
                    </div>
                  </div>
                </div>

                <div className={styles.detailItem}>
                  <div className={styles.icon}>
                    <LocationPin />
                  </div>
                  <div className={styles.textContainer}>
                    <div className={styles.text}>
                      <div className={styles.label}>Address</div>
                      <div className={styles.value}>
                        <div>12</div>
                        <div>Street Name</div>
                        <div>City Name</div>
                        <div>Post Code</div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className={styles.detailItem}>
                  <div className={styles.icon}>
                    <Hash />
                  </div>
                  <div className={styles.textContainer}>
                    <div className={styles.text}>
                      <div className={styles.label}>Channel</div>
                      <div className={styles.value}>General</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

type ModalProps = {
  open: boolean;
  onClose: () => void;
  onConfirm?: () => void;
  email?: string;
  customerName?: string;
};

const SendQRCodeEmailModal = ({ open, onClose, email }: ModalProps) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      size="sm"
      headerIcon={<ExclaimCircle />}
      headerTitle="Send QR Code Email?"
      headerSubtitle={`We'll send an email with the eSIM QR code to ${email}`}
      confirmButtonText="Yes, Send QR Code"
      confirmButtonOnClick={onClose}
      cancelButtonText="Cancel"
    />
  );
};

const PasswordResetLinkModal = ({ open, onClose, email }: ModalProps) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      size="sm"
      headerIcon={<ExclaimCircle />}
      headerTitle="Send Password Reset Link?"
      headerSubtitle={`We'll send a password reset link to ${email}.`}
      confirmButtonText="Yes, Send Link"
      confirmButtonOnClick={onClose}
      cancelButtonText="Cancel"
    />
  );
};

const LogIntoAccountModal = ({ open, onClose, customerName }: ModalProps) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      size="sm"
      headerIcon={<ExclaimCircle />}
      headerTitle="Log into Account?"
      headerSubtitle={`You will now be redirected to ${customerName}'s account.`}
      confirmButtonText="Yes, Log into Account"
      confirmButtonOnClick={() => {
        onClose();
        window.open("https://www.google.com", "_blank");
      }}
      cancelButtonText="Cancel"
    />
  );
};

const DeleteAccountModal = ({ open, onClose, email }: ModalProps) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      size="sm"
      headerIcon={<WarningCircle />}
      headerTitle="Delete Account?"
      headerSubtitle={`This will permanently delete the account for ${email}. The customer will lose all access, and this action cannot be undone.`}
      confirmButtonText="Yes, Delete Account"
      confirmButtonOnClick={onClose}
      confirmButtonVariant="destructive"
      cancelButtonText="Cancel"
    />
  );
};

const DisableAccountModal = ({ open, onClose, email }: ModalProps) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      size="sm"
      headerIcon={<ExclaimCircle />}
      headerTitle="Disable Account?"
      headerSubtitle={`This will disable the account for ${email} and set their status to Inactive. The customer will no longer have access. You can enable the account again if needed.`}
      confirmButtonText="Yes, Disable"
      confirmButtonOnClick={onClose}
      cancelButtonText="Cancel"
    />
  );
};

const EnableAccountModal = ({ open, onClose, email }: ModalProps) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      size="sm"
      headerIcon={<ExclaimCircle />}
      headerTitle="Enable Account?"
      headerSubtitle={`This will enable the account for ${email} and set their status to Active. The customer will regain access to their account.`}
      confirmButtonText="Yes, Enable"
      confirmButtonOnClick={onClose}
      cancelButtonText="Cancel"
    />
  );
};
