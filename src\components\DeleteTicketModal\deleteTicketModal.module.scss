@import "../../styles/table-mixin.module.scss";

.tableContainer {
  @include curvedEdgesTwoColumnTableContainer;
  margin-top: 18px;
  margin-bottom: 8px;
  width: fit-content;
  min-width: 320px;
  border: 1px solid var(--gray-100);
  border-radius: 16px;
}

.table {
  color: var(--primary-900);
  font-size: 12px;
  border-collapse: collapse;
  width: 100%;
  table-layout: auto;

  .label, .value {
    border-top: 1px solid var(--gray-100);
    padding: 12px 10px;
  }

  .value {
    border-left: 1px solid var(--gray-100);
    text-transform: capitalize;
  }

  tr:first-child {
    .label, .value {
      border-top: none
    }
  }
}