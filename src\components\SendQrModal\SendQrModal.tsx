import { handleInputChange, createStateObject, labels, clearInput } from "@/components/utils/InputHandlers";
import { ExclaimCircle } from "../svgs";
import { Input } from "../Input";
import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import Dialog from "../Dialog";

type selectedRow = {
    email: string,
    availability: string
}

const fields = ['email']
const SendQrModal = ({ show, close, selected }: {show:boolean, close: Function, selected:selectedRow}) => {
    const [data, setData] = useState(createStateObject(fields))
    const dispatch = useDispatch()

    useEffect(() => {
        if (selected.availability === 'Pending') {
            setData({
                "email": selected.email,
                "errors": {
                  "email": ""
                }
              })
        }
    }, [selected])

    const handleSubmit = () => {
        dispatch({
            type: "notify",
            payload: {
              error: false,
              message: 'QR code is sended successfully',
            },
        });
        close()
    }

    return (
        <Dialog
            open={show}
            onClose={() => close()}
            headerTitle='Send QR Code via email?'
            cancelButtonText='Cancel'
            headerIcon={<ExclaimCircle />}
            confirmButtonOnClick={() => handleSubmit()}
            size="sm"
            confirmButtonText='Yes, Send QR code'>
            <Input
                label={labels['email']}
                value={data['email']}
                onChange={(e: any) => {
                    handleInputChange('email', e, data, setData);
                }}
                disabled={selected.availability === 'Pending'}
                error={data.errors['email']}
                clear={() => {
                    clearInput('email', setData);
                }}
                infoTooltipText
            />
        </Dialog>
            
    )
}

export default SendQrModal