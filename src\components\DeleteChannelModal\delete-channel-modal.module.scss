.main {
  margin: 0 auto;
  width: 100%;
  display: flex;
  height: 100%;
  flex-direction: column;
  justify-content: center;
  .table {
    border: 1px solid #DFE2E7;
    border-radius: 24px;
    margin-top: 10px;
    .tableRow {
      display: grid;
      grid-template-columns: 1fr 1fr;
      font-size: 12px;
      color: #061632;
      .labels {
        padding: 15px 20px;
        flex-grow: 1;
        border-bottom: 1px solid #DFE2E7;
        border-right: 1px solid #DFE2E7;
      }
      .value {
        padding: 15px 20px;
        flex-grow: 1;
        border-bottom: 1px solid #DFE2E7;
      }
      &:last-of-type {
        div {
          border-bottom: 0;
        }
      }
    }
  }
}
