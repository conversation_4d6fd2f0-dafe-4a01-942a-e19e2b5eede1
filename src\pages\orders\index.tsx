import CollapsiblePanel from "@/components/CollapsiblePanel";
import InsightCard from "@/components/InsightCard";
import Title from "@/components/Title";
import styles from "@/styles/orders.module.scss"
import InsightList from "@/components/InsightList";
import { handleInputChange, createStateObject, labels, clearInput } from "@/components/utils/InputHandlers";
import { useEffect, useRef, useState } from "react";
import { Input } from "@/components/Input";
import Button from "@/components/Button";
import { Export, MagnifyingGlass } from "@/components/svgs";
import CheckboxDropdownInput from "@/components/CheckboxDropdownInput";
import { getOrders } from "@/components/utils/dataCreator";
import { orderFields } from "@/components/utils/orderFields";
import { formatDateWithTime } from "@/components/utils/formatDate";
import OrderStatusBadge from "@/components/OrderStatusBadge";
import UserSkeleton from "@/components/UserSkeleton";
import TableControl from "@/components/TableControl";
import Tag from "@/components/Tag";
import OrderManagementModals from "@/pages/orders/order-management-modals";

const Orders = () => {
  const searchOrdersFields = ["orderNumber", "subscriber", "simType", "status", "productName"]
  const [data, setData] = useState(createStateObject(searchOrdersFields));

  const [loading, setLoading] = useState(true);
  const [orders, setOrders] = useState([]);
  const [activeOrder, setActiveOrder] = useState(null);

  const populate = (itemsPerPage: number) => {
    setLoading(true);

    setTimeout(() => {
      setOrders(getOrders(itemsPerPage))
      setLoading(false)
    }, 500)
  }

  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [numberOfPages, setNumberOfPages] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);


  useEffect(() => {
    populate(itemsPerPage)
  }, [itemsPerPage, currentPage])

  const searchPanelRef = useRef<any>(null);
  const overviewPanelRef = useRef<any>(null);

  const [showSearchResults, setShowSearchResults] = useState(false);

  const formatDataItem = (item: any, key: string) => {
    if (key === "dateTime") {
      return formatDateWithTime(item[key])
    } else if (key === "status") {
      return (
        <OrderStatusBadge status={item[key]} />
      )
    } else if (key === "product") {
      return <span style={{ textTransform: 'capitalize' }}>{item[key]}</span>
    } else {
      return item[key]
    }
  }

  return (
    <>
      <OrderManagementModals
        activeOrder={activeOrder}
        setActiveOrder={setActiveOrder}
      />
      <Title>Order Management</Title>
      {/* Overview */}
      <div className={styles.main}>
        <CollapsiblePanel
          title="Overview"
          summaryWhenClosed={
            <div style={{ marginLeft: 22 }}>
              <InsightList insights={overviewStats} />
            </div>
          }
          ref={overviewPanelRef}
        >
          <div className={styles.overview}>
            {overviewStats.map((stat, index) => (
              <InsightCard key={index} title={stat.title} value={stat.value.toString()} />
            ))}
          </div>
        </CollapsiblePanel>

        {/* Search orders */}
        <div style={{ marginTop: 16 }}>
          <CollapsiblePanel
            title="Search Orders"
            summaryWhenClosed={
              <div style={{ display: 'flex', flex: 1, justifyContent: 'space-between', marginLeft: 16, marginRight: 8 }}>
                <Tag text="3 filters applied" />
                <Button color="secondary">Clear Filters</Button>
              </div>
            }
            ref={searchPanelRef}
          >
            <div className={styles.fields}>
              {searchOrdersFields.map((prop) => {
                if (["orderNumber", "subscriber", "productName"].includes(prop)) {
                  return (
                    <Input
                      key={"orders-" + prop}
                      label={labels[prop]}
                      value={data[prop]}
                      onChange={(e: any) => {
                        handleInputChange(prop, e, data, setData);
                      }}
                      error={data.errors[prop]}
                      clear={() => {
                        clearInput(prop, setData);
                      }}
                      infoTooltipText
                    />)
                } else if (["simType", "status"].includes(prop)) {
                  return (
                    <CheckboxDropdownInput
                      key={"orders-" + prop}
                      options={selectOptionsByField[prop]}
                      label={labels[prop]}
                      selected={data[prop]}
                      onChange={(values) => {
                        handleInputChange(
                          prop,
                          values,
                          data,
                          setData,
                          'select'
                        );
                      }}
                      error={data.errors[prop]}
                      infoTooltipText
                    />
                  );
                }
              })}
            </div>
            <div className={styles.actions}>
              <Button
                color="blue"
                onClick={() => {
                  searchPanelRef.current.close()
                  overviewPanelRef.current.close()
                  setShowSearchResults(true)

                }}
              ><MagnifyingGlass /> Search</Button>
            </div>
          </CollapsiblePanel>
        </div>

        {/* Orders Table*/}
        {showSearchResults && <div className={styles.panel}>
          <div className={styles.panelTopBar}>
            <h4>Orders</h4>
            <div className={styles.actions}>
              <Button color="secondary">
                <Export /> Export to CSV
              </Button>
            </div>
          </div>

          <div className={`${styles.tableContainer} table-scroll`}>
            <table>
              <thead>
                <tr>
                  {orderFields.map((field: any) => (
                    <th>{field.label}</th>
                  ))}
                  <th></th>
                </tr>
              </thead>
              <tbody>
                {!loading
                  ? orders.map((item: any, i: number) => {
                    return (
                      <tr key={`order-${item.orderNumber}`} style={{ cursor: 'pointer' }} onClick={() => setActiveOrder(item)}>
                        {orderFields.map((field: any) => (
                          <td key={`order-${item.orderNumber}-${field.key}`}>
                            <div style={{ display: "flex", justifyContent: "flex-start" }}>
                              {formatDataItem(item, field.key)}
                            </div>
                          </td>
                        ))}
                        <td>
                          <span
                            className={styles.viewRowBtn}
                            onClick={() => { setActiveOrder(item) }}>
                            View
                          </span>
                        </td>
                      </tr>
                    )
                  }) : Array.from({ length: itemsPerPage }, (v, i) => i).map(
                    (i) => (
                      <UserSkeleton
                        key={"order-skeleton-" + i}
                        noOfStandard={8}
                      />
                    )
                  )}
              </tbody>
            </table>
          </div>
          <div style={{ marginTop: "16px" }}>
            <TableControl
              show
              itemsPerPage={itemsPerPage}
              setItemsPerPage={(val: any) => setItemsPerPage(val)}
              currentPage={currentPage}
              setCurrentPage={setCurrentPage}
              numberOfPages={numberOfPages}
              label="orders"
              loading={loading}
            />
          </div>
        </div>}
      </div>
    </>
  )
};

export default Orders;

const overviewStats = [
  { title: 'Total Orders', value: '63,629' },
  { title: 'Ready', value: '63,629' },
  { title: 'ICCID Required', value: '63,629' },
  { title: 'Port-in Required', value: '63,629' },
  { title: 'BAN Change', value: '63,629' },
  { title: 'eSIM Orders', value: '63,629' },
  { title: 'SIM Card Orders', value: '63,629' },
]

const selectOptionsByField: Record<string, any> = {
  simType: [
    { label: "Physical SIM", value: "Physical SIM" },
    { label: "eSIM", value: "eSIM" }
  ],
  status: [
    { label: "Ready", value: "Ready" },
    { label: "ICCID Required", value: "ICCID Required" },
    { label: "Port-in Required", value: "Port-in Required" },
    { label: "BAN Change", value: "BAN Change" }
  ]
}