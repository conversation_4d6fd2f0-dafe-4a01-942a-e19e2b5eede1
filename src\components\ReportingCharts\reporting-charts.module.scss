@use "../../styles/theme.scss" as *;

.mainContainer {
  background-color: #fff;
  border-radius: 16px;
  padding: 12px 16px;
  @media (max-width: 1600px) {
    grid-template-columns: 1fr;
  }
  .grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-column-gap: 24px;
    grid-row-gap: 24px;
  }
}

.topBar {
  width: 100%;
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
}

.chartTile {
  width: 100%;
  border: 1.5px solid #dfe2e7;
  border-radius: 16px;
  padding: 24px;
  .title {
    font-size: 16px;
    font-weight: 400;
    line-height: 22px;
    margin-bottom: 12px;
  }
}

.summaryContainer {
  display: flex;
  align-items: center;
  padding: 0px 26px;
}

.doughnut {
  width: 142px;
  margin-right: 49px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.legend {
  .legendItem {
    display: flex;
    align-items: center;
    .legendTitle {
      font-size: 14px;
      font-weight: 400;
      line-height: 18px;
      color: #838ca0;
    }
    .legendDataSm {
      font-size: 14px;
      font-weight: 400;
      line-height: 18px;
      color: #061632;
    }
    .legendDataLg {
      font-size: 20px;
      font-weight: 700;
      line-height: 28px;
      letter-spacing: -0.015em;
      color: #061632;
    }
  }
}

.divider {
  height: 163px;
  width: 1px;
  background-color: #dfe2e7;
  margin: 0px 43px 0px 49px;
}

.users {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 20px;

  .total {
    background-color: $open;
    padding: 20px 0;
    border-radius: 24px;
    text-align: center;
    width: 156px;

    h3 {
      font-size: 14px;
      font-weight: 400;
      margin-bottom: 2px;
      line-height: 21px;
    }

    p {
      font-size: 27px;
      line-height: 41px;
      font-weight: bold;
    }
  }

  .active {
    background-color: #6361dc;
    color: white;
    padding: 20px 0;
    border-radius: 24px;
    text-align: center;
    width: 156px;

    h3 {
      font-size: 14px;
      font-weight: 400;
      margin-bottom: 2px;
      line-height: 21px;
    }

    p {
      font-size: 27px;
      line-height: 41px;
      font-weight: bold;
    }
  }
}

.totalPayments {
  background-color: #9e3dea;
  color: white;
  padding: 22px 16px;
  border-radius: 24px;
  text-align: center;

  h3 {
    font-size: 14px;
    font-weight: 400;
    margin-bottom: 2px;
    line-height: 21px;
  }

  p {
    font-size: 27px;
    line-height: 41px;
    font-weight: bold;
  }

  .totalValue {
    background-color: #efdcfe;
    color: #000;
    padding: 15px 20px;
    border-radius: 24px;
    margin-top: 16px;
  }
}

.summaryItemsContainer {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  padding: 16px;
  .summaryItem {
    background: #f1f6fd;
    border-radius: 24px;
    padding: 16px;
    display: flex;
    align-items: center;
    .icon {
      padding: 12px;
      display: flex;
      align-items: center;
      background-color: #d6e3fa;
      border-radius: 1000px;
      margin-right: 16px;
      .svgContainer {
        width: 24px;
        height: 24px;
        svg {
          color: #1857c3;
        }
      }
    }
    .label {
      font-size: 14px;
      font-weight: 400;
      line-height: 18px;
      color: #667085;
      margin-bottom: 2px;
    }
    .amount {
      font-size: 20px;
      font-weight: 700;
      line-height: 28px;
      color: #061632;
    }
  }
}
