import BlobSwitchBar from "@/components/BlobSwitchBar";
import Button from "@/components/Button";
import Dialog from "@/components/Dialog";
import { useState } from "react";
import styles from './order-detail-modal.module.scss'
import { CallCircle, Export, BarCodeCircle, LocationPinCircle, SimCardCircle, TagCircle, CoinCircle, PercentCircle, ScaleCircle, LightningCircle, CardCircle, MasterCard, WalletCircle, ArrowRight } from "@/components/svgs";
import OrderStatusBadge from "@/components/OrderStatusBadge";
import { formatDateWithTime } from "@/components/utils/formatDate";
import { orderStatuses } from "@/components/utils/dataCreator";
import TableControl from "@/components/TableControl";
import DetailsCard from "@/components/DetailsCard";

type OrderDetailModalProps = {
  activeOrder: any;
  open: boolean;
  onClose: () => void
  onAddPortInDetails: () => void
  onAddIccid: () => void
}

const OrderDetailModal = ({
  activeOrder,
  open,
  onClose,
  onAddPortInDetails,
  onAddIccid
}: OrderDetailModalProps) => {
  const [activeTab, setActiveTab] = useState("overview")
  const tabs = [
    {
      id: "overview",
      label: "Overview"
    },
    {
      id: "activityLog",
      label: "Activity Log"
    }
  ]

  const shouldShowAddCCID = (["DETAILSREQUIRED", "ICCIDREQUIRED"] as Array<typeof orderStatuses[number]>).includes(activeOrder.status);
  const shouldShowAddPortIn = (["DETAILSREQUIRED", "PORTINREQUIRED"] as Array<typeof orderStatuses[number]>).includes(activeOrder.status);

  if (!activeOrder) return null

  return (
    <Dialog
      open={open}
      onClose={onClose}
      size="md"
      heightInPx={600}
    >
      <h3 className={styles.title}>Order</h3>
      <p className={styles.subTitle}>#{activeOrder.orderNumber}</p>

      <div className={styles.tabsAndActionsContainer}>
        <BlobSwitchBar
          selected={activeTab}
          setSelected={setActiveTab}
          layoutId="order-details-modal-tabs"
          options={tabs}
        />

        <div className={styles.actions}>
          {activeTab === "overview" && (
            <>
              {shouldShowAddPortIn && <Button color="secondary" onClick={onAddPortInDetails}>Add Port In Details</Button>}
              {shouldShowAddCCID && <Button color="secondary" onClick={onAddIccid}>Add ICCID</Button>}
            </>
          )}
          {activeTab === "activityLog" && (
            <Button color="secondary"><Export /> Export to CSV</Button>
          )}
        </div>
      </div>

      {activeTab === "overview" && (
        <>
          <div className={styles.statusAndDateContainer}>
            <OrderStatusBadge status={activeOrder.status} />
            <p className={styles.dateTime}>
              Creation Date: {formatDateWithTime(activeOrder.dateTime)}
            </p>
          </div>

          <div className={styles.detailCardsContainer}>
            <DetailsCard
              title="Subscriber Details"
              items={[
                {
                  title: "Subscriber",
                  value: activeOrder.subscriber,
                  icon: <CallCircle />
                },
                {
                  title: "Delivery Address",
                  value: "123 E Main StreetSpringfield, IL 62701",
                  icon: <LocationPinCircle />
                }
              ]}
            />

            <DetailsCard
              title="Order Details"
              items={[
                {
                  title: "SIM Type",
                  value: activeOrder.simType,
                  icon: <SimCardCircle />
                },
                {
                  title: "Product",
                  value: <span style={{ textTransform: 'capitalize' }}>{activeOrder.product}</span>,
                  icon: <TagCircle />
                },
                {
                  title: "IMEI",
                  value: "9812745098165987",
                  icon: <BarCodeCircle />
                },
                {
                  title: "ICCID",
                  value: getICCIDValueForOrderStatus(activeOrder.status),
                  icon: <SimCardCircle />
                }
              ]}
            />

            <DetailsCard
              title="Payment Details"
              items={[
                {
                  title: "Order Total",
                  value: `$${activeOrder.price}`,
                  icon: <CoinCircle />
                },
                {
                  title: "Tax Amount",
                  value: "$6.78",
                  icon: <PercentCircle />
                },
                {
                  title: "Regulatory Fee",
                  value: "$6.78",
                  icon: <ScaleCircle />
                },
                {
                  title: "Activation Fee",
                  value: "$6.78",
                  icon: <LightningCircle />
                },
                {
                  title: "Payment Method",
                  value: <MasterCardPaymentMethod />,
                  icon: <CardCircle />
                },
                {
                  title: "Final Checkout Amount",
                  value: "$134.56",
                  icon: <WalletCircle />
                }
              ]}
            />
          </div>
        </>
      )}

      {activeTab === "activityLog" && (
        <div className={styles.activityLogTabContainer}>
          <div className={styles.activityLogTableContainer}>
            <table>
              <thead>
                <tr>
                  <th>Date & Time</th>
                  <th>Email Address</th>
                  <th>Activity</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>03 Dec 2024 17:50:20</td>
                  <td><EMAIL></td>
                  <td className={styles.activityCell}><span>ICCID Required</span> <ArrowRight width="16" height="16" /> <span>Ready</span></td>
                </tr>
                <tr>
                  <td>03 Dec 2024 16:10:58</td>
                  <td><EMAIL></td>
                  <td className={styles.activityCell}><span>Details Required</span> <ArrowRight width="16" height="16" /> <span>ICCID Required</span></td>
                </tr>
              </tbody>
            </table>
          </div>

          <div style={{ marginTop: "auto" }}>
            <TableControl
              show
              itemsPerPage={2}
              setItemsPerPage={() => { }}
              currentPage={1}
              setCurrentPage={() => { }}
              numberOfPages={1}
            />
          </div>
        </div>
      )}
    </Dialog>
  )
}

export default OrderDetailModal


function getICCIDValueForOrderStatus(orderStatus: typeof orderStatuses[number]) {
  switch (orderStatus) {
    case "ICCIDREQUIRED":
    case "DETAILSREQUIRED":
      return "-"
    case "READY":
    case "BANCHANGE":
    case "PORTINREQUIRED":
      return "9812745098165987"
    default:
      return "-"
  }

}

const MasterCardPaymentMethod = () => {
  return (
    <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
      <MasterCard />
      <span>•••• 1234</span>
    </div>
  )
}