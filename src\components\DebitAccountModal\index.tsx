import Dialog from "@/components/Dialog";
import { MinusCircle } from "@/components/svgs";
import { clearInput, createStateObject, handleInputChange, labels } from "@/components/utils/InputHandlers";
import { useState } from "react";
import styles from "./debit-account-modal.module.scss";
import { Input } from "@/components/Input";

type DebitAccountModalProps = {
  open: boolean;
  onClose: () => void;
}

const DebitAccountModal = ({
  open,
  onClose
}: DebitAccountModalProps) => {
  const fields = ["amount", "reason"];
  const [formData, setFormData] = useState(createStateObject(fields));

  return (
    <Dialog
      open={open}
      onClose={onClose}
      size="sm"
      headerIcon={<MinusCircle />}
      headerTitle="Debit Account"
      headerSubtitle="Deduct an amount from [<EMAIL>]. This action will reduce their account balance."
      confirmButtonText="Debit Account"
      confirmButtonOnClick={onClose}
      cancelButtonText="Cancel"
    >
      <div className={styles.formContainer}>
        {fields.map(field => (
          <Input
            key={"debit-" + field}
            label={labels[field]}
            value={formData[field]}
            onChange={(e: any) => {
              handleInputChange(field, e, formData, setFormData);
            }}
            clear={() => {
              clearInput(field, setFormData);
            }}
            infoTooltipText
          />
        ))}
      </div>
    </Dialog>
  );
};

export default DebitAccountModal;