import { useEffect, useState } from "react";
import {
  clearInput,
  createStateObject,
  displayErrors,
  getMessages,
  getRules,
  handleInputChange,
  labels,
  placeholders,
} from "../utils/InputHandlers";
import styles from "./first-password-change.module.scss";
import { Input } from "../Input";
import Toggle from "../Toggle";
import Button from "../Button";
import { validateAll } from "indicative/validator";
import { ApiFirstPostAuth, ApiPostAuth } from "../../pages/api/api";
import { useDispatch } from "react-redux";
import { useNavigate, useSearchParams } from "react-router-dom";

const fields = ["password", "confirmPassword"];
const rules = getRules(fields);
const messages = getMessages(fields);

const FirstPasswordChange = () => {
  const dispatch = useDispatch();
  const [data, setData] = useState(createStateObject(fields));

  const [loading, setLoading] = useState(false);

  const navigate = useNavigate();

  const handleBackToLogin = () => {
    navigate("/login");
  };

  const [searchParams, setSearchParams] = useSearchParams();

  let authToken: string = searchParams.get("auth") as string;
  useEffect(() => {
    if (authToken) {
      if (authToken[authToken.length - 1] === "/") {
        authToken = authToken.slice(0, authToken.length - 1);
      }
      localStorage.removeItem("crmUserInfo");
      localStorage.setItem("token", authToken);
    }
  }, [searchParams]);
  const handleSubmit = () => {
    const input = {
      password: data.password,
      confirmPassword: data.confirmPassword,
    };
    validateAll(input, rules, messages)
      .then((response) => {
        if (input.password !== input.confirmPassword) {
          setData({
            ...data,
            errors: {
              password: " ",
              confirmPassword: "Passwords do not match",
            },
          });
        } else {
          setLoading(true);
          ApiFirstPostAuth(
            "/agent/first-login",
            {
              newPassword: input.password,
              confirmNewPassword: input.confirmPassword,
            },
            authToken
          )
            .then((response) => {
              dispatch({
                type: "notify",
                payload: {
                  error: false,
                  message: response.data.message,
                },
              });
              handleBackToLogin();
            })
            .catch((error) => {
              setLoading(false);
              dispatch({
                type: "notify",
                payload: {
                  error: true,
                  message: error.response.data.message,
                },
              });
            });
        }
      })
      .catch((errors) => {
        displayErrors(errors, setData);
      });
  };

  return (
    <div className={styles.form}>
      <h2>Finish Registration</h2>
      <p className={styles.tag}>
        Set up a password to finish your registration.
      </p>
      <div className={styles.inputContainer}>
        {fields.map((key: string) => (
          <Input
            key={key}
            label={labels[key]}
            placeholder={placeholders[key]}
            value={data[key]}
            disabled={loading}
            onChange={(e: any) => {
              handleInputChange(key, e, data, setData);
            }}
            error={data.errors[key]}
            clear={() => {
              clearInput(key, setData);
            }}
            id={"change-password-" + key}
            onKeyDown={handleSubmit}
            password
          />
        ))}
      </div>
      <Button
        style={{ marginTop: 32 }}
        loading={loading}
        color="primary"
        onClick={handleSubmit}
      >
        Finish Registration
      </Button>
    </div>
  );
};

export default FirstPasswordChange;
