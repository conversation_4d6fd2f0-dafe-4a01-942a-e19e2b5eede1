@use "./theme.scss" as *;
@import "./table-mixin.module.scss";
@import "./mixins.module.scss";

.main {
  width: 100%;
  padding: 23px 40px 50px 40px;
}

.overview {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.fields {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-top: 20px
}

.actions {
  display: flex;
  justify-content: end;
}

.panel {
  @include panel;
  @include table;

  margin-top: 16px;
}

.tableContainer {
  overflow: auto;
  padding-bottom: 5px;
  margin-top: 20px;
  border-radius: 16px;

  .viewRowBtn {
    color: var(--button-tertiary-text);
    font-size: 14px;
    font-weight: 700;
    text-decoration: underline;
    cursor: pointer;
    visibility: hidden;
    display: flex;
    align-items: center;
    span {
      margin-right: 8px;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  td {
    width: 17%;
  }
  tr:first-of-type .viewRowBtn {
    visibility: visible;
  }
  tr:hover .viewRowBtn{
    visibility: visible;
  }
}

.topBar {
  display: flex;
  align-items: center;
}

.usersPanel:not(.products) {
  table {
    td:last-child,
    th:last-child {
      text-align: end;
    }
  }
}

.logs {
  overflow-x: auto;
  td,
  th {
    white-space: nowrap;
  }
}

.logHeading {
  display: flex;
  align-items: center;
  h2 {
    margin: 0;
  }
}

.products .filters {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  .label {
    margin-right: 13px;
    font-size: 14px;
    line-height: 21px;
  }
}

.actionPanel {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-left: auto;
}

.actionButton {
  background: none;
  border: none;
  width: 44px;
  height: 41px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background 0.15s ease;
  border-radius: 8px;
  margin-right: 12px;
  color: #1a1a1a;
  &:last-child {
    margin-right: 0px;
  }
  &:hover {
    background: #fff;
  }
}

.noneFound {
  display: grid;
  width: 100%;
  min-height: 510px;
  align-items: center;
  justify-content: center;
  padding-top: 55px;
  img,
  h3 {
    grid-area: 1 / 1 / 2 / 2;
    font-size: 24px;
    font-weight: 700;
    line-height: 36px;
  }
  h3 {
    width: 100%;
    text-align: center;
  }
}

.skeletonContainer {
  display: flex;
  flex-direction: column;
}

.graphic {
  display: flex;
  align-items: center;
  svg {
    margin: 0 20px;
  }
}
