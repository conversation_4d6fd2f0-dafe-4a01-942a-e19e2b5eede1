/*
==========================================================================================

                                        Checkbox

Description: Reusable checkbox component

Parameters: checked (bool) - whether checkbox is checked or not
            onClick (func) - handle click on checkbox
            error (str) - inline error message
            size (num) - default = 27 - size of box in px
            disabled (bool) - whether checkbox is disabled or not

==========================================================================================
*/

import styles from "./radio.module.scss";

const Radio = ({ checked, onClick, disabled, id = "" }: any) => {
  return (
    <div className={styles.container}>
      <input
        type="checkbox"
        className={styles.checkbox}
        onChange={() => {}}
        checked={checked}
        disabled={disabled}
      />
      <div
        className={`${styles.box} ${checked && styles.checked}`}
        onClick={(e: any) => {
          !disabled && onClick(e);
        }}
        id={id}
      >
        <div
          className={`${styles.circle} ${checked && styles.checkedCircle}`}
        />
      </div>
    </div>
  );
};

export default Radio;
