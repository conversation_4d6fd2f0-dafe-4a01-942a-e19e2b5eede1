# Dependencies
node_modules/

# Build outputs
dist/
build/
*.tsbuildinfo

# Package manager files
package-lock.json
yarn.lock
pnpm-lock.yaml
bun.lockb

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/

# OS files
.DS_Store
Thumbs.db

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Coverage reports
coverage/

# Public assets (images, etc.)
public/

# Generated files
*.min.js
*.min.css
