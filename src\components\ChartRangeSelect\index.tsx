import styles from "./chart-range-select.module.scss";
import "@szhsin/react-menu/dist/index.css";
import "@szhsin/react-menu/dist/transitions/slide.css";
import { Calendar } from "../svgs";
import { useRef, useState } from "react";
import { formatReportsDate } from "../utils/formatDate";
import moment from "moment";
import DateOnlyPicker from "../DateOnlyPicker";

const ChartRangeSelect = ({
  label,
  selected,
  setSelected,
  noClear,
  masterFrom,
  masterUntil,
}: any) => {
  const ref = useRef(null);

  const options = ["yesterday", "week", "month", "Custom"];

  const labels = {
    yesterday: `Yesterday (${moment().subtract(1, "d").format("D MMM")})`,
    week: `Week (${moment().subtract(7, "d").format("D")}-${moment().subtract(1, "d").format("D MMM")})`,
    month: `Month (${moment().subtract(31, "d").format("D MMM")} - ${moment().subtract(1, "d").format("D MMM")})`,
  } as any;

  ////////// Custom Range Select

  const today = new Date();

  const [fromDate, setFromDate] = useState(null);
  const [untilDate, setUntilDate] = useState(null as any);
  const [showDatePicker, setShowDatePicker] = useState(false);

  const interval = {
    yesterday: 1,
    week: 7,
    month: 30,
  } as any;

  const handleSelectDateRange = (item: string) => {
    if ((item === 'yesterday') || (item === 'week') || (item === 'month')) {
      setSelected({
        start: formatReportsDate(
          new Date(
            new Date().setDate(
              new Date().getDate() - ((interval[item] as number) ?? 30)
            )
          )
        ),
        end: formatReportsDate(new Date()),
        key: item,
      });
      setShowDatePicker(false)
    } else {
      setShowDatePicker(true)
    }
  }

  return (
    <div className={styles.container}>
      {options.map((item: any) => (
        <div
          className={`${styles.menuItem} ${selected === item && styles.selected}`}
          key={item}
          onClick={() => handleSelectDateRange(item)}>
          {item === 'Custom' ? <span><Calendar /></span> : labels[item]}
          {
            showDatePicker && item === "Custom" && (
                <DateOnlyPicker
                  masterFrom={fromDate}
                  setFromDate={setFromDate}
                  masterUntil={untilDate}
                  setUntilDate={setUntilDate}
                  today={today}
                  showDate={false}
                  onChange={(newFrom: Date, newUntil: Date) => {
                    setSelected({
                      start: formatReportsDate(newFrom),
                      end: formatReportsDate(newUntil),
                      key: 'Custom'
                    });
                  }}
                  reports
                />
            )
          }
        </div>
      ))}
    </div>
  );
};

export default ChartRangeSelect;
