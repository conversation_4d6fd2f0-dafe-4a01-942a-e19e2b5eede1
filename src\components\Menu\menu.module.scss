@use "../../styles/theme.scss" as *;

.menuButton {
  height: 56px;
  background: none;
  border: none;
  font-size: 16px;
  display: flex;
  align-items: center;
  cursor: pointer;
  -webkit-user-select: none; /* Safari */
  -ms-user-select: none; /* IE 10 and IE 11 */
  user-select: none;
  svg {
    transition: all 0.2s ease;
    margin-left: 8px;
  }
  &.iconOpen {
    svg {
      transform: rotate(180deg);
    }
  }
}

.box {
  height: 56px;
}

.menuItem {
  padding: 0 18px 0 24px;
  display: flex;
  align-items: center;
  height: 56px;
  border-radius: 6px;
  transition: all 0.1s ease;
  color: $black;
  svg {
    margin-right: 16px;
  }
  &:hover {
    background: $light-orange;
  }
}
