export type ReportTypes = {
  Voice: any[];
  SMS: any[];
  Numbers: any[];
  Finance: any[];
  Charts: any[];
};

export type ReportSections = keyof ReportTypes;

export type NewReport = {
  [K in ReportSections]: ReportTypes[K];
};

export type SectionedReport = ReportTypes[keyof ReportTypes];

export type RangeStr =
  | "last day"
  | "last 7 days"
  | "last month"
  | "last 3 months"
  | "last 6 months"
  | "last 12 months";

export type Range = {
  start: string;
  end: string;
  label: string;
};

export type Interval = { [Key in RangeStr]: number };
