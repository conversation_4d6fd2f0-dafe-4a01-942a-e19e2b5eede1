import { Collapse } from "@mui/material";
import styles from "./remove-filters-bar.module.scss";
import Button from "../Button";
import { AnimatePresence } from "framer-motion";
import RemoveFilter from "../RemoveFilter";
import formatDate, { formatDateWords } from "../utils/formatDate";
import { handleFilterChange } from "../utils/searchAndFilter";
import { statuses } from "../StatusPill";
import moment from "moment";

const RemoveFiltersBar = ({ filters, setFilters, resetFilters, grey }: any) => {
  const getCurrencySymbol = (code: string) => {
    switch (code) {
      case "usd":
        return "$";
      case "gbp":
        return "£";
      case "eur":
        return "€";
      default:
        return "";
    }
  };

  return (
    <Collapse
      in={Object.keys(filters).some((key: string) => {
        if (Array.isArray(filters[key])) {
          return filters[key].length !== 0;
        } else {
          if ("from" in filters[key]) {
            return filters[key].from !== "" && filters[key].from !== null;
          } else {
            return filters[key].start;
          }
        }
      })}
    >
      <div className={styles.removeFilters}>
        <Button
          style={{
            padding: 0,
            fontWeight: 500,
            fontSize: 14,
            marginRight: 24,
            marginBottom: 8,
          }}
          color="quaternary"
          onClick={resetFilters}
        >
          Clear all
        </Button>
        <AnimatePresence>
          {Object.keys(filters).map((filterType: string) => {
            if (!Array.isArray(filters[filterType])) {
              if (filters[filterType].start) {
                return (
                  <RemoveFilter
                    type={filterType}
                    handleRemoveFilter={(x: any, y: any) => {
                      setFilters({
                        ...filters,
                        [filterType]: {
                          start: null,
                          end: null,
                          startTime: {
                            hh: "09",
                            mm: "00",
                          },
                          endTime: {
                            hh: "21",
                            mm: "00",
                          },
                        },
                      });
                    }}
                    key={"remove-" + filterType}
                    filter={filterType}
                    grey={grey}
                  >
                    {filters[filterType].start === filters[filterType].end ||
                    filters[filterType].end === null
                      ? formatDateWords(filters[filterType].start, true)
                      : `${formatDateWords(
                          filters[filterType].start,
                          true
                        )} - ${formatDateWords(filters[filterType].end, true)}`}
                    &nbsp;&nbsp;&nbsp;&nbsp;
                    {"startTime" in filters[filterType] && (
                      <>
                        {filters[filterType].startTime.hh}:
                        {filters[filterType].startTime.mm}
                        {filters[filterType].endTime &&
                          ` - ${filters[filterType].endTime.hh}:
                        ${filters[filterType].endTime.mm}`}
                      </>
                    )}
                  </RemoveFilter>
                );
              } else if (
                filterType === "creationTime" &&
                filters[filterType].from
              ) {
                return (
                  <RemoveFilter
                    type={filterType}
                    handleRemoveFilter={(x: any, y: any) => {
                      setFilters({
                        ...filters,
                        [filterType]: {
                          from: null,
                          to: null,
                        },
                      });
                    }}
                    key={"remove-" + filterType}
                    filter={filterType}
                    grey={grey}
                  >
                    Sign up date:{" "}
                    {filters[filterType].from === filters[filterType].to ||
                    filters[filterType].to === null
                      ? moment(filters[filterType].from).format("D MMMM YYYY")
                      : `${moment(filters[filterType].from).format(
                          "D MMMM YYYY"
                        )} - ${moment(filters[filterType].to).format("D MMMM YYYY")}`}
                  </RemoveFilter>
                );
              } else if (["usd", "gbp", "eur"].includes(filterType)) {
                if (filters[filterType].from) {
                  return (
                    <RemoveFilter
                      type={filterType}
                      handleRemoveFilter={(x: any, y: any) => {
                        setFilters({
                          ...filters,
                          [filterType]: {
                            from: "",
                            to: "",
                          },
                        });
                      }}
                      filterObject={filters}
                      setFilterObject={setFilters}
                      key={"remove-" + filterType}
                      filter={filterType}
                      grey={grey}
                    >
                      {filters[filterType].to !== "" ? (
                        <>
                          {getCurrencySymbol(filterType)}
                          {filters[filterType].from} -{" "}
                          {getCurrencySymbol(filterType)}
                          {filters[filterType].to}
                        </>
                      ) : (
                        <>
                          {getCurrencySymbol(filterType)}
                          {filters[filterType].from}
                        </>
                      )}
                    </RemoveFilter>
                  );
                }
              }
            } else {
              return filters[filterType].map((filter: any) => (
                <RemoveFilter
                  type={filterType}
                  handleRemoveFilter={handleFilterChange}
                  filterObject={filters}
                  setFilterObject={setFilters}
                  key={"remove-" + filter}
                  filter={filter}
                  status={
                    filterType === "subscriberNumberStatus"
                      ? statuses.indexOf(filter)
                      : filterType === "status"
                        ? statuses.indexOf(
                            filter === "true" ? "Active" : "Inactive"
                          )
                        : null
                  }
                  grey={grey}
                >
                  {filterType === "status"
                    ? filter === "true"
                      ? "Active"
                      : "Inactive"
                    : filterType === "paymentStatus"
                      ? filter === 1
                        ? "Completed"
                        : "Failed"
                      : filter}
                </RemoveFilter>
              ));
            }
          })}
        </AnimatePresence>
      </div>
    </Collapse>
  );
};

export default RemoveFiltersBar;
