import { useState } from "react";
import PlanDataBar from "../PlanDataBar";
import ProductsMenu from "../ProductsMenu";
import { Signal, Phone, Chat, Calendar, DotsThree, Cog } from "../svgs";
import Toggle from "../Toggle";
import { formatDateWords } from "../utils/formatDate";
import { formatDashboardPlanData } from "../utils/formatPlanData";
import styles from "./customer-plan-tile.module.scss";
import ActiveDeactiveCustomerDID from "../ActiveDeactiveCustomerDID";
import CancelCustomerProduct from "../CancelCustomerProduct";
import { formatNumber } from "../utils/formatNumber";
import StatusPill from "../StatusPill";

const CustomerPlanTile = ({
  data,
  repopulate,
  cancelled,
  expired,
  inactive,
}: any) => {
  const [modal, setModal] = useState(false);

  const [showCancelProduct, setShowCancelProduct] = useState(false);

  const getDateDisplay = () => {
    if (cancelled) {
      return `Cancelled on ${formatDateWords(data?.endDate)}`;
    } else if (expired) {
      return `Expired on ${formatDateWords(data?.endDate)}`;
    } else if (inactive) {
      return `Expires on ${formatDateWords(data?.activationLimit)}`;
    } else if (data.autorenew) {
      return `Renews on ${formatDateWords(data?.endDate)}`;
    } else {
      return `Expires on ${formatDateWords(data?.endDate)}`;
    }
  };

  return (
    <div className={`${styles.main}`}>
      <ActiveDeactiveCustomerDID
        show={modal}
        setShow={setModal}
        number={data.didNumber}
        mode={data.deactivatedPhone ? "activate" : "deactivate"}
        repopulate={repopulate}
      />

      <CancelCustomerProduct
        show={showCancelProduct}
        setShow={setShowCancelProduct}
        data={data}
        repopulate={repopulate}
        isNumber={data.planType === "phone"}
      />

      <div className={styles.topSection}>
        <div style={{ display: "flex" }}>
          <StatusPill
            status={
              cancelled
                ? "Cancelled"
                : expired
                  ? "Expired"
                  : inactive
                    ? "Inactive"
                    : "Active"
            }
          />
          <div className={styles.classification}>{data.classification}</div>
        </div>
        {!cancelled && !expired && (
          <ProductsMenu
            data={{
              icon: <Cog />,
              items:
                data.planType === "phone"
                  ? [
                      {
                        label: `${
                          data.deactivatedPhone ? "Activate" : "Deactivate"
                        }`,
                        onClick: () => {
                          setModal(true);
                        },
                      },
                      {
                        label: "Delete",
                        onClick: () => {
                          setShowCancelProduct(true);
                        },
                      },
                    ]
                  : [
                      {
                        label: "Cancel",
                        onClick: () => {
                          setShowCancelProduct(true);
                        },
                      },
                    ],
            }}
          />
        )}
      </div>
      <div className={styles.title}>
        <CountryDisplay data={data} />
        <div className={styles.info}>
          <div className={styles.titleText}>
            {data.planName}
            {data.region && " • " + data.region}
            {data.city && " • " + data.city}
          </div>
          <div className={styles.planData}>{formatDashboardPlanData(data)}</div>
        </div>
      </div>
      {!cancelled && !expired && (
        <>
          {data.initialBytes !== 0 && (
            <PlanDataBar
              Icon={Signal}
              displayText={`${data.remainingData} left`}
              percentage={(data.remainingBytes / data.initialBytes) * 100 + "%"}
              faded={cancelled || expired}
              grey={inactive}
            />
          )}
          {data.initialMinutes !== 0 && (
            <PlanDataBar
              Icon={Phone}
              displayText={`${data.remainingMinutes} mins left`}
              percentage={
                (data.remainingMinutes / data.initialMinutes) * 100 + "%"
              }
              faded={cancelled || expired}
              grey={inactive}
            />
          )}
          {data.initialMessages !== 0 && (
            <PlanDataBar
              Icon={Chat}
              displayText={`${data.remainingMessages} SMS left`}
              percentage={
                (data.remainingMessages / data.initialMessages) * 100 + "%"
              }
              faded={cancelled || expired}
              grey={inactive}
            />
          )}
        </>
      )}
      <div
        className={`${styles.bottomSection} ${cancelled ? styles.cancelled : expired ? styles.expired : inactive ? styles.cancelled : data?.autorenew ? styles.renew : styles.cancelled}`}
      >
        {/*<div className={styles.date}>
          <Calendar />
          {formatDateWords(data.startDate, true)} -{" "}
          {formatDateWords(data.endDate, true)}
        </div>
        <div className={styles.autorenew}>
          <div className={styles.autorenewText}>Auto-renew</div>
          <Toggle
            on={data.autorenew}
            readonly={inactive || cancelled || expired}
          />
        </div>*/}
        {getDateDisplay()}
      </div>
    </div>
  );
};

export default CustomerPlanTile;

const CountryDisplay = ({ data }: any) => {
  return (
    <div className={styles.country}>
      <div
        className={styles.flag}
        style={{
          backgroundImage: `url(${data?.flagImage})`,
        }}
      />
    </div>
  );
};
