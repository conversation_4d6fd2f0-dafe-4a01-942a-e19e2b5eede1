import Shimmer from "../Shimmer";
import styles from "./number-skeleton.module.scss";

const NumberSkeleton = ({ noOfStandard, showActionBar }: any) => {
  return (
    <tr className={styles.main}>
      {Array.from({ length: noOfStandard }, (v, i) => i).map((i) => (
        <td>
          <div className={styles.box}>
            <Shimmer />
          </div>
        </td>
      ))}
      {showActionBar && (
        <td>
          <div className={styles.dataBar}>
            <div className={styles.smallBox}>
              <Shimmer />
            </div>
            <div className={styles.smallBox}>
              <Shimmer />
            </div>
            <div className={styles.smallBox}>
              <Shimmer />
            </div>
          </div>
        </td>
      )}
    </tr>
  );
};

export default NumberSkeleton;
