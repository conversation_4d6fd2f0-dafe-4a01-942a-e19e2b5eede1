import CollapsiblePanel from "@/components/CollapsiblePanel";
import InsightCard from "@/components/InsightCard";
import InsightList from "@/components/InsightList";
import {
  clearInput,
  createStateObject,
  handleInputChange,
  labels,
} from "@/components/utils/InputHandlers";
import { useEffect, useRef, useState } from "react";
import styles from "./retail-tab.module.scss";
import Tag from "@/components/Tag";
import Button from "@/components/Button";
import SelectInput from "@/components/SelectInput";
import { Input } from "@/components/Input";
import { Delete, Export, MagnifyingGlass, Pencil } from "@/components/svgs";
import { getProducts } from "@/components/utils/dataCreator";
import { formatDateWords } from "@/components/utils/formatDate";
import { getProductFields } from "@/components/utils/productFields";
import UserSkeleton from "@/components/UserSkeleton";
import TableControl from "@/components/TableControl";
import Toggle from "@/components/Toggle";
import EditRetailProductModal from "@/components/EditRetailProductModal";
import DeleteProductModal from "@/components/DeleteProductModal";
import CheckboxDropdownInput from "@/components/CheckboxDropdownInput";

const RetailTab = () => {
  const searchOrdersFields = [
    "family",
    "name",
    "sizeGB",
    "billing",
    "visibleOnRetail",
  ];
  const [data, setData] = useState(createStateObject(searchOrdersFields));

  const overviewPanelRef = useRef<any>(null);
  const searchPanelRef = useRef<any>(null);
  const [showSearchResults, setShowSearchResults] = useState(false);

  const [loading, setLoading] = useState(true);
  const [products, setProducts] = useState<any[]>([]);

  const populate = (itemsPerPage: number) => {
    setLoading(true);

    setTimeout(() => {
      setProducts(getProducts(itemsPerPage, "retail"));
      setLoading(false);
    }, 500);
  };

  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [numberOfPages, setNumberOfPages] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  useEffect(() => {
    populate(itemsPerPage);
  }, [itemsPerPage, currentPage]);

  const changeStatus = (i: number) => {
    let selectedProduct = products[i];
    selectedProduct.status = !products[i].status;
    let updatedFeatures = [...products];
    updatedFeatures[i] = products[i];
    setProducts(updatedFeatures);
  };

  const formatDataItem = (item: any, key: string, index: number) => {
    if (key === "creationDate") {
      return formatDateWords(item[key]);
    }

    if (key === "status") {
      return <Toggle on={item[key]} onChange={() => changeStatus(index)} />;
    } else {
      if (!item[key]) {
        return "-";
      }

      return item[key];
    }
  };

  const [activelyEditedProduct, setActivelyEditedProduct] = useState(null);
  const [activelyDeletedProduct, setActivelyDeletedProduct] = useState(null);

  return (
    <>
      {!!activelyEditedProduct && (
        <EditRetailProductModal
          onClose={() => setActivelyEditedProduct(null)}
          open={!!activelyEditedProduct}
          productData={activelyEditedProduct}
        />
      )}
      {!!activelyDeletedProduct && (
        <DeleteProductModal
          onClose={() => setActivelyDeletedProduct(null)}
          open={!!activelyDeletedProduct}
          productData={activelyDeletedProduct}
          type="retail"
        />
      )}

      {/* Overview */}
      <CollapsiblePanel
        title="Overview"
        summaryWhenClosed={
          <div style={{ marginLeft: 22 }}>
            <InsightList insights={overviewStats} />
          </div>
        }
        ref={overviewPanelRef}
      >
        <div className={styles.overview}>
          {overviewStats.map((stat, index) => (
            <InsightCard
              key={index}
              title={stat.title}
              value={stat.value.toString()}
            />
          ))}
        </div>
      </CollapsiblePanel>

      {/* Search */}
      <div style={{ marginTop: 16 }}>
        <CollapsiblePanel
          title="Search Retail Products"
          summaryWhenClosed={
            <div
              style={{
                display: "flex",
                flex: 1,
                justifyContent: "space-between",
                marginLeft: 16,
                marginRight: 8,
              }}
            >
              <Tag text="3 filters applied" />
              <Button color="secondary">Clear Filters</Button>
            </div>
          }
          ref={searchPanelRef}
        >
          <div className={styles.searchPanelForm}>
            <div className={styles.fields}>
              {searchOrdersFields.map((prop) => {
                if (["name"].includes(prop)) {
                  return (
                    <Input
                      key={"att-" + prop}
                      label={labels[prop]}
                      value={data[prop]}
                      onChange={(e: any) => {
                        handleInputChange(prop, e, data, setData);
                      }}
                      error={data.errors[prop]}
                      clear={() => {
                        clearInput(prop, setData);
                      }}
                      infoTooltipText
                    />
                  );
                } else if (
                  ["family", "sizeGB", "billing", "visibleOnRetail"].includes(
                    prop
                  )
                ) {
                  return (
                    <CheckboxDropdownInput
                      key={"promotions-" + prop}
                      options={selectOptionsByField[prop]}
                      label={labels[prop]}
                      selected={data[prop]}
                      onChange={(values) => {
                        handleInputChange(
                          prop,
                          values,
                          data,
                          setData,
                          "select"
                        );
                      }}
                      error={data.errors[prop]}
                      infoTooltipText
                    />
                  );
                }
              })}
            </div>
            <div className={styles.actions}>
              <Button
                color="blue"
                onClick={() => {
                  searchPanelRef.current.close();
                  overviewPanelRef.current.close();
                  setShowSearchResults(true);
                }}
              >
                <MagnifyingGlass /> Search
              </Button>
            </div>
          </div>
        </CollapsiblePanel>
      </div>

      {/* Table */}
      {showSearchResults && (
        <div className={styles.panel}>
          <div className={styles.panelTopBar}>
            <h4>Products</h4>
            <div className={styles.actions}>
              <Button color="secondary">
                <Export /> Export to CSV
              </Button>
            </div>
          </div>

          <div className={`${styles.tableContainer} table-scroll`}>
            <table>
              <thead>
                <tr>
                  {getProductFields("retail").map((field: any) => (
                    <th>{field.label}</th>
                  ))}
                  <th></th>
                </tr>
              </thead>
              <tbody>
                {!loading
                  ? products.map((item: any, i: number) => {
                      return (
                        <tr key={`product-${i}`}>
                          {getProductFields("retail").map((field: any) => (
                            <td key={`product-${i}-${field.key}`}>
                              <div
                                style={{
                                  display: "flex",
                                  justifyContent: "flex-start",
                                }}
                              >
                                {formatDataItem(item, field.key, i)}
                              </div>
                            </td>
                          ))}
                          <td style={{ width: 100 }}>
                            <div className={styles.tableRowActions}>
                              <span
                                onClick={() => setActivelyEditedProduct(item)}
                              >
                                <Pencil />
                              </span>
                              <span
                                onClick={() => setActivelyDeletedProduct(item)}
                              >
                                <Delete />
                              </span>
                            </div>
                          </td>
                        </tr>
                      );
                    })
                  : Array.from({ length: itemsPerPage }, (v, i) => i).map(
                      (i) => (
                        <UserSkeleton
                          key={"order-skeleton-" + i}
                          noOfStandard={11}
                        />
                      )
                    )}
              </tbody>
            </table>
          </div>
          <div style={{ marginTop: "16px" }}>
            <TableControl
              show
              itemsPerPage={itemsPerPage}
              setItemsPerPage={(val: any) => setItemsPerPage(val)}
              currentPage={currentPage}
              setCurrentPage={setCurrentPage}
              numberOfPages={numberOfPages}
              label="orders"
              loading={loading}
            />
          </div>
        </div>
      )}
    </>
  );
};

export default RetailTab;

const overviewStats = [
  { title: "Total Products", value: "63,629" },
  { title: "Active Products", value: "63,629" },
  { title: "Inactive Products", value: "63,629" },
];

const selectOptionsByField: Record<string, any> = {
  family: [
    { label: "Velocity suite", value: "Velocity suite" },
    { label: "Raging Thunder", value: "Raging Thunder" },
  ],
  sizeGB: [
    { label: "1 GB", value: "1gb" },
    { label: "2 GB", value: "2gb" },
  ],
  billing: [
    { label: "Postpaid", value: "postpaid" },
    { label: "Prepaid", value: "prepaid" },
  ],
  visibleOnRetail: [
    { label: "Yes", value: "yes" },
    { label: "No", value: "no" },
  ],
};
