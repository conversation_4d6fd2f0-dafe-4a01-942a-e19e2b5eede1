import { useEffect, useState } from "react";
import styles from "./customer-details.module.scss";
import SettingsSkeleton from "../SettingsSkeleton";
import { formatDateWithTime } from "../utils/formatDate";
import { formatNumber } from "../utils/formatNumber";
import { getCallSmsLogs } from "../utils/dataCreator";
import IconButton from "../IconButton";
import { Funnel } from "../svgs";
import TableControl from "../TableControl";

function CustomerActivityDisplay() {
  const [initialLoading, setInitialLoading] = useState(true);
  const [error, setError] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [customerActivity, setCustomerActivity] = useState([] as any);

  useEffect(() => {
    setInitialLoading(true);
    setError("");
    setTimeout(() => {
      setCustomerActivity(getCallSmsLogs(itemsPerPage));
      setInitialLoading(false);
    }, 500);
  }, [currentPage]);

  return (
    <div className={styles.container}>
      <div
        className="flex-justify-content-between flex align-items-center"
        style={{
          marginBottom: "15px",
        }}
      >
        <div className={styles.title}>Call & SMS Log</div>
        <IconButton style={{ margin: "0px 8px" }}>
          <Funnel />
        </IconButton>
      </div>
      <div className={`${styles.tableContainer} table-scroll`}>
        <table>
          <thead style={{ backgroundColor: "white" }}>
            <tr>
              <th>Type</th>
              <th>Date & time</th>
              <th>Source</th>
              <th>Destination</th>
              <th>Cost (GDP)</th>
              <th>Charge Method</th>
            </tr>
          </thead>
          <tbody>
            {initialLoading &&
              Array.from(
                {
                  length: itemsPerPage,
                },
                (v, i) => i
              ).map((_, i) => (
                <SettingsSkeleton
                  key={"settings-skeleton-" + i}
                  noOfStandard={5}
                />
              ))}

            {!!error && (
              <tr>
                <td colSpan={5}>Error: {error}</td>
              </tr>
            )}

            {customerActivity.length > 0 ? (
              customerActivity
                .slice(
                  (currentPage - 1) * itemsPerPage,
                  currentPage * itemsPerPage
                )
                .map((activity: any, i: number) => (
                  <tr key={i}>
                    <td>{activity.type}</td>
                    <td>{formatDateWithTime(activity.time)}</td>
                    <td>{formatNumber(activity.source)}</td>
                    <td>{formatNumber(activity.destination)}</td>
                    <td>{activity.totalCost}</td>
                    <td>{activity.chargeMethod}</td>
                  </tr>
                ))
            ) : (
              <tr>
                <td colSpan={5}>No activity</td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
      <div>
        <TableControl
          show
          itemsPerPage={itemsPerPage}
          setItemsPerPage={() => {}}
          currentPage={currentPage}
          setCurrentPage={setCurrentPage}
          numberOfPages={7}
          label="logs"
        />
      </div>
    </div>
  );
}

export default CustomerActivityDisplay;
