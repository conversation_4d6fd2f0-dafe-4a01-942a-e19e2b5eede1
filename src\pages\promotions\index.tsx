import Title from '@/components/Title'
import styles from '@/styles/promotions.module.scss'
import CollapsiblePanel from "@/components/CollapsiblePanel";
import InsightCard from "@/components/InsightCard";
import InsightList from "@/components/InsightList";
import { useRef, useState, useEffect } from 'react';
import Tag from '@/components/Tag';
import Button from '@/components/Button';
import { Input } from '@/components/Input';
import { clearInput, createStateObject, handleInputChange, labels } from '@/components/utils/InputHandlers';
import { Delete, Export, MagnifyingGlass, Pencil, Plus, Settings } from '@/components/svgs';
import { promotionsFields } from './promotionsFields';
import UserSkeleton from '@/components/UserSkeleton';
import TableControl from '@/components/TableControl';
import { getPromotions } from '@/components/utils/dataCreator';
import { formatDateWithTime } from '@/components/utils/formatDate';
import StatusPill from '@/components/StatusPill';
import AddEditPromotionModal from '@/components/AddEditPromotionModal/AddEditPromotionModal';
import ProductsMenu from '@/components/ProductsMenu';
import DeletePromotionModal from '@/components/DeletePromotionModal/DeletePromotionModal';
import PromotionViewModal from '@/components/PromotionViewModal/PromotionViewModal';
import CheckboxDropdownInput from '@/components/CheckboxDropdownInput';


const Promotions = () => {
    const overviewPanelRef = useRef<any>(null);
    const searchPanelRef = useRef<any>(null);
    const searchFields = ["type", "name", "status"]
    const [data, setData] = useState(createStateObject(searchFields));
    const [showSearchResults, setShowSearchResults] = useState(false);
    const [loading, setLoading] = useState(true);
    const [promotions, setPromotions] = useState([]);
    const [itemsPerPage, setItemsPerPage] = useState(10);
    const [numberOfPages, setNumberOfPages] = useState(10);
    const [currentPage, setCurrentPage] = useState(1);
    const [activePromotion, setActivePromotion] = useState(null as any)
    const [promotionModalMode, setPromotionModalMode] = useState<'' | 'create' | 'edit' | 'delete'>('')
    const [showPromotion, setShowPromotion] = useState(false)

    const populate = (itemsPerPage: number) => {
        setLoading(true);
    
        setTimeout(() => {
          setPromotions(getPromotions(itemsPerPage))
          setLoading(false)
        }, 500)
    }

    useEffect(() => {
        populate(itemsPerPage)
    }, [itemsPerPage, currentPage])

    const handleActions = (promotion:any, action: 'delete' | 'edit' | 'view') => {
        setActivePromotion(promotion)
        if (action === 'view') {
            setShowPromotion(true)
        } else {
            setPromotionModalMode(action)
        }
    }

    const formatDataItem = (item: any, key: string) => {
        if (key === "creationDate" || key === "expiryDate") {
          return formatDateWithTime(item[key])
        } else if (key === "status") {
          return <StatusPill status={item[key] === 'Active'} />
        } else if (key === "amount") {
            return '$'+ item[key]
          } else {
          return item[key]
        }
      }

    return (
        <>
            {
                showPromotion && (
                    <PromotionViewModal
                        show={showPromotion}
                        promotion={activePromotion}
                        close={() => setShowPromotion(false)}
                        type='' />
                )
            }
            {
                promotionModalMode && (promotionModalMode === 'edit' || promotionModalMode === 'create')  && (
                    <AddEditPromotionModal 
                    show={promotionModalMode}
                    promotion={activePromotion}
                    setShow={() => setPromotionModalMode('')}
                    repopulate={() => populate(15)} />
                ) 
            }
            {
                promotionModalMode === 'delete' && (
                    <DeletePromotionModal 
                    show={promotionModalMode}
                    promotion={activePromotion}
                    setShow={() => setPromotionModalMode('')}
                    repopulate={() => populate(15)} />
                )
            }
            <Title>Promotions</Title>
            <div className={styles.main}>
                <CollapsiblePanel
                    title="Overview"
                    summaryWhenClosed={
                        <div style={{ marginLeft: 22 }}>
                        <InsightList insights={overviewStats} />
                        </div>
                    }
                    actionBtn={<Button style={{ marginRight: 10}} onClick={(e) => {
                            e.stopPropagation();
                            setPromotionModalMode('create')
                        }
                    }><Plus /> Create Promotion</Button>}
                    ref={overviewPanelRef}>
                    <div className={styles.overview}>
                        {overviewStats.map((stat, index) => (
                        <InsightCard key={index} title={stat.title} value={stat.value.toString()} />
                        ))}
                    </div>
                </CollapsiblePanel>

                {/* Search promotions */}
                <div style={{ marginTop: 16 }}>
                    <CollapsiblePanel
                        title="Search Promotions"
                        summaryWhenClosed={
                        <div style={{ display: 'flex', flex: 1, justifyContent: 'space-between', marginLeft: 16, marginRight: 8 }}>
                            <Tag text="3 filters applied" />
                            <Button color="secondary">Clear Filters</Button>
                        </div>
                        }
                        ref={searchPanelRef}>
                    <div className={styles.fields}>
                    {searchFields.map((prop) => {
                        if (["name"].includes(prop)) {
                            return (
                                <Input
                                    key={"orders-" + prop}
                                    label={labels[prop]}
                                    value={data[prop]}
                                    onChange={(e: any) => {
                                        handleInputChange(prop, e, data, setData);
                                    }}
                                    error={data.errors[prop]}
                                    clear={() => {
                                        clearInput(prop, setData);
                                    }}
                                    infoTooltipText
                                />
                            )
                        } else if (["type", "status"].includes(prop)) {
                        return (
                            <CheckboxDropdownInput
                                key={"promotions-" + prop}
                                options={selectOptionsByField[prop]}
                                label={labels[prop]}
                                selected={data[prop]}
                                onChange={(values) => {
                                handleInputChange(
                                    prop,
                                    values,
                                    data,
                                    setData,
                                    'select'
                                )}}
                                error={data.errors[prop]}
                                infoTooltipText
                            />
                        )}
                    })}
                    </div>
                    <div className={styles.actions}>
                    <Button
                        color="blue"
                        onClick={() => {
                        searchPanelRef.current.close()
                        overviewPanelRef.current.close()
                        setShowSearchResults(true)
                        }}
                    ><MagnifyingGlass /> Search</Button>
                    </div>
                </CollapsiblePanel>
                </div>

                {showSearchResults && <div className={styles.panel}>
                    <div className={styles.panelTopBar}>
                        <h4>Promotions</h4>
                        <div className={styles.actions}>
                        <Button color="secondary">
                            <Export /> Export to CSV
                        </Button>
                        </div>
                    </div>

                    <div className={`${styles.tableContainer} table-scroll`}>
                        <table>
                            <thead>
                                <tr>
                                    {promotionsFields.map((field: any) => (
                                        <th>{field.label}</th>
                                    ))}
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody>
                                {!loading
                                ? promotions.map((item: any, i: number) => {
                                    return (
                                    <tr key={`promotion-${item.code}`} style={{ cursor: 'pointer' }}>
                                        {promotionsFields.map((field: any) => (
                                        <td key={`promotion-${item.code}-${field.key}`}>
                                            <div style={{ display: "flex", justifyContent: "flex-start" }}>
                                            {formatDataItem(item, field.key)}
                                            </div>
                                        </td>
                                        ))}
                                        <td>
                                            <span
                                                className={styles.viewRowBtn + ' ticketsMenu'}>
                                                <ProductsMenu
                                                    data={{
                                                    icon:  (<Settings />),
                                                    items:
                                                        [
                                                        {
                                                            label: 'Edit Ticket',
                                                            icon: <Pencil />,
                                                            onClick: () => handleActions(item, 'edit')
                                                        },
                                                        {
                                                            label: "Delete Ticket",
                                                            icon: <Delete />,
                                                            onClick: () => handleActions(item, 'delete')
                                                        },
                                                        ]
                                                    }}
                                                    />
                                                <span className={styles.rowActionBtn} onClick={() => handleActions(item, 'view')}>
                                                    View
                                                </span>
                                            </span>
                                        </td>
                                    </tr>
                                    )
                                }) : Array.from({ length: itemsPerPage }, (v, i) => i).map(
                                    (i) => (
                                    <UserSkeleton
                                        key={"order-skeleton-" + i}
                                        noOfStandard={8}
                                    />
                                    )
                                )}
                            </tbody>
                </table>
            </div>
            <div style={{ marginTop: "16px" }}>
                <TableControl
                    show
                    itemsPerPage={itemsPerPage}
                    setItemsPerPage={(val: any) => setItemsPerPage(val)}
                    currentPage={currentPage}
                    setCurrentPage={setCurrentPage}
                    numberOfPages={numberOfPages}
                    label="orders"
                    loading={loading} />
            </div>
            </div>}
            </div>
        </>
    )
}

export default Promotions

const overviewStats = [
    { title: 'Total Promotions', value: '63,629' },
    { title: 'Active Promotions', value: '63,629' },
    { title: 'Inactive Promotions', value: '63,629' },
  ]
  
const selectOptionsByField: Record<string, any> = {
    status: [
        { label: "Active", value: "active" },
        { label: "Inactive", value: "Inactive" },
    ],
    type: [
        { label: "Free Discount", value: "Free Discount" },
        { label: "Discount", value: "Discount" },
    ],
  }