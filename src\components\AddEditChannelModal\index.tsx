import styles from "./add-edit-channel.module.scss";
import { InfoCircle2, PencilCircle, PersonCircle, Plus, PlusCircle, Tick, XCircle } from "../svgs";
import { Input } from "../Input";
import { useEffect, useState } from "react";
import { validateAll } from "indicative/src/Validator";
import { useDispatch } from "react-redux";
import {
  clearInput,
  createStateObject,
  displayErrors,
  getMessages,
  getRules,
  handleInputChange,
  labels,
} from "../utils/InputHandlers";
import { ApiPostAuth } from "../../pages/api/api";
import { useParams } from "react-router-dom";
import TextArea from "../TextArea";
import SearchBar from "../SearchBar";
import { getUsers } from "@/pages/user-management/dummyData";
import StatusSwitch from "../StatusSwitch";
import Dialog from "../Dialog";
const fields = ["channelName", "channelDescription", "status"];
const rules = getRules(fields);
const messages = getMessages(fields);

const AddEditChannelModal = ({ show, setShow, repopulate, channel }: any) => {
  const dispatch = useDispatch();

  const [data, setData] = useState(createStateObject(fields));
  const [step, setStep] = useState(1)
  const [query, setQuery] = useState('')
  const [subscribersQuery, setSubscribersQuery] = useState('')
  const [users, setUsers] = useState([] as any)
  const [filteredUsers, setfilteredUsers] = useState([])
  const [filteredSubs, setFilteredSubs] = useState([])
  const [selectedUsers, setSelectedUsers] = useState([] as any)
  const [status, setStatus] = useState<boolean | null>(null)
  const [selectedSubs, setSelectedSubs] = useState([] as any)
  const [mode, setMode] = useState<'create' | 'edit'>('create')
  const [title, setTitle] = useState("")

  useEffect(() => {
    handleStepsData()
  }, [step, mode])

  useEffect(() => {
    if (!channel) {
      const users = getUsers(30);
      setUsers(users)
    }
  }, [])

  useEffect(() => {
    if (channel) {
      setMode('edit')
      handleStepsData()
      setData({
        channelName: channel.name,
        channelDescription: "channel description",
        status: channel.status === "Active" && true,
        errors: {}
      })
      setStatus(channel.status === "Active" && true)
    }
  }, [channel])

  useEffect(() => {
    setData({
      ...data,
      status
    })
  }, [status])

  useEffect(() => {
    if (query) {
      const filteredUsers = users.filter((user:any) => user.firstName.toLowerCase().includes(query) || 
        user.lastName.toLowerCase().includes(query) || 
        user.email.includes(query))
      setfilteredUsers(filteredUsers)
    } else {
      setfilteredUsers([])
    }
  }, [query])

  useEffect(() => {
    if (subscribersQuery) {
      const filteredUsers = users.filter((user:any) => user.firstName.toLowerCase().includes(subscribersQuery) || 
        user.lastName.toLowerCase().includes(subscribersQuery) || 
        user.email.includes(subscribersQuery))
      setFilteredSubs(filteredUsers)
    } else {
      setFilteredSubs([])
    }
  }, [subscribersQuery])

  const { mvnoId }: any = useParams();

  // Reset modal data when closed
  const reset = () => {
    setData(createStateObject(fields));
    setLoading(false);
  };

  const handleCloseModal = () => {
    setStep(1)
    setLoading(false)
    setFilteredSubs([])
    setSelectedSubs([])
    setSelectedUsers([])
    setfilteredUsers([])
    setMode("create")
    setShow(false)
  }

  const handleChooseFromDropDown = (item:any, type: string) => {
    if (selectedUsers.length > 0 && selectedUsers.find((a:any) => a.email === item.email)) {
      setQuery('')
      return
    } else if (type === 'users') {
      setSelectedUsers([...selectedUsers, item])
      setQuery('')
    } else {
      setSelectedSubs([...selectedSubs, item])
      setSubscribersQuery('')
    }
  }

  const removeFromSelectedUsers = (user:any, type: string) => {
    if (type === 'users') {
      const splicedUser = selectedUsers.filter((item:any) => item.firstName !== user.firstName)
      setSelectedUsers(splicedUser)
    } else {
      const splicedUser = selectedSubs.filter((item:any) => item.firstName !== user.firstName)
      setSelectedSubs(splicedUser)
    }
  }

  // Handles creation of new channel
  const createChannel = () => {
    validateAll(data, rules, messages)
      .then((response) => {
        setLoading(true);
        ApiPostAuth("/channels/create", {
          name: data.channelName,
          description: data.channelDescription,
          mvnoId: mvnoId,
        })
          .then((response) => {
            reset();
            repopulate();
            dispatch({
              type: "notify",
              payload: {
                error: false,
                heading: "Success",
                message: response.data.message,
              },
            });
            setShow(false);
          })
          .catch((error) => {
            setLoading(false);
            dispatch({
              type: "notify",
              payload: {
                error: true,
                heading: "Something went wrong",
                message: error.response.data.message,
              },
            });
          });
      })
      .catch((errors) => {
        displayErrors(errors, setData);
      });
  };

  const handleStepsData = () => {
    if (step === 1) {
      if (mode === 'create') {
        setTitle("Create Channel")
      } else {
        setTitle("Edit Channel")
      }
    } else if (step === 2) {
      setTitle('Add Users')
    } else {
      setTitle("Add Subscribers")
    }
  }

  const handleSuccess = () => {
    if (step === 3 || (step === 1 && mode === 'edit')) {
      dispatch({
        type: "notify",
        payload: {
          error: false,
          heading: "Success",
          message: `Channel ${data.name} ${mode === 'create' ? 'created' : 'edited'} successfully`,
        },
      });
      handleCloseModal()
    } else {
      setStep(step + 1)
    }
  }

  const [loading, setLoading] = useState(false);

  return (
    <Dialog 
      open={show}
      onClose={() => handleCloseModal()}
      size="sm"
      confirmButtonText={step === 3 || (step === 1 && mode === 'edit') ? "Finish" : 'Continue'}
      headerTitle={title}
      headerIcon={step === 1 && mode === 'create' ? <PlusCircle /> : step === 1 && mode === 'edit' ? <PencilCircle /> : <PersonCircle />}
      cancelButtonText="Cancel"
      customButtonOnClick={() => setStep(step + 1)}
      cancelButtonOnClick={() => setShow(false)}
      customButtonText={step === 2 && "Skip"}
      scroll={(step === 2 && selectedUsers.length > 3) || (step === 3 && selectedUsers.length > 3)}
      confirmButtonOnClick={() => handleSuccess()}>
      <div className={`${styles.main} normal-select-input`}>
        {
          step === 1 && (
            <>
              {fields.map((field) => {
                if (field === "channelDescription") {
                  return (
                    <TextArea
                      key={`${field}-input`}
                      label={labels[field]}
                      value={data[field]}
                      onChange={(e: any) => {
                        handleInputChange(field, e, data, setData);
                      }}
                      error={data.errors[field]}
                      onKeyDown={createChannel}
                      clear={() => {
                        clearInput(field, setData);
                      }}
                      disabled={loading}
                    />
                  );
                } else if (field === "channelName") {
                  return (
                    <Input
                      key={`${field}-input`}
                      label={labels[field]}
                      value={data[field]}
                      onChange={(e: any) => {
                        handleInputChange(field, e, data, setData);
                      }}
                      error={data.errors[field]}
                      onKeyDown={createChannel}
                      clear={() => {
                        clearInput(field, setData);
                      }}
                      disabled={loading}
                      infoTooltipText
                    />
                  );
                }
              })}
              <StatusSwitch status={status} setStatus={setStatus} />
            </>
          )
        }
        {
          step === 2 && (
            <div className={styles.usersAdd}>
              <p>Search by name or email address <InfoCircle2 /></p>
              <SearchBar 
                query={query}
                setQuery={setQuery}
              />
              {
                filteredUsers.length > 0 && (
                  <div className={styles.usersDropdown}>
                    {
                      filteredUsers.length > 0 && filteredUsers.map((user:any) => (
                        <div className={styles.usersContainer} onClick={() => handleChooseFromDropDown(user, 'users')}>
                          <div className={styles.user}>
                            <h5>{user.firstName + ' ' + user.lastName}</h5>
                            <p>{user.email}</p>
                          </div>
                          {
                            selectedUsers.length > 0 && selectedUsers.find((a:any) => a.email === user.email) ? (
                              <div className={styles.actions}>
                                <span><Tick /> Added</span> 
                              </div>
                            ) : (
                              <div className={styles.actions}>
                                <Plus />
                              </div>
                            )
                          }
                        </div>
                      ))
                    }
                  </div>
                )
              }
              {
                selectedUsers.length > 0 && (
                  <div className={styles.addedUsers}>
                    <h5>Users Added:</h5>
                    <div className={styles.selectedUsers}>
                      {
                        selectedUsers.map((user:any, i:number) => (
                          <div>
                            <div>
                              <h5>{user.firstName + ' ' + user.lastName}</h5>
                              <p>{user.email}</p>
                            </div>
                            <div onClick={() => removeFromSelectedUsers(user, 'users')}>
                              <XCircle />
                            </div>
                          </div>
                        ))
                      }
                    </div>
                  </div>
                )
              }
            </div>
          )
        }
        {
          step === 3 && (
            <div className={styles.usersAdd}>
              <p>Search by name or email address <InfoCircle2 /></p>
              <SearchBar 
                query={subscribersQuery}
                setQuery={setSubscribersQuery}
              />
              {
                filteredSubs.length > 0 && (
                  <div className={styles.usersDropdown}>
                    {
                      filteredSubs.length > 0 && filteredSubs.map((user:any) => (
                        <div className={styles.usersContainer} onClick={() => handleChooseFromDropDown(user, 'subs')}>
                          <div className={styles.user}>
                            <h5>{user.firstName + ' ' + user.lastName}</h5>
                            <p>{user.email}</p>
                          </div>
                          <div className={styles.actions}>
                            <Plus />
                          </div>
                        </div>
                      ))
                    }
                  </div>
                )
              }
            {
              selectedSubs.length > 0 && (
                <div className={styles.addedUsers}>
                  <h5>Users Added:</h5>
                  <div className={styles.selectedUsers}>
                    {
                      selectedSubs.map((user:any, i:number) => (
                        <div>
                          <div>
                            <h5>{user.firstName + ' ' + user.lastName}</h5>
                            <p>{user.email}</p>
                          </div>
                          <div onClick={() => removeFromSelectedUsers(user, 'subs')}>
                            <XCircle />
                          </div>
                        </div>
                      ))
                    }
                  </div>
                </div>
              )
            }
          </div>
          )
        }
      </div>
    </Dialog>
  );
};

export default AddEditChannelModal;
