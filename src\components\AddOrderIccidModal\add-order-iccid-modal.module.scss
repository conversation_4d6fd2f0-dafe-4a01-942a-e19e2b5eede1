.heading {
  h4 {
    font-size: 20px;
    font-weight: 700;
    color: var(--text-primary);
    margin-top: 16px;
  }
  p {
    font-size: 14px;
    color: var(--text-secondary);
    margin-top: 8px;
  }
}

.tableContainer {
  margin-top: 18px;
  width: fit-content;
  min-width: 320px;
  border: 1px solid var(--gray-100);
  border-radius: 16px;
}

.table {
  color: var(--primary-900);
  font-size: 12px;
  border-collapse: collapse;
  width: 100%;

  .label,
  .value {
    border-top: 1px solid var(--gray-100);
    padding: 12px 10px;
  }

  .value {
    border-left: 1px solid var(--gray-100);
    text-transform: capitalize;
  }

  tr:first-child {
    .label,
    .value {
      border-top: none;
    }
  }
}

.formContainer {
  max-width: 320px;
  margin-top: 16px;
}

.buttonsContainer {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}
