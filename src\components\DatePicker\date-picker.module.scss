@use "../../styles/theme.scss" as *;

.main {
  display: flex;
  background: #fff;
  border-radius: 12px;
  padding: 24px;
  align-items: stretch;
}

.dateContainer {
  width: 386px;
  padding-right: 36px;
  border-right: 1px solid rgba(0, 0, 20, 0.12);
}

.timeContainer {
  padding-left: 36px;
  width: 266px;
  display: flex;
  flex-direction: column;
  .time {
    margin-bottom: 16px;
    font-size: 14px;
    line-height: 21px;
  }
  .fromLabel {
    margin: 0 auto;
    font-size: 12px;
    line-height: 18px;
    margin-bottom: 12px;
    width: 95px;
  }
}

.menuButton {
  border: none;
  display: flex;
  align-items: center;
  font-size: 14px;
  line-height: 21px;
  gap: 4px;
  cursor: pointer;
  transition:
    color 0.2s ease,
    background-color 0.2s ease;
  -webkit-user-select: none; /* Safari */
  -ms-user-select: none; /* IE 10 and IE 11 */
  user-select: none;
  &.reports {
    &:hover {
      color: $black;
    }
  }
  &:hover {
    color: var(--primary-500);
    &.background {
      color: $black;
      background: var(--primary-100);
    }
  }
  &.background {
    height: 32px;
    background-color: #f7f6f6;
    padding: 5.5px 6px 5.5px 12px;
    border-radius: 6px;
    font-weight: 500;
    margin-right: 12px;
  }
  svg {
    display: inline;
    vertical-align: middle;
    transition: transform 0.2s ease;
    margin-left: 5px;
  }
}

.buttons {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: flex-end;
  user-select: none;
  margin-top: auto;
}

.prevNext {
  display: flex;
  align-items: center;
  svg {
    vertical-align: middle;
  }
  .prev {
    margin-right: 16px;
  }
  .prev,
  .next {
    cursor: pointer;
    transition: color 0.1s ease;
    &:hover {
      color: var(--primary-500);
    }
  }
}

.input {
  width: 100%;
  height: 56px;
  border: 1px solid #74767e;
  border-radius: 8px;
  padding: 16px 20px;
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  color: $placeholder;
  svg {
    margin-right: 8px;
  }
}

.mainDatePicker {
  width: 100%;
  border: 1px solid #74767e;
  border-radius: 8px;
}

.calendar {
  padding: 16px 0;
  user-select: none;
  .days {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    width: 100%;
    justify-items: center;
    .letter {
      color: $black;
      font-size: 12px;
      line-height: 24px;
      padding: 9px 16px;
    }
  }
}

.datesGrid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  grid-template-rows: repeat(6, 42px);
  width: 100%;
  justify-content: stretch;
  align-items: center;
  .cellContainer {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    &:hover {
      .day {
        background-color: var(--primary-100);
      }
    }
  }
  .gridCell {
    position: absolute;
    width: 100%;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    &.highlight {
      background-color: var(--primary-100);
    }
    &.curveLeft {
      width: 50%;
      right: 0px;
    }
    &.curveRight {
      width: 50%;
      left: 0px;
    }
  }
  .day {
    font-size: 12px;
    line-height: 24px;
    height: 32px;
    width: 32px;
    border-radius: 1000px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    position: relative;
    z-index: 20;
    &.now {
      border: 1px solid var(--primary-500);
    }
    &.active {
      color: #fff;
      background-color: var(--primary-500);
      cursor: auto;
      &:hover {
        background-color: var(--primary-500);
      }
    }
    &.pad {
      color: #74767e;
      &.active {
        color: #fff;
      }
    }
  }
}

.disable {
  pointer-events: none;
  opacity: 0.5;
  cursor: not-allowed;
}
