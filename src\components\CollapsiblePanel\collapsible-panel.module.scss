.panel {
  background: #fff;
  border-radius: 24px;
  padding: 24px;
}

.panelTopBar {
  display: flex;
  align-items: center;
  cursor: pointer;

  .title {
    font-size: 16px;
    font-weight: 700;
    line-height: 22px;
  }

  .caret {
    margin-left: auto;
    transition: transform 0.3s;
    
    &.isOpen {
      transform: rotate(180deg);
      transition: transform 0.3s;
    }
  }
}

.topContent {
  flex: 1;
}

.content {
  margin-top: 12px;
}