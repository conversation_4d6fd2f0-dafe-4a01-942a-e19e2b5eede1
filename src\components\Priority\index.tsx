import styles from "./priority.module.scss";

export const convertPriority = (priority: number) => {
  switch (priority) {
    case 4:
      return "Critical";
    case 3:
      return "Major";
    case 2:
      return "Medium";
    case 1:
      return "Low";
    default:
      return "";
  }
};

const Priority = ({ priority, small, iconOnly }: any) => {
  return (
    <div className={`${styles.main} ${small && styles.small}`}>
      {priority === "Critical" ? (
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path d="M12 19L5 7L19 7L12 19Z" fill="#F04747" />
        </svg>
      ) : priority === "Major" ? (
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path d="M12 5L19 12L12 19L5 12L12 5Z" fill="#FFB302" />
        </svg>
      ) : priority === "Medium" ? (
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M14.6578 5H9.34219C9.26556 4.99994 9.18967 5.01498 9.11886 5.04425C9.04805 5.07353 8.98369 5.11647 8.92948 5.17063L5.17063 8.92948C5.11647 8.98369 5.07353 9.04805 5.04425 9.11886C5.01498 9.18967 4.99994 9.26556 5 9.34219V14.6578C4.99994 14.7344 5.01498 14.8103 5.04425 14.8811C5.07353 14.952 5.11647 15.0163 5.17063 15.0705L8.92948 18.8294C8.98369 18.8835 9.04805 18.9265 9.11886 18.9557C9.18967 18.985 9.26556 19.0001 9.34219 19H14.6578C14.7344 19.0001 14.8103 18.985 14.8811 18.9557C14.952 18.9265 15.0163 18.8835 15.0705 18.8294L18.8294 15.0705C18.8835 15.0163 18.9265 14.952 18.9557 14.8811C18.985 14.8103 19.0001 14.7344 19 14.6578V9.34219C19.0001 9.26556 18.985 9.18967 18.9557 9.11886C18.9265 9.04805 18.8835 8.98369 18.8294 8.92948L15.0705 5.17063C15.0163 5.11647 14.952 5.07353 14.8811 5.04425C14.8103 5.01498 14.7344 4.99994 14.6578 5Z"
            fill="#FCE83A"
          />
        </svg>
      ) : (
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <circle cx="12" cy="12" r="6" fill="#4BC962" />
        </svg>
      )}
      {!iconOnly && priority}
    </div>
  );
};

export default Priority;
