@use "../../styles/theme.scss" as *;

.main {
  width: 100%;
  padding: 0 32px;
  height: 92px;
  border-radius: 12px;
  background: #f7f6f6;
  position: relative;
  overflow: hidden;
  margin-bottom: 12px;
  td {
    vertical-align: middle !important;
  }
}

.summary {
  width: 100%;
  height: 100%;
  align-items: center;
}
.box {
  height: 26px;
  width: 100px;
  background-color: $skeleton;
  border-radius: 4px;
  position: relative;
  overflow: hidden;
}

.dataBar {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  .smallBox {
    height: 41px;
    width: 44px;
    background-color: $skeleton;
    border-radius: 4px;
    position: relative;
    overflow: hidden;
    margin-right: 12px;
    &:last-child {
      margin-right: 0px;
    }
  }
}
