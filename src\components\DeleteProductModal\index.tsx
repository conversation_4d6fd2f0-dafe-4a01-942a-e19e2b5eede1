import Dialog from "@/components/Dialog";
import { WarningCircle } from "@/components/svgs";
import styles from "./delete-product-modal.module.scss";
import { formatDateWords } from "@/components/utils/formatDate";
import StatusPill from "@/components/StatusPill";

type DeleteProductModalProps = {
  onClose: () => void;
  open: boolean;
  productData: any;
  type: "wholesale" | "retail";
};

const DeleteProductModal = ({
  onClose,
  open,
  productData,
  type,
}: DeleteProductModalProps) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      size="sm"
      headerIcon={<WarningCircle />}
      headerTitle="Delete Product?"
      headerSubtitle="Once removed, it cannot be recovered"
      confirmButtonText="Yes, Delete Product"
      confirmButtonOnClick={onClose}
      confirmButtonVariant="destructive"
      cancelButtonText="Cancel"
    >
      <div className={styles.tableContainer}>
        <table className={styles.table}>
          <tbody>
            <tr>
              <td className={styles.label}>Family</td>
              <td className={styles.value}>{productData.family}</td>
            </tr>
            <tr>
              <td className={styles.label}>Wholesale Name</td>
              <td className={styles.value}>{productData.name}</td>
            </tr>
            <tr>
              <td className={styles.label}>Retail Name</td>
              <td className={styles.value}>{productData.retailName}</td>
            </tr>
            <tr>
              <td className={styles.label}>Wholesale Price</td>
              <td className={styles.value}>{productData.wholesalePrice}</td>
            </tr>
            <tr>
              <td className={styles.label}>Retail Price</td>
              <td className={styles.value}>{productData.retailPrice}</td>
            </tr>
            <tr>
              <td className={styles.label}>Size</td>
              <td className={styles.value}>{productData.sizeGB}</td>
            </tr>
            <tr>
              <td className={styles.label}>Created on</td>
              <td className={styles.value}>
                {formatDateWords(productData.creationDate)}
              </td>
            </tr>
            {type === "retail" && (
              <tr>
                <td className={styles.label}>Status</td>
                <td className={styles.value}>
                  {
                    <StatusPill
                      status={productData.status ? "Active" : "Inactive"}
                      color={productData.status ? "active" : "inactive"}
                    />
                  }
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </Dialog>
  );
};

export default DeleteProductModal;
