import { faker } from "@faker-js/faker";

export const exampleCustomerDetails = {
  firstName: "Hero",
  lastName: "User",
  email: "<EMAIL>",
  registeredPhoneNumber: "************",
  profileUrl: null,
  introPlanPurchased: false,
  tacFlag: false,
  sipCredentials: {
    username: "",
    password: "",
    host: "",
    transport: "",
    port: "",
    accountId: "",
  },
  credit: "10.0",
  creditCurrency: "GBP",
  msisdn: "*************",
  esimActive: "true",
  status: "Active",
  country: "United Kingdom",
  comboCreditEligible: true,
  comboPlanFlag: true,
  comboCreditFlag: false,
  iccid: "8943108310000001609",
  imei: "***************",
  defaultVnNumberId: null,
  defaultVnNumberValue: null,
  esimSubscriptions: [
    {
      subscriptionId: 970,
      planName: "Afghanistan",
      planRegion: "Asia",
      validity: 7,
      classification: "Data",
      activated: true,
      cancelled: true,
      held: false,
      planId: 6,
      flagImage:
        "https://public-trill-bucket.s3.eu-north-1.amazonaws.com/flag-icons/png/AF.png",
      countries: [
        {
          countryCode: "AF",
          iconUrl:
            "https://public-trill-bucket.s3.eu-north-1.amazonaws.com/flag-icons/png/AF.png",
          countryName: "Afghanistan",
          countryImage:
            "https://public-gist-bucket.s3.eu-north-1.amazonaws.com/country-images/afghanistan-min.jpg",
        },
      ],
      priceInfo: [
        {
          currencyCode: "USD",
          cost: 79.25,
          currencySymbol: "$",
        },
        {
          currencyCode: "GBP",
          cost: 63.4,
          currencySymbol: "£",
        },
        {
          currencyCode: "EUR",
          cost: 72.05,
          currencySymbol: "€",
        },
      ],
      initialBytes: 1073741824,
      remainingBytes: 0,
      dataAllowance: 1,
      remainingData: "0GB",
      didServiceId: null,
      didNumber: null,
      didNumberAvailable: false,
      didVoicemailId: null,
      didNumberAvailabilityStatus: null,
      sipUsername: null,
      sipPassword: null,
      sipDidId: null,
      initialMinutes: 0,
      remainingMinutes: 0,
      initialMessages: 0,
      remainingMessages: 0,
      activationDescription:
        "The plan will auto-start when you connect to a network at the plan's destination.",
      purchaseTime: "2024-09-20 14:25:22",
      activationLimit: null,
      dataStart: "1727447117000",
      dataEnd: "1726842317000",
      startDate: "2024-09-20 14:25:17",
      endDate: "2024-09-27 14:25:17",
      autorenewal: true,
      renewDescription:
        "When enabled, your plan will automatically renew when you have used all your data allowance, or on plan renewal date.",
      autorenew: false,
      iccid: "8943108310000001609",
      imsi: null,
      ban: "123456789012345",
    },
    {
      subscriptionId: 971,
      planName: "Canada",
      planRegion: "North America",
      validity: 7,
      classification: "Data",
      activated: true,
      cancelled: false,
      held: true,
      planId: 211,
      flagImage:
        "https://public-gist-bucket.s3.eu-north-1.amazonaws.com/flag-icons/png/CA.png",
      countries: [
        {
          countryCode: "CA",
          iconUrl:
            "https://public-gist-bucket.s3.eu-north-1.amazonaws.com/flag-icons/png/CA.png",
          countryName: "Canada",
          countryImage:
            "https://public-gist-bucket.s3.eu-north-1.amazonaws.com/country-images/canada+-+CN+Tower-min.jpeg",
        },
      ],
      priceInfo: [
        {
          currencyCode: "USD",
          cost: 8.97,
          currencySymbol: "$",
        },
        {
          currencyCode: "EUR",
          cost: 8.15,
          currencySymbol: "€",
        },
        {
          currencyCode: "GBP",
          cost: 7.18,
          currencySymbol: "£",
        },
      ],
      initialBytes: 1073741824,
      remainingBytes: 0,
      dataAllowance: 1,
      remainingData: "0GB",
      didServiceId: null,
      didNumber: null,
      didNumberAvailable: false,
      didVoicemailId: null,
      didNumberAvailabilityStatus: null,
      sipUsername: null,
      sipPassword: null,
      sipDidId: null,
      initialMinutes: 0,
      remainingMinutes: 0,
      initialMessages: 0,
      remainingMessages: 0,
      activationDescription:
        "The plan will auto-start when you connect to a network at the plan's destination.",
      purchaseTime: "2024-09-20 14:26:25",
      activationLimit: null,
      dataStart: "1727447180000",
      dataEnd: "1726842380000",
      startDate: "2024-09-20 14:26:20",
      endDate: "2024-09-27 14:26:20",
      autorenewal: true,
      renewDescription:
        "When enabled, your plan will automatically renew when you have used all your data allowance, or on plan renewal date.",
      autorenew: false,
      iccid: "8943108310000001609",
      imsi: null,
      ban: "123456789012345",
    },
    {
      subscriptionId: 972,
      planName: "United Arab Emirates",
      planRegion: "Middle East and North Africa",
      validity: 7,
      classification: "Data",
      activated: true,
      cancelled: false,
      held: true,
      planId: 1253,
      flagImage:
        "https://public-trill-bucket.s3.eu-north-1.amazonaws.com/flag-icons/png/AE.png",
      countries: [
        {
          countryCode: "AE",
          iconUrl:
            "https://public-trill-bucket.s3.eu-north-1.amazonaws.com/flag-icons/png/AE.png",
          countryName: "United Arab Emirates",
          countryImage:
            "https://public-trill-bucket.s3.eu-north-1.amazonaws.com/Country-Images/Placeholder_Image.png",
        },
      ],
      priceInfo: [
        {
          currencyCode: "USD",
          cost: 10.4,
          currencySymbol: "$",
        },
        {
          currencyCode: "GBP",
          cost: 8.32,
          currencySymbol: "£",
        },
        {
          currencyCode: "EUR",
          cost: 9.45,
          currencySymbol: "€",
        },
      ],
      initialBytes: 1073741824,
      remainingBytes: 0,
      dataAllowance: 1,
      remainingData: "0GB",
      didServiceId: null,
      didNumber: null,
      didNumberAvailable: false,
      didVoicemailId: null,
      didNumberAvailabilityStatus: null,
      sipUsername: null,
      sipPassword: null,
      sipDidId: null,
      initialMinutes: 0,
      remainingMinutes: 0,
      initialMessages: 0,
      remainingMessages: 0,
      activationDescription:
        "The plan will auto-start when you connect to a network at the plan's destination.",
      purchaseTime: "2024-09-20 14:28:59",
      activationLimit: null,
      dataStart: "1727447334000",
      dataEnd: "1726842534000",
      startDate: "2024-09-20 14:28:54",
      endDate: "2024-09-27 14:28:54",
      autorenewal: true,
      renewDescription:
        "When enabled, your plan will automatically renew when you have used all your data allowance, or on plan renewal date.",
      autorenew: false,
      iccid: "8943108310000001609",
      imsi: null,
      ban: "123456789012345",
    },
    {
      subscriptionId: 993,
      planName: "Bulgaria",
      planRegion: "Europe",
      validity: 7,
      classification: "Data",
      activated: true,
      cancelled: false,
      held: false,
      planId: 188,
      flagImage:
        "https://public-gist-bucket.s3.eu-north-1.amazonaws.com/flag-icons/png/BG.png",
      countries: [
        {
          countryCode: "BG",
          iconUrl:
            "https://public-gist-bucket.s3.eu-north-1.amazonaws.com/flag-icons/png/BG.png",
          countryName: "Bulgaria",
          countryImage:
            "https://public-gist-bucket.s3.eu-north-1.amazonaws.com/country-images/bulgaria+-+St+sophia+church-min.jpg",
        },
      ],
      priceInfo: [
        {
          currencyCode: "USD",
          cost: 3.24,
          currencySymbol: "$",
        },
        {
          currencyCode: "GBP",
          cost: 2.59,
          currencySymbol: "£",
        },
        {
          currencyCode: "EUR",
          cost: 2.94,
          currencySymbol: "€",
        },
      ],
      initialBytes: 1073741824,
      remainingBytes: 473741824,
      dataAllowance: 1,
      remainingData: "455 MB",
      didServiceId: null,
      didNumber: null,
      didNumberAvailable: false,
      didVoicemailId: null,
      didNumberAvailabilityStatus: null,
      sipUsername: null,
      sipPassword: null,
      sipDidId: null,
      initialMinutes: 0,
      remainingMinutes: 0,
      initialMessages: 0,
      remainingMessages: 0,
      activationDescription:
        "The plan will auto-start when you connect to a network at the plan's destination.",
      purchaseTime: "2024-10-10 10:46:55",
      activationLimit: null,
      dataStart: "1729162009000",
      dataEnd: "1728557209000",
      startDate: faker.date.recent({ days: 30 }),
      endDate: faker.date.soon({ days: 30 }),
      autorenewal: true,
      renewDescription:
        "When enabled, your plan will automatically renew when you have used all your data allowance, or on plan renewal date.",
      autorenew: false,
      iccid: "8943108310000001609",
      imsi: null,
      ban: "123456789012345",
    },
  ],
  phoneNumberSubscriptions: [
    {
      subscriptionId: 968,
      planName: "United Kingdom",
      planRegion: "Europe",
      validity: 30,
      classification: "Mobile Numbers",
      activated: true,
      cancelled: false,
      held: false,
      planId: 1272,
      flagImage:
        "https://public-gist-bucket.s3.eu-north-1.amazonaws.com/flag-icons/png/GB.png",
      countries: [
        {
          countryCode: "GB",
          iconUrl:
            "https://public-gist-bucket.s3.eu-north-1.amazonaws.com/flag-icons/png/GB.png",
          countryName: "United Kingdom",
          countryImage:
            "https://public-gist-bucket.s3.eu-north-1.amazonaws.com/country-images/united+kingdom.jpg",
        },
      ],
      priceInfo: [
        {
          currencyCode: "EUR",
          cost: 5.3,
          currencySymbol: "€",
        },
        {
          currencyCode: "GBP",
          cost: 4.67,
          currencySymbol: "£",
        },
        {
          currencyCode: "USD",
          cost: 5.83,
          currencySymbol: "$",
        },
      ],
      initialBytes: 0,
      remainingBytes: 0,
      dataAllowance: 0,
      remainingData: "0GB",
      didServiceId: 356,
      didNumber: "447474645546",
      didNumberAvailable: true,
      didVoicemailId: null,
      didNumberAvailabilityStatus: null,
      sipUsername: "",
      sipPassword: "",
      sipDidId: "",
      initialMinutes: 100,
      remainingMinutes: 34,
      initialMessages: 20,
      remainingMessages: 17,
      activationDescription:
        "The plan will auto-start when you connect to a network at the plan's destination.",
      purchaseTime: "2024-09-19 14:27:30",
      activationLimit: null,
      dataStart: "1729348043000",
      dataEnd: "1726756043000",
      startDate: faker.date.recent({ days: 30 }),
      endDate: faker.date.soon({ days: 30 }),
      autorenewal: true,
      renewDescription:
        "When enabled, your plan will automatically renew when you have used all your data allowance, or on plan renewal date.",
      autorenew: false,
      country: "United Kingdom",
      iccid: null,
      imsi: null,
    },
  ],
  comboSubscriptions: [],
  creditSubscriptions: [],
  deactivatedPhoneSubscriptions: [],
  notificationSettings: {
    emailOffers: true,
    emailService: true,
    emailPayment: true,
    smsOffers: true,
    smsService: true,
    smsPayment: true,
    appOffers: true,
    appService: true,
    appPayment: true,
  },
  notes: [
    {
      id: 8,
      note: "Lorem ipsum dolor sit amet consectetur. Lobortis sed risus in cras. Sit cras varius vulputate tortor suscipit. Duis enim bibendum ac nunc. Praesent eget posuere ac molestie tincidunt faucibus senectus nunc. Tincidunt senectus nullam mauris fermentum tortor. Quis felis egestas porttitor augue nibh malesuada viverra morbi. Lobortis arcu tellus arcu ipsum. Elit interdum at in ornare a enim aliquet dictum. At consequat ut vestibulum.",
    },
    {
      id: 9,
      note: "Lorem ipsum dolor sit amet consectetur. Lobortis sed risus in cras. Sit cras varius vulputate tortor suscipit. Duis enim bibendum ac nunc. Praesent eget posuere ac molestie tincidunt faucibus senectus nunc. Tincidunt senectus nullam mauris fermentum tortor. Quis felis egestas porttitor augue nibh malesuada viverra morbi. Lobortis arcu tellus arcu ipsum. Elit interdum at in ornare a enim aliquet dictum. At consequat ut vestibulum.",
    },
    {
      id: 10,
      note: "Lorem ipsum dolor sit amet consectetur. Lobortis sed risus in cras. Sit cras varius vulputate tortor suscipit. Duis enim bibendum ac nunc. Praesent eget posuere ac molestie tincidunt faucibus senectus nunc. Tincidunt senectus nullam mauris fermentum tortor. Quis felis egestas porttitor augue nibh malesuada viverra morbi. Lobortis arcu tellus arcu ipsum. Elit interdum at in ornare a enim aliquet dictum. At consequat ut vestibulum.",
    },
    {
      id: 11,
      note: "Lorem ipsum dolor sit amet consectetur. Lobortis sed risus in cras. Sit cras varius vulputate tortor suscipit. Duis enim bibendum ac nunc. Praesent eget posuere ac molestie tincidunt faucibus senectus nunc. Tincidunt senectus nullam mauris fermentum tortor. Quis felis egestas porttitor augue nibh malesuada viverra morbi. Lobortis arcu tellus arcu ipsum. Elit interdum at in ornare a enim aliquet dictum. At consequat ut vestibulum.",
    },
  ],
  topUpLimit: {
    dailyLimitExceeded: false,
    weeklyLimitExceeded: false,
  },
  "eSIM status": "Not Installed",
};

export const exampleCustomerTickets = [
  {
    id: 55,
    creationDate: "2024-09-23T10:01:14Z",
    dueDate: "2025-06-23T10:01:14Z",
    subject: "I can't install my eSIM",
    description:
      "<div>Lorem ipsum dolor sit amet consectetur. Lobortis sed risus in cras. Sit cras varius vulputate tortor suscipit. Duis enim bibendum ac nunc. Praesent eget posuere ac molestie tincidunt faucibus senectus nunc. Tincidunt senectus nullam mauris fermentum tortor. Quis felis egestas porttitor augue nibh malesuada viverra morbi. Lobortis arcu tellus arcu ipsum. Elit interdum at in ornare a enim aliquet dictum. At consequat ut vestibulum.</div>",
    assigneeEmail: null,
    category: "Technical Support",
    priority: 4,
    mid: null,
    customerEmail: null,
    status: 2,
    assignee: "Robin Billington",
  },
  {
    id: 56,
    creationDate: "2024-09-23T10:01:14Z",
    dueDate: "2025-06-23T10:01:14Z",
    subject: "Refund request",
    description:
      "<div>Lorem ipsum dolor sit amet consectetur. Lobortis sed risus in cras. Sit cras varius vulputate tortor suscipit. Duis enim bibendum ac nunc. Praesent eget posuere ac molestie tincidunt faucibus senectus nunc. Tincidunt senectus nullam mauris fermentum tortor. Quis felis egestas porttitor augue nibh malesuada viverra morbi. Lobortis arcu tellus arcu ipsum. Elit interdum at in ornare a enim aliquet dictum. At consequat ut vestibulum.</div>",
    assigneeEmail: null,
    category: "Finance",
    priority: 3,
    mid: null,
    customerEmail: null,
    status: 2,
    assignee: "Robin Billington",
  },
];

export const exampleCustomerTransactions = {
  totalElements: 6,
  totalPages: 1,
  first: true,
  last: true,
  pageable: {
    pageNumber: 0,
    pageSize: 15,
    sort: {
      sorted: false,
      empty: true,
      unsorted: true,
    },
    offset: 0,
    paged: true,
    unpaged: false,
  },
  size: 15,
  content: [
    {
      transactionId: 3986,
      time: "2024-09-20 14:25:17",
      result: "Success",
      itemPurchased: "Data",
      price: "63.4",
      orderId: 64849,
      type: "mastercard",
      lastdigits: 8355,
    },
    {
      transactionId: 3987,
      time: "2024-09-20 14:26:20",
      result: "Success",
      itemPurchased: "SMS",
      price: "7.18",
      orderId: 92840,
      type: "visa",
      lastdigits: 9355,
    },
    {
      transactionId: 3988,
      time: "2024-09-20 14:28:54",
      result: "Success",
      itemPurchased: "Data",
      price: "8.32",
      orderId: 3848,
      type: "mastercard",
      lastdigits: 4355,
    },
    {
      transactionId: 4013,
      time: "2024-09-27 08:42:18",
      result: "Failed",
      itemPurchased: "Account Credit",
      price: "10.0",
      orderId: 83930,
      type: "visa",
      lastdigits: 9355,
    },
    {
      transactionId: 4064,
      time: "2024-10-10 10:44:42",
      result: "Success",
      itemPurchased: "Combo",
      price: "59.38",
      orderId: 43838,
      type: "mastercard",
      lastdigits: 8355,
    },
    {
      transactionId: 4066,
      time: "2024-10-10 10:46:49",
      result: "Success",
      itemPurchased: "Virtual Number",
      price: "2.59",
      orderId: 46749,
      type: "visa",
      lastdigits: 9355,
    },
  ],
  number: 0,
  sort: {
    sorted: false,
    empty: true,
    unsorted: true,
  },
  numberOfElements: 6,
  empty: false,
};

export const exampleCustomerActivity = [
  {
    type: "sms",
    source: "***********",
    destination: "***********",
    time: "2024-09-27 08:42:18",
    totalCost: 0,
    chargeMethod: "allowance",
  },
  {
    type: "sms",
    source: "************",
    destination: "*************",
    time: "2024-10-27 08:42:18",
    totalCost: 0.316803,
    chargeMethod: "credit",
  },
  {
    type: "sms",
    source: "************",
    destination: "*************",
    time: "2024-11-27 08:42:18",
    totalCost: 0.316795,
    chargeMethod: "credit",
  },
  {
    type: "sms",
    source: "************",
    destination: "*************",
    totalCost: 0.316803,
    chargeMethod: "credit",
    time: "2024-12-28 08:42:18",
  },
  {
    type: "call",
    source: "18184366562",
    destination: "447418353690",
    totalCost: 0,
    time: "2024-01-11 08:42:18",
    chargeMethod: "free",
  },
  {
    type: "call",
    source: "18184366562",
    destination: "447418353690",
    totalCost: 0,
    time: "2024-05-06 08:42:18",
    chargeMethod: "free",
  },
  {
    type: "call",
    source: "************",
    destination: "19073024672",
    time: "2024-07-11 08:42:18",
    totalCost: 0,
    chargeMethod: "free",
  },
  {
    type: "call",
    source: "************",
    destination: "19073024672",
    time: "2024-08-12 08:42:18",
    totalCost: 0,
    chargeMethod: "free",
  },
];
