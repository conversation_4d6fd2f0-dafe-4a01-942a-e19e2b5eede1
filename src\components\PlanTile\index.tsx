import { Calendar, Gift, Phone, SMS, Signal } from "../svgs";
import { formatPrice } from "../utils/formatPrice";
import styles from "./plan-tile.module.scss";

const PlanTile = ({ planData, selectPlan, selectedPlan }: any) => {
  return (
    <div
      className={`${styles.container} ${
        selectedPlan &&
        selectedPlan.planId === planData.planId &&
        styles.selected
      }`}
      onClick={() => {
        selectPlan(planData);
      }}
    >
      <div className={styles.data}>
        {planData.dataAllowance > 0 && (
          <div className={styles.item}>
            <Signal />
            {planData.dataAllowance} GB
          </div>
        )}
        {planData.voiceAllowance > 0 && (
          <div className={styles.item}>
            <Phone />
            {planData.voiceAllowance} mins
          </div>
        )}
        {planData.smsAllowance > 0 && (
          <div className={styles.item}>
            <SMS />
            {planData.smsAllowance} SMS
          </div>
        )}
        <div className={styles.item}>
          <Calendar />
          {planData.validity} days validity
        </div>
        {planData.credit > 0 && (
          <div className={styles.item}>
            <Gift />£{formatPrice(planData.credit)} free global credit
          </div>
        )}
      </div>
      <div className={styles.price}>
        £{formatPrice(planData.prices[0].cost)}
      </div>
    </div>
  );
};

export default PlanTile;
