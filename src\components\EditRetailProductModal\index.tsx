import Dialog from "@/components/Dialog";
import { FloppyDisk, PencilCircle } from "@/components/svgs";
import {
  clearInput,
  createStateObject,
  createStateObjectWithInitialState,
  handleInputChange,
  labels,
} from "@/components/utils/InputHandlers";
import { useState } from "react";
import styles from "./edit-retail-product-modal.module.scss";
import { Input } from "@/components/Input";
import ToggleButtonGroup from "@/components/ToggleButtonGroup";
import TextArea from "@/components/TextArea";

type EditRetailProductModalProps = {
  productData: any;
  open: boolean;
  onClose: () => void;
};

const EditRetailProductModal = ({
  open,
  onClose,
  productData,
}: EditRetailProductModalProps) => {
  const fields = ["retailName", "description", "price", "status"];
  const [formData, setFormData] = useState(
    createStateObjectWithInitialState(createStateObject(fields), {
      retailName: productData.retailName,
      description: productData.description,
      price: productData.retailPrice,
      status: productData.status,
    })
  );

  return (
    <Dialog
      open={open}
      onClose={onClose}
      size="sm"
      headerIcon={<PencilCircle />}
      headerTitle="Edit Retail Product"
      confirmButtonText="Save Changes"
      confirmButtonOnClick={onClose}
      confirmButtonIcon={<FloppyDisk />}
      cancelButtonText="Cancel"
    >
      <div className={styles.formContainer}>
        {fields.map((field) => {
          if (["status"].includes(field)) {
            return (
              <ToggleButtonGroup
                selected={formData[field] ? "active" : undefined}
                options={[
                  {
                    label: "Active",
                    key: "active",
                  },
                  {
                    label: "Inactive",
                    key: "inactive",
                  },
                ]}
                label={labels[field]}
                onChange={(value) => {
                  handleInputChange(
                    field,
                    { target: { value } },
                    formData,
                    setFormData
                  );
                }}
              />
            );
          } else if (["description"].includes(field)) {
            return (
              <TextArea
                label={labels[field]}
                value={formData[field]}
                clear={() => clearInput(field, setFormData)}
                onChange={(e: any) => {
                  handleInputChange(field, e, formData, setFormData);
                }}
              />
            );
          } else {
            return (
              <Input
                key={"product-" + field}
                label={labels[field]}
                value={formData[field]}
                onChange={(e: any) => {
                  handleInputChange(field, e, formData, setFormData);
                }}
                error={formData.errors[field]}
                clear={() => {
                  clearInput(field, setFormData);
                }}
                infoTooltipText
              />
            );
          }
        })}
      </div>
    </Dialog>
  );
};

export default EditRetailProductModal;
