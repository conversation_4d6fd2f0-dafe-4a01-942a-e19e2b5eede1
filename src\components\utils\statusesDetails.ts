type OuterObject = {
  [key: string]: InnerObject;
};

type InnerObject = {
  status: string;
  color: string;
};

export const subscriberStatuses: OuterObject = {
  cancelled: {
    status: "Cancelled",
    color: "cancelled",
  },
  rejected: {
    status: "Rejected",
    color: "rejected",
  },
  active: {
    status: "Active",
    color: "active",
  },
  suspended: {
    status: "Suspended",
    color: "suspended",
  },
  banchange: {
    status: "Pending BAN Change",
    color: "pending",
  },
  readytoactivite: {
    status: "Ready to Activate",
    color: "active",
  },
  pending: {
    status: "Pending Activiation",
    color: "pending",
  },
};
