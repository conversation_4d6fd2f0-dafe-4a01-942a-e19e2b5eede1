@use "../../styles/theme.scss" as *;

h5 {
  font-size: 16px;
  color: #061632;
  margin-bottom: 10px;
  margin-top: 10px;
}

.eligible,
.notEligible {
  padding: 14px 16px;
  background-color: #f0faf2;
  border-radius: 15px;
  display: grid;
  grid-template-columns: 48px 1fr;
  align-items: end;
  width: 80%;
  .topText {
    margin-bottom: 6px;
    color: #061632;
    font-weight: 700;
    font-size: 16px;
  }
  .bottomText {
    color: #667085;
    font-size: 12px;
  }
}
.notEligible {
  background-color: #ffebeb;
  color: $urgent;
  grid-template-columns: 28px 1fr;
  align-items: center;
}

.choicesStep {
  margin-top: 10px;
  p,
  span {
    font-size: 14px;
    color: #061632;
  }
  > div {
    margin-top: 5px;
  }
}

.addressStep {
  width: 85%;
  .title {
    color: #061632;
    font-size: 14px;
    margin-top: 14px;
    margin-bottom: 5px;
  }
  .addressBoxes {
    div {
      border: 1px solid #dfe2e7;
      padding: 13px;
      border-radius: 20px;
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      cursor: pointer;
      span {
        color: #061632;
        font-size: 14px;
        width: 160px;
        text-wrap: auto;
        display: inline-block;
        line-height: 20px;
      }
      &.selected {
        border: 2px solid #2e70e5;
      }
    }
  }
}

.successMsg {
  p {
    font-size: 14px;
    font-weight: 400;
    color: #667085;
    line-height: 18px;
    margin-bottom: 10px;
  }
}

.choicesBg {
  padding: 13px 15px;
  border: 1px solid #dfe2e7;
  background: #f1f6fd;
  border-radius: 15px;
  cursor: pointer;
  color: #061632;
  margin-bottom: 10px;
  margin-right: 7px;
  &:hover {
    background: #d6e3fa;
    transition: all 0.7s ease;
  }
  &.selected {
    background: #2e70e5;
    color: #fff;
    &:hover {
      background: #2e70e5;
    }
  }
  span {
    border: 1px solid #838ca0;
    border-radius: 50%;
    width: 21px;
    height: 20px;
    margin-right: 10px;
    font-size: 0;
  }
  h5 {
    margin: 0;
    font-size: 14px;
  }
  .none {
    display: none;
  }
  > div {
    width: 100%;
    p {
      font-size: 14px;
      font-weight: 400;
      line-height: 20px;
    }
  }
}
