import * as React from "react";
import styles from "../../styles/table-test.module.scss";

import {
  createColumnHelper,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { parsePhoneNumber } from "libphonenumber-js";
import { formatDateWords } from "@/components/utils/formatDate";

type Person = {
  mid: string;
  email: string;
  phoneNumber: string;
  firstName: string;
  lastName: string;
  isLoggedIn: boolean;
  introductoryPlan: boolean;
  credit: number;
  currency: string;
  tacFlag: boolean;
  creationTime: string;
  country: string;
  status: string;
};

const defaultData: Person[] = [
  {
    mid: "1869858464",
    email: "<EMAIL>",
    phoneNumber: "2349134230367",
    firstName: "Wale",
    lastName: "Rich",
    isLoggedIn: true,
    introductoryPlan: false,
    credit: 0,
    currency: "USD",
    tacFlag: true,
    creationTime: "2024-11-18 13:56:18",
    country: "Nigeria",
    status: "Active",
  },
  {
    mid: "1867990439",
    email: "<EMAIL>",
    phoneNumber: "2347068572465",
    firstName: "Chinemeze",
    lastName: "ThankGod",
    isLoggedIn: false,
    introductoryPlan: false,
    credit: 0,
    currency: "USD",
    tacFlag: true,
    creationTime: "2024-11-06 16:27:29",
    country: "Nigeria",
    status: "Active",
  },
  {
    mid: "1005489415",
    email: "<EMAIL>",
    phoneNumber: "447591145245",
    firstName: "Mobilise",
    lastName: "Rest",
    isLoggedIn: false,
    introductoryPlan: false,
    credit: 295.33,
    currency: "GBP",
    tacFlag: true,
    creationTime: "2024-10-29 10:15:37",
    country: "United Kingdom/Guernsey/Isle of Man/Jersey",
    status: "Active",
  },
  {
    mid: "310639855",
    email: "<EMAIL>",
    phoneNumber: "4499988987",
    firstName: "Olawale",
    lastName: "Lawal",
    isLoggedIn: false,
    introductoryPlan: false,
    credit: 0,
    currency: "GBP",
    tacFlag: false,
    creationTime: "2024-10-09 08:57:11",
    country: "United Kingdom/Guernsey/Isle of Man/Jersey",
    status: "Active",
  },
  {
    mid: "1850939183",
    email: "<EMAIL>",
    phoneNumber: "*************",
    firstName: "Madss",
    lastName: "Test",
    isLoggedIn: true,
    introductoryPlan: false,
    credit: 0,
    currency: "GBP",
    tacFlag: true,
    creationTime: "2024-10-07 21:58:30",
    country: "United Kingdom/Guernsey/Isle of Man/Jersey",
    status: "Active",
  },
  {
    mid: "1813521436",
    email: "<EMAIL>",
    phoneNumber: "44092384758",
    firstName: "Olawale",
    lastName: "Mobilise",
    isLoggedIn: false,
    introductoryPlan: false,
    credit: 15,
    currency: "GBP",
    tacFlag: false,
    creationTime: "2024-10-04 09:49:00",
    country: "United Kingdom/Guernsey/Isle of Man/Jersey",
    status: "Active",
  },
  {
    mid: "670189742",
    email: "<EMAIL>",
    phoneNumber: "447364019827",
    firstName: "Test",
    lastName: "Test",
    isLoggedIn: true,
    introductoryPlan: false,
    credit: 25.65,
    currency: "GBP",
    tacFlag: true,
    creationTime: "2024-10-01 12:36:01",
    country: "United Kingdom/Guernsey/Isle of Man/Jersey",
    status: "Active",
  },
  {
    mid: "1750872274",
    email: "<EMAIL>",
    phoneNumber: "447364086975",
    firstName: "Polonskii",
    lastName: "Polonskii",
    isLoggedIn: true,
    introductoryPlan: false,
    credit: 0,
    currency: "GBP",
    tacFlag: true,
    creationTime: "2024-10-01 12:11:01",
    country: "United Kingdom/Guernsey/Isle of Man/Jersey",
    status: "Active",
  },
  {
    mid: "1275630432",
    email: "<EMAIL>",
    phoneNumber: "447365468753",
    firstName: "Gosha",
    lastName: "Gosha",
    isLoggedIn: true,
    introductoryPlan: false,
    credit: 0,
    currency: "GBP",
    tacFlag: true,
    creationTime: "2024-10-01 12:04:52",
    country: "United Kingdom/Guernsey/Isle of Man/Jersey",
    status: "Active",
  },
  {
    mid: "1204931882",
    email: "<EMAIL>",
    phoneNumber: "447365753648",
    firstName: "BEN",
    lastName: "WENI",
    isLoggedIn: true,
    introductoryPlan: false,
    credit: 0,
    currency: "GBP",
    tacFlag: true,
    creationTime: "2024-09-27 15:08:03",
    country: "United Kingdom/Guernsey/Isle of Man/Jersey",
    status: "Active",
  },
];

const columnHelper = createColumnHelper<Person>();

const phoneDisplay = (phoneNumber: string) => {
  let parsed = parsePhoneNumber(
    phoneNumber[0] === "+" ? phoneNumber : "+" + phoneNumber
  );
  return parsed ? parsed.formatInternational() : phoneNumber;
};

const dateDisplay = (date: string) => {
  return formatDateWords(date);
};

const columns = [
  columnHelper.accessor((row) => `${row.firstName} ${row.lastName}`, {
    id: "fullName",
    header: "Name",
  }),
  columnHelper.accessor("email", {
    id: "email",
    header: "Email Address",
  }),
  columnHelper.accessor((row) => phoneDisplay(row.phoneNumber), {
    id: "phoneNumber",
    header: "SIM No.",
  }),
  columnHelper.accessor("country", {
    id: "country",
    header: "Country",
  }),
  columnHelper.accessor("currency", {
    id: "currency",
    header: "Currency",
  }),
  columnHelper.accessor("credit", {
    id: "credit",
    header: "Credit Amount",
  }),
  columnHelper.accessor((row) => dateDisplay(row.creationTime), {
    id: "creationTime",
    header: "Sign Up Date",
  }),
  columnHelper.accessor((row) => dateDisplay(row.creationTime), {
    id: "creationTime",
    header: "Sign Up Date",
  }),
  columnHelper.accessor("status", {
    id: "status",
    header: "Status",
  }),
];

const TableTest = () => {
  const [data, _setData] = React.useState(() => [...defaultData]);

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    columnResizeMode: "onChange",
    columnResizeDirection: "ltr",
  });

  return (
    <div className={styles.main}>
      <div className={styles.tableContainer}>
        <table
          style={{
            width: table.getCenterTotalSize(),
          }}
        >
          <thead>
            {table.getHeaderGroups().map((headerGroup) => (
              <tr key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <th
                    key={header.id}
                    colSpan={header.colSpan}
                    style={{ width: `${header.getSize()}px` }}
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                    <div
                      onDoubleClick={() => header.column.resetSize()}
                      onMouseDown={header.getResizeHandler()}
                      onTouchStart={header.getResizeHandler()}
                      className={`${styles.resizer} ${styles.ltr} ${header.column.getIsResizing() ? styles.isResizing : ""}`}
                    />
                  </th>
                ))}
              </tr>
            ))}
          </thead>
          <tbody>
            {table.getRowModel().rows.map((row) => (
              <tr key={row.id}>
                {row.getVisibleCells().map((cell) => (
                  <td
                    key={cell.id}
                    style={{ width: `${cell.column.getSize()}px` }}
                  >
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default TableTest;
