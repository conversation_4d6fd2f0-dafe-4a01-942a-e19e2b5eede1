import { useEffect, useState } from "react";
import Menu from "../Menu";
import SearchBar from "../SearchBar";
import { LogOut, User } from "../svgs";
import { filterList, submitSearch } from "../utils/searchAndFilter";
import styles from "./search-section.module.scss";
import { logOut } from "../utils/logOut";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate, useParams } from "react-router-dom";

const SearchSection = ({
  data,
  filters = null,
  setFilteredData,
  placeholder,
  searchQuery,
  setSearchQuery,
  setQueryDisplay,
  setCurrentPage,
  id,
}: any) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { userInfo, ticketOpen } = useSelector((state: any) => state);
  const { mvnoId } = useParams();

  useEffect(() => {
    if (data) {
      if (searchQuery === "") {
        setQueryDisplay("");
        submitSearch(data, setFilteredData, searchQuery, setQueryDisplay);
      }
    }
  }, [searchQuery]);

  return (
    <div className={`${styles.topBar} ${ticketOpen && styles.ticketOpen}`}>
      <SearchBar
        query={searchQuery}
        setQuery={setSearchQuery}
        onSubmit={() => {
          if (filters === null) {
            submitSearch(data, setFilteredData, searchQuery, setQueryDisplay);
          } else {
            submitSearch(
              filterList(data, filters),
              setFilteredData,
              searchQuery,
              setQueryDisplay
            );
          }
          setCurrentPage(1);
        }}
        placeholder={placeholder}
        id={id}
      />
      <div style={{ width: 50 }} />
      {/*  <Menu
        data={{
          label: userInfo ? userInfo.firstName : "",
          items: [
            {
              label: "Profile",
              icon: <User />,
              link: `/user-profile`,
            },
            {
              label: "Logout",
              icon: <LogOut />,
              onClick: () => {
                logOut(dispatch, navigate);
              },
            },
          ],
        }}
      /> */}
    </div>
  );
};

export default SearchSection;
