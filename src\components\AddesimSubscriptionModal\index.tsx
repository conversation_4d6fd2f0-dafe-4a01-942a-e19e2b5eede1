import Modal from "../Modal";
import { Input } from "../Input";
import inputStyles from "../Input/input.module.scss";
import { countryListAlpha2 } from "../utils/countryList";
import SelectDropdown from "../SelectDropdown";
import styles from "../AddSubscriberModal/AddSubscriberModal.module.scss";
import { useEffect, useState } from "react";
import { plans } from "../utils/dataCreator";
import {
  clearInput,
  createStateObject,
  getMessages,
  getRules,
  handleInputChange,
  labels,
  serviceTypesValues,
} from "../utils/InputHandlers";
import { Chip } from "../svgs";
import CountryDisplay from "../CountryDisplay";
import SearchBar from "../SearchBar";
import { useDispatch } from "react-redux";
import SelectInput from "../SelectInput";
import { getCountryOptions } from "../utils/getCountryOptions";

const fields = ["serviceType", "country", "plan", "iccid"];
const rules = getRules(fields);
const messages = getMessages(fields);

const AddesimSubscriptionModal = ({
  show,
  close,
}: {
  show: boolean;
  close: Function;
}) => {
  const dispatch = useDispatch();
  const [data, setData] = useState(createStateObject(fields));
  const [step, setStep] = useState(1);
  const [query, setQuery] = useState("");
  const [filteredPlans, setFilteredPlans] = useState<string[]>([]);

  useEffect(() => {
    handleSearch();
  }, [query]);

  const handleCloseModal = () => {
    close(false);
    setStep(1);
  };

  const handleNextStep = () => {
    if (step < 3) {
      setStep(step + 1);
    } else {
      handleCloseModal();
      dispatch({
        type: "notify",
        payload: {
          error: false,
          message: "eSIM subscription added.",
        },
      });
    }
  };

  const handleSearch = () => {
    if (query.length > 0) {
      const filteredPlans = plans.filter((item) => item.includes(query));
      setFilteredPlans(filteredPlans);
    } else {
      setFilteredPlans(plans);
    }
  };

  return (
    <Modal
      show={show}
      close={() => handleCloseModal()}
      title="Add eSIM Subscription"
      cancelButton="Cancel"
      icon={<Chip />}
      saveButton={step === 3 ? "Finish" : "Continue"}
      proceed={() => handleNextStep()}
    >
      <div className={styles.main}>
        {step === 1 && (
          <>
            <form>
              <div className={inputStyles.inputContainer} role="radiogroup">
                <div
                  className={inputStyles.label}
                  style={{ marginBottom: "15px" }}
                >
                  Service Type
                </div>
                {serviceTypesValues.map((val: string) => (
                  <span
                    className={
                      styles.choicesBg +
                      " " +
                      (data["serviceType"] === val ? styles.selected : "")
                    }
                    key={val}
                    role="radio"
                    aria-checked={data["serviceType"] === val}
                    tabIndex={0}
                    style={{ marginBottom: "8px" }}
                    onClick={(e: any) => {
                      handleInputChange("serviceType", e, data, setData);
                    }}
                    aria-labelledby="serviceTypeRadio"
                  >
                    {val}
                  </span>
                ))}
              </div>
            </form>
            <p>Country</p>
            <SelectInput
              selected={data["country"]}
              placeholder="Country"
              options={getCountryOptions()}
              onChange={(e: any) => {
                handleInputChange(
                  "country",
                  { target: { value: e } },
                  data,
                  setData
                );
              }}
            />
          </>
        )}
        {step === 2 && (
          <>
            <p>Showing Plans for United Kingdom.</p>
            <h5>Select a plan</h5>
            <SearchBar
              placeholder="Search plan"
              query={query}
              setQuery={(val: string) => setQuery(val)}
            />
            {filteredPlans.map((item: string, index: number) => (
              <div className={styles.radioPlans} key={index}>
                <div className={styles.choicesBg + " flex"}>
                  <span
                    style={{
                      background: data["plan"] === item ? "#2E70E5" : "",
                    }}
                    key={`${item}-inputRadio`}
                    role="radio"
                    aria-checked={data["plan"] === item}
                    tabIndex={0}
                    onClick={(e: any) => {
                      handleInputChange("plan", e, data, setData);
                    }}
                    aria-labelledby="planRadio"
                  >
                    {item}
                  </span>
                  <div>
                    <h5>{item}</h5>
                    <p>1 GB Data | 7 Days Validity</p>
                  </div>
                </div>
              </div>
            ))}
          </>
        )}
        {step === 3 && (
          <>
            <p>Provide ICCID</p>
            <Input
              key="iccid-input"
              label={labels["iccid"]}
              value={data["iccid"]}
              onChange={(e: any) => {
                handleInputChange("iccid", e, data, setData);
              }}
              error={data.errors["iccid"]}
              clear={() => {
                clearInput("iccid", setData);
              }}
            />
          </>
        )}
      </div>
    </Modal>
  );
};

export default AddesimSubscriptionModal;
