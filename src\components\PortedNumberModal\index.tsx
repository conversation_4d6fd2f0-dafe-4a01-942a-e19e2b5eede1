import styles from "./ported-model.module.scss";
import Modal from "../Modal";
import { formatNumber } from "../utils/formatNumber";

const PortedNumberModal = ({ show, setShow, activeNumber }: any) => {
  return (
    <Modal
      image="/bulk_edit_confirm_graphic.svg"
      show={show}
      setShow={setShow}
      close={() => setShow(false)}
      cancelButton="Close Window"
      onClose={() => setShow(false)}
    >
      <div className={`${styles.main} `}>
        <h3>Ported Number</h3>

        <div className={styles.modalContent}>
          <div>
            <p> {!activeNumber?.email ? null : <>Customer email:</>}</p>
            <p> {!activeNumber?.msisdn ? null : <>Ported MSISDN:</>}</p>
            <p> {!activeNumber?.country ? null : <>Country:</>}</p>

            <p> {!activeNumber?.city ? null : <>City:</>}</p>
            <p> {!activeNumber?.status ? null : <>Status:</>}</p>
          </div>
          <div>
            <p>{activeNumber?.email}</p>
            <p>{formatNumber(activeNumber?.msisdn)}</p>
            <p>{activeNumber?.country}</p>
            <p>{activeNumber?.city}</p>
            <p>
              {activeNumber?.status[0] +
                activeNumber?.status.slice(1).toLowerCase()}
            </p>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default PortedNumberModal;
