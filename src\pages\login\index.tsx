import styles from "../../styles/login.module.scss";
import LoginForm from "../../components/LoginForm";
import { useState } from "react";
import { SwitchTransition, CSSTransition } from "react-transition-group";
import CheckOtp from "../../components/CheckOtp";
import { logOut } from "../../components/utils/logOut";
import { LogoFull } from "@/components/svgs";

const version = import.meta.env.VITE_VERSION;
const Login = () => {
  const [section, setSection] = useState("login");

  const [oldPassword, setOldPassword] = useState("");

  const handleForcePasswordChange = (password: string) => {
    setOldPassword(password);
    setSection("change-password");
  };

  const handleBackToLogin = () => {
    logOut();
    setSection("login");
  };

  return (
    <div className={styles.container}>
      <div className={styles.main}>
        <div className={styles.logos}>
          <LogoFull />
        </div>

        <SwitchTransition>
          <CSSTransition
            key={section}
            addEndListener={(node, done) =>
              node.addEventListener("transitionend", done, false)
            }
            classNames="fade"
          >
            {section === "otp" ? (
              <div className={styles.formContainer}>
                <CheckOtp />
              </div>
            ) : (
              <>
                <div className={styles.formContainer}>
                  <LoginForm
                    handleForcePasswordChange={handleForcePasswordChange}
                  />
                </div>
                <div className={styles.version}>Demo Version</div>
              </>
            )}
          </CSSTransition>
        </SwitchTransition>
      </div>
      <img src="/login.jpg" className={styles.graphic} />
    </div>
  );
};

export default Login;
