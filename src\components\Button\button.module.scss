@use "../../styles/theme.scss" as *;

.button {
  height: 48px;
  padding: 0 24px;
  font-size: 14px;
  border: none;
  border-radius: 100px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: grid;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  &:disabled {
    pointer-events: none;
    opacity: 0.5;
  }
  &.primary {
    background: #2e70e5;
    color: #fff;
    box-shadow: 0px 8px 20px 0px #578dea33;
    &:hover {
      background: #214f9c;
    }
  }
  &.secondary {
    background: #d6e3fa;
    color: #1857c3;
    &:hover {
      background: var(--primary-200);
    }
  }
  &.transparent {
    background: transparent;
    color: #1857c3;
    text-decoration: underline;
  }
  &.tertiary {
    background: none;
    color: $black;
    min-width: initial;
    &:hover {
      color: $dark-orange;
    }
  }
  &.destructive {
    background: var(--systemStatus-red-50);
    color: var(--systemStatus-red-800);
    &:hover {
      background: rgba(255, 122, 122, 1);
    }
  }
  &.quaternary {
    background: none;
    color: $orange;
    min-width: initial;
    padding: 0 15px;
    &:hover {
      color: $dark-orange;
    }
  }
  &.search {
    height: 100%;
    background: $orange;
    color: #fff;
    margin-left: auto;
    &:hover {
      background: $dark-orange;
    }
  }
  &.export {
    background: $orange;
    color: #fff;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.15);
    padding: 0 24px;
    height: 50px;
    min-width: 0;
    &:hover {
      background: $dark-orange;
    }
  }
  &.blue {
    background: #2e70e5;
    color: #fff;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.15);
    padding: 0 25px;
    min-width: auto;
    font-size: 14px;
    font-weight: 500;
  }
  &.customerActionBlue {
    width: 100%;
    background-color: $lightblue;
    color: #1857c3;
    border-radius: 100px;
    padding: 0 16px;
    font-size: 14px;
    font-weight: 700;
    line-height: 18px;
    justify-content: flex-start;
  }
  &.customerActionRed {
    background-color: #fdecec;
    color: #710909;
    border-radius: 100px;
    padding: 0 24px;
    font-size: 14px;
    font-weight: 700;
    line-height: 18px;
    justify-content: flex-start;
  }
}

.content {
  grid-area: 1 / 1 / 2 / 2;
  display: flex;
  align-items: center;
  svg {
    margin-right: 8px;
    vertical-align: middle;
  }
}
