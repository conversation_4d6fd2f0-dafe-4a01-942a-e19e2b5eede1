import { useState } from "react";
import styles from "./check-otp.module.scss";
import Button from "../Button";
import OtpInput from "../OtpInput";

const CheckOtp = ({ proceed }: any) => {
  const [otp, setOtp] = useState("");

  const codeLength = 5;

  const [loading, setLoading] = useState(false);

  const handleSubmit = () => {};

  return (
    <div className={styles.form}>
      <h2>Verify Your Code</h2>
      <p className={styles.tag}>
        We’ve sent a code to your email. Please enter it below.
      </p>
      <OtpInput
        handleSuccess={handleSubmit}
        codeLength={codeLength}
        value={otp}
        setValue={setOtp}
      />
      <div className={styles.notReceived}>
        Didn't get the code ?
      </div>
      <p className={styles.tag}>Ensure your email address is correct, check your spam or junk folder, or request a new code.</p>
      <Button
        style={{ height: 50 }}
        loading={loading}
        disabled={otp.length !== codeLength}
        color="primary"
        onClick={handleSubmit}
      >
        Request New Code
      </Button>
    </div>
  );
};

export default CheckOtp;
