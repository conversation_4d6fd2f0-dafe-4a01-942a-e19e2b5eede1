import styles from './insight-list.module.scss';

type InsightListProps = {
  insights: {
    title: string;
    value: string;
    icon?: React.ReactNode
  }[]
}

const InsightList = (props: InsightListProps) => {
  const { insights } = props

  return (
    <div className={styles.container}>
      {insights.map(i => (<div key={i.value} className={styles.item}>
        <p className={styles.title}>{i.title}</p>
        <p className={styles.value}>{i.value}</p>
      </div>))}
    </div>
  )
}

export default InsightList;