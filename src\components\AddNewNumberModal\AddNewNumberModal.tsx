import styles from "../AddSubscriberModal/AddSubscriberModal.module.scss";
import { useEffect, useState } from "react";
import { clearInput, createStateObject, labels, handleInputChange } from "../utils/InputHandlers";
import { Plus, XCircle, CheckCircle, PencilCircle, PlusCircle, Check } from "../svgs";
import { AddressStep, ImeiField, InputFieldsStep, ChoicesStep, PlansStep } from "../ModalsSteps/ModalsSteps";
import Button from "../Button";
import { Collapse } from "@mui/material";
import Dialog from "../Dialog";
import { Input } from "../Input";

const detailsFields = ['imei', 'plan', 'carrier', 'iccid']
const subscriberDetails = ["firstName", "lastName", "email", "phoneNumber"]
const AddressFields = ["streetNumber", "streetDirection", "streetName", "state", "city", "zipCode"];
const AddNewNumberModal = ({show, close, endStep = 5}: { show: boolean, close: Function, endStep?:number }) => {
    const [data, setData] = useState(createStateObject([...subscriberDetails, 'imei', ...detailsFields, 'isTether']));
    const [step, setStep] = useState(1)
    const [numberIneligible, setNumberIneligible] = useState(false);
    const [numberVerified, setNumberVerified] = useState(false);
    const [saveBtnText, setSaveBtnText] = useState('')
    const [newAddress, setNewAddress] = useState(createStateObject(AddressFields))
    const [isNewAddress, setIsNewAddress] = useState(false)
    const [selectedAddress, setSelectedAddress] = useState('')


    const handleCloseModal = () => {
        close(false)
        setSaveBtnText('')
        setStep(1)
        setNumberIneligible(false)
        setNumberVerified(false)
        setData(createStateObject([...subscriberDetails, 'imei', ...detailsFields, 'isTether']))
    }

    useEffect(() => {
        handleButtonsText()
    }, [step, isNewAddress, data.isTether, data.plan])


    const handleButtonsText = () => {
        if (step === 1) {
            setSaveBtnText('Continue')
        } else if (step === 2) {
            if (isNewAddress) {
              setSaveBtnText('Save New Address')
            } else if (endStep == 2) {
              setSaveBtnText('Request MDN Change')
            } else {
              setSaveBtnText('Continue')
            }
        } else if (step === 3) {
            setSaveBtnText('Check Eligibility')
        } else if (step === 4) {
            if (data.isTether && data.plan) {
                setSaveBtnText('Add plan')
            } else {
                setSaveBtnText('')
            }
        } else {
            setSaveBtnText('Add Subscription')
        }
      }

    const handleNextStep = () => {
        if (step === 3 && !data.imei) {
            setNumberIneligible(true)
            setSaveBtnText('Check another number')
        } else if (step === 3 && data.imei && numberVerified) {
            setStep(step + 1)
        }  else if (step === 3 && data.imei) {
            setNumberIneligible(false)
            setNumberVerified(true)
            setSaveBtnText('Continue')
        } else if (step === 2 && isNewAddress) {
            setIsNewAddress(false)
        } else if (step < 5) {
            setStep(step + 1)
            if (endStep && endStep === 2 && step > 1) {
                handleCloseModal()
            }
        } else {
            handleCloseModal()
        }
    }

    return (
        <Dialog 
            size="sm"
            open={show}
            onClose={() => handleCloseModal()}
            headerTitle={isNewAddress ? 'Add new address' : endStep === 2 ? 'Change Address & MDN' : 'Add Subscription'}
            cancelButtonText={ (!data.isTether || !data.plan) && step === 4 ? '' : 'Cancel' }
            headerIcon={isNewAddress ? <PlusCircle /> : step === 2 ? <PencilCircle /> : <PlusCircle />}
            confirmButtonText={saveBtnText}
            confirmButtonOnClick={() => handleNextStep()}>
                {
                    step === 1 && (
                        <>
                            <h5>Subscriber details</h5>
                            <InputFieldsStep data={data} setData={(val:any) => setData(val)} fields={subscriberDetails} />
                        </>
                    )
                }
                {
                    step === 2 && (
                        <div className={styles.addressStep}>
                            {
                                isNewAddress ? (
                                    <AddressStep data={newAddress} setData={(val:any) => setNewAddress(val)} />
                                ) : (
                                    <>
                                        <h5>Address</h5>
                                        <Button color="secondary" style={{ width: '100%' }} onClick={() => setIsNewAddress(true)}><Plus /> Add New Address</Button>
                                        <div className={styles.addressBoxes}>
                                            <p className={styles.title}>Default Address</p>
                                            <div onClick={(e:any) => setSelectedAddress(e.target.innerText)}
                                                className={(selectedAddress === '12, NW, streetName, cityName, State, Zip Code') ? styles.selected : ''}>
                                                <span>12, NW, streetName, cityName, State, Zip Code </span> 
                                                { (selectedAddress === '12, NW, streetName, cityName, State, Zip Code') && <Check /> }
                                            </div>
                                            <p className={styles.title}>Previous Addresses</p>
                                            <div onClick={(e:any) => setSelectedAddress(e.target.innerText)}
                                                className={(selectedAddress === '20, Cairo, streetName, cityName, State, Zip Code') ? styles.selected : ''}>
                                                <span>20, Cairo, streetName, cityName, State, Zip Code</span>
                                                { (selectedAddress === '20, Cairo, streetName, cityName, State, Zip Code') && <Check /> }
                                            </div>
                                            <div onClick={(e:any) => setSelectedAddress(e.target.innerText)}
                                                className={(selectedAddress === '13, London, streetName, cityName, State, Zip Code') ? styles.selected : ''}>
                                                <span>13, London, streetName, cityName, State, Zip Code</span>
                                                { (selectedAddress === '13, London, streetName, cityName, State, Zip Code') && <Check /> }
                                            </div>
                                            <div onClick={(e:any) => setSelectedAddress(e.target.innerText)}
                                                className={(selectedAddress === '100, Sao Paulo, streetName, cityName, State, Zip Code') ? styles.selected : ''}>
                                                <span>100, Sao Paulo, streetName, cityName, State, Zip Code</span>
                                                { (selectedAddress === '100, Sao Paulo, streetName, cityName, State, Zip Code') && <Check /> }
                                            </div>
                                        </div>
                                    </>
                                )

                            }
                        </div>
                    )
                }
                {
                    step === 3 && (
                        <div>
                            <h5>Eligibility Check</h5>
                            <ImeiField data={data} setData={(val:any) => setData(val)} />
                            <Collapse in={numberIneligible}>
                                <div className={styles.notEligible}>
                                    <XCircle />
                                    <div>
                                        <div className={styles.topText}>
                                            <p>IMEI is not eligible</p>
                                        </div>
                                        <div className={styles.bottomText}>
                                            <p>Double check the number entered or try a different one.</p>
                                        </div>
                                    </div>
                                </div>
                            </Collapse>
                            <Collapse style={{ width: "100%" }} in={numberVerified}>
                                <div className={styles.eligible}>
                                    <CheckCircle />
                                    <div>
                                        <div className={styles.topText}>
                                            <p>IMEI is eligible</p>
                                        </div>
                                        <div className={styles.bottomText}>
                                            <p>You can continue</p>
                                        </div>
                                    </div>
                                </div>
                            </Collapse>
                        </div>
                    )
                }
                {
                    step === 4 && (
                        <>
                            <ChoicesStep fieldName='isTether' data={data} setData={(val:any) => setData(val)} title="Tether Plans?" choices={["Yes", "No"]} />
                            {
                                ((data.isTether === 'Yes') || (data.isTether === 'No')) && (
                                    <>
                                        <h5>Select a plan</h5>
                                        <PlansStep data={data} setData={(val:any) => setData(val)} />
                                    </>
                                )
                            }
                        </>
                    )
                }
                {
                    step === 5 && (
                        <>
                            {
                                detailsFields.map((field:string) => (
                                    <Input
                                        key={`${field}-input`}
                                        label={labels[field]}
                                        value={field === 'carrier' ? 'AT&T' : data[field]}
                                        onChange={(e: any) => {
                                            handleInputChange(field, e, data, setData);
                                        }}
                                        error={data.errors[field]}
                                        clear={() => {
                                            clearInput(field, setData);
                                        }}
                                        noClear={field !== "iccid"}
                                        infoTooltipText
                                        disabled={field !== "iccid"}
                                        editOnClick={
                                            () => {
                                                setStep(field === 'imei' ? 3 : 4)
                                            }
                                        }
                                        editable={field === "imei" || field === "plan"}
                                    />
                                ))
                            }
                        </>
                    )
                }
        </Dialog>
    )
}

export default AddNewNumberModal