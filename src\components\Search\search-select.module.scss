@use "../../styles/theme.scss" as *;
@import "../../styles/mixins.module.scss";

.menuButton {
  @include tableHeadFilterButton;
}

.box {
  height: 100%;
}

.menuItem {
  display: flex;
  align-items: center;
  border-radius: 6px;
  transition: all 0.1s ease;
  color: $black;
  padding: 0;
  line-height: 21px;
  font-size: 14px;
  padding: 0;
  gap: 11px;
  br {
    display: none;
  }
  cursor: auto;
  &:hover {
    background: none;
  }
}

.container {
  display: grid;
  grid-template-columns: 1fr;
  grid-row-gap: 18px;
  max-height: 318px;
  overflow: auto;
  grid-column-gap: 32px;
  padding-right: 12px;
  white-space: initial;
  &.grid {
    grid-template-columns: repeat(3, auto);
  }
  &.twoColumnGrid {
    grid-template-columns: repeat(2, 180px);
  }
}

.buttons {
  display: flex;
  width: 100%;
  justify-content: flex-end;
  margin-top: 32px;
}
