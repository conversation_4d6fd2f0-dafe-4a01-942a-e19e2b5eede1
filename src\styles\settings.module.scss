.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.main {
  padding: 24px;
  background: white;
  border-radius: 16px;
  flex: 1;
  padding: 22px 16px;
  margin: 16px;
  h2 {
    color: var(--text-primary);
    font-size: 20px;
    font-weight: 700;
    line-height: 28px;
    letter-spacing: -0.3px;
  }
}

.userRole {
  padding: 6px 12px;
  border-radius: 8px;
  background: var(--systemStatus-orange-100);
  color: var(--systemStatus-orange-800);
  font-size: 12px;
  line-height: 15px; /* 125% */
  width: fit-content;
  margin-top: 16px;
}

.userRole {
  color: #666;
  margin-bottom: 24px;
}

.formContainer {
  max-width: 400px;
  display: flex;
  flex-direction: column;
  gap: 2px;
  margin-bottom: 24px;
}

.twoFAToggleContainer {
  border-radius: 16px;
  background: var(--primary-50);
  margin-top: 2px;
  padding: 16px;
  display: flex;
  align-items: center;
  max-width: 320px;

  .label {
    font-size: 14px;
    line-height: 18px;
    color: var(--text-primary);
  }
  .toggleLabel {
    margin-left: auto;
    margin-right: 6px;
    font-size: 12px;
    line-height: 15px;
  }
}

.buttonContainer {
  display: flex;
  gap: 16px;
}
