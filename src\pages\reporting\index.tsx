
import { useState } from "react";
import { CSSTransition, SwitchTransition } from "react-transition-group";
import ReportingCharts from "../../components/ReportingCharts";
import styles from "../../styles/reporting.module.scss";
import Title from "@/components/Title";
import SwitchBar from "@/components/SwitchBar";
import ReportTables from "@/components/ReportTables";

const Reporting = () => {
  const [selection, setSelection] = useState("Overview");

  return (
    <div className={styles.container}>
      <Title>Reports</Title>
      <SwitchBar
        options={[
          { label: "Overview", id: "Overview" },
          { label: "Reports", id: "Reports" },
        ]}
        selected={selection}
        setSelected={setSelection}
        layoutId="reports-sections"
      />
      <div className={styles.main}>
        <SwitchTransition>
          <CSSTransition
            key={selection}
            addEndListener={(node, done) =>
              node.addEventListener("transitionend", done, false)
            }
            classNames="fade"
          >
            <div style={{ height: '100%' }}>
              {selection === "Overview" && <ReportingCharts />}
              {selection === "Reports" && <ReportTables />}
            </div>
          </CSSTransition>
        </SwitchTransition>
      </div>

    </div>
  );
};

export default Reporting;
