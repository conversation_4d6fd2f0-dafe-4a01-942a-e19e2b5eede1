import { formatPrice } from "./formatPrice";

const formatPlanData = (plan: any) => {
  let dataArr = [];
  if ("creditAmount" in plan) plan.credit = plan.creditAmount;
  if (plan.dataAllowance !== 0) {
    dataArr.push(`${plan.dataAllowance} GB`);
  }
  if (plan.voiceAllowance !== 0) {
    dataArr.push(`${plan.voiceAllowance} mins`);
  }
  if (plan.smsAllowance !== 0) {
    dataArr.push(`${plan.smsAllowance} SMS`);
  }
  if (plan.validity !== 0) {
    dataArr.push(`${plan.validity} days validity`);
  }
  if (plan.credit !== 0) {
    dataArr.push(`£${formatPrice(plan.credit)} global credit`);
  }
  return dataArr.join(", ");
};

export default formatPlanData;

export const formatDashboardPlanData = (plan: any) => {
  let dataArr = [];
  if (plan.initialBytes !== 0) {
    dataArr.push(`${plan.initialBytes / (1024 * 1024 * 1024)} GB`);
  }
  if (plan.initialMinutes !== 0) {
    dataArr.push(`${plan.initialMinutes} mins`);
  }
  if (plan.initialMessages !== 0) {
    dataArr.push(`${plan.initialMessages} SMS`);
  }
  return dataArr.join(", ");
};
