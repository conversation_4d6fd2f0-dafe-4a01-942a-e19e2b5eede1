@use "../../styles/theme.scss" as *;

.main {
  box-shadow: 0px 8px 20px rgba(0, 0, 0, 0.1);
  background: #EBF9EE;
  min-height: 86px;
  display: grid;
  grid-template-columns: 500px 1fr 24px;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  max-width: 756px;
  padding: 18px 37px;
  z-index: 3000;
  pointer-events: all;
  border-radius: 16px;
  position: relative;
  &.error {
    background: #FDECEC;
  }
}
.content {
  display: flex;
  svg {
    stroke: #4BC962;
    width: 24px;
    height: 24px;
  }
  div {
    margin-left: 6px;
    h5 {
      color: #061632;
      font-size: 18px;
      margin: 0
    }
    .message {
      font-size: 14px;
      line-height: 18px;
      color: #667085;
      margin-right: auto;
      margin-top: 5px;
    }
  }
}

.close {
  background: none;
  border: none;
  outline: none;
  padding: 0;
  margin: 0;
  position: absolute;
  right: 20px;
  cursor: pointer;
  top: 17px;
}
