import { useState } from "react";
import SubscriberPlanTile from "../SubscriberPlanTile/SubscriberPlanTile";
import styles from "../TravellerProductContainer/customer-product.module.scss";
import { Chip, Export, Funnel, Plus, PlusFilled, SortIcon } from "../svgs";
import BlobSwitchBar from "../BlobSwitchBar";
import CountryDisplay from "../CountryDisplay";
import { exampleSubscriberPlans } from "../utils/exampleSubscriberDetails";
import IconButton from "../IconButton";
import SearchBar from "../SearchBar";
import ActivityLogTable from "../ActivityLogTable/ActivityLogTable";
import ConfigurationsSection from "../ConfigurationsSection/ConfigurationsSection";
import SubscriberPortInSection from "../SubscriberPortInSection/SubscriberPortInSection";
import ProductsMenu from "../ProductsMenu";
import Button from "../Button";
import AddesimSubscriptionModal from "../AddesimSubscriptionModal";
import AddNewNumberModal from "../AddNewNumberModal/AddNewNumberModal";
import UpdateAddPortInSubscriptionModal from "../UpdateAddPortInSubscriptionModal/UpdateAddPortInSubscriptionModal";

const NationalSubscriptionsTab = ({
  //data,
  setAddCustomerProduct,
  repopulate,
  customer,
}: any) => {
  const datetimeToUTC = (fromDate: any) => {
    return new Date(
      Date.UTC(
        fromDate.getFullYear(),
        fromDate.getMonth(),
        fromDate.getDate(),
        fromDate.getHours(),
        fromDate.getMinutes(),
        fromDate.getSeconds(),
        fromDate.getMilliseconds()
      )
    );
  };

  const data = { ...exampleSubscriberPlans };
  const [isFullSize, setIsFullSize] = useState(false);
  const [subSelection, setSubSelection] = useState("active");
  const [mainSelected, setMainSelected] = useState("details");
  const [showAddEsimModal, setShowAddEsimModal] = useState(false);
  const [showAddNumberModal, setShowAddNumberModal] = useState(false);
  const [showAddPortInModal, setAddPortInModal] = useState(false);

  const sections = [
    {
      id: "details",
      label: "Subscriptions details",
    },
    {
      id: "port-in",
      label: "Port-in History",
    },
    {
      id: "configurations",
      label: "Configurations",
    },
    {
      id: "activityLog",
      label: "Activity Log",
    },
  ];

  const [filteredSubs, setFilteredSubs] = useState([] as any);

  const [filters, setFilters] = useState({
    classification: [],
    countries: [],
    region: [],
  } as any);

  const handleFilterChange = (type: string, option: string) => {
    let current = filters[type];
    if (current.includes(option)) {
      current = current.filter((item: any) => item !== option);
    } else {
      current.push(option);
    }
    setFilters({
      ...filters,
      [type]: current,
    });
  };

  const getAllOfType = (all: any, key: string) => {
    let allProducts = [] as any;
    all.forEach((log: any) => {
      if (key === "countries") {
        if (
          !allProducts.some(
            (item: any) => item.key === log[key][0]?.countryCode
          )
        ) {
          allProducts.push({
            key: log[key][0]?.countryCode,
            label: <CountryDisplay country={log[key][0]?.countryCode} />,
          });
        }
      } else {
        if (!allProducts.some((item: any) => item.key === log[key])) {
          allProducts.push({ key: log[key], label: log[key] });
        }
      }
    });
    return allProducts;
  };

  return (
    <>
      <AddesimSubscriptionModal
        show={showAddEsimModal}
        close={(val: boolean) => setShowAddEsimModal(val)}
      />
      <AddNewNumberModal
        show={showAddNumberModal}
        close={(val: boolean) => setShowAddNumberModal(val)}
      />
      <UpdateAddPortInSubscriptionModal
        show={showAddPortInModal}
        close={(val: boolean) => setAddPortInModal(val)}
        title="Add Porting In Subscription"
      />
      <div className={styles.panelTopBar}>
        <h4>Subscriptions</h4>
        <div className={styles.actions}>
          {!isFullSize && (
            <>
              <SearchBar placeholder="Search by MDN, IMEI or ICCID" />
              <IconButton style={{ margin: "0px 8px" }}>
                <Funnel />
              </IconButton>
              <IconButton style={{ margin: "0px 8px" }}>
                <SortIcon />
              </IconButton>
            </>
          )}
          <ProductsMenu
            data={{
              icon: (
                <IconButton>
                  <PlusFilled />
                </IconButton>
              ),
              items: [
                {
                  label: "Add New Number Subscription",
                  icon: <Plus />,
                  onClick: () => setShowAddNumberModal(true),
                },
                {
                  label: "Add Port-In Number Subscription",
                  onClick: () => setAddPortInModal(true),
                  icon: <Plus />,
                },
                {
                  label: "Add eSIM Subscription",
                  icon: <Chip />,
                  onClick: () => setShowAddEsimModal(true),
                },
              ],
            }}
          />
        </div>
      </div>
      {isFullSize && (
        <div className="flex-justify-content-between flex">
          <BlobSwitchBar
            options={sections}
            selected={mainSelected}
            setSelected={setMainSelected}
            layoutId="subscriber-sub-selection"
          />
          {mainSelected === "activityLog" && (
            <Button color="secondary">
              <Export /> Export to CSV
            </Button>
          )}
        </div>
      )}
      {mainSelected === "details" && (
        <div className={`${styles.plansContainer}`}>
          {data.activeSubscriptions.map((subscription: any) => (
            <SubscriberPlanTile
              data={subscription}
              customer={customer}
              key={`plan-tile-${subscription.id}`}
              repopulate={repopulate}
              selectedTab={(tab: string) => setMainSelected(tab)}
              isFullSize={(val: boolean) => setIsFullSize(val)}
            />
          ))}
          {data.tempSubscriptions.map((subscription: any) => (
            <SubscriberPlanTile
              data={subscription}
              customer={customer}
              key={`plan-tile-${subscription.id}`}
              repopulate={repopulate}
              isFullSize={(val: boolean) => setIsFullSize(val)}
            />
          ))}
        </div>
      )}
      {isFullSize && mainSelected === "activityLog" && <ActivityLogTable />}
      {isFullSize && mainSelected === "configurations" && (
        <ConfigurationsSection />
      )}
      {isFullSize && mainSelected === "port-in" && <SubscriberPortInSection />}
      {!isFullSize && !data.activeSubscriptions.length && (
        <div className={styles.noneFound}>
          <img src="/none_found.svg" />
          <h3>No {subSelection.replaceAll("-", "")} plans</h3>
        </div>
      )}
    </>
  );
};

export default NationalSubscriptionsTab;
