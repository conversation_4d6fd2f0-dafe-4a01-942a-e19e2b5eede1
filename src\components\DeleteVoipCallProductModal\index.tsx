import Dialog from "@/components/Dialog";
import { WarningCircle } from "@/components/svgs";
import styles from "./delete-voip-call-product-modal.module.scss";
import StatusPill from "@/components/StatusPill";
import CountryDisplay from "@/components/CountryDisplay";

type DeleteVoipCallProductModalProps = {
  onClose: () => void;
  open: boolean;
  productData: any;
};

const DeleteVoipCallProductModal = ({
  onClose,
  open,
  productData,
}: DeleteVoipCallProductModalProps) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      size="sm"
      headerIcon={<WarningCircle />}
      headerTitle="Delete Product?"
      headerSubtitle="Once removed, it cannot be recovered"
      confirmButtonText="Yes, Delete Product"
      confirmButtonOnClick={onClose}
      confirmButtonVariant="destructive"
      cancelButtonText="Cancel"
    >
      <div className={styles.tableContainer}>
        <table className={styles.table}>
          <tbody>
            <tr>
              <td className={styles.label}>Country</td>
              <td className={styles.value}>
                <CountryDisplay
                  country={productData.country.code}
                  width={16}
                  height={16}
                />
              </td>
            </tr>
            <tr>
              <td className={styles.label}>Validity</td>
              <td className={styles.value}>{productData.validity}</td>
            </tr>
            <tr>
              <td className={styles.label}>Call Allowance</td>
              <td className={styles.value}>{productData.callAllowance} Mins</td>
            </tr>
            <tr>
              <td className={styles.label}>USD Price</td>
              <td className={styles.value}>{productData.usdPrice}</td>
            </tr>
            <tr>
              <td className={styles.label}>GBP Price</td>
              <td className={styles.value}>{productData.gbpPrice}</td>
            </tr>
            <tr>
              <td className={styles.label}>EUR Price</td>
              <td className={styles.value}>{productData.eurPrice}</td>
            </tr>
            <tr>
              <td className={styles.label}>Status</td>
              <td className={styles.value}>
                <StatusPill
                  status={productData.status ? "Active" : "Inactive"}
                  color={productData.status ? "active" : "inactive"}
                />
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </Dialog>
  );
};

export default DeleteVoipCallProductModal;
