import styles from "./a-z-order.module.scss";
import { ControlledMenu, MenuItem, useMenuState } from "@szhsin/react-menu";
import "@szhsin/react-menu/dist/index.css";
import "@szhsin/react-menu/dist/transitions/slide.css";
import { AToZ, ChevronDown, ZToA } from "../svgs";
import { useRef } from "react";
import Radio from "../Radio";

const AToZOrder = ({ label, order, setOrder }: any) => {
  const ref = useRef(null);

  const [menuProps, toggleMenu] = useMenuState({ transition: true });

  return (
    <div className={`${styles.box} multi-select select`}>
      <div
        ref={ref}
        className={`${styles.menuButton} ${
          menuProps.state === "open" || menuProps.state === "opening"
            ? styles.iconOpen
            : styles.iconClosed
        }`}
        onClick={(e: any) => {
          e.stopPropagation();
          if (menuProps.state === "closing") {
            toggleMenu(false);
          } else {
            toggleMenu(true);
          }
        }}
      >
        {label}
        <ChevronDown />
      </div>
      <ControlledMenu
        {...menuProps}
        anchorRef={ref}
        onClose={() => toggleMenu(false)}
        align="start"
        viewScroll="initial"
        position="initial"
        onItemClick={(e) => (e.keepOpen = true)}
      >
        <MenuItem
          className={`${styles.menuItem} ${order === "a-z" && styles.selected}`}
        >
          <Radio
            checked={order === "a-z"}
            onClick={() => {
              setOrder("a-z");
            }}
          />
          <span className={styles.itemLabel} style={{ marginLeft: 11 }}>
            <AToZ />
            Sort A to Z
          </span>
        </MenuItem>
        <MenuItem
          className={`${styles.menuItem} ${order === "z-a" && styles.selected}`}
        >
          <Radio
            checked={order === "z-a"}
            onClick={() => {
              setOrder("z-a");
            }}
          />
          <span className={styles.itemLabel} style={{ marginLeft: 11 }}>
            <ZToA />
            Sort Z to A
          </span>
        </MenuItem>
      </ControlledMenu>
    </div>
  );
};

export default AToZOrder;
