@use "../../styles/theme.scss" as *;

.container {
  position: relative;
  display: flex;
  height: 50px;
  overflow: hidden;
}

.label {
  font-size: 14px;
  line-height: 16px;
  margin-top: 15px;
  margin-bottom: 8px;
  color: #061632;
}

.role {
  width: 80px;
  height: 45px;
  display: flex;
  margin-right: 10px;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: $black;
  border-radius: 15px;
  background-color: #f1f6fd;
  position: relative;
  z-index: 1000;
  cursor: pointer;
  &.active {
    background-color: #2e70e5;
    color: #f1f2f4;
  }
}
