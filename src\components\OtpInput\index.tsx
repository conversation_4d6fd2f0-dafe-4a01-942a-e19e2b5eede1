import { useEffect, useRef, useState } from "react";
import styles from "./otp-input.module.scss";

const OtpInput = ({ handleSuccess, codeLength = 5, value, setValue }: any) => {
  const values = value.split("");

  const input = useRef(null) as any;

  const handleClick = () => {
    input.current!.focus();
  };

  const handleBlur = () => {
    input.current!.blur();
  };

  const handleChange = (e: any) => {
    const inputValue = e.target.value;

    setValue((prev: any) => {
      if (prev.length >= codeLength) {
        return prev;
      }
      return (prev + inputValue).slice(0, codeLength);
    });
  };

  useEffect(() => {
    if (value.length === codeLength) {
      handleBlur();
      handleSuccess(value);
    }
  }, [value]);

  const getInputGridArea = () => {
    if (value.length >= codeLength - 1) {
      return `1 / ${codeLength} / 2 / ${codeLength + 1}`;
    } else {
      return `1 / ${value.length + 1} / 2 / ${value.length + 2}`;
    }
  };

  const handleKeyUp = (e: any) => {
    if (e.key === "Backspace") {
      setValue((prev: any) => {
        return prev.slice(0, prev.length - 1);
      });
    }
  };

  return (
    <div
      className={styles.container}
      style={{ gridTemplateColumns: `repeat(${codeLength}, 1fr)` }}
    >
      <input
        value=""
        ref={input}
        className={styles.input}
        onChange={handleChange}
        style={{
          gridArea: getInputGridArea(),
          textAlign: value.length === codeLength ? "end" : "center",
        }}
        onKeyUp={handleKeyUp}
      />
      {Array(codeLength)
        .fill(0)
        .map((x, index) => {
          return (
            <div
              onClick={handleClick}
              className={styles.numberDisplay}
              style={{ gridArea: `1 / ${index + 1} / 2 / ${index + 2}` }}
              key={`otp-input-box-${index}`}
            >
              {values[index]}
            </div>
          );
        })}
    </div>
  );
};

export default OtpInput;
