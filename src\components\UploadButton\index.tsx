/*
==========================================================================================

                                        Input

Description: Input component for use accross the whole app
             with label, input and inline errors

Parameters: label (str) - label for the input
            placeholder (str) - placeholder for the input
            value (str) - input value state
            onChange (func) - function to handle input change
            error (str) - inline error for input
            onKeyDown (func) - function to call when user 
                               presses enter while focus on input
            password (bool) - whether input is a password field
            forgotPassword (bool) - whether to show forgot password link, 
                                    only used for login page
            clear (func) - function to call when clear button is pressed
            disabled (bool) - whether input is disabled
            tooltip (string) - string to display on tooltip

==========================================================================================
*/

import { useState } from "react";
import styles from "./upload-button.module.scss";
import { Fade, Collapse } from "@mui/material";
import Button from "../Button";
import { Export } from "../svgs";

export const UploadButton = ({ label, value }: any) => {
  const [selectedFile, setSelectedFile] = useState(null as any);
  return (
    <Button>
      <div className={styles.button_wrapper}>
        <Export />
        {label}
        <input
          className={styles.input}
          type="file"
          onChange={(event: any) => {
            const file = event.target.files[0];
            setSelectedFile(file);
          }}
        />
        {/* <span className="file-name">
          {selectedFile ? selectedFile?.name : `${label}`}
        </span> */}
      </div>
    </Button>
  );
};
