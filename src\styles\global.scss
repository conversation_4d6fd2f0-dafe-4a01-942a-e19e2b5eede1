@use "./theme.scss" as *;
@import url("https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100..900;1,100..900&display=swap");

* {
  box-sizing: border-box;
  font-family:
    Roboto,
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Oxygen,
    Ubuntu,
    Cantarell,
    "Open Sans",
    "Helvetica Neue",
    sans-serif;
  margin: 0;
  padding: 0;
}

body,
html {
  padding: 0;
  margin: 0;
  // min-width: 1050px;
  max-width: 100vw;
  overflow-x: hidden;
}

.tooltip-highlight {
  .highlight-search-result {
    color: $placeholder;
    font-weight: 700;
  }
}

.flex {
  display: flex;
  margin-top: 10px;
}

.align-items-center {
  align-items: center;
}

.align-items-basline {
  align-items: baseline;
}

.flex-justify-content-between {
  justify-content: space-between;
}

.highlight-search-result {
  color: var(--primary-500);
  font-weight: 600;
}

.slide-top-enter {
  opacity: 0;
  transform: translateY(-100px);
}
.slide-top-exit {
  opacity: 1;
  transform: translateY(0px);
}
.slide-top-enter-active {
  opacity: 1;
  transform: translateY(0px);
}
.slide-top-exit-active {
  opacity: 0;
  transform: translateY(-100px);
}
.slide-top-enter-active,
.slide-top-exit-active {
  transition: all 300ms ease;
}

.fade-enter {
  opacity: 0;
}
.fade-exit {
  opacity: 1;
}
.fade-enter-active {
  opacity: 1;
}
.fade-exit-active {
  opacity: 0;
}
.fade-enter-active,
.fade-exit-active {
  transition: opacity 100ms;
}

.edit-menu .szh-menu-container,
.edit-menu .szh-menu {
  z-index: 9999;
}

.szh-menu {
  border-radius: 12px !important;
  box-shadow: 0px 6px 25px rgba(0, 0, 0, 0.15) !important;
  z-index: 9999 !important;
}

.menu .szh-menu {
  box-shadow: 0px 6px 25px rgba(0, 0, 0, 0.15) !important;
  border-radius: 12px !important;
  background: #fff !important;
  padding: 10px !important;
  display: flex;
  flex-direction: column;
  min-width: 202px !important;
}

.filter-cell .date-select .szh-menu {
  font-weight: initial !important;
  padding: 0px !important;
  box-shadow: 0px 6px 25px rgba(0, 0, 0, 0.15) !important;
  border-radius: 12px !important;
}

.single-date-select .szh-menu {
  padding: 0 !important;
  box-shadow: 0px 6px 25px rgba(0, 0, 0, 0.15) !important;
  border-radius: 12px !important;
  background: #fff !important;
}

.select .szh-menu {
  box-shadow: 0px 6px 25px rgba(0, 0, 0, 0.15) !important;
  border-radius: 12px !important;
  background: #fff !important;
  padding: 24px !important;
  display: flex;
  flex-direction: column;
  min-width: initial !important;
}

.multi-select .szh-menu {
  padding: 24px !important;
  //top: 10px !important;
}

// .normal-select-input .szh-menu-container {
//   width: 350px !important;
// }

.settingsUserMenu .szh-menu-container ul {
  left: -185px !important;
  top: -130px !important;
}

.ticketsMenu .szh-menu-container ul {
  left: -85px !important;
  top: -110px !important;
}

// .normal-select-input .szh-menu {
//   box-shadow: 0px 10px 20px rgba(46, 112, 229, 0.1) !important;
//   border-radius: 0 0 8px 8px !important;
//   background: #fff !important;
//   padding: 22px 16px !important;
//   display: flex;
//   flex-direction: column;
//   width: 100%;
// }

// .normal-select-input.select-upwards .szh-menu {
//   border-radius: 8px 8px 0 0 !important;
// }

.__react_component_tooltip {
  border-radius: 4px !important;
  min-width: 146px;
  opacity: 0.3 !important;
  div {
    text-align: center;
  }
}

.notification-wrapper {
  position: fixed;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 10px;
  z-index: 999999;
  padding: 63px 25px;
  pointer-events: none;
  align-items: center;
}

.pagination {
  .Mui-disabled.MuiPaginationItem-previousNext {
    opacity: 0 !important;
  }
  .MuiPaginationItem-root {
    font-family: Poppins !important;
    margin: 0 4px !important;
    @media (max-width: 335px) {
      margin: 0 2px !important;
    }
    &.MuiButtonBase-root:hover {
      background: #bdbdbd;
    }
  }
  .Mui-selected.MuiPaginationItem-root {
    color: #fff !important;
    background: #000 !important;
    .MuiTouchRipple-root {
      display: none;
    }
  }
}

.modal-scroll {
  overflow-y: auto;
}

.modal-scroll::-webkit-scrollbar {
  width: 8px;
}

/* Track */
.modal-scroll::-webkit-scrollbar-track {
  background: none;
  border-radius: 9999px;
  background: var(--primary-100);
}

/* Handle */
.modal-scroll::-webkit-scrollbar-thumb {
  background-clip: padding-box;
  border-radius: 9999px;
  background-color: var(--primary-500);
  transition: background-color 0.2s ease;
}

/* Handle on hover */
.table-scroll::-webkit-scrollbar-thumb:hover {
  background-clip: padding-box;
  border-radius: 9999px;
  background-color: var(--primary-800);
}

.table-scroll::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

/* Track */
.table-scroll::-webkit-scrollbar-track {
  background: var(--primary-100);
  border-radius: 9999px;
}

/* Handle */
.table-scroll::-webkit-scrollbar-thumb {
  background-clip: padding-box;
  border-radius: 9999px;
  background-color: var(--primary-500);
  transition: background-color 0.2s ease;
}

/* Handle on hover */
.table-scroll::-webkit-scrollbar-thumb:hover {
  background-clip: padding-box;
  border-radius: 9999px;
  background-color: var(--primary-800);
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
textarea:-webkit-autofill,
textarea:-webkit-autofill:hover,
textarea:-webkit-autofill:focus,
select:-webkit-autofill,
select:-webkit-autofill:hover,
select:-webkit-autofill:focus {
  -webkit-box-shadow: 0 0 0px 1000px #ffffff inset !important;
}

/* Handle on hover */
.container-scroll::-webkit-scrollbar-thumb:hover {
  background-clip: padding-box;
  border-radius: 9999px;
  background-color: var(--primary-800);
}

.container-scroll::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

/* Track */
.container-scroll::-webkit-scrollbar-track {
  background: var(--primary-100);
  border-radius: 9999px;
}

/* Handle */
.container-scroll::-webkit-scrollbar-thumb {
  background-clip: padding-box;
  border-radius: 9999px;
  background-color: var(--primary-500);
  transition: background-color 0.2s ease;
}

/* Handle on hover */
.container-scroll::-webkit-scrollbar-thumb:hover {
  background-clip: padding-box;
  border-radius: 9999px;
  background-color: var(--primary-800);
}

// Drag & Drop

.MuiDropzoneArea-root {
  width: 100%;
  border: dashed !important;
  border-width: 1px !important;
  cursor: pointer;
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  border-color: $admin !important ;
  border-radius: 8px;
  background-color: #fff;
  min-height: 0px !important;
  // margin: 12px 0 12px 0;
}

.MuiDropzoneArea-textContainer {
  height: 56px !important;
  text-align: center;
  display: flex;
  flex-direction: row-reverse;
  align-items: center;
  justify-content: center;
  gap: 8px !important;
}
.MuiDropzoneArea-text {
  margin-top: 0px !important;
  margin-bottom: 0px !important;
  font-size: 14px !important;
  font-weight: 400 !important;
}

.MuiDropzoneArea-icon {
  width: 24px !important;
  height: 24px !important;
}
