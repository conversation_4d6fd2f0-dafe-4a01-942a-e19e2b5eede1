import styles from "./product-menu.module.scss";
import { ControlledMenu, MenuDirection, MenuItem, useMenuState } from "@szhsin/react-menu";
import "@szhsin/react-menu/dist/index.css";
import "@szhsin/react-menu/dist/transitions/slide.css";
import { useRef } from "react";

type ProductsMenuProps = {
  data: any;
  direction?: MenuDirection;
  portal?: boolean;
}

const ProductsMenu = ({ data, direction, portal = false }: ProductsMenuProps) => {
  const ref = useRef(null);

  const [menuProps, toggleMenu] = useMenuState({ transition: true });

  return (
    <div>
      <div
        ref={ref}
        className={styles.menuButton + ' ' + (data.label && styles.withLabel)}
        onClick={(e) => {
          e.stopPropagation();
          toggleMenu(true)
        }}
      >
        {data.icon} {data.label}
      </div>
      <ControlledMenu
        {...menuProps}
        anchorRef={ref}
        onClose={() => toggleMenu(false)}
        className={styles.menu}
        direction={direction}
        portal={portal}
      >
        {data.items.map((item: any, index: number) => (
          <MenuItem onClick={item.onClick} className={styles.menuItem}>
            {item.icon}
            {item.label}
          </MenuItem>
        ))}
      </ControlledMenu>
    </div>
  );
};

export default ProductsMenu;
