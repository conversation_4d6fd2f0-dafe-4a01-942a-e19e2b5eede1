import styles from "./guides-tab.module.scss";
import { PlayFilled } from "@/components/svgs";

interface VideoMockupProps {
  title: string;
}

const VideoMockup = ({ title }: VideoMockupProps) => (
  <div className={styles.videoMockup}>
    <div className={styles.videoTop}>
      <PlayFilled />
    </div>
    <div className={styles.videoBottom}>
      <span>{title}</span>
    </div>
  </div>
);

const GuidesTab = () => {
  const guides = [
    "How to activate a subscriber",
    "How to delete a subscriber",
    "How to suspend a subscription",
    "How to create a new user",
    "How to change a user's role",
    "All about reports",
  ];

  return (
    <div className={styles.panel}>
      <h2>Guides</h2>
      <div className={styles.grid}>
        {guides.map((title, index) => (
          <VideoMockup key={index} title={title} />
        ))}
      </div>
    </div>
  );
};

export default GuidesTab;
