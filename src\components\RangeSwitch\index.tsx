import { SwitchTransition, CSSTransition } from "react-transition-group";
import styles from "./range-switch.module.scss";

const RangeSwitch = ({ small, role, setRole, custom }: any) => {
  return (
    <div>
      <div
        className={`${styles.container} ${small && styles.small} ${custom && styles.custom}`}
      >
        <div
          onClick={() => {
            setRole("specific");
          }}
          className={styles.role}
        >
          Specific
        </div>
        <div
          onClick={() => {
            setRole("range");
          }}
          className={styles.role}
        >
          {custom && "Custom "}Range
        </div>
        <div className={`${styles.thumb} ${role === "range" && styles.right}`}>
          <SwitchTransition>
            <CSSTransition
              key={role}
              addEndListener={(node, done) =>
                node.addEventListener("transitionend", done, false)
              }
              classNames="fade"
            >
              <div>
                {role === "specific" ? (
                  <>Specific</>
                ) : (
                  <>{custom && "Custom "}Range</>
                )}
              </div>
            </CSSTransition>
          </SwitchTransition>
        </div>
      </div>
    </div>
  );
};

export default RangeSwitch;
