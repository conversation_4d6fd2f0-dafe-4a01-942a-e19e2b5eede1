@use "./theme.scss" as *;

.main {
  width: 100%;
  background: #f1f1f1;
  padding: 24px;
}

.top {
  display: flex;
  background: #fff;
  margin-left: -24px;
  padding: 0 24px 0 48px;
  height: 80px;
  align-items: center;
  justify-content: space-between;
  transition: all 0.5s ease;
  h2 {
    margin: 0;
    font-weight: 700;
    font-size: 20px;
    line-height: 30px;
  }
}

.mainTile {
  width: 100%;
  background: #fff;
  border-radius: 24px;
  padding: 128px 0 83px 0;
  display: flex;
  align-items: center;
}

.illustration {
  border-right: 1px solid #000;
  margin-right: 60px;
}

.userDetails {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.nameContainer {
  display: flex;
  align-items: center;
  margin: 16px 0 12px 0;
  .name {
    font-weight: 600;
    font-size: 20px;
    line-height: 30px;
    margin-right: 12px;
  }
  .editButton {
    padding: 0;
    border: none;
    background: none;
    svg {
      vertical-align: middle;
    }
    cursor: pointer;
    &:hover {
      color: $orange;
    }
  }
}

.email {
  margin-bottom: 24px;
  font-size: 14px;
  line-height: 21px;
}
