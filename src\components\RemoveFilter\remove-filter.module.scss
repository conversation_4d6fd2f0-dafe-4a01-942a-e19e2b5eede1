@use "../../styles/theme.scss" as *;

.main {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 0 12px 0 12px;
  margin: 0 12px 12px 0;
  height: 32px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  line-height: 21px;
  color: $black;
  background: #fff;
  &.grey {
    background: #f7f6f6;
  }
}
.flag {
  background-size: cover;
  background-position: center;
  width: 20px;
  height: 20px;
  border-radius: 1000px;
  margin-right: 4px;
}

.remove {
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.1s ease;
}

.active {
  color: $off-white;
  background: $active;
}

.inactive {
  color: $black;
  background: $inactive;
}

.open {
  color: $black;
  background: $open;
}

.pending {
  color: $black;
  background: $pending;
}

.resolved {
  color: $off-white;
  background: $resolved;
}

.closed {
  color: $off-white;
  background: $closed;
}
