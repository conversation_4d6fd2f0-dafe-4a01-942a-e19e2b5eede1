import { useNavigate, useSearchParams } from "react-router-dom";
import FirstPasswordChange from "../../components/FirstPasswordChange";
import styles from "../../styles/login.module.scss";
import { useEffect } from "react";

const NewLogin = () => {
  return (
    <div className={styles.container}>
      <div className={styles.main}>
        <div className={styles.logos}>
          <img src="/Logo.png" className={styles.logo} />
        </div>
        <div className={styles.formContainer}>
          <FirstPasswordChange />
        </div>
      </div>
      <img src="/Login_Graphic.svg" className={styles.graphic} />
    </div>
  );
};

export default NewLogin;
