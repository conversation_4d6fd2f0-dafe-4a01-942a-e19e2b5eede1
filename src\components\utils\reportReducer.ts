import {
  ReportSections,
  ReportTypes,
  SectionedReport,
  Range,
} from "@/types/report";
import { emptyFilters } from "./reportFields";
import { filterList } from "./searchAndFilter";
import { formatReportsDate } from "./formatDate";

export type Action = {
  type: string;
  payload?: any;
};

export type State = {
  is_loading: boolean;
  report: ReportTypes;
  selection: ReportSections;
  sectionedReports: SectionedReport;
  filteredReports: SectionedReport;
  filters: any; // TODO: fix filer types
  chartTimeRange: Range;
  currentPage: number;
  reportsPerPage: number;
};

export const initialReportState: State = {
  is_loading: false,
  report: {
    Voice: [],
    SMS: [],
    Numbers: [],
    Finance: [],
    Charts: [],
  },
  selection: "Charts", // Default selection is chart
  sectionedReports: [],
  filteredReports: [],
  filters: emptyFilters,
  chartTimeRange: {
    start: formatReportsDate(
      new Date(new Date().setDate(new Date().getDate() - 30))
    ),
    end: formatReportsDate(
      new Date(new Date().setDate(new Date().getDate() - 1))
    ),
    label: "last month",
  },
  currentPage: 1,
  reportsPerPage: 15,
};

export enum reportDispatchActions {
  IS_LOADING = "IS_LOADING",
  SET_SELECTION = "SET_SELECTION",
  SET_CHART_RANGE = "SET_CHART_RANGE",
  SET_FILTER = "SET_FILTER",
  RESET_FILTER = "RESET_FILTER",
  SET_REPORT_PER_PAGE = "SET_REPORT_PER_PAGE",
  SET_CURRENT_PAGE = "SET_CURRENT_PAGE",
  SET_REPORT = "SET_REPORT",
}

// TODO: fix state type
function reportReducer(state: State, action: Action): State {
  if (action.type === reportDispatchActions.IS_LOADING) {
    return { ...state, is_loading: action?.payload || false };
  } else if (action.type === reportDispatchActions.SET_SELECTION) {
    const newSelection = action.payload as ReportSections;

    return {
      ...state,
      selection: newSelection,
      chartTimeRange: {
        start: formatReportsDate(
          new Date(new Date().setDate(new Date().getDate() - 30))
        ),
        end: formatReportsDate(
          new Date(new Date().setDate(new Date().getDate() - 1))
        ),
        label: "last month",
      },
      currentPage: 1,
    };
  } else if (action.type === reportDispatchActions.SET_CHART_RANGE) {
    return {
      ...state,
      chartTimeRange: action.payload as Range,
    };
  } else if (action.type === reportDispatchActions.SET_FILTER) {
    const filteredReports = filterList(state.sectionedReports, action.payload);
    return {
      ...state,
      filters: action.payload,
      filteredReports: filteredReports,
    };
  } else if (action.type === reportDispatchActions.RESET_FILTER) {
    return {
      ...state,
      filters: emptyFilters,
    };
  } else if (action.type === reportDispatchActions.SET_REPORT_PER_PAGE) {
    return {
      ...state,
      reportsPerPage: action.payload,
    };
  } else if (action.type === reportDispatchActions.SET_CURRENT_PAGE) {
    return {
      ...state,
      currentPage: action.payload,
    };
  } else if (action.type === reportDispatchActions.SET_REPORT) {
    const key = Object.keys(action.payload)[0];

    return {
      ...state,
      report: { ...state.report, ...action.payload },
      is_loading: false,
      filters: emptyFilters,
      sectionedReports: action.payload[key],
      filteredReports: action.payload[key],
    };
  } else {
    return state;
  }
}

export default reportReducer;
