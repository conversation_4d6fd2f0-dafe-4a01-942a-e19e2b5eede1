import Dialog from "@/components/Dialog";
import { PersonCircle } from "@/components/svgs";
import styles from "./add-traveller-subscriber-modal.module.scss";
import { useState } from "react";
import { clearInput, createStateObject, handleInputChange, labels } from "@/components/utils/InputHandlers";
import { Input } from "@/components/Input";
import SelectInput from "@/components/SelectInput";
import AddTravellerCustomerProductModal from "../AddTravellerCustomerProductModal";
import Button from "../Button";
import PlanCard from "../PlanCard";
import { getCountryOptions } from "../utils/getCountryOptions";

type AddTravellerSubscriberModalProps = {
  open: boolean;
  onClose: () => void;
}

const AddTravellerSubscriberModal = ({
  open,
  onClose,
}: AddTravellerSubscriberModalProps) => {
  const fields = ["firstName", "lastName", "email", "phoneNumber", "country"];
  const [formData, setFormData] = useState(createStateObject(fields));

  const [step, setStep] = useState<"addSubscriber" | "addProduct">("addSubscriber")
  const [hasAddedProduct, setHasAddedProduct] = useState(false)
  return (
    <>
      {step === "addSubscriber" && (
        <AddSubscriberModal
          formData={formData}
          setFormData={setFormData}
          open={open && step === "addSubscriber"}
          onAddProduct={() => setStep("addProduct")}
          hasAddedProduct={hasAddedProduct}
          onRemoveProduct={() => setHasAddedProduct(false)}
          onEditProduct={() => {
            setHasAddedProduct(false);
            setStep("addProduct")
          }}
          onContinue={onClose}
          onClose={onClose}
        />
      )}
      {step === "addProduct" && (
        <AddTravellerCustomerProductModal
          onClose={() => setStep("addSubscriber")}
          onContinue={() => {
            setHasAddedProduct(true)
            setStep("addSubscriber")
          }}
          open={open && step === "addProduct"}
        />
      )}
    </>
  )
};

export default AddTravellerSubscriberModal;

type AddSubscriberModalProps = {
  open: boolean;
  hasAddedProduct: boolean;
  onAddProduct: () => void;
  onRemoveProduct: () => void;
  onEditProduct: () => void;
  onContinue: () => void;
  onClose: () => void;
  formData: any;
  setFormData: (values: any) => void;
}

const AddSubscriberModal = ({
  open,
  onClose,
  onContinue,
  hasAddedProduct,
  onAddProduct,
  onEditProduct,
  onRemoveProduct,
  formData,
  setFormData
}: AddSubscriberModalProps) => {


  return (
    <Dialog
      open={open}
      onClose={onClose}
      size="sm"
      headerTitle="Add Subscriber"
      headerIcon={<PersonCircle />}
      confirmButtonText="Add Subscriber"
      confirmButtonOnClick={onContinue}
      cancelButtonText="Cancel"
      cancelButtonOnClick={onClose}
    >
      <div className={styles.formContainer}>
        {addSubscriberFields.map(field => {
          if (field === "country") {
            return (
              <SelectInput
                key={"add-subscriber-" + field}
                options={getCountryOptions()}
                selected={formData[field]}
                label={labels[field]}
                added={formData[field]}
                onChange={(e: any) => {
                  handleInputChange(field, { target: { value: e } }, formData, setFormData);
                }}
                error={formData.errors[field]}
                infoTooltipText
              />
            )
          }

          return (
            <Input
              key={"add-subscriber-" + field}
              label={labels[field]}
              value={formData[field]}
              onChange={(e: any) => {
                handleInputChange(field, e, formData, setFormData);
              }}
              error={formData.errors[field]}
              clear={() => {
                clearInput(field, setFormData);
              }}
              infoTooltipText
            />
          )
        })}

        {hasAddedProduct ?
          (<div className={styles.addedProductContainer}>
            <PlanCard
              standaloneMode
              onEdit={onEditProduct}
              onRemove={onRemoveProduct}
            />
          </div>) :
          (<div className={styles.addProductButtonContainer}>
            <Button
              color="customerActionBlue"
              style={{ width: '100%', justifyContent: "center" }}
              onClick={onAddProduct}
            >
              Add Products to Accounts
            </Button>
          </div>)
        }
      </div>
    </Dialog>
  );
}

const addSubscriberFields = ["firstName", "lastName", "email", "phoneNumber", "country"];
