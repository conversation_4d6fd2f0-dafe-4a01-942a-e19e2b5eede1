@use "../../styles/theme.scss" as *;

.main {
  margin: auto;
  width: 100%;
  max-width: 350px;
  display: flex;
  flex-direction: column;
}

.plansMain {
  width: 100%;
  display: flex;
  flex-direction: column;
}

.plans {
  margin-right: 12px;
}

.searchSection {
  position: absolute;
  display: flex;
  align-items: center;
  top: 32px;
  left: 42px;
  width: calc(100% - 130px);
  h3 {
    white-space: nowrap;
    margin: 0 24px 0 0;
    font-weight: 700;
    font-size: 24px;
    line-height: 36px;
  }
}

.planAdded {
  display: flex;
  svg {
    margin: 0 0 0 8px;
  }
}

.features {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-column-gap: 26px;
  grid-row-gap: 24px;
  margin-right: 12px;
}

.inputTitle {
  color: $placeholder;
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  margin-bottom: 12px;
}

.porting {
  display: flex;
  align-items: center;
  justify-content: center;
}

.portingLabel {
  margin-right: 15px;
  color: $placeholder;
}

.portingSwitch {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 50px;
  width: 147px;
  border-radius: 1000px;
  position: relative;
  user-select: none;
  overflow: hidden;
  transition: all 0.3s ease;
  .yesNo {
    position: relative;
    z-index: 1000;
    font-weight: 600;
    transition: color 0.3s ease;
  }
  .thumb {
    position: absolute;
    height: 100%;
    border-radius: 1000px;
    width: 77px;
    transition: all 0.3s ease;
    z-index: 900;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.15);
  }
}

.notEligible {
  border: 2px solid $urgent;
  margin-bottom: 24px;
  color: $urgent;
  .topText {
    margin-bottom: 6px;
    color: $placeholder;
  }
  .bottomText {
    color: $placeholder;
    font-size: 12px;
    line-height: 18px;
  }
  svg {
    width: 32px;
    height: 32px;
  }
}

.eligible {
  border: 2px solid $success;
  color: $success;
  margin-bottom: 12px;
  align-items: center;
}

.eligible,
.notEligible {
  padding: 12px 16px;
  border-radius: 8px;
  display: grid;
  grid-template-columns: 32px 1fr;
  grid-column-gap: 12px;
}
