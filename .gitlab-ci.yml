image: node:lts

stages:
  - build
  - deploy-dev

# Define variables for environment
variables:
  OUTPUT_DIR: "dist"
  TITLE_ENTRY: "VITE_APP_WEBSITE_NAME=HERO CRM"
  HOST_DEV: "hero-crm.mobilisedev.co.uk"
  BRANCH_DEV: "dev"
  HOST_STAGE: "hero-crm.mobilisedev.co.uk"
  BRANCH_STAGE: "stage"
  HOST_PROD: "hero-crm.mobilisedev.co.uk"
  BRANCH_PROD: "main"
  FF_USE_FASTZIP: "true"
  ARTIFACT_COMPRESSION_LEVEL: default
  CACHE_COMPRESSION_LEVEL: default

Build:
  stage: build

  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      when: never
    - if: $CI_COMMIT_REF_NAME == $BRANCH_DEV
      variables:
        HOST_ENTRY: "VITE_APP_HOST_NAME=${HOST_DEV}"
        COM_CODE_ENTRY: "VITE_APP_COM_CODE=Web"
    - if: $CI_COMMIT_REF_NAME == $BRANCH_STAGE
      variables:
        HOST_ENTRY: "VITE_APP_HOST_NAME=${HOST_STAGE}"
        COM_CODE_ENTRY: "VITE_APP_COM_CODE=Web"
    - if: $CI_COMMIT_REF_NAME == $BRANCH_PROD
      variables:
        HOST_ENTRY: "VITE_APP_HOST_NAME=${HOST_PROD}"
        COM_CODE_ENTRY: "VITE_APP_COM_CODE=Web"

  script:
    - echo "Installing dependencies"
    - npm ci --cache .npm --prefer-offline
    - echo "Preparing environment files and config"
    - rm -rf .env
    - sed -i "s/{FINAL_DIR}/${OUTPUT_DIR}/g" vite.config.js
    - echo -e "${TITLE_ENTRY}\n${HOST_ENTRY}\n${COM_CODE_ENTRY}" > .env
    - echo "Running build process"
    - npm run build

  artifacts:
    paths:
      - dist/

Deployment for Development:
  stage: deploy-dev
  image: ubuntu:latest

  dependencies:
    - Build

  before_script:
    - apt-get update
    - apt-get install -y openssh-client rsync
    - eval $(ssh-agent -s)
    - echo "$GROUP_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh && chmod 700 ~/.ssh
    - ssh-keyscan hero-crm.mobilisedev.co.uk >> ~/.ssh/known_hosts

  only:
    - dev

  script:
    - echo "Ensuring the target directory exists on the server"
    - ssh gitlab-runner@${HOST_DEV} "mkdir -p /var/www/hero-crm"
    - echo "Deploying to DEV environment on HERO CRM server"
    - rsync -rtvpl --info=name0 --info=progress2 dist/* gitlab-runner@${HOST_DEV}:/var/www/hero-crm/
    - echo "Deployment complete"

  artifacts:
    paths:
      - dist/
