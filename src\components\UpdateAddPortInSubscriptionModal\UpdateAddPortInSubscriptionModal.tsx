import styles from "../AddSubscriberModal/AddSubscriberModal.module.scss";
import { useEffect, useState } from "react";
import { createStateObject } from "../utils/InputHandlers";
import { Plus, CheckCircle, XCircle, PersonCircle, PencilCircle, PlusCircle } from "../svgs";
import { AddressStep, InputFieldsStep, ImeiField, ChoicesStep, PlansStep } from "../ModalsSteps/ModalsSteps";
import Button from "../Button";
import { Collapse } from "@mui/material";
import Dialog from "../Dialog";

const portInFields = ["portInNumber", "zipCode"];
const detailsFields = ['imei', 'plan', 'carrier', 'iccid']
const oldNumberFields = ["firstName", "lastName", "billingAccountNumber", "pinPassword"];
const subscriberDetails = ["firstName", "lastName", "email", "phoneNumber"]
const AddressFields = ["streetNumber", "streetDirection", "streetName", "state", "city", "zipCode"];
const UpdateAddPortInSubscriptionModal  = ({show, close, title}: { show: boolean, close: Function, title: string }) => {
    const [data, setData] = useState(createStateObject([...portInFields, ...oldNumberFields, ...subscriberDetails, ...detailsFields]));
    const [step, setStep] = useState(1)
    const [numberIneligible, setNumberIneligible] = useState(false);
    const [numberVerified, setNumberVerified] = useState(false);
    const [imeiIneligible, setImeiIneligible] = useState(false);
    const [imeiVerified, setImeiVerified] = useState(false);
    const [saveBtnText, setSaveBtnText] = useState('')
    const [newAddress, setNewAddress] = useState(createStateObject(AddressFields))
    const [isNewAddress, setIsNewAddress] = useState(false)

    const handleCloseModal = () => {
        close(false)
        setSaveBtnText('')
        setStep(1)
    }

    useEffect(() => {
        handleButtonsText()
    }, [step, isNewAddress, data.isTether, data.plan])

    const handleButtonsText = () => {
        if (step === 1 || step === 6) {
            setSaveBtnText('Check Eligibility')
        } else if (step > 1 && step < 6) {
            if (isNewAddress) {
                setSaveBtnText('Save New Address')
              } else {
                setSaveBtnText('Continue')
            }
        } else if (step === 7) {
            if (data.isTether && data.plan) {
                setSaveBtnText('Add plan')
            } else {
                setSaveBtnText('')
            }
        } else {
            setSaveBtnText('Add Subscription')
        }
    }

    const handleNextStep = () => {
        if (step === 1 && !data.portInNumber) {
            setNumberIneligible(true)
        } else if (step === 1 && data.portInNumber && numberVerified) {
            setStep(step + 1)
        } else if (step === 1 && data.portInNumber) {
            setNumberIneligible(false)
            setNumberVerified(true)
            setSaveBtnText('Continue')
        } else if (step === 5 && isNewAddress) {
            setIsNewAddress(false)
        }  else if (step === 6 && !data.imei) {
            setImeiIneligible(true)
        } else if (step === 6 && data.imei && imeiVerified) {
            setStep(step + 1)
        } else if (step === 6 && data.imei) {
            setImeiIneligible(false)
            setImeiVerified(true)
            setSaveBtnText('Continue')
        } else if (step < 8) {
            setStep(step + 1)
        } else {
            handleCloseModal()
        }
    }

    return (
        <Dialog
            open={show}
            onClose={() => handleCloseModal()}
            headerTitle={title}
            cancelButtonText={ data.isTether && data.plan && step === 7 ? 'Cancel' : '' }
            headerIcon={step < 4 ? <PersonCircle /> : step === 5 ? <PencilCircle /> : <PlusCircle />}
            confirmButtonText={saveBtnText}
            size="sm"
            confirmButtonOnClick={() => handleNextStep()}>
            <div className={styles.main}>
                {
                    step === 1 && (
                        <div>
                            <h5>Eligibility Check</h5>
                            <InputFieldsStep data={data} setData={(val:any) => setData(val)} fields={portInFields} />
                            <Collapse in={numberIneligible}>
                                <div className={styles.notEligible}>
                                    <XCircle />
                                    <div>
                                        <div className={styles.topText}>
                                            <p>Number is not eligible for port in</p>
                                        </div>
                                        <div className={styles.bottomText}>
                                            <p>The entered phone number has an outstanding balance with their current service provider.</p>
                                        </div>
                                    </div>
                                </div>
                            </Collapse>
                            <Collapse style={{ width: "100%" }} in={numberVerified}>
                                <div className={styles.eligible}>
                                    <CheckCircle />
                                    <div>
                                        <div className={styles.topText}>
                                            <p>Number is eligible for port in</p>
                                        </div>
                                        <div className={styles.bottomText}>
                                            <p>You can continue</p>
                                        </div>
                                    </div>
                                </div>
                            </Collapse>
                        </div>
                    )
                }
                {
                    step === 2 && (
                        <div>
                            <p>Portin Number: 071234567890</p>
                            <h5>Old Carrier Details</h5>
                            <InputFieldsStep data={data} setData={(val:any) => setData(val)} fields={oldNumberFields} />
                        </div>
                    )
                }
                {
                    step === 3 && (
                        <div>
                            <p>Portin Number: 071234567890</p>
                            <h5>Old Carrier Address Details</h5>
                            <AddressStep data={data} setData={(val:any) => setData(val)} />
                        </div>
                    )
                }
                {
                    step === 4 && (
                        <>
                            <h5>Subscriber details</h5>
                            <InputFieldsStep data={data} setData={(val:any) => setData(val)} fields={subscriberDetails} />
                        </>
                    )
                }
                {
                    step === 5 && (
                        <div className={styles.addressStep}>
                        {
                            isNewAddress ? (
                                <AddressStep data={newAddress} setData={(val:any) => setNewAddress(val)} />
                            ) : (
                                <>
                                    <h5>Address</h5>
                                    <Button color="secondary" style={{ width: '100%' }} onClick={() => setIsNewAddress(true)}><Plus /> Add New Address</Button>
                                    <div className={styles.addressBoxes}>
                                        <p className={styles.title}>Default Address</p>
                                        <div className={styles.selected}>
                                            <p className={styles.title}>12, NW, streetName, cityName, State, Zip Code</p>
                                        </div>
                                        <p className={styles.title}>Previous Addresses</p>
                                        <div>
                                            <p className={styles.title}>12, NW, streetName, cityName, State, Zip Code</p>
                                        </div>
                                        <div>
                                            <p className={styles.title}>12, NW, streetName, cityName, State, Zip Code</p>
                                        </div>
                                        <div>
                                            <p className={styles.title}>12, NW, streetName, cityName, State, Zip Code</p>
                                        </div>
                                    </div>
                                </>
                            )
                        }
                    </div>
                    )
                }
                {
                    step === 6 && (
                        <div>
                            <h5>Eligibility Check</h5>
                            <ImeiField data={data} setData={(val:any) => setData(val)} />
                            <Collapse in={imeiIneligible}>
                                <div className={styles.notEligible}>
                                    <XCircle />
                                    <div>
                                        <div className={styles.topText}>
                                            <p>IMEI is not eligible</p>
                                        </div>
                                        <div className={styles.bottomText}>
                                            <p>Double check the number entered or try a different one.</p>
                                        </div>
                                    </div>
                                </div>
                            </Collapse>
                            <Collapse style={{ width: "100%" }} in={imeiVerified}>
                                <div className={styles.eligible}>
                                    <CheckCircle />
                                    <div>
                                        <div className={styles.topText}>
                                            <p>IMEI is eligible</p>
                                        </div>
                                        <div className={styles.bottomText}>
                                            <p>You can continue</p>
                                        </div>
                                    </div>
                                </div>
                            </Collapse>
                        </div>
                    )
                }
                {
                    step === 7 && (
                        <>
                            <ChoicesStep fieldName='isTether' data={data} setData={(val:any) => setData(val)} title="Tether Plans?" choices={["Yes", "No"]} />
                            {
                                ((data.isTether === 'Yes') || (data.isTether === 'No')) && (
                                    <>
                                        <h5>Select a plan</h5>
                                        <PlansStep data={data} setData={(val:any) => setData(val)} />
                                    </>
                                )
                            }
                        </>
                    )
                }
                {
                    step === 8 && (
                        <>
                            <InputFieldsStep data={data} setData={(val:any) => setData(val)} fields={detailsFields} />
                        </>
                    )
                }

            </div>
        </Dialog>
    )
}

export default UpdateAddPortInSubscriptionModal