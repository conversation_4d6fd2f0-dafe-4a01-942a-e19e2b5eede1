import { useState } from "react";
import PlanDataBar from "../PlanDataBar";
import { Signal, Minimize, FullScreen } from "../svgs";
import formatDate, { formatDateWords } from "../utils/formatDate";
import { SwitchTransition, CSSTransition } from "react-transition-group";
import IconButton from "../IconButton";
import { motion } from "framer-motion";
import styles from "../CustomerPlanTile/customer-plan-tile.module.scss";
import sizeStyles from "../CustomerTickets/customer-ticket.module.scss";
import StatusPill from "../StatusPill";
import SubscriberManageMenu from "../SubscriberMangeMenu/SubscriberManageMenu";
import { subscriberStatuses } from "../utils/statusesDetails";
import NationalSubscriberModals from "../NationalSubscriberModals/NationalSubscriberModals";

const SubscriberPlanTile = ({
  data,
  selectedTab,
  cancelled,
  customer,
  isFullSize
}: any) => {
  const [fullSize, setFullSize] = useState(false);
  const [modalName, setModalName] = useState('')
  const [customModalName, setCustomModalName] = useState('')

  const handleChangingTab = (tab:string) => {
    selectedTab(tab)
    isFullSize(true)
  }

  return (
    <motion.div
      layout
      transition={{
        type: "tween",
        ease: "easeInOut",
        duration: 0.3,
      }}
      key={"motion-ticket-" + data.dataAllowance}
      className={`${sizeStyles.main} ${fullSize && sizeStyles.fullSize}`}>
      <NationalSubscriberModals
        modalName={modalName} 
        customModalName={customModalName} 
        setCustomModalName={(name:string) => setCustomModalName(name)}
        setModalName={(name:string) => setModalName(name)}
        data={data} 
      />
      <SwitchTransition>
        <CSSTransition
          key={fullSize ? "large" : "small"}
          addEndListener={(node, done) =>
            node.addEventListener("transitionend", done, false)
          }
          classNames="fade">
        <div>
        {
          fullSize ? (
            <div className={styles.overview}>
              <div className={styles.top}>
                <div
                  className={styles.typeContainer}
                  style={{ display: "flex", justifyContent: "space-between", alignItems: 'center' }}
                >
                  <div className={styles.type} style={{ margin: 0 }}>
                    <StatusPill
                      text={data.subscriberNumberStatus ? subscriberStatuses[data.subscriberNumberStatus.toLowerCase().replaceAll(" ", '')].status : subscriberStatuses.readytoactivite.status}
                      color={data.subscriberNumberStatus ? subscriberStatuses[data.subscriberNumberStatus.toLowerCase().replaceAll(" ", '')].color : subscriberStatuses.readytoactivite.color}
                    />
                  </div>
                  <div className={styles.buttons}>
                    {
                      (data.subscriberNumberStatus !== 'Pending' && data.subscriberNumberStatus !== 'Cancelled' && data.subscriberNumberStatus !== 'BAN Change') && (
                        <SubscriberManageMenu
                          customModal={(name:string) => setCustomModalName(name)}
                          status={data.subscriberNumberStatus}
                          menuLabel="Manage"
                          modalName={(name:string) => setModalName(name)}
                          selectedTab={(tab:string) => handleChangingTab(tab) } />
                      )
                    }
                    <IconButton
                      noOutline
                      onClick={() => {
                        setFullSize(false);
                        isFullSize(false)
                      }}
                    >
                      <Minimize />
                    </IconButton>
                </div>
                </div>
              </div>
              <div className={styles.title}>
                <div className={styles.info}>
                  <div className={styles.titleText}>
                    Subscription title goes here
                  </div>
                  <div className={styles.planData}>
                    <p>MDN: { data.mdn }</p>
                    <p>{ formatDate(data.creationDate) }</p>
                  </div>
                </div>
              </div>
              <div className={styles.title}>
                <div className={styles.info}>
                  <div className={styles.planData}>
                    <div>
                      <p>Subscription Description</p>
                      <p>Egestas imperdiet vitae sed diam lacinia in dolor. Ultrices id diam phasellus placerat pellentesque eget. Eu praesent eget maecenas consequat pulvinar diam. </p>
                    </div>
                  </div>
                </div>
              </div>
              <div className={styles.subscriberCards}>
                <div className={styles.main}>
                  <p>General Details</p>
                  <>
                    <PlanDataBar
                      Icon={Signal}
                      displayText={`${data.totalDataUsageGB} GB`}
                      percentage={(data.totalDataUsageGB / data.kbBankGB) * 100 + "%"}
                      faded={cancelled}
                      grey={data.subscriberNumberStatus === 'Suspended'}
                    />
                  </>
                  <div className={styles.title}>
                    <div className={styles.info}>
                      <div className={styles.planData}>
                        <div>
                          <p>Activation Date</p>
                          <p>{ formatDateWords(data.startDate) }</p>
                        </div>
                        <div>
                          <p>Bill Cycle</p>
                          <p>{ formatDateWords(data.endDate) }</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className={styles.title}>
                    <div className={styles.info}>
                      <div className={styles.planData}>
                        <div>
                          <p>Service Type</p>
                          <p>ABC</p>
                        </div>
                        <div>
                          <p>ICCID</p>
                          <p>{ data.iccid }</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className={styles.title}>
                    <div className={styles.info}>
                      <div className={styles.planData}>
                        <div>
                          <p>IMEI</p>
                          <p>{ data.imei }</p>
                        </div>
                        <div>
                          <p>BAN</p>
                          <p>{ data.ban }</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className={styles.main}>
                  <p>Subscription Details</p>
                  <div className={styles.subData}>
                    <div>
                      <p className={styles.title}>First Name</p>
                      <p className={styles.customerData}>{ customer.firstName }</p>
                    </div>
                    <div>
                      <p className={styles.title}>Last Name</p>
                      <p className={styles.customerData}>{ customer.lastName }</p>
                    </div>
                    <div>
                      <p className={styles.title}>Street Address</p>
                      <p className={styles.customerData}>{ customer.street }</p>
                    </div>
                    <div>
                      <p className={styles.title}>City</p>
                      <p className={styles.customerData}>{ customer.city }</p>
                    </div>
                    <div>
                      <p className={styles.title}>State</p>
                      <p className={styles.customerData}>{ customer.state }</p>
                    </div>
                    <div>
                      <p className={styles.title}>Zip code</p>
                      <p className={styles.customerData}>{ customer.zipNo }</p>
                    </div>
                    <div>
                      <p className={styles.title}>Email Address</p>
                      <p className={styles.customerData}>{ customer.email }</p>
                    </div>
                    <div>
                      <p className={styles.title}>Contact Number</p>
                      <p className={styles.customerData}>{ customer.phone }</p>
                    </div>
                  </div>
                </div>
                <div className={styles.main}>
                  <p>Provisioning Details</p>
                  <div className={styles.subData}>
                    <div>
                      <p className={styles.title}>First Name</p>
                      <p className={styles.customerData}>{ customer.firstName }</p>
                    </div>
                    <div>
                      <p className={styles.title}>Last Name</p>
                      <p className={styles.customerData}>{ customer.lastName }</p>
                    </div>
                    <div>
                      <p className={styles.title}>Street Address</p>
                      <p className={styles.customerData}>{ customer.street }</p>
                    </div>
                    <div>
                      <p className={styles.title}>City</p>
                      <p className={styles.customerData}>{ customer.city }</p>
                    </div>
                    <div>
                      <p className={styles.title}>State</p>
                      <p className={styles.customerData}>{ customer.state }</p>
                    </div>
                    <div>
                      <p className={styles.title}>Zip code</p>
                      <p className={styles.customerData}>{ customer.zipNo }</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <>
              <div className={styles.topSection}>
              <div style={{ display: "flex" }}>
                <StatusPill
                  icon={data.subscriberNumberStatus === 'Rejected'}
                  additionalText={(data.subscriberNumberStatus === 'Suspended' || data.subscriberNumberStatus === 'Cancelled') && `On ${formatDate(data.creationDate)}`}
                  text={data.subscriberNumberStatus ? subscriberStatuses[data.subscriberNumberStatus.toLowerCase().replaceAll(" ", '')].status : subscriberStatuses.readytoactivite.status}
                  color={data.subscriberNumberStatus ? subscriberStatuses[data.subscriberNumberStatus.toLowerCase().replaceAll(" ", '')].color : subscriberStatuses.readytoactivite.color}
                />
              </div>
              <div className={styles.buttons}>
                {
                  (data.subscriberNumberStatus !== 'Pending' && data.subscriberNumberStatus !== 'Cancelled' && data.subscriberNumberStatus !== 'BAN Change') && (
                    <SubscriberManageMenu
                      customModal={(name:string) => setCustomModalName(name)}
                      status={data.subscriberNumberStatus}
                      modalName={(name:string) => setModalName(name)}
                      selectedTab={(tab:string) => handleChangingTab(tab) } />
                  )
                }
                <IconButton
                  noOutline
                  onClick={() => {
                    setFullSize(true);
                    isFullSize(true)
                  }}
                >
                  <FullScreen />
                </IconButton>
              </div>
            </div>
            <div className={styles.title}>
              <div className={styles.info}>
                <div className={styles.titleText}>
                  {data.kbBankGB} GB Unlimited
                </div>
                <div className={styles.planData}>
                  <p>MDN: { data.subscriberNumberStatus ? data.mdn : 'To be assigned' }</p>
                  <p>{ formatDate(data.creationDate) }</p>
                </div>
              </div>
            </div>
              <>
                <PlanDataBar
                  Icon={Signal}
                  displayText={`${data.totalDataUsageGB} GB`}
                  percentage={(data.totalDataUsageGB / data.kbBankGB) * 100 + "%"}
                  faded={cancelled}
                  grey={data.subscriberNumberStatus === 'Suspended'}
                />
              </>
              <div className={styles.title}>
                <div className={styles.info}>
                  <div className={styles.planData}>
                    {
                      data.subscriberNumberStatus && (
                        <>
                          <div>
                            <p>Activation Date</p>
                            <p>{ formatDateWords(data.activationDate) }</p>
                          </div>
                          <div>
                            <p>Bill Cycle</p>
                            <p>{ formatDateWords(data.nextBillCycleDate) }</p>
                          </div>
                        </>
                      )
                    }
                  </div>
                </div>
              </div>
              <div className={styles.title}>
                <div className={styles.info}>
                  <div className={styles.planData}>
                    <div>
                      <p>ICCID</p>
                      <p>{ data.iccid }</p>
                    </div>
                    {
                      data.subscriberNumberStatus && (
                        <div>
                          <p>BAN</p>
                          <p>{ data.ban }</p>
                        </div>
                      )
                    }
                    {
                      !data.subscriberNumberStatus && (
                        <div>
                          <p>IMEI</p>
                          <p>{ data.imei }</p>
                        </div>
                      )
                    }
                  </div>
                </div>
              </div>
            </>
          )
        }
        </div>
    </CSSTransition>
    </SwitchTransition>
  </motion.div>
  );
};

export default SubscriberPlanTile;
