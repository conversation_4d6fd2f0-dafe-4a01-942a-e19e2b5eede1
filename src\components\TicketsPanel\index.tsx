import styles from "./tickets-panel.module.scss";
import Select from "../Select";
import MultiSelect from "../MultiSelect";
import { useEffect, useState } from "react";
import StatusBadge, { convertStatus } from "../StatusBadge";
import Priority, { convertPriority } from "../Priority";
import Assignee from "../Assignee";
import Category from "../Category";
import {
  Coin,
  Cube,
  Export,
  Home,
  Spanner,
  CaretLeft,
  CaretRight,
  FloppyDisk,
} from "../svgs";
import Button from "../Button";
import Ticket from "../Ticket";
import RemoveFilter from "../RemoveFilter";
import { useDispatch, useSelector } from "react-redux";
import { Fade } from "@mui/material";
import { AnimatePresence } from "framer-motion";
import formatDate from "../utils/formatDate";
import TicketSkeleton from "../TicketSkeleton";
import Checkbox from "../Checkbox";
import TicketSelect from "../TicketSelect";
import Initials from "../Initials";
import Tooltip from "../Tooltip";
import Modal from "../Modal";
import Pagination from "../Pagination";
import TicketSummary from "../TicketSummary";
import { assignees, categories, priorities } from "../utils/dataCreator";
import { statuses } from "../utils/dataCreator";
import TableControl from "../TableControl";
import StatusPill from "../StatusPill";
import { ApiPatch } from "src/pages/api/api";

const TicketsPanel = ({
  tickets,
  showFilters,
  ticketsLoading,
  agents,
  repopulate,
}: any) => {
  const dispatch = useDispatch();
  const { ticket, ticketOpen, sidebarOpen } = useSelector(
    (state: any) => state
  );
  const [orderBy, setOrderBy] = useState("date-created");
  const [filters, setFilters] = useState({
    status: [],
    assignee: [],
    priority: [],
    category: [],
  } as any);

  const clearFilters = () => {
    setFilters({
      status: [],
      assignee: [],
      priority: [],
      category: [],
    });
  };

  const handleOrderChange = (option: string) => {
    setOrderBy(option);
  };

  const handleFilterChange = (type: string, option: string) => {
    let current = filters[type];
    if (current.includes(option)) {
      current = current.filter((item: any) => item !== option);
    } else {
      current.push(option);
    }
    setFilters({
      ...filters,
      [type]: current,
    });
  };

  const handleFilterBulkChange = (type: string, options: any) => {
    setFilters({
      ...filters,
      [type]: options,
    });
  };

  const handleTicketUpdate = (type: string, option: string, ticket: any) => {
    const updatedTicket = { ...ticket };

    const {
      id,
      creationDate,
      assigneeEmail,
      mid,
      customerEmail,
      subject,
      description,
      ...rest
    } = updatedTicket;

    console.log(type, option);

    const result = {
      ...rest,
      //responder_id: 201006510951,
      [type]: option,
      title: subject,
      body: description,
    };
    setLoading(true);
    ApiPatch(`/tickets/support/${ticket.id}`, result)
      .then((res) => {
        repopulate();
        dispatch({
          type: "notify",
          payload: {
            message: res.data.message,
            error: false,
          },
        });
        setLoading(false);
      })
      .catch((error) => {
        dispatch({
          type: "notify",
          payload: {
            message: error.response.data.message,
            error: true,
          },
        });
        setLoading(false);
      });
  };

  const [filteredTickets, setFilteredTickets] = useState([] as any);

  const sortByDate = (a: any, b: any) => {
    return (
      new Date(b.createdDate).getTime() - new Date(a.createdDate).getTime()
    );
  };

  const sortByPriority = (a: any, b: any) => {
    return b.priority - a.priority;
  };

  const sortByReversePriority = (a: any, b: any) => {
    return a.priority - b.priority;
  };

  const sortTickets = (array: any) => {
    if (orderBy === "date-created") {
      return array.sort(sortByDate);
    } else if (orderBy === "high-to-low") {
      return array.sort(sortByPriority);
    } else {
      return array.sort(sortByReversePriority);
    }
  };

  useEffect(() => {
    console.log(tickets);
    let allTickets = tickets;
    console.log(filters, allTickets);
    allTickets = allTickets
      .filter(
        (ticket: any) =>
          filters.priority.includes(ticket.priority) ||
          filters.priority.length === 0
      )
      .filter(
        (ticket: any) =>
          filters.category.includes(ticket.category) ||
          filters.category.length === 0
      )
      .filter(
        (ticket: any) =>
          filters.assignee.includes(ticket.assignee) ||
          filters.assignee.length === 0
      )
      .filter(
        (ticket: any) =>
          filters.status.includes(ticket.status) || filters.status.length === 0
      );

    setFilteredTickets(sortTickets(allTickets));
  }, [filters, tickets, orderBy]);

  const [selectedTickets, setSelectedTickets] = useState([] as any);

  const handleSelectTicket = (ticket: any) => {
    let currentSelected = [...selectedTickets];
    if (currentSelected.includes(ticket.id)) {
      currentSelected = currentSelected.filter(
        (item: any) => item !== ticket.id
      );
    } else {
      currentSelected.push(ticket.id);
    }
    setSelectedTickets(currentSelected);
  };

  const encodeCommasAndQuotes = (string: string) => {
    return '"' + string.replaceAll('"', '""') + '"';
  };

  const exportTickets = () => {
    let csvContent =
      "data:text/csv;charset=utf-8,ID,Date,Subject,Email,Body,Priority,Category,Assignee,Status\n";
    let ticketsToExport;
    if (selectedTickets.length) {
      ticketsToExport = filteredTickets.filter((ticket: any) =>
        selectedTickets.includes(ticket.id)
      );
    } else {
      ticketsToExport = filteredTickets;
    }
    ticketsToExport.forEach((ticket: any) => {
      csvContent += ticket.id + ",";
      csvContent += formatDate(ticket.date) + ",";
      csvContent += encodeCommasAndQuotes(ticket.subject) + ",";
      csvContent += encodeCommasAndQuotes(ticket.email) + ",";
      csvContent += encodeCommasAndQuotes(ticket.body) + ",";
      csvContent += ticket.priority + ",";
      csvContent += ticket.category + ",";
      csvContent += ticket.assignee + ",";
      csvContent += ticket.status + ",";
      csvContent += "\n";
    });
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", "tickets.csv");
    document.body.appendChild(link);

    link.click();

    dispatch({
      type: "notify",
      payload: {
        message: "Tickets .CSV file exported.",
        error: false,
      },
    });
  };

  /************    BULK EDIT     **********/

  const [showBulkEditModal, setShowBulkEditModal] = useState(false);

  const [bulkEdit, setBulkEdit] = useState({
    priority: "",
    category: "",
    assignee: "",
    status: "",
  } as any);

  const editBulkValues = (type: string, option: string) => {
    if (bulkEdit[type] === option) {
      setBulkEdit({
        ...bulkEdit,
        [type]: "",
      });
    } else {
      setBulkEdit({
        ...bulkEdit,
        [type]: option,
      });
    }
  };

  const cancelBulkEdit = () => {
    setBulkEdit({
      priority: "",
      category: "",
      assignee: "",
      status: "",
    });
    setSelectedTickets([] as any);
  };

  const saveBulkEdit = () => {
    setLoading(true);
    setTimeout(() => {
      /*let currentTickets = [...allTickets];
      selectedTickets.forEach((id: any) => {
        const objIndex = currentTickets.findIndex((obj: any) => obj.id === id);
        if (bulkEdit.priority)
          currentTickets[objIndex].priority = bulkEdit.priority;
        if (bulkEdit.category)
          currentTickets[objIndex].category = bulkEdit.category;
        if (bulkEdit.assignee)
          currentTickets[objIndex].assignee = bulkEdit.assignee;
        if (bulkEdit.status) currentTickets[objIndex].status = bulkEdit.status;
      });*/
      dispatch({
        type: "set",
        tickets: [],
      });
      setBulkEdit({
        priority: "",
        category: "",
        assignee: "",
        status: "",
      });
      dispatch({
        type: "notify",
        payload: {
          message: "Your changes have been saved.",
          error: false,
        },
      });
      setLoading(false);
      setShowBulkEditModal(false);
    }, 2000);
  };

  const [selectAll, setSelectAll] = useState(false);
  const [selectAllIndeterminate, setSelectAllIndeterminate] = useState(true);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (selectedTickets.length === filteredTickets.length) {
      setSelectAllIndeterminate(false);
      setSelectAll(true);
    } else {
      setSelectAll(false);
      setSelectAllIndeterminate(true);
    }
  }, [selectedTickets]);

  const handleSelectAll = () => {
    if (filteredTickets.length === selectedTickets.length) {
      setSelectedTickets([] as any);
    } else {
      let allTicketIds = [] as any;
      filteredTickets.forEach((ticket: any) => {
        allTicketIds.push(ticket.id);
      });
      setSelectedTickets(allTicketIds);
    }
  };

  /************    PAGINATION     ***********/

  const [ticketsShown, setTicketsShown] = useState(15);

  const [showTicketNumberMenu, setShowTicketNumberMenu] = useState(false);

  const [currentPage, setCurrentPage] = useState(1);

  useEffect(() => {
    window.addEventListener("click", (e: any) => {
      let classes = e.target.className;
      if (typeof classes === "string") {
        if (!e.target.className.includes("ticketsPerPageButton")) {
          setShowTicketNumberMenu(false);
        }
      }
    });
  }, []);

  const nextTicket = () => {
    const index = filteredTickets.indexOf(ticket);
    const newIndex = index === filteredTickets.length - 1 ? 0 : index + 1;
    dispatch({
      type: "set",
      ticket: filteredTickets[newIndex],
    });
  };

  const prevTicket = () => {
    const index = filteredTickets.indexOf(ticket);
    const newIndex = index === 0 ? filteredTickets.length - 1 : index - 1;
    dispatch({
      type: "set",
      ticket: filteredTickets[newIndex],
    });
  };

  return (
    <div
      className={`${styles.main} select ${sidebarOpen && styles.sidebarOpen} ${ticketOpen && styles.ticketOpen}`}
    >
      <Modal
        show={showBulkEditModal}
        setShow={setShowBulkEditModal}
        image="/bulk_edit_confirm_graphic.svg"
        proceed={saveBulkEdit}
        loading={loading}
        saveButton={
          <>
            <FloppyDisk />
            Save Changes
          </>
        }
      >
        <div className={styles.bulkModalContainer}>
          <h2 className={styles.bulkTitle}>
            Are you sure you want to make these changes?
          </h2>
          <div className={styles.bulkReminderGrid}>
            {bulkEdit.priority && (
              <>
                <div className={styles.bulkReminderName}>Priority</div>
                <div className={styles.bulkReminderValue}>
                  <Priority priority={bulkEdit.priority} />
                </div>
              </>
            )}
            {bulkEdit.category && (
              <>
                <div className={styles.bulkReminderName}>Category</div>
                <div className={styles.bulkReminderValue}>
                  <Category category={bulkEdit.category} />
                </div>
              </>
            )}
            {bulkEdit.assignee && (
              <>
                <div className={styles.bulkReminderName}>Assignee</div>
                <div className={styles.bulkReminderValue}>
                  <Assignee name={bulkEdit.assignee} />
                </div>
              </>
            )}
            {bulkEdit.status && (
              <>
                <div className={styles.bulkReminderName}>Status</div>
                <div className={styles.bulkReminderValue}>
                  <StatusBadge status={bulkEdit.status} />
                </div>
              </>
            )}
          </div>
        </div>
      </Modal>
      <div className={styles.topGrid}>
        <div
          className={styles.topMain}
          style={{ gap: selectedTickets.length ? 0 : "" }}
        >
          <div className={styles.topPanel}>
            {selectedTickets.length === 0 ? (
              <>
                <div className={styles.order}>
                  <span className={styles.label}>Sort by</span>
                  <TicketSelect
                    options={[
                      {
                        key: "date-created",
                        label: "Date Created",
                        displayLabel: "Date Created",
                      },
                      {
                        key: "high-to-low",
                        label: "Priority: Urgent to Low",
                        displayLabel: "Priority: Urgent to Low",
                      },
                      {
                        key: "low-to-high",
                        label: "Priority: Low to Urgent",
                        displayLabel: "Priority: Low to Urgent",
                      },
                    ]}
                    selected={orderBy}
                    onChange={handleOrderChange}
                    orderby
                  />
                </div>
                {showFilters && (
                  <div className={styles.filters}>
                    <span className={styles.label}>Filters</span>
                    <MultiSelect
                      label="Priority"
                      options={[
                        {
                          key: 4,
                          label: <Priority priority="Urgent" />,
                          displayLabel: <Priority priority="Urgent" />,
                        },
                        {
                          key: 3,
                          label: <Priority priority="High" />,
                          displayLabel: <Priority priority="High" />,
                        },
                        {
                          key: 2,
                          label: <Priority priority="Medium" />,
                          displayLabel: <Priority priority="Medium" />,
                        },
                        {
                          key: 1,
                          label: <Priority priority="Low" />,
                          displayLabel: <Priority priority="Low" />,
                        },
                      ]}
                      selected={filters.priority}
                      setSelected={(option: string) => {
                        handleFilterBulkChange("priority", option);
                      }}
                    />
                    <MultiSelect
                      label="Category"
                      options={categories.map((item: any) => ({
                        key: item,
                        label: <Category category={item} />,
                      }))}
                      selected={filters.category}
                      setSelected={(option: string) => {
                        handleFilterBulkChange("category", option);
                      }}
                    />
                    <MultiSelect
                      label="Assignee"
                      options={assignees.map((item: any) => ({
                        key: item,
                        label: <Assignee name={item} />,
                      }))}
                      selected={filters.assignee}
                      setSelected={(option: string) => {
                        handleFilterBulkChange("assignee", option);
                      }}
                    />
                    <MultiSelect
                      label="Status"
                      options={[
                        {
                          key: 2,
                          label: <StatusBadge status="Open" />,
                          displayLabel: <StatusBadge status="Open" />,
                        },
                        {
                          key: 3,
                          label: <StatusBadge status="Pending" />,
                          displayLabel: <StatusBadge status="Pending" />,
                        },
                        {
                          key: 4,
                          label: <StatusBadge status="Resolved" />,
                          displayLabel: <StatusBadge status="Resolved" />,
                        },
                        {
                          key: 5,
                          label: <StatusBadge status="Closed" />,
                          displayLabel: <StatusBadge status="Closed" />,
                        },
                      ]}
                      selected={filters.status}
                      setSelected={(option: string) => {
                        handleFilterBulkChange("status", option);
                      }}
                    />
                  </div>
                )}
              </>
            ) : (
              <div className={styles.ticketsSelected}>
                <Tooltip show text={selectAll ? "Deselect all" : "Select all"}>
                  <Checkbox
                    checked={selectAll}
                    onClick={handleSelectAll}
                    indeterminate={selectAllIndeterminate}
                    disabled={loading}
                  />
                </Tooltip>
                <span className={styles.ticketsNumber}>
                  {selectedTickets.length} Ticket
                  {selectedTickets.length > 1 && "s"} selected
                </span>
                <div className={styles.bulkEdit}>
                  <span className={styles.change}>Change:</span>
                  <TicketSelect
                    disabled={loading}
                    label="Priority"
                    options={[
                      {
                        key: 4,
                        label: <Priority priority="Urgent" />,
                        displayLabel: <Priority priority="Urgent" />,
                      },
                      {
                        key: 3,
                        label: <Priority priority="High" />,
                        displayLabel: <Priority priority="High" />,
                      },
                      {
                        key: 2,
                        label: <Priority priority="Medium" />,
                        displayLabel: <Priority priority="Medium" />,
                      },
                      {
                        key: 1,
                        label: <Priority priority="Low" />,
                        displayLabel: <Priority priority="Low" />,
                      },
                    ]}
                    selected={bulkEdit.priority}
                    onChange={(option: string) => {
                      editBulkValues("priority", option);
                    }}
                    bulkEdit
                  />
                  <TicketSelect
                    disabled={loading}
                    label="Category"
                    options={[
                      {
                        key: "Mobilise",
                        label: <Category category="Mobilise" />,
                        displayLabel: <Home />,
                      },
                      {
                        key: "Technical Support",
                        label: <Category category="Technical Support" />,
                        displayLabel: <Spanner />,
                      },
                      {
                        key: "Finance",
                        label: <Category category="Finance" />,
                        displayLabel: <Coin />,
                      },
                      {
                        key: "Product Management",
                        label: <Category category="Product Management" />,
                        displayLabel: <Cube />,
                      },
                    ]}
                    selected={bulkEdit.category}
                    onChange={(option: string) => {
                      editBulkValues("category", option);
                    }}
                    bulkEdit
                  />
                  <TicketSelect
                    disabled={loading}
                    label="Assignee"
                    options={agents.map((agent: any) => ({
                      key: agent.freshdeskId,
                      label: (
                        <Assignee
                          name={agent.firstName + " " + agent.lastName}
                        />
                      ),
                      displayLabel: (
                        <Initials>
                          {agent.firstName.slice(0, 1)}
                          {agent.lastName.slice(0, 1)}
                        </Initials>
                      ),
                    }))}
                    selected={bulkEdit.assignee}
                    onChange={(option: string) => {
                      editBulkValues("assignee", option);
                    }}
                    bulkEdit
                  />
                  <TicketSelect
                    disabled={loading}
                    label="Status"
                    options={[
                      {
                        key: 2,
                        label: <StatusBadge status="Open" />,
                        displayLabel: <StatusBadge status="Open" />,
                      },
                      {
                        key: 3,
                        label: <StatusBadge status="Pending" />,
                        displayLabel: <StatusBadge status="Pending" />,
                      },
                      {
                        key: 4,
                        label: <StatusBadge status="Resolved" />,
                        displayLabel: <StatusBadge status="Resolved" />,
                      },
                      {
                        key: 5,
                        label: <StatusBadge status="Closed" />,
                        displayLabel: <StatusBadge status="Closed" />,
                      },
                    ]}
                    selected={bulkEdit.status}
                    onChange={(option: string) => {
                      editBulkValues("status", option);
                    }}
                    bulkEdit
                  />
                  <div className={styles.bulkEditButtons}>
                    <Button
                      color="tertiary"
                      onClick={cancelBulkEdit}
                      disabled={loading}
                    >
                      Cancel
                    </Button>
                    <Button
                      color="quaternary"
                      onClick={() => {
                        if (
                          bulkEdit.priority === "" &&
                          bulkEdit.category === "" &&
                          bulkEdit.assignee === "" &&
                          bulkEdit.status === ""
                        ) {
                          dispatch({
                            type: "notify",
                            payload: {
                              message: "No changes have been staged.",
                              error: true,
                            },
                          });
                        } else {
                          setShowBulkEditModal(true);
                        }
                      }}
                      loading={loading}
                    >
                      Save
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </div>
          <Fade in={selectedTickets.length === 0} unmountOnExit>
            <div className={styles.removeFilters}>
              <AnimatePresence>
                {filters.priority.map((filter: any) => (
                  <RemoveFilter
                    type="priority"
                    handleRemoveFilter={handleFilterChange}
                    key={"remove-" + filter}
                    filter={filter}
                    grey
                  >
                    <Priority priority={convertPriority(filter)} small />
                  </RemoveFilter>
                ))}
                {filters.category.map((filter: any) => (
                  <RemoveFilter
                    type="category"
                    handleRemoveFilter={handleFilterChange}
                    key={"remove-" + filter}
                    filter={filter}
                    grey
                  >
                    <Category category={filter} small />
                  </RemoveFilter>
                ))}
                {filters.assignee.map((filter: any) => (
                  <RemoveFilter
                    type="assignee"
                    handleRemoveFilter={handleFilterChange}
                    key={"remove-" + filter}
                    filter={filter}
                    grey
                  >
                    <Assignee name={filter} small />
                  </RemoveFilter>
                ))}
                {filters.status.map((filter: any) => (
                  <RemoveFilter
                    type="status"
                    handleRemoveFilter={handleFilterChange}
                    key={"remove-" + filter}
                    filter={filter}
                    status={filter}
                  >
                    {convertStatus(filter)}
                  </RemoveFilter>
                ))}
                {
                  <Fade
                    in={
                      filters.priority.length ||
                      filters.category.length ||
                      filters.assignee.length ||
                      filters.status.length
                        ? true
                        : false
                    }
                  >
                    <div className={styles.clearAll} onClick={clearFilters}>
                      Clear all
                    </div>
                  </Fade>
                }
              </AnimatePresence>
            </div>
          </Fade>
        </div>
        {/*<div className={styles.exportContainer}>
          <Button
            color="export"
            onClick={exportTickets}
            loading={ticketsLoading}
            disabled={loading}
          >
            <Export />
            Export
          </Button>
              </div>*/}
      </div>
      <Fade in={selectedTickets.length === 0}>
        <div className={styles.divider} />
      </Fade>
      <div className={`${styles.tickets} table-scroll`}>
        {ticketsLoading ? (
          [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 13, 14].map((n) => (
            <TicketSkeleton key={"skeleton-ticket-" + n} />
          ))
        ) : filteredTickets.length ? (
          filteredTickets
            .slice((currentPage - 1) * ticketsShown, currentPage * ticketsShown)
            .map((ticket: any) => (
              <Ticket
                ticket={ticket}
                handleTicketUpdate={handleTicketUpdate}
                key={"ticket-comp-" + ticket.id}
                handleSelectTicket={handleSelectTicket}
                selectedTickets={selectedTickets}
                disabled={loading}
                agents={agents}
              />
            ))
        ) : (
          <div className={styles.noneFound}>
            <img src="/none_found.svg" />
            <h3>We couldn't find any tickets</h3>
          </div>
        )}
      </div>
      <TableControl
        itemsPerPage={ticketsShown}
        setItemsPerPage={setTicketsShown}
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        numberOfPages={Math.ceil(filteredTickets.length / ticketsShown)}
        label="tickets"
      />
      <TicketSummary
        nextTicket={nextTicket}
        prevTicket={prevTicket}
        handleTicketUpdate={handleTicketUpdate}
        agents={agents}
      />
    </div>
  );
};

export default TicketsPanel;
