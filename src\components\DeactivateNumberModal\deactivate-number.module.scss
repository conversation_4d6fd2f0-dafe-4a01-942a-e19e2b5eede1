@use "../../styles/theme.scss" as *;
@import "../../styles/number-management.module.scss";

.main {
  margin: 0 auto;
  width: 100%;
  display: flex;
  height: 100%;
  flex-direction: column;
  justify-content: center;
  h3 {
    width: 100%;
    text-align: center;
    font-weight: 700;
    font-size: 24px;
    line-height: 36px;
    color: $primary;
  }
}

.remove {
  color: $primary;
  font-weight: 600;
  &:hover {
    color: $admin;
    cursor: pointer;
  }
}

.topRow {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 18px;
  h3 {
    font-size: 20px;
    line-height: 30px;
    font-weight: 700;
  }
  .buttons {
    display: flex;
    align-items: center;
  }
}

.panel {
  @include stripedTablePanel;
  max-height: calc(100vh - 196px);
  margin: auto 0;
  thead {
    position: sticky;
    top: 0px;
    left: 0px;
    background: #fff;
  }
}

.tableContainer {
  overflow: auto;
  padding-bottom: 15px;
  padding-right: 15px;
  position: relative;
}

.country {
  display: grid;
  grid-template-columns: 24px 1fr;
  grid-column-gap: 8px;
  align-items: center;
  font-weight: 400;
  .flag {
    background-size: cover;
    background-position: center;
    width: 24px;
    height: 24px;
    border-radius: 1000px;
  }
}

.actionBox {
  display: grid;
  align-items: center;
  grid-template-columns: auto auto;
  grid-column-gap: 12px;
}

.noResults {
  margin: 0 auto;
  display: grid;
  align-items: center;
  position: relative;
  .searchImage {
    grid-area: 1 / 1 / 2 / 2;
    height: 80%;
  }
  .text {
    grid-area: 1 / 1 / 2 / 2;
    display: flex;
    flex-direction: column;
    align-items: center;
    h4 {
      font-size: 24px;
      line-height: 36px;
      margin-bottom: 12px;
    }
    p {
      font-size: 12px;
      line-height: 18px;
      margin-bottom: 40px;
    }
  }
}
