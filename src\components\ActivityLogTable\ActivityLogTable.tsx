import TableControl from "../../components/TableControl";
import styles from "../../styles/customer-management.module.scss";
import { activityLogFields } from "../utils/ActivityLogFields";
import { useDispatch, useSelector } from "react-redux";
import UserSkeleton from "../../components/UserSkeleton";
import { highlightSearch } from "../../components/utils/searchAndFilter";
import StatusPill from "../../components/StatusPill";
import { getActivityLog } from "../utils/dataCreator";
import { useState, useEffect } from "react";
import { formatDateWithTime } from "../utils/formatDate";

const ActivityLogTable = () => {
  const [initialLoading, setInitialLoading] = useState(true);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [queryDisplay, setQueryDisplay] = useState("");
  const [numberOfPages, setNumberOfPages] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const dispatch = useDispatch();
  const { activityLog } = useSelector((state: any) => state);

  const getActivities = () => {
    dispatch({ type: "set", activityLog: getActivityLog(itemsPerPage) });
    setInitialLoading(false);
  };

  useEffect(() => {
    getActivities();
  }, []);

  const formatDataItem = (item: any, key: string) => {
    if (key === "dateTime") {
      return formatDateWithTime(item[key]);
    } else if (key === "status") {
      return <StatusPill status={item.status} />;
    } else {
      return highlightSearch(item[key], key, queryDisplay);
    }
  };

  return (
    <>
      <div className={`${styles.tableContainer} table-scroll`}>
        <table>
          <thead>
            <tr>
              {activityLogFields.map((field: any) => (
                <th key={field.label}>{field.label}</th>
              ))}
            </tr>
          </thead>
          <tbody>
            {!initialLoading
              ? activityLog.map((item: any, i: number) => {
                  return (
                    <tr key={`subscriber-${item.mid}`}>
                      {activityLogFields.map((field: any) => (
                        <td key={`subscriber-${item.mid}-${field.key}`}>
                          <div
                            style={{
                              display: "flex",
                              justifyContent: "flex-start",
                            }}
                          >
                            {formatDataItem(item, field.key)}
                          </div>
                        </td>
                      ))}
                    </tr>
                  );
                })
              : Array.from({ length: itemsPerPage }, (v, i) => i).map((i) => (
                  <UserSkeleton key={"user-skeleton-" + i} noOfStandard={9} />
                ))}
          </tbody>
        </table>
      </div>
      <div style={{ marginTop: "16px" }}>
        <TableControl
          show
          itemsPerPage={itemsPerPage}
          setItemsPerPage={setItemsPerPage}
          currentPage={currentPage}
          setCurrentPage={setCurrentPage}
          numberOfPages={numberOfPages}
          label="activity"
          loading={initialLoading}
        />
      </div>
    </>
  );
};

export default ActivityLogTable;
