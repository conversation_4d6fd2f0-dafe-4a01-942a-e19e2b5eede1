import { ApiPostAuth } from "../../pages/api/api";

export const logOut = async (
  dispatch: any = (x: any) => {},
  navigate: any = (x: any) => {}
) => {
  await ApiPostAuth("/agent/logout")
    .then((response) => {
      localStorage.removeItem("token");
      localStorage.removeItem("crmUserInfo");
      dispatch({
        type: "notify",
        payload: {
          error: false,
          message: response.data.message,
        },
      });
      dispatch({
        type: "set",
        isLoggedIn: false,
      });
      navigate("/login");
    })
    .catch((err) => {
      dispatch({
        type: "notify",
        payload: {
          error: true,
          message: err.response.data.message,
        },
      });
    });
};
