import styles from "./customer-ticket.module.scss";
import {
  <PERSON><PERSON>,
  Cog,
  FullScreen,
  Envelope,
  Paperclip,
  Minimize,
  ChevronRight,
  WarningCircle,
} from "../svgs";
import { useEffect, useState } from "react";
import TicketSelect from "../TicketSelect";
import Priority from "../Priority";
import Category from "../Category";
import Assignee from "../Assignee";
import StatusBadge from "../StatusBadge";
import formatDate, { formatDateWords } from "../utils/formatDate";
import { motion } from "framer-motion";
import { SwitchTransition, CSSTransition } from "react-transition-group";
import IconButton from "../IconButton";
import { Input } from "../Input";
import Attachment from "../Attachment";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import Dialog from "../Dialog";

const CustomerTicket = ({
  ticket,
  handleTicketUpdate,
  selectedTickets = [],
  disabled,
  size,
  ticketsStyle,
  closeFn,
}: any) => {
  const [selected, setSelected] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  useEffect(() => {
    if (selectedTickets.includes(ticket.id)) {
      setSelected(true);
    } else {
      setSelected(false);
    }
  }, [selectedTickets]);

  useEffect(() => {
    if (size) {
      setFullSize(true);
    }
  }, [size]);

  const convertPriority = (num: number) => {
    switch (num) {
      case 1:
        return "Low";
      case 2:
        return "Medium";
      case 3:
        return "High";
      case 4:
        return "Urgent";
      default:
        return "";
    }
  };

  const SampleNextArrow = (props: any) => {
    const { className, style, onClick } = props;
    return (
      <div
        className={`${className} ${styles.arrow} ${styles.next}`}
        onClick={onClick}
      >
        <ChevronRight />
      </div>
    );
  };

  const SamplePrevArrow = (props: any) => {
    const { className, style, onClick } = props;
    return (
      <div
        className={`${className} ${styles.arrow} ${styles.previous}`}
        onClick={onClick}
      >
        <ChevronRight />
      </div>
    );
  };

  const settings = {
    className: "slider variable-width",
    dots: false,
    arrows: true,
    infinite: false,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 3,
    variableWidth: true,
    nextArrow: <SampleNextArrow />,
    prevArrow: <SamplePrevArrow />,
  };

  const [fullSize, setFullSize] = useState(false);

  const handleClose = () => {
    if (size) {
      closeFn();
    } else {
      setFullSize(false);
    }
  };

  return (
    <>
      <Dialog
        headerIcon={<WarningCircle />}
        size="sm"
        open={showDeleteModal}
        headerTitle="Delete Attachemnt?"
        headerSubtitle="Once deleted, it cannot be recovered."
        confirmButtonOnClick={() => setShowDeleteModal(false)}
        confirmButtonText="Yes, delete attachment"
        confirmButtonVariant="customerActionRed"
        cancelButtonText="Cancel"
        onClose={() => setShowDeleteModal(false)}
      />
      <motion.div
        layout
        transition={{
          type: "tween",
          ease: "easeInOut",
          duration: 0.3,
        }}
        key={"motion-ticket-" + ticket.id}
        style={{ padding: "12px" }}
        className={`${ticketsStyle ? "" : styles.main} modal-scroll ${disabled && styles.disabled} ${
          fullSize && !ticketsStyle && styles.fullSize
        }`}
      >
        <SwitchTransition>
          <CSSTransition
            key={fullSize ? "large" : "small"}
            addEndListener={(node, done) =>
              node.addEventListener("transitionend", done, false)
            }
            classNames="fade"
          >
            {fullSize ? (
              <div className={styles.overview}>
                <div className={styles.top}>
                  <div
                    className={styles.typeContainer}
                    style={{ display: "flex" }}
                  >
                    {!ticketsStyle && (
                      <div className={styles.typeIcon}>
                        <Spanner />
                      </div>
                    )}
                    <div className={styles.type} style={{ margin: 0 }}>
                      Support #{ticket.id}
                    </div>
                  </div>
                  <div className={styles.buttons}>
                    <IconButton style={{ marginRight: 8 }}>
                      <Envelope />
                    </IconButton>
                    <IconButton style={{ marginRight: 8 }}>
                      <Paperclip />
                    </IconButton>
                    <IconButton noOutline onClick={() => handleClose()}>
                      <Minimize />
                    </IconButton>
                  </div>
                </div>
                <div className={styles.mainContent}>
                  <div className={styles.ticketContent}>
                    <div className={styles.subtitle}>Subject</div>
                    <div className={styles.subject}>{ticket.subject}</div>
                    <div className={styles.subtitle}>Description</div>
                    <div
                      className={styles.description}
                      dangerouslySetInnerHTML={{ __html: ticket.description }}
                    />
                    <div className={styles.subtitle}>Attachments (12)</div>
                    <div className={styles.attachmentsContainer}>
                      <Slider {...settings}>
                        <Attachment
                          showDeleteModal={() => setShowDeleteModal(true)}
                        />
                        <Attachment
                          showDeleteModal={() => setShowDeleteModal(true)}
                        />
                        <Attachment
                          showDeleteModal={() => setShowDeleteModal(true)}
                        />
                        <Attachment
                          showDeleteModal={() => setShowDeleteModal(true)}
                        />
                        <Attachment
                          showDeleteModal={() => setShowDeleteModal(true)}
                        />
                        <Attachment
                          showDeleteModal={() => setShowDeleteModal(true)}
                        />
                        <Attachment
                          showDeleteModal={() => setShowDeleteModal(true)}
                        />
                        <Attachment
                          showDeleteModal={() => setShowDeleteModal(true)}
                        />
                        <Attachment
                          showDeleteModal={() => setShowDeleteModal(true)}
                        />
                        <Attachment
                          showDeleteModal={() => setShowDeleteModal(true)}
                        />
                        <Attachment
                          showDeleteModal={() => setShowDeleteModal(true)}
                        />
                      </Slider>
                    </div>
                    <div className={styles.subtitle}>Notes</div>
                    <Input placeholder="Add a note..." />
                  </div>
                  <div className={styles.sideSection}>
                    <div className={styles.title}>Details</div>
                    <div className={styles.ticketFields}>
                      <div className={styles.fieldName}>Status</div>
                      <TicketSelect
                        disabled={disabled}
                        label="Status"
                        options={[
                          {
                            key: 2,
                            label: <StatusBadge status="Open" />,
                            displayLabel: <StatusBadge status="Open" />,
                          },
                          {
                            key: 3,
                            label: <StatusBadge status="Pending" />,
                            displayLabel: <StatusBadge status="Pending" />,
                          },
                          {
                            key: 4,
                            label: <StatusBadge status="Resolved" />,
                            displayLabel: <StatusBadge status="Resolved" />,
                          },
                          {
                            key: 5,
                            label: <StatusBadge status="Closed" />,
                            displayLabel: <StatusBadge status="Closed" />,
                          },
                        ]}
                        selected={ticket.status}
                        onChange={(option: string) => {
                          handleTicketUpdate("status", option, ticket);
                        }}
                      />
                      <div className={styles.fieldName}>Priority</div>
                      <TicketSelect
                        disabled={disabled}
                        label="Priority"
                        selected={ticket.priority}
                        onChange={(option: string) => {
                          handleTicketUpdate("priority", option, ticket);
                        }}
                        options={[
                          {
                            key: 4,
                            label: <Priority priority="Critical" />,
                            displayLabel: <Priority priority="Critical" />,
                          },
                          {
                            key: 3,
                            label: <Priority priority="Major" />,
                            displayLabel: <Priority priority="Major" />,
                          },
                          {
                            key: 2,
                            label: <Priority priority="Medium" />,
                            displayLabel: <Priority priority="Medium" />,
                          },
                          {
                            key: 1,
                            label: <Priority priority="Low" />,
                            displayLabel: <Priority priority="Low" />,
                          },
                        ]}
                        customerPage={
                          <Priority
                            priority={convertPriority(ticket.priority)}
                          />
                        }
                      />
                      <div className={styles.fieldName}>Assignee</div>
                      <div className={styles.blueInfo}>{ticket.assignee}</div>
                      <div className={styles.fieldName}>Due Date</div>
                      <div className={styles.blueInfo}>
                        {formatDate(ticket.dueDate)}
                      </div>
                      <div className={styles.fieldName}>Ticket Type</div>
                      <div className={styles.blueInfo}>Support</div>
                      <div className={styles.fieldName}>Created</div>
                      <div className={styles.creationDate}>
                        {formatDate(ticket.creationDate)}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className={styles.overview}>
                <div className={styles.top}>
                  <div style={{ display: "flex" }}>
                    <TicketSelect
                      disabled={disabled}
                      label="Status"
                      options={[
                        {
                          key: 2,
                          label: <StatusBadge status="Open" />,
                          displayLabel: <StatusBadge status="Open" />,
                        },
                        {
                          key: 3,
                          label: <StatusBadge status="Pending" />,
                          displayLabel: <StatusBadge status="Pending" />,
                        },
                        {
                          key: 4,
                          label: <StatusBadge status="Resolved" />,
                          displayLabel: <StatusBadge status="Resolved" />,
                        },
                        {
                          key: 5,
                          label: <StatusBadge status="Closed" />,
                          displayLabel: <StatusBadge status="Closed" />,
                        },
                      ]}
                      selected={ticket.status}
                      onChange={(option: string) => {
                        handleTicketUpdate("status", option, ticket);
                      }}
                    />
                    <div style={{ width: 6 }} />
                    <TicketSelect
                      disabled={disabled}
                      label="Priority"
                      selected={ticket.priority}
                      onChange={(option: string) => {
                        handleTicketUpdate("priority", option, ticket);
                      }}
                      options={[
                        {
                          key: 4,
                          label: <Priority priority="Critical" />,
                          displayLabel: <Priority priority="Critical" />,
                        },
                        {
                          key: 3,
                          label: <Priority priority="Major" />,
                          displayLabel: <Priority priority="Major" />,
                        },
                        {
                          key: 2,
                          label: <Priority priority="Medium" />,
                          displayLabel: <Priority priority="Medium" />,
                        },
                        {
                          key: 1,
                          label: <Priority priority="Low" />,
                          displayLabel: <Priority priority="Low" />,
                        },
                      ]}
                      customerPage={
                        <Priority priority={convertPriority(ticket.priority)} />
                      }
                    />
                  </div>
                  <div className={styles.buttons}>
                    <IconButton noOutline style={{ marginRight: 4 }}>
                      <Cog />
                    </IconButton>
                    <IconButton
                      noOutline
                      onClick={() => {
                        setFullSize(true);
                      }}
                    >
                      <FullScreen />
                    </IconButton>
                  </div>
                </div>
                <div className={styles.info}>
                  <div className={styles.typeContainer}>
                    <div className={styles.typeIcon}>
                      <Spanner />
                    </div>
                    <div>
                      <div className={styles.type}>Support #{ticket.id}</div>
                      <div className={styles.date}>
                        {formatDateWords(ticket.creationDate)}
                      </div>
                    </div>
                  </div>
                  <div className={styles.dueDate}>
                    <div>Due</div>
                    <div>{formatDate(ticket.dueDate, true)}</div>
                  </div>
                </div>
                <div>
                  <div className={styles.subject}>{ticket.subject}</div>
                  <div
                    className={styles.description}
                    dangerouslySetInnerHTML={{ __html: ticket.description }}
                  />
                </div>
                <div className={styles.bottom}>
                  <TicketSelect
                    disabled={disabled}
                    label="Ca..."
                    options={[
                      {
                        key: "Mobilise",
                        label: <Category category="Mobilise" />,
                        displayLabel: <Category category="Mobilise" />,
                      },
                      {
                        key: "Technical Support",
                        label: <Category category="Technical Support" />,
                        displayLabel: <Category category="Technical Support" />,
                      },
                      {
                        key: "Finance",
                        label: <Category category="Finance" />,
                        displayLabel: <Category category="Finance" />,
                      },
                      {
                        key: "Product Management",
                        label: <Category category="Product Management" />,
                        displayLabel: (
                          <Category category="Product Management" />
                        ),
                      },
                    ]}
                    selected={ticket.category}
                    onChange={(option: string) => {
                      handleTicketUpdate("category", option, ticket);
                    }}
                    customerPage={
                      ticket.category ? (
                        <Category category={ticket.category} />
                      ) : (
                        "Select Category"
                      )
                    }
                  />
                  <TicketSelect
                    disabled={disabled}
                    label="As..."
                    options={[
                      {
                        key: "Robin Billington",
                        label: <Assignee name="Robin Billington" />,
                        displayLabel: <Assignee name="Robin Billington" />,
                      },
                      {
                        key: "Christina Choong",
                        label: <Assignee name="Christina Choong" />,
                        displayLabel: <Assignee name="Christina Choong" />,
                      },
                      {
                        key: "Ahmed Houssein",
                        label: <Assignee name="Ahmed Houssein" />,
                        displayLabel: <Assignee name="Ahmed Houssein" />,
                      },
                      {
                        key: "Malwina Roczniak",
                        label: <Assignee name="Malwina Roczniak" />,
                        displayLabel: <Assignee name="Malwina Roczniak" />,
                      },
                    ]}
                    selected={ticket.assignee}
                    onChange={(option: string) => {
                      handleTicketUpdate("assignee", option, ticket);
                    }}
                    customerPage={
                      ticket.assignee ? (
                        <Assignee name={ticket.assignee} />
                      ) : (
                        "Select Assignee"
                      )
                    }
                  />
                </div>
              </div>
            )}
          </CSSTransition>
        </SwitchTransition>
      </motion.div>
    </>
  );
};

export default CustomerTicket;
