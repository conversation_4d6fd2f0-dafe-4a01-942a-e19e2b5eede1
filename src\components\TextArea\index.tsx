import { useState } from "react";
import styles from "./text-area.module.scss";
import { Fade, Collapse } from "@mui/material";
import { InfoCircle2, InputError } from "@/components/svgs";

const TextArea = ({
  label,
  placeholder,
  value,
  onChange,
  error,
  onKeyDown,
  clear,
  disabled,
  readonly,
  white,
  infoTooltipText,
  id = null,
}: any) => {
  return (
    <form
      style={{ width: "100%", maxWidth: "320px" }}
      autoComplete="none"
      onSubmit={(e) => {
        e.preventDefault();
      }}
    >
      <div className={styles.inputContainer}>
        <div
          className={`${styles.label} ${readonly && styles.readonly} ${
            value && styles.hasValue
          } ${error && styles.labelError} ${white && styles.white}`}
        >
          <span>{label}</span>
          <span style={{ color: "#838CA0" }}>
            {infoTooltipText && <InfoCircle2 />}
          </span>
        </div>
        <Collapse in={error ? true : false}>
          <div className={styles.errorText} id={`${id}-error`}>
            <InputError />
            <span>{error}</span>
          </div>
        </Collapse>
        <div className={styles.inputWrapper}>
          <textarea
            id={id}
            className={`modal-scroll ${styles.input} ${
              readonly && styles.readonly
            } ${error && styles.error}`}
            value={value}
            placeholder={placeholder}
            onChange={(e: any) => {
              onChange(e);
            }}
            disabled={disabled || readonly}
          />
          <img
            src="/input_clear.svg"
            className={styles.clearIcon}
            onMouseDown={(e) => {
              e.preventDefault();
            }}
            onClick={clear}
          />
          <Fade in={error ? true : false}>
            <img
              src="/input_error.svg"
              className={styles.errorIcon}
              onMouseDown={(e) => {
                e.preventDefault();
              }}
              style={{ right: 20 }}
            />
          </Fade>
        </div>
      </div>
    </form>
  );
};

export default TextArea;
