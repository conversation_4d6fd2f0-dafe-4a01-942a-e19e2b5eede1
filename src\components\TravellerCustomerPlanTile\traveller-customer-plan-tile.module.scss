.container {
  border: 1px solid var(--gray-100);
  padding-top: 12px;
  border-radius: 16px;
  display: flex;
  flex-direction: column;
}

.topSection {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-inline: 12px;
}

.badgesContainer {
  display: flex;
  gap: 6px;
}

/* start badge */
.badge {
  padding: 6px 12px;
  font-size: 12px;
  line-height: 15px;
  border-radius: 8px;
  width: auto;
  display: inline-block;
}
/* end badge */

.productAndCountrySection {
  display: flex;
  justify-content: space-between;
  padding-inline: 12px;
  margin-top: 12px;

  .productCountryContainer {
    display: flex;
    gap: 8px;
    align-items: center;

    img {
      border-radius: 1000px;
      width: 30px;
      height: 30px;
    }

    .countryNameAndOffersContainer {
      display: flex;
      flex-direction: column;
      gap: 2px;
    }

    .countryName {
      font-size: 14px;
      font-weight: 700;
      line-height: 18px;
      color: var(--primary-900);
    }

    .offers {
      font-size: 12px;
      color: var(--gray-500);
      line-height: 15px;
    }
  }
  .date {
    font-size: 12px;
    color: var(--gray-500);
    line-height: 15px;
    align-self: flex-end;
  }
}

.mobileNumberSection {
  display: flex;
  gap: 8px;
  padding-inline: 12px;
  margin-top: 12px;

  .text {
    display: flex;
    flex-direction: column;
    gap: 2px;
  }

  .label {
    font-size: 12px;
    line-height: 15px;
    color: var(--gray-500);
  }

  .phoneNumber {
    font-size: 12px;
    color: var(--primary-900);
    line-height: 15px;
  }
}

.planUsageSection {
  margin-top: 12px;
  padding-inline: 12px;
  display: flex;
  flex-direction: column;
}

.bottomSection {
  margin-top: auto;
  font-size: 12px;
  line-height: 15px;
  padding-block: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-900);
  border-bottom-right-radius: 16px;
  border-bottom-left-radius: 16px;
}
