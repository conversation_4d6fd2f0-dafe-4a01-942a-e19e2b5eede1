.box {
  display: flex;
  flex-direction: column;
  gap: 4px;
  width: 100%;
}

.label {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: var(--text-primary);
}

.menuButton {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 16px;
  border: 1px solid var(--textField-border-primary);
  border-radius: 16px;
  background: var(--textField-background-primary);
  cursor: pointer;
  font-size: 14px;
  color: var(--gray-500);
  height: 48px;

  &:hover {
    transition: all 0.4s ease;
    border-color: var(--textField-border-active);
  }

  &.selected {
    color: var(--primary-900);
  }

  &.disabled {
    background: var(--gray-50);
    cursor: not-allowed;
  }

  &.readonly {
    background: var(--gray-50);
    cursor: default;
  }

  &.error {
    border-color: var(--error);
  }
}

.chevronContainer {
  display: flex;
  align-items: center;
  transition: transform 0.2s ease;

  .iconOpen & {
    transform: rotate(180deg);
  }
}

.menu {
  width: 320px;
  padding: 8px;
  border-radius: 16px !important;
  background: white;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.15);
}

.menuItem {
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;

  .checkboxContainer {
    width: 24px;
    height: 24px;
  }

  &:hover {
    background: var(--primary-50);
  }
}

.actions {
  display: flex;
  gap: 8px;
  padding: 8px;
  border-top: 1px solid var(--gray-200);
  margin-top: 8px;
}

.applyButton {
  flex: 1;
  padding: 8px;
  border: none;
  border-radius: 4px;
  background: var(--primary-600);
  color: white;
  font-weight: 500;
  cursor: pointer;

  &:hover {
    background: var(--primary-700);
  }
}

.cancelButton {
  flex: 1;
  padding: 8px;
  border: 1px solid var(--gray-200);
  border-radius: 4px;
  background: white;
  color: var(--gray-700);
  font-weight: 500;
  cursor: pointer;

  &:hover {
    background: var(--gray-50);
  }
}

.errorText {
  color: var(--error);
  font-size: 12px;
  margin: 0;
}
