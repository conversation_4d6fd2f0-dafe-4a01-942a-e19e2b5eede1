.card {
  display: flex;
  align-items: center;
  gap: 16px;
  background-color: #F1F6FD;
  border-radius: 24px;
  padding: 16px;
  width: 231px;
}

.icon {
  width: 48px;
  height: 48px;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.title {
  font-size: 14px;
  font-weight: 400;
  line-height: 18px;
  color: #667085;
}

.value {
  font-size: 20px;
  line-height: 28px;
  font-weight: 700;
  color: #061632;
}