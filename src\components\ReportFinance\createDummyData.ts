import { faker } from "@faker-js/faker";
import { countryListAlpha2 } from "../utils/countryList";

export const createReport = (size: number) => {
  return Array.from({ length: size }).map(() => createEntry());
};

const createEntry = () => {
  let alpha2 = faker.location.countryCode("alpha-2");
  return {
    userMid: faker.string.numeric(8),
    email: "<EMAIL>",
    product: countryListAlpha2.filter(
      (country: any) => country.code === alpha2
    )[0]?.name,
    paymentType: "stripe",
    currencyCode: ["GBP", "USD", "EUR"][faker.number.int(2)],
    amount: faker.number.int(50),
    paymentStatus: 1,
    paymentDate: faker.date.recent({ days: 100 }),
  };
};
