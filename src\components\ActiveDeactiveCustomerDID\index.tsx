import styles from "./active-deactive.module.scss";
import Modal from "../Modal";
import { useState } from "react";
import { useDispatch } from "react-redux";
import { ApiPatch } from "../../pages/api/api";
import { parsePhoneNumber } from "libphonenumber-js";

const ActiveDeactiveCustomerDID = ({
  show,
  setShow,
  number,
  mode,
  repopulate,
}: any) => {
  const [loading, setLoading] = useState(false);
  const dispatch = useDispatch();

  const handleDeactivate = () => {
    setLoading(true);

    ApiPatch(
      `/agent/${
        mode === "deactivate" ? "deactivate-number" : "activate-number"
      }`,
      {
        didNumbers: [number],
      }
    )
      .then((res) => {
        dispatch({
          type: "notify",
          payload: {
            error: false,
            message: res.data.message,
          },
        });
        repopulate();
        setLoading(false);
        setShow(false);
      })
      .catch((err) => {
        setLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: err.response.data.message,
          },
        });
      });
  };
  return (
    <Modal
      saveButton={
        <>
          Yes, {mode === "deactivate" ? <>Deactivate</> : <>Activate</>} Number
        </>
      }
      image="/bulk_edit_confirm_graphic.svg"
      show={show}
      close={() => {
        setShow(false);
      }}
      proceed={handleDeactivate}
      loading={loading}
    >
      <div className={styles.main}>
        <h3>
          {mode === "deactivate" ? <>Deactivate</> : <>Activate</>} DID Number
          <br />
          {(() => {
            if (number) {
              let phone = parsePhoneNumber(
                number.slice(0, 1) === "+" ? number : "+" + number
              );
              if (phone) {
                return phone.formatInternational();
              } else {
                return number;
              }
            }
          })()}
          ?
        </h3>
      </div>
    </Modal>
  );
};

export default ActiveDeactiveCustomerDID;
