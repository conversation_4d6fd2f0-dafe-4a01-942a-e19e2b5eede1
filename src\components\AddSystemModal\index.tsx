import styles from "./add-system-modal.module.scss";
import Modal from "../Modal";
import { Input } from "../Input";
import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { ApiPostAuth } from "../../pages/api/api";
import {
  clearInput,
  createStateObject,
  displayErrors,
  handleInputChange,
  labels,
} from "../utils/InputHandlers";
import { z, ZodError } from "zod";
import { Plus } from "../svgs";

const fields = ["name", "prefix", "mode", "inBoundIp", "outBoundIp"];
const SystemSchema = z.object({
  name: z.string({ message: "name is required" }),
  prefix: z.string({ message: "prefix is required" }),
  mode: z.string({ message: "mode is required" }),
  inBoundIp: z.string({ message: "Please add an In Bound IP" }),
  outBoundIp: z.string({ message: "Please add an Out Bound IP" }),
  allowCodecs: z.boolean({ message: "Please add allowed codecs" }),
  providerType: z.enum(["SMS", "CALL"]),
});

const AddSystemModal = ({
  show,
  setShow,
  provider,
  resetActiveUser,
  repopulateData,
}: any) => {
  const dispatch = useDispatch();
  const [data, setData] = useState(createStateObject(fields));
  const [loading, setLoading] = useState(false);
  const [status, setStatus] = useState("Active");
  const [Codecs, setCodecs] = useState("Yes");
  const [Type, setType] = useState("SMS");

  useEffect(() => {
    if (provider && show) {
      setData({
        ...data,
        mode: provider.mode,
        inboundIP: provider.inboundIP,
        outboundIP: provider.outboundIP,
        allowCodecs: provider.allowCodecs,
      });
      setStatus(provider.status);
      setCodecs(provider.allowCodecs);
    }
  }, [provider, show]);

  const reset = () => {
    setLoading(false);
    setShow(false);
  };

  const AddUser = () => {
    const testData = {
      name: data.name,
      prefix: data.prefix,
      mode: data.mode,
      inBoundIp: data.inboundIP,
      outBoundIp: data.outboundIP,
      allowCodecs: Codecs === "Yes" ? true : false,
      providerType: Type === "SMS" ? "SMS" : "CALL",
    };

    try {
      SystemSchema.parse(testData);
      setLoading(true);
      ApiPostAuth("/providers", testData)
        .then((response) => {
          reset();
          dispatch({
            type: "notify",
            payload: {
              error: false,
              message: response.data.message,
            },
          });
          repopulateData();
        })
        .catch((error) => {
          setLoading(false);
          dispatch({
            type: "notify",
            payload: {
              error: true,
              message: error.response.data.message,
            },
          });
        });
    } catch (errors: unknown | ZodError) {
      if (errors instanceof ZodError) {
        console.log(errors.issues);
        const error = errors.issues.map((e) => ({
          field: e.path[0],
          message: e.message,
        }));
        displayErrors(error, setData);
        return;
      }

      displayErrors(errors, setData);
      setLoading(false);
    }
  };

  return (
    <Modal
      title="Add Provider"
      icon={<Plus />}
      saveButton="Add Provider"
      show={show}
      setShow={setShow}
      proceed={() => setShow(false)}
      close={() => setShow(false)}
      loading={loading}
    >
      <div className={`${styles.main} normal-select-input`}>
        {fields.map((prop) => (
          <Input
            key={`${prop}`}
            label={labels[prop]}
            value={data[prop]}
            onChange={(e: any) => {
              handleInputChange(prop, e, data, setData);
            }}
            error={data.errors[prop]}
            onKeyDown={AddUser}
            clear={() => {
              clearInput(prop, setData);
            }}
            disabled={loading}
            white
            infoTooltipText
          />
        ))}
      </div>
    </Modal>
  );
};

export default AddSystemModal;
