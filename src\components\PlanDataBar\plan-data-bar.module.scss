@use "../../styles/theme.scss" as *;

.container {
  width: 100%;
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  svg {
    width: 24px;
    height: 24px;
    margin-right: 14px;
  }
}

.info {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
  line-height: 15px;
  margin-bottom: 2px;

  .text {
    color: #061632;
  }
  .percentageText {
    color: #a3a9b8;
  }
}

.barContainer {
  position: relative;
  height: 7px;
  flex-grow: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #d6e3fa;
  border-radius: 100px;
  overflow: hidden;
  &.faded {
    background: var(--gray-50);
    .bar {
      background: var(--gray-300);
    }
  }
  &.grey {
    background: $lightgrey;
    .bar {
      background: #838ca0;
    }
  }
}

.bar {
  position: absolute;
  left: 0px;
  height: 100%;
  background: #2e70e5;
  border-radius: 100px;
  z-index: 800;
  transition: width 0.8s ease;
}
