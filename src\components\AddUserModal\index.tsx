import { FloppyDisk, PencilCircle, PlusCircle } from "../svgs";
import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { createStateObject, labels, handleInputChange } from "../utils/InputHandlers";
import { ApiPostAuth } from "../../pages/api/api";
import { InputFieldsStep } from "../ModalsSteps/ModalsSteps";
import SelectInput from "../SelectInput";
import RoleSwitch from "../RoleSwitch";
import StatusSwitch from "../StatusSwitch";
import Dialog from "../Dialog";

const fields = ["firstName", "lastName", "email", "password", "channel", "role", "status"];
const channelOptions = ["London", "General", "Dublin", "Birmingham", "Manchester", "Glasgow"]
const inputFields = ["firstName", "lastName", "email", "password"]

const AddEditUserModal = ({ show, setShow, repopulateUsers, currentUser }: any) => {
  const dispatch = useDispatch();

  const [data, setData] = useState(createStateObject(fields));
  const [role, setRole] = useState(1)
  const [status, setStatus] = useState(false)
  const [loading, setLoading] = useState(false);
  const [mode, setMode] = useState<'add' | 'edit'>('add')

  useEffect(() => {
    if (currentUser) {
      setMode('edit')
      setRole(currentUser.role)
      setStatus(currentUser.status)
      setData({
        ...data,
        firstName: currentUser.firstName,
        lastName: currentUser.lastName,
        email: currentUser.email,
        role: currentUser.role,
        channel: currentUser.channel,
        status: currentUser.status,
        errors: {
          ...data.errors
        }
      })
    }
  }, [currentUser])

  useEffect(() => {
    if (role) {
      const updatedData = data
      updatedData['role'] = role
      setData(updatedData)
    }
  }, [role])

  useEffect(() => {
    if (status) {
      const updatedData = data
      updatedData['status'] = status
      setData(updatedData)
    }
  }, [status])

  // Reset modal data when closed
  const reset = () => {
    setData(createStateObject(fields));
    setMode('add')
    setLoading(false);
  };

  const handleSuccess = () => {
    dispatch({
      type: "notify",
      payload: {
        error: false,
        heading: "Success",
        message: `User has been ${mode === 'add' ? 'created' : 'edited'} successfully`,
      },
    });
    setShow(false);
  }

  // Handles creation of new user
  const createUser = () => {
    const testData = {
      firstName: data.firstName.trim(),
      lastName: data.lastName.trim(),
      email: data.email.trim(),
      // password: data.password.trim(),
      role: data.role.trim(),
    };

    setLoading(true);
    ApiPostAuth("/agent/create", {
      firstName: testData.firstName,
      lastName: testData.lastName,
      email: testData.email,
      // password: testData.password,
      // mvnoId: mvnoId,
    })
      .then((response) => {
        reset();
        repopulateUsers();
        dispatch({
          type: "notify",
          payload: {
            error: false,
            heading: "Success",
            message: response.data.message,
          },
        });
        setShow(false);
      })
      .catch((error) => {
        setLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            heading: "Something went wrong",
            message: error.response.data.message,
          },
        });
      });
  };

  return (
      <Dialog 
        open={show}
        size='sm'
        headerTitle={mode === "add" ? "Create User" : "Edit User"}
        confirmButtonOnClick={() => handleSuccess()}
        headerIcon={mode === "add" ? <PlusCircle /> : <PencilCircle />}
        cancelButtonText='Cancel'
        cancelButtonOnClick={() => setShow(false)}
        confirmButtonText={mode === "add" ? "Create User" : <><FloppyDisk /> Save Changes</>}
        onClose={() => {
          setShow(false);
          setTimeout(() => {
            reset();
          }, 300);
        }}>
      <div className="normal-select-input">
        <InputFieldsStep data={data} setData={(val:any) => setData(val)} fields={inputFields} />
        <SelectInput
          key="user-channel"
          options={channelOptions}
          label={labels['channel']}
          value={data['channel']}
          onChange={(e: any) => {
            handleInputChange('channel', e, data, setData);
          }}
          error={data.errors['channel']}
          infoTooltipText
        />
        <RoleSwitch setRole={(val:number) => setRole(val)} role={role} />
        <StatusSwitch setStatus={(val:boolean) => setStatus(val)} status={status} />
      </div>
    </Dialog>
  );
};

export default AddEditUserModal;
