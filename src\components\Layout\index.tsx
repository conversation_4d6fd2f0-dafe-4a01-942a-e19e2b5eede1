import Sidebar from "../Sidebar";
import styles from "./layout.module.scss";
import { useDispatch, useSelector } from "react-redux";
import { useLocation, useParams } from "react-router-dom";
import { useEffect } from "react";
import SearchSection from "../SearchSection";

const Layout = ({ children }: any) => {
  const { sidebarOpen, projects } = useSelector((state: any) => state);
  const dispatch = useDispatch();

  const { project } = useParams();

  useEffect(() => {
    if (project) {
      const projectObj = projects.find((item: any) => item.urlName === project);
      dispatch({
        type: "set",
        logoSmall: projectObj.logoSmall,
      });
      dispatch({
        type: "set",
        logoLarge: projectObj.logoLarge,
      });
    }
  }, [project]);

  return (
    <div
      className={`${styles.main} ${sidebarOpen ? styles.open : styles.closed}`}
    >
      <Sidebar />
      {children}
    </div>
  );
};

export default Layout;
