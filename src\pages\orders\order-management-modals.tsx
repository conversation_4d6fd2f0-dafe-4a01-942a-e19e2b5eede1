import AddPortInDetailsModal from "@/components/AddPortInDetailsModal";
import OrderDetailModal from "@/components/OrderDetailModal";
import { useState } from "react";
import AddOrderIccidModal from "@/components/AddOrderIccidModal";

type OrderManagementModalsProps = {
  activeOrder: any;
  setActiveOrder: (value: any) => void;
};

const OrderManagementModals = ({
  activeOrder,
  setActiveOrder,
}: OrderManagementModalsProps) => {
  const [showPortInAddModal, setShowPortinAddModal] = useState(false);
  const [showAddIccidModal, setShowAddIccidModal] = useState(false);

  if (!activeOrder) return null;

  return (
    <>
      <OrderDetailModal
        activeOrder={activeOrder}
        open={!!activeOrder}
        onClose={() => setActiveOrder(null)}
        onAddPortInDetails={() => setShowPortinAddModal(true)}
        onAddIccid={() => setShowAddIccidModal(true)}
      />
      <AddPortInDetailsModal
        open={showPortInAddModal}
        onClose={() => setShowPortinAddModal(false)}
      />
      <AddOrderIccidModal
        open={showAddIccidModal}
        onClose={() => setShowAddIccidModal(false)}
        activeOrder={activeOrder}
      />
    </>
  );
};

export default OrderManagementModals;
