@use "../../styles/theme.scss" as *;

.input {
  width: 100%;
  resize: none;
  height: 120px;
  border-radius: 16px;
  border: none;
  box-shadow: 0px 0px 0px 1px var(--textField-border-primary);
  padding: 12px 32px 12px 16px;
  transition: border 0.1s ease;
  font-size: 16px;
  color: var(--text-primary);
  background: var(--textField-background-primary);
  &::placeholder {
    color: var(----textField-text-placeholder);
  }
  &:focus {
    box-shadow: 0px 0px 0px 2px var(--primary-500);
    caret-color: var(--primary-500);
    outline: none;
    &::placeholder {
      color: var(--textField-text-placeholder);
    }
    & ~ .errorIcon {
      display: none;
    }
    & ~ .clearIcon {
      display: block;
    }
  }
  &.error {
    box-shadow: 0px 0px 0px 2px var(--textField-border-error);
  }
  &:disabled {
    border: 1px solid $disabled;
    color: $disabled-text;
    background: none;
    &.readonly {
      color: $black;
      border: 1px solid #cacaca;
      & + .label {
        color: $placeholder;
      }
    }
    & + .label {
      color: $disabled-text;
    }
    & ~ .eyeIcon {
      opacity: 0.38;
    }
  }
}

.label {
  display: flex;
  gap: 6px;
  align-items: center;
  line-height: 18px;
  font-size: 14px;
  pointer-events: none;
  color: #061632;
}

.inputContainer {
  width: 100%;
  margin-bottom: 12px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.inputWrapper {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.eyeIcon,
.clearIcon,
.errorIcon {
  position: absolute;
  right: 12px;
  top: 12px;
}

.clearIcon,
.eyeIcon {
  cursor: pointer;
}

.clearIcon {
  display: none;
}

.errorText {
  font-size: 14px;
  color: var(--systemStatus-red-500);
  display: flex;
  align-items: center;
  gap: 4px;
}
