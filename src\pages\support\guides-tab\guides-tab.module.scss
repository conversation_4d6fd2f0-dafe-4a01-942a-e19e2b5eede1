@import "../../../styles/mixins.module.scss";

.panel {
  @include panel;
  padding-bottom: 48px;
  margin-inline: 16px;
  margin-top: 16px;
  
  h2 {
    font-size: 16px;
    font-weight: 700;
    line-height: 22px;
    margin-bottom: 16px;
    padding-top: 6px;
  }
}

.grid {
  margin-top: 24px;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 16px;
}

.videoMockup {
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  transition: transform 0.2s ease;

  &:hover {
    transform: scale(1.02);
  }
}

.videoTop {
  background: var(--primary-50);
  aspect-ratio: 16/9;
  display: flex;
  position: relative;
  color: var(--primary-500);

  svg {
    position: absolute;
    top: 40px;
    right: 40px;
  }
}

.videoBottom {
  background: var(--primary-500);
  padding: 34px 24px;
  color: white;
  font-size: 14px;
  font-weight: 700;
}