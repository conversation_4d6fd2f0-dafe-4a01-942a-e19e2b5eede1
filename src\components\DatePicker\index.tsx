import { useState, useEffect } from "react";
import { Calendar, CaretDown, CaretUp } from "../svgs";
import styles from "./date-picker.module.scss";
import {
  getMonthDays,
  getMonthFirstDay,
  zeroPad,
  getMonthArray,
  formatDate,
  isDateNow,
} from "../utils/dateHelpers";
import Button from "../Button";
import { ControlledMenu, useMenuState } from "@szhsin/react-menu";
import "@szhsin/react-menu/dist/index.css";
import "@szhsin/react-menu/dist/transitions/slide.css";
import { useRef } from "react";
import MonthPicker from "../MonthPicker";
import RangeSwitch from "../RangeSwitch";
import TimeSelector from "../TimeSelector";
import { SwitchTransition, CSSTransition } from "react-transition-group";
import { createPortal } from "react-dom";
import { Input } from "../Input";
import { labels } from "../utils/InputHandlers";

const now = new Date();

const DatePicker = ({
  label,
  masterFrom,
  masterUntil,
  startTime,
  endTime,
  onChange,
  background,
  reports,
  placeholder
}: any) => {
  const [fromDate, setFromDate] = useState(null as any);
  const [untilDate, setUntilDate] = useState(null as any);
  useEffect(() => {
    setFromDate(masterFrom);
    setUntilDate(masterUntil);
  }, [masterFrom, masterUntil]);

  useEffect(() => {
    setFromTime(startTime);
    setUntilTime(endTime);
    if (endTime) {
      setTimeType("range");
    }
  }, [startTime, endTime]);

  const [fromTime, setFromTime] = useState({
    hh: "00",
    mm: "00",
  } as any);
  const [untilTime, setUntilTime] = useState({
    hh: "00",
    mm: "00",
  } as any);

  const daysLetters = ["M", "T", "W", "T", "F", "S", "S"];

  const [month, setMonth] = useState(now.getMonth() + 1);
  const [year, setYear] = useState(now.getFullYear());
  const [displayMonths, setDisplayMonths] = useState([] as any);

  const [hoverDate, setHoverDate] = useState(null as any);

  useEffect(() => {
    const days = getMonthDays(month, year);
    const first = getMonthFirstDay(month, year);
    const newMonthArray = getMonthArray(
      Array.from({ length: days }, (v, i) => ({
        type: "main",
        value: i + 1,
        date: new Date(`${year}/${zeroPad(month)}/${zeroPad(i + 1)}`),
      })),
      days,
      first,
      month,
      year,
    );
    setDisplayMonths(newMonthArray);
  }, [month, year]);

  const ref = useRef(null);

  const [menuProps, toggleMenu] = useMenuState({ transition: true });

  const reset = () => {
    toggleMenu(false);
    setFromDate(masterFrom);
    setUntilDate(masterUntil);
  };
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const [dateType, setDateType] = useState("specific");
  const [timeType, setTimeType] = useState("range");

  useEffect(() => {
    if (reports) {
      reset()
    }
  }, [])

  useEffect(() => {
    if (dateType === "specific") {
      setUntilDate(null);
    }
  }, [dateType]);

  useEffect(() => {
    if (timeType === "specific") {
      setUntilTime(null);
    } else {
      setUntilTime({
        hh: "21",
        mm: "00",
      });
    }
  }, [timeType]);

  return (
    <div className={`${styles.box} date-select`}>
      <div
        ref={ref}
        className={`${styles.menuButton} ${reports && styles.reports} ${
          background && styles.background
        } ${
          (menuProps.state === "open" || menuProps.state === "opening") &&
          !reports
            ? styles.iconOpen
            : styles.iconClosed
        }`}
        onClick={(e: any) => {
          e.stopPropagation();
          if (menuProps.state === "closing") {
            toggleMenu(false);
          } else {
            toggleMenu(true);
          }
        }}
      >
        <Input 
          label={labels[label]}
          infoTooltipText
          placeholder={placeholder}
          rightIcon={<Calendar />}
        />
      </div>
      {createPortal(
        <div className="date-select">
          <ControlledMenu
            {...menuProps}
            anchorRef={ref}
            onClose={reset}
            align="start"
            position="auto"
            viewScroll="close"
            onItemClick={(e: any) => (e.stopPropagation = true)}
          >
            <div className={styles.main}>
              <div className={styles.dateContainer}>
                <RangeSwitch small role={dateType} setRole={setDateType} />
                <div className={styles.input}>
                  <Calendar />
                  <div className={styles.text}>
                    {dateType === "specific" ? (
                      fromDate === null ? (
                        "DD/MM/YYYY"
                      ) : (
                        formatDate(fromDate)
                      )
                    ) : (
                      <>
                        {fromDate ? formatDate(fromDate) : "DD/MM/YYYY"}
                        {" - "}
                        {untilDate ? formatDate(untilDate) : "DD/MM/YYYY"}
                      </>
                    )}
                  </div>
                </div>
                <div className={styles.mainDatePicker}>
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "space-between",
                      padding: "12px 26px 0 26px",
                    }}
                  >
                    <MonthPicker
                      activeMonth={month}
                      setActiveMonth={setMonth}
                      activeYear={year}
                      setActiveYear={setYear}
                    />
                    <input
                      style={{
                        position: "absolute",
                        opacity: 0,
                        zIndex: -1,
                      }}
                      id="close-month-menu"
                    />
                    <div className={styles.prevNext}>
                      <div
                        className={styles.prev}
                        onClick={() => {
                          if (month === 1) {
                            setMonth(12);
                            setYear(year - 1);
                          } else {
                            setMonth(month - 1);
                          }
                        }}
                      >
                        <CaretUp />
                      </div>
                      <div
                        className={styles.next}
                        onClick={() => {
                          if (month === 12) {
                            setMonth(1);
                            setYear(year + 1);
                          } else {
                            setMonth(month + 1);
                          }
                        }}
                      >
                        <CaretDown />
                      </div>
                    </div>
                  </div>
                  <div className={styles.calendar}>
                    <div className={styles.days}>
                      {daysLetters.map((letter, i) => (
                        <div key={i} className={styles.letter}>
                          {letter}
                        </div>
                      ))}
                    </div>
                    <div className={styles.datesGrid}>
                      {displayMonths.map((day: any) => (
                        <div
                          className={styles.cellContainer}
                          onMouseEnter={() => {
                            setHoverDate(day.date);
                          }}
                          onMouseLeave={() => {
                            setHoverDate(null as any);
                          }}
                          key={`${day.type}-date-${day.date.getTime()}`}
                        >
                          <div
                            className={`${styles.gridCell} ${
                              untilDate && dateType === "range"
                                ? day.date.getTime() >= fromDate.getTime() &&
                                  day.date.getTime() <= untilDate.getTime()
                                  ? styles.highlight
                                  : ""
                                : fromDate &&
                                    hoverDate &&
                                    !untilDate &&
                                    dateType === "range"
                                  ? day.date.getTime() >= fromDate.getTime() &&
                                    day.date.getTime() <= hoverDate.getTime() &&
                                    hoverDate.getTime() !== fromDate.getTime()
                                    ? styles.highlight
                                    : ""
                                  : ""
                            } ${
                              fromDate
                                ? fromDate.getTime() === day.date.getTime()
                                  ? styles.curveLeft
                                  : ""
                                : ""
                            } ${
                              untilDate
                                ? untilDate.getTime() === day.date.getTime()
                                  ? styles.curveRight
                                  : ""
                                : hoverDate && !untilDate
                                  ? hoverDate.getTime() === day.date.getTime()
                                    ? styles.curveRight
                                    : ""
                                  : ""
                            }`}
                          />
                          <div
                            className={`${styles.day} ${
                              day.date > today && styles.disable
                            } ${isDateNow(day.date) && styles.now} ${
                              day.type === "pad" && styles.pad
                            } ${
                              fromDate
                                ? fromDate.getTime() === day.date.getTime()
                                  ? styles.active
                                  : untilDate
                                    ? untilDate.getTime() === day.date.getTime()
                                      ? styles.active
                                      : ""
                                    : ""
                                : ""
                            }`}
                            onClick={() => {
                              if (
                                fromDate === null ||
                                (fromDate && untilDate) ||
                                dateType === "specific"
                              ) {
                                setFromDate(day.date);
                                setUntilDate(null as any);
                              } else if (
                                day.date.getTime() < fromDate.getTime()
                              ) {
                                setUntilDate(fromDate);
                                setFromDate(day.date);
                              } else {
                                setUntilDate(day.date);
                              }
                            }}
                          >
                            {day.value}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
              <div className={styles.timeContainer}>
                <div className={styles.time}>Time</div>
                <RangeSwitch small role={timeType} setRole={setTimeType} />
                <div style={{ height: 11 }} />

                <SwitchTransition>
                  <CSSTransition
                    key={timeType}
                    addEndListener={(node, done) =>
                      node.addEventListener("transitionend", done, false)
                    }
                    classNames="fade"
                  >
                    <div>
                      {timeType === "specific" ? (
                        <TimeSelector time={fromTime} setTime={setFromTime} />
                      ) : (
                        <>
                          <div className={styles.fromLabel}>From:</div>
                          <TimeSelector time={fromTime} setTime={setFromTime} />
                          <div
                            style={{ marginTop: 24 }}
                            className={styles.fromLabel}
                          >
                            To:
                          </div>
                          <TimeSelector
                            time={untilTime}
                            setTime={setUntilTime}
                          />
                        </>
                      )}
                    </div>
                  </CSSTransition>
                </SwitchTransition>
                <div className={styles.buttons}>
                  <Button
                    onClick={reset}
                    style={{
                      minWidth: "initial",
                      height: 42,
                      marginRight: 12,
                      padding: "0 24px",
                      fontSize: 14,
                    }}
                    color="secondary"
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={() => {
                      onChange(fromDate, untilDate, fromTime, untilTime);
                      toggleMenu(false);
                    }}
                    style={{
                      minWidth: "initial",
                      height: 42,
                      padding: "0 24px",
                      fontSize: 14,
                    }}
                  >
                    Apply
                  </Button>
                </div>
              </div>
            </div>
          </ControlledMenu>
        </div>,
        document.getElementById("root")!,
      )}
    </div>
  );
};

export default DatePicker;
