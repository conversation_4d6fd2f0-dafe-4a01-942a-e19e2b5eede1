import { faker } from "@faker-js/faker";

export const getUsers = (size: number) => {
  return Array.from({ length: size }).map(() => createEntry());
};

const createEntry = () => {
  const channels = ["London", "General", "Dublin", "Birmingham", "Manchester", "Glasgow"]
  const status = ["Active", "InActive"]
  return {
    userMid: faker.string.numeric(12),
    email: faker.internet.email(),
    firstName: faker.person.firstName(), 
    lastName:faker.person.lastName(),
    status: status[faker.number.int(1)],
    role: faker.number.int({min: 1, max: 2}),
    channel: channels[faker.number.int(5)]
  };
};

export const userFields = [
  {
    label: "First name",
    labelStr: "First name",
    key: "firstName",
  },
  {
    label: "Last name",
    labelStr: "Last name",
    key: "lastName",
  },
  {
    label: "Channel",
    labelStr: "Channel",
    key: "channel",
  },
  {
    label: "Email Address",
    labelStr: "Email Address",
    key: "email",
  },
  {
    label: "Role",
    labelStr: "Role",
    key: "role",
  },
  {
    label: "Status",
    labelStr: "Status",
    key: "status",
  }
];
