@use "../../styles/theme.scss" as *;
@import "../../styles/table-mixin.module.scss";

.tableContainer {
  overflow: auto;
  padding-bottom: 5px;

  tr:first-of-type .actionBox {
    visibility: visible;
  }

  tr:hover .actionBox {
    visibility: visible;
  }
}

.container {
  @include table;
  width: 100%;
  flex-grow: 1;
  overflow-y: auto;
  min-height: 0;
}

.actionBox {
  visibility: hidden;
  color: #1857c3;
  font-size: 14px;
  stroke: #1857c3;
  font-weight: 700;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  cursor: pointer;
  font-size: 12px;
}

.title {
  font-size: 20px;
  font-weight: 700;
  line-height: 30px;
}
