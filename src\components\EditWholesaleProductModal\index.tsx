import Dialog from "@/components/Dialog";
import { FloppyDisk, PencilCircle } from "@/components/svgs";
import { clearInput, createStateObject, createStateObjectWithInitialState, handleInputChange, labels } from "@/components/utils/InputHandlers";
import { useState } from "react";
import styles from "./edit-wholesale-product-modal.module.scss"
import { Input } from "@/components/Input";
import ToggleButtonGroup from "@/components/ToggleButtonGroup";


type EditWholeProductModalProps = {
  productData: any;
  open: boolean;
  onClose: () => void;
}

const EditWholesaleProductModal = ({
  open,
  onClose,
  productData
}: EditWholeProductModalProps) => {
  const fields = ["family", "wholesaleName", "wholesalePrice", "sizeGB", "addToRetail"];
  const [formData, setFormData] = useState(createStateObjectWithInitialState(createStateObject(fields), {
    family: productData.family,
    wholesaleName: productData.name,
    wholesalePrice: productData.wholesalePrice,
    sizeGB: productData.sizeGB,
    addToRetail: productData.retailPrice || productData.retailName ? "yes" : undefined
  }))

  return (
    <Dialog
      open={open}
      onClose={onClose}
      size="sm"
      headerIcon={<PencilCircle />}
      headerTitle="Edit Wholesale Product"
      confirmButtonText="Save Changes"
      confirmButtonOnClick={onClose}
      confirmButtonIcon={<FloppyDisk />}
      cancelButtonText="Cancel"
    >
      <div className={styles.formContainer}>
        {fields.map(field => {
          if (["addToRetail"].includes(field)) {
            return (
              <ToggleButtonGroup
                selected={formData[field]}
                options={[{
                  label: "Yes",
                  key: "yes"
                }, {
                  label: "No",
                  key: "no"
                }]}
                label={labels[field]}
                onChange={(value) => {
                  handleInputChange(
                    field,
                    { target: { value } },
                    formData,
                    setFormData
                  )
                }}
              />
            )
          }
          else {
            return (
              <Input
                key={"product-" + field}
                label={labels[field]}
                value={formData[field]}
                onChange={(e: any) => {
                  handleInputChange(field, e, formData, setFormData)
                }}
                error={formData.errors[field]}
                clear={() => {
                  clearInput(field, setFormData)
                }}
                infoTooltipText
              />
            )
          }
        })}
      </div>
    </Dialog>
  )
}

export default EditWholesaleProductModal