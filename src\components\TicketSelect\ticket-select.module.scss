@use "../../styles/theme.scss" as *;

.menuButton {
  border: none;
  cursor: pointer;
  transition: background 0.2s ease;
  -webkit-user-select: none; /* Safari */
  -ms-user-select: none; /* IE 10 and IE 11 */
  user-select: none;
  &.small {
    height: auto;
  }
  &.orderby {
    padding: 5.5px 6px 5.5px 12px;
    height: 32px;
    &:hover {
      background: $light-orange;
    }
  }
  &.white {
    background: $off-white;
  }
  &.disabled {
    cursor: auto;
    pointer-events: none;
    opacity: 0.5;
  }
  &.bulkEdit {
    padding: 0 10px;
    height: 40px;
    &:hover {
      background: $light-orange;
    }
  }
  &:hover {
    background: #fff;
  }
  svg {
    transition: all 0.2s ease;
  }
  &.iconOpen {
    .expand svg {
      transform: rotate(180deg);
    }
  }
}

.expand {
  display: flex;
  align-items: center;
}

.box {
  height: 100%;
}

.menuItem {
  display: flex;
  align-items: center;
  border-radius: 6px;
  transition: all 0.1s ease;
  color: $black;
  padding: 0;
  line-height: 21px;
  gap: 14px;
  margin-bottom: 18px;
  &:last-of-type {
    margin-bottom: 0px;
  }
  &:hover {
    background: none;
  }
}

.tooltip {
  font-size: 14px;
  line-height: 21px;
}
