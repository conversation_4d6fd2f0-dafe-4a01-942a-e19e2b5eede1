import { useEffect, useState } from "react";
import styles from "./plan-data-bar.module.scss";

const PlanDataBar = ({ Icon, displayText, percentage, faded, grey }: any) => {
  return (
    <div className={styles.container}>
      <Icon />
      <div style={{ width: "100%" }}>
        <div className={styles.info}>
          <div className={styles.text}>
            {displayText} <span style={{ color: "#667085" }}>Remaining</span>
          </div>
          <div className={styles.percentageText}>52% used</div>
        </div>
        <div
          className={`${styles.barContainer} ${faded ? styles.faded : grey ? styles.grey : ""}`}
        >
          <div className={styles.bar} style={{ width: percentage }} />
        </div>
      </div>
    </div>
  );
};

export default PlanDataBar;
