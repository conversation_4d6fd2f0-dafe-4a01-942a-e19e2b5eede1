@use "../../styles/theme.scss" as *;

.main {
  margin: 0 auto;
  width: 100%;
  max-width: 350px;
  display: flex;
  height: 150%;
  flex-direction: column;
  justify-content: center;
  h3 {
    line-height: 36px;
    font-weight: 700;
    font-size: 24px;
    margin-bottom: 24px;
  }
}

.modalContent {
  display: flex;
  gap: 24px;
  justify-content: center;
  background-color: #f7f6f6;
  border-radius: 10px;
  padding: 24px;
  margin-bottom: 12px;

  p {
    margin: 0 0 12px 0;
  }
}
