@use "../../styles/theme.scss" as *;

.menuButton {
  height: 32px;
  background: #f7f6f6;
  padding: 5.5px 6px 5.5px 12px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  line-height: 21px;
  display: flex;
  align-items: center;
  cursor: pointer;
  margin-right: 12px;
  transition: background 0.2s ease;
  -webkit-user-select: none; /* Safari */
  -ms-user-select: none; /* IE 10 and IE 11 */
  user-select: none;
  &:hover {
    background: $light-orange;
  }
  svg {
    transition: all 0.2s ease;
    margin-left: 4px;
  }
  &.iconOpen {
    svg {
      transform: rotate(180deg);
    }
  }
}

.box {
  height: 100%;
}

.menuItem {
  display: flex;
  align-items: center;
  border-radius: 6px;
  transition: all 0.1s ease;
  color: $black;
  padding: 0;
  line-height: 21px;
  padding: 0;
  margin-bottom: 18px;
  cursor: auto;
  &:last-of-type {
    margin-bottom: 0px;
  }
  &:hover {
    background: none;
  }
}

.buttons {
  display: flex;
  width: 100%;
  justify-content: flex-end;
  margin-top: 32px;
}
