import { useCallback, useEffect, useRef, useState } from "react";
import styles from "./combo-tab.module.scss";
import CollapsiblePanel from "@/components/CollapsiblePanel";
import InsightList from "@/components/InsightList";
import Button from "@/components/Button";
import {
  MagnifyingGlass,
  Plus,
  Delete,
  Export,
  Pencil,
} from "@/components/svgs";
import InsightCard from "@/components/InsightCard";
import {
  clearInput,
  createStateObject,
  handleInputChange,
  labels,
} from "@/components/utils/InputHandlers";
import Tag from "@/components/Tag";
import { Input } from "@/components/Input";
import SelectInput from "@/components/SelectInput";
import { countryList } from "@/components/utils/countries";
import { getHutchComboProducts } from "@/components/utils/dataCreator";
import UserSkeleton from "@/components/UserSkeleton";
import TableControl from "@/components/TableControl";
import { comboProductFields } from "@/components/utils/comboProductFields";
import { formatDateWords } from "@/components/utils/formatDate";
import CountryDisplay from "@/components/CountryDisplay";
import StatusPill from "@/components/StatusPill";
import CreateComboProductModal from "@/components/CreateComboProductModal";
import EditComboProductModal from "@/components/EditComboProductModal";
import DeleteComboProductModal from "@/components/DeleteComboProductModal";
import { getCountryOptions } from "@/components/utils/getCountryOptions";
import CheckboxDropdownInput from "@/components/CheckboxDropdownInput";

const HutchComboTab = () => {
  const searchFields = [
    "dataAllowance",
    "smsAllowance",
    "callAllowance",
    "country",
    "status",
    "validity",
  ];
  const [data, setData] = useState(createStateObject(searchFields));

  const overviewPanelRef = useRef<any>(null);
  const searchPanelRef = useRef<any>(null);
  const [showSearchResults, setShowSearchResults] = useState(false);

  const [loading, setLoading] = useState(true);
  const [products, setProducts] = useState<any[]>([]);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [numberOfPages, setNumberOfPages] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  const [showCreateProductModal, setShowCreateProductModal] = useState(false);
  const [activelyEditedProduct, setActivelyEditedProduct] = useState(null);
  const [activelyDeletedProduct, setActivelyDeletedProduct] = useState(null);

  const populate = (itemsPerPage: number) => {
    setLoading(true);
    setTimeout(() => {
      setProducts(getHutchComboProducts(itemsPerPage));
      setLoading(false);
    }, 500);
  };

  useEffect(() => {
    populate(itemsPerPage);
  }, [itemsPerPage, currentPage]);

  const formatDataItem = (item: any, key: string) => {
    if (key === "dateAdded") {
      return formatDateWords(item[key]);
    }

    if (key === "status") {
      return (
        <StatusPill
          status={item[key] ? "Active" : "Inactive"}
          color={item[key] ? "active" : "inactive"}
        />
      );
    }

    if (key === "country") {
      return <CountryDisplay country={item[key].code} width={16} height={16} />;
    }

    if (key === "dataAllowance") {
      return `${item[key]}GB`;
    }

    if (!item[key]) {
      return "-";
    }

    return item[key];
  };

  const renderCreateProductButton = useCallback(
    () => (
      <Button
        onClick={(e) => {
          e.stopPropagation();
          setShowCreateProductModal(true);
        }}
      >
        <Plus />
        Create Product
      </Button>
    ),
    []
  );

  return (
    <>
      {showCreateProductModal && (
        <CreateComboProductModal
          open={showCreateProductModal}
          onClose={() => setShowCreateProductModal(false)}
        />
      )}

      {!!activelyEditedProduct && (
        <EditComboProductModal
          onClose={() => setActivelyEditedProduct(null)}
          open={!!activelyEditedProduct}
          productData={activelyEditedProduct}
        />
      )}

      {!!activelyDeletedProduct && (
        <DeleteComboProductModal
          onClose={() => setActivelyDeletedProduct(null)}
          open={!!activelyDeletedProduct}
          productData={activelyDeletedProduct}
        />
      )}

      {/* Overview */}
      <CollapsiblePanel
        title="Overview"
        summaryWhenClosed={
          <div className={styles.overviewSummaryWrapper}>
            <InsightList insights={overviewStats} />
            {renderCreateProductButton()}
          </div>
        }
        headerWhenOpen={
          <div className={styles.overviewHeaderOpenWrapper}>
            {renderCreateProductButton()}
          </div>
        }
        ref={overviewPanelRef}
      >
        <div className={styles.overview}>
          {overviewStats.map((stat, index) => (
            <InsightCard
              key={index}
              title={stat.title}
              value={stat.value.toString()}
            />
          ))}
        </div>
      </CollapsiblePanel>

      {/* Search */}
      <div style={{ marginTop: 16 }}>
        <CollapsiblePanel
          title="Search Combo Products"
          summaryWhenClosed={
            <div
              style={{
                display: "flex",
                flex: 1,
                justifyContent: "space-between",
                marginLeft: 16,
                marginRight: 8,
              }}
            >
              <Tag text="3 filters applied" />
              <Button color="secondary">Clear Filters</Button>
            </div>
          }
          ref={searchPanelRef}
        >
          <div className={styles.searchPanelForm}>
            <div className={styles.fields}>
              {searchFields.map((prop) => (
                <CheckboxDropdownInput
                  key={"promotions-" + prop}
                  options={selectOptionsByField[prop]}
                  label={labels[prop]}
                  selected={data[prop]}
                  onChange={(values) => {
                    handleInputChange(prop, values, data, setData, "select");
                  }}
                  searchOption={prop === "country"}
                  error={data.errors[prop]}
                  infoTooltipText
                />
              ))}
            </div>
            <div className={styles.actions}>
              <Button
                color="blue"
                onClick={() => {
                  searchPanelRef.current.close();
                  overviewPanelRef.current.close();
                  setShowSearchResults(true);
                }}
              >
                <MagnifyingGlass /> Search
              </Button>
            </div>
          </div>
        </CollapsiblePanel>
      </div>

      {/* Table */}
      <div className={styles.panel}>
        <div className={styles.panelTopBar}>
          <h4>Combo Products</h4>
          <div className={styles.actions}>
            <Button color="secondary">
              <Export /> Export to CSV
            </Button>
          </div>
        </div>

        <div className={`${styles.tableContainer} table-scroll`}>
          <table>
            <thead>
              <tr>
                {comboProductFields.map((field: any) => (
                  <th key={field.key}>{field.label}</th>
                ))}
                <th></th>
              </tr>
            </thead>
            <tbody>
              {!loading
                ? products.map((item: any, i: number) => (
                    <tr key={`product-${i}`}>
                      {comboProductFields.map((field: any) => (
                        <td key={`product-${i}-${field.key}`}>
                          <div
                            style={{
                              display: "flex",
                              justifyContent: "flex-start",
                            }}
                          >
                            {formatDataItem(item, field.key)}
                          </div>
                        </td>
                      ))}
                      <td style={{ width: 100 }}>
                        <div className={styles.tableRowActions}>
                          <span onClick={() => setActivelyEditedProduct(item)}>
                            <Pencil />
                          </span>
                          <span onClick={() => setActivelyDeletedProduct(item)}>
                            <Delete />
                          </span>
                        </div>
                      </td>
                    </tr>
                  ))
                : Array.from({ length: itemsPerPage }, (v, i) => i).map((i) => (
                    <UserSkeleton
                      key={"product-skeleton-" + i}
                      noOfStandard={comboProductFields.length}
                    />
                  ))}
            </tbody>
          </table>
        </div>
        <div style={{ marginTop: "16px" }}>
          <TableControl
            show
            itemsPerPage={itemsPerPage}
            setItemsPerPage={(val: any) => setItemsPerPage(val)}
            currentPage={currentPage}
            setCurrentPage={setCurrentPage}
            numberOfPages={numberOfPages}
            label="products"
            loading={loading}
          />
        </div>
      </div>
    </>
  );
};

export default HutchComboTab;

const overviewStats = [
  { title: "Total Products", value: "63,629" },
  { title: "Active Products", value: "63,629" },
  { title: "Inactive Products", value: "63,629" },
  { title: "Countries", value: "63,629" },
];

const selectOptionsByField: Record<string, any> = {
  dataAllowance: [
    { label: "1 GB", value: "1" },
    { label: "2 GB", value: "2" },
    { label: "5 GB", value: "5" },
    { label: "10 GB", value: "10" },
  ],
  smsAllowance: [
    { label: "100", value: "100" },
    { label: "200", value: "200" },
    { label: "300", value: "300" },
    { label: "400", value: "400" },
    { label: "500", value: "500" },
    { label: "600", value: "600" },
    { label: "700", value: "700" },
    { label: "800", value: "800" },
  ],
  callAllowance: [
    { label: "100", value: "100" },
    { label: "200", value: "200" },
    { label: "300", value: "300" },
    { label: "400", value: "400" },
    { label: "500", value: "500" },
    { label: "600", value: "600" },
    { label: "700", value: "700" },
    { label: "800", value: "800" },
  ],
  country: getCountryOptions(),
  status: [
    { label: "Active", value: "active" },
    { label: "Inactive", value: "Inactive" },
  ],
  validity: [
    { label: "30 days", value: "30" },
    { label: "7 days", value: "7" },
    { label: "5 days", value: "5" },
  ],
};
