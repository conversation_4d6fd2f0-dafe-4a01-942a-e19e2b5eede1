import styles from "../../styles/select-project.module.scss";
import ProjectTile from "../../components/ProjectTile";
import { useDispatch, useSelector } from "react-redux";
import { useEffect, useState } from "react";
import { ApiGet } from "../api/api";
import { logOut } from "../../components/utils/logOut";
import { Link, useNavigate, useParams } from "react-router-dom";
import ProjectSkeleton from "../../components/ProjectSkeleton";
import Title from "@/components/Title";
import { LogoFull } from "@/components/svgs";
import { dummyProjects as projects } from "./projects";
import { SwitchTransition, CSSTransition } from "react-transition-group";

const SelectProject = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { resetMessage } = useSelector((state: any) => state);

  const { mvne } = useParams();

  useEffect(() => {
    /*ApiGet("/users")
      .then((response) => {
        setProjects(response.data.mvnos);
        console.log(response);
      })
      .catch((error) => {
        logOut(dispatch, navigate);
      });*/

    dispatch({
      type: "set",
      subscribers: [],
      throttleNotifications: null,
    });
  }, []);

  useEffect(() => {
    if (resetMessage !== null) {
      dispatch({
        type: "notify",
        payload: {
          error: false,
          message: resetMessage,
        },
      });
      dispatch({
        type: "set",
        resetMessage: null,
      });
    }
  }, [resetMessage]);

  return (
    <>
      <Title>
        <LogoFull />
      </Title>

      <SwitchTransition>
        <CSSTransition
          key={mvne === undefined ? "mvne" : mvne}
          addEndListener={(node, done) =>
            node.addEventListener("transitionend", done, false)
          }
          classNames="fade"
        >
          {mvne ? (
            (() => {
              const activeMvne = projects.find(
                (item: any) => mvne === item.name
              );

              return (
                <div className={styles.main}>
                  <div className={styles.container}>
                    <h3>
                      <Link to="/select-project" className={styles.backLink}>
                        <span style={{ fontWeight: 400 }}>MVNEs</span>
                      </Link>{" "}
                      - <span>{activeMvne?.name}</span>
                    </h3>
                    <div className={styles.projects}>
                      {activeMvne?.mvnos?.map((project: any) => (
                        <ProjectTile project={project} key={project.name} />
                      ))}
                    </div>
                  </div>
                </div>
              );
            })()
          ) : (
            <div className={styles.main}>
              <div className={styles.container}>
                <h3>MVNEs</h3>
                <div className={styles.projects}>
                  {projects.length === 0
                    ? Array.from({ length: 20 }).map((i: any) => (
                        <ProjectSkeleton key={i} />
                      ))
                    : projects.map((project: any) => (
                        <ProjectTile
                          project={project}
                          key={project.name}
                          mvne
                        />
                      ))}
                </div>
              </div>
            </div>
          )}
        </CSSTransition>
      </SwitchTransition>
    </>
  );
};

export default SelectProject;
