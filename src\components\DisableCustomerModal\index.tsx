import styles from "./disable-customer.module.scss";
import Modal from "../Modal";
import { Delete } from "../svgs";
import { useState } from "react";
import { useDispatch } from "react-redux";
import { ApiPut } from "src/pages/api/api";
import { useParams } from "react-router-dom";

const DisableCustomerModal = ({ show, setShow, user, populate }: any) => {
  const dispatch = useDispatch();

  const [loading, setLoading] = useState(false);

  const { mid } = useParams();

  const deleteUser = () => {
    setLoading(true);
    ApiPut(
      `/customer/${mid}/${user.status === "Active" ? "disable" : "enable"}`
    )
      .then((response) => {
        setLoading(false);
        setShow(false);
        dispatch({
          type: "notify",
          payload: {
            error: false,
            message: response.data.message,
          },
        });
        populate();
      })
      .catch((error) => {
        setLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: error.response.data.message,
          },
        });
      });
  };

  return (
    <Modal
      saveButton={
        <>Yes, {user.status === "Active" ? "Disable" : "Enable"} Account</>
      }
      image="/bulk_edit_confirm_graphic.svg"
      show={show}
      close={() => {
        setShow(false);
      }}
      proceed={deleteUser}
      loading={loading}
    >
      <div className={styles.main}>
        <h3>
          {user.status === "Active" ? "Disable" : "Enable"} the account for{" "}
          {user.firstName}?
        </h3>
      </div>
    </Modal>
  );
};

export default DisableCustomerModal;
