import Dialog from "@/components/Dialog";
import { FloppyDisk, PencilCircle } from "@/components/svgs";
import { clearInput, createStateObject, createStateObjectWithInitialState, handleInputChange, labels } from "@/components/utils/InputHandlers";
import { useState } from "react";
import styles from "./edit-account-details-modal.module.scss";
import { Input } from "@/components/Input";
import ToggleButtonGroup from "@/components/ToggleButtonGroup";
import SelectInput from "@/components/SelectInput";

type EditAccountDetailsModalProps = {
  accountData: any;
  open: boolean;
  onClose: () => void;
}

const EditAccountDetailsModal = ({
  open,
  onClose,
  accountData
}: EditAccountDetailsModalProps) => {
  const fields = [
    "firstName", "lastName", "email",
    "streetNumber", "streetDirection", "streetName",
    "city", "state", "zipCode",
    "contactNumber", "channel"
  ];

  const [formData, setFormData] = useState(createStateObjectWithInitialState(createStateObject(fields), {
    firstName: accountData.firstName,
    lastName: accountData.lastName,
    email: accountData.email,
    streetNumber: 123,
    streetDirection: 'W',
    streetName: 'Best Street Ever',
    city: 'City Ville',
    state: 'StateName',
    zipCode: '12345',
    contactNumber: '+************',
    channel: "General"
  }));

  return (
    <Dialog
      open={open}
      onClose={onClose}
      size="sm"
      headerIcon={<PencilCircle />}
      headerTitle="Edit Account Details"
      confirmButtonText="Save Changes"
      confirmButtonOnClick={onClose}
      confirmButtonIcon={<FloppyDisk />}
      cancelButtonText="Cancel"
    >
      <div className={styles.formContainer}>
        {fields.map(field => {
          if (field === "streetDirection") {
            return (
              <div style={{ marginBottom: 16 }}>
                <ToggleButtonGroup
                  key={`account-${field}`}
                  selected={formData[field]}
                  options={[
                    { label: "N", key: "N" },
                    { label: "S", key: "S" },
                    { label: "E", key: "E" },
                    { label: "W", key: "W" },
                    { label: "NE", key: "NE" },
                    { label: "NW", key: "NW" },
                    { label: "SE", key: "SE" },
                    { label: "SW", key: "SW" }
                  ]}
                  label={labels[field]}
                  onChange={(value) => {
                    handleInputChange(
                      field,
                      value,
                      formData,
                      setFormData,
                      "select"
                    )
                  }}
                />
              </div>
            )
          }

          if (field === "channel") {
            return (
              <SelectInput
                key={`account-${field}`}
                options={["General", "Other Channel"]}
                label={labels[field]}
                value={formData[field]}
                onChange={(e: any) => {
                  handleInputChange(field, e, formData, setFormData);
                }}
                error={formData.errors[field]}
                infoTooltipText
              />
            )
          }

          return (
            <Input
              key={`account-${field}`}
              label={labels[field]}
              value={formData[field]}
              type={["streetNumber", "zipCode"].includes(field) ? "number" : "text"}
              onChange={(e: any) => {
                handleInputChange(field, e, formData, setFormData)
              }}
              error={formData.errors[field]}
              clear={() => {
                clearInput(field, setFormData)
              }}
              infoTooltipText
            />
          )
        })}
      </div>
    </Dialog>
  )
}

export default EditAccountDetailsModal
