@use "./theme.scss" as *;
@import "./mixins.module.scss";

@include animatedSelection;

.container {
  display: flex;
  flex-direction: column;
}

.main {
  padding: 16px;
  flex: 1
}

.actionBar {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.buttons {
  display: flex;
  align-items: center;
}

.tablePanel {
  @include stripedTablePanel;
}

.tableContainer {
  overflow: auto;
  padding-bottom: 15px;
  padding-right: 15px;
  position: relative;
}

.noneFound {
  display: grid;
  width: 100%;
  min-height: 510px;
  align-items: center;
  justify-content: center;
  padding-top: 55px;

  img,
  h3 {
    grid-area: 1 / 1 / 2 / 2;
    font-size: 24px;
    font-weight: 700;
    line-height: 36px;
  }

  h3 {
    width: 100%;
    text-align: center;
  }
}

.paymentSuccess {
  color: $success;
  font-weight: bold;
}

.paymentFailed {
  color: $error;
  font-weight: bold;
}