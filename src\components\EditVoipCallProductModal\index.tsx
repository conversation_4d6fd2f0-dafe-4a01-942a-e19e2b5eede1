import Dialog from "@/components/Dialog";
import { FloppyDisk, PencilCircle } from "@/components/svgs";
import {
  clearInput,
  createStateObject,
  createStateObjectWithInitialState,
  handleInputChange,
  labels,
} from "@/components/utils/InputHandlers";
import { useState } from "react";
import styles from "./edit-voip-call-product-modal.module.scss";
import { Input } from "@/components/Input";
import SelectInput from "@/components/SelectInput";
import ToggleButtonGroup from "@/components/ToggleButtonGroup";
import { getCountryOptions } from "../utils/getCountryOptions";

type EditVoipCallProductModalProps = {
  productData: any;
  open: boolean;
  onClose: () => void;
};

const EditVoipCallProductModal = ({
  open,
  onClose,
  productData,
}: EditVoipCallProductModalProps) => {
  const fields = [
    "country",
    "validity",
    "callAllowance",
    "usdPrice",
    "gbpPrice",
    "eurPrice",
    "status",
  ];
  const [formData, setFormData] = useState(
    createStateObjectWithInitialState(createStateObject(fields), {
      country: productData.country.code,
      validity: productData.validity,
      callAllowance: productData.callAllowance,
      usdPrice: productData.usdPrice,
      gbpPrice: productData.gbpPrice,
      eurPrice: productData.eurPrice,
      status: productData.status,
    })
  );

  return (
    <Dialog
      open={open}
      onClose={onClose}
      size="sm"
      headerIcon={<PencilCircle />}
      headerTitle="Edit Call Product"
      confirmButtonText="Save Changes"
      confirmButtonOnClick={onClose}
      confirmButtonIcon={<FloppyDisk />}
      cancelButtonText="Cancel"
    >
      <div className={styles.formContainer}>
        {fields.map((field) => {
          if (["country", "validity"].includes(field)) {
            return (
              <SelectInput
                key={"product-" + field}
                options={selectOptionsByField[field]}
                label={labels[field]}
                selected={formData[field]}
                onChange={(e: any) => {
                  handleInputChange(
                    field,
                    { target: { value: e } },
                    formData,
                    setFormData
                  );
                }}
                error={formData.errors[field]}
                infoTooltipText
              />
            );
          } else if (["status"].includes(field)) {
            return (
              <ToggleButtonGroup
                key={"product-" + field}
                selected={formData[field] ? "active" : undefined}
                options={toggleButtonGroupOptions}
                label={labels[field]}
                onChange={(value) => {
                  handleInputChange(
                    field,
                    { target: { value } },
                    formData,
                    setFormData
                  );
                }}
              />
            );
          } else if (["usdPrice", "gbpPrice", "eurPrice"].includes(field)) {
            if (field === "usdPrice") {
              return (
                <div key={"price-fields"} className={styles.priceFields}>
                  {["usdPrice", "gbpPrice", "eurPrice"].map((priceField) => (
                    <Input
                      key={"product-" + priceField}
                      label={labels[priceField]}
                      value={formData[priceField]}
                      onChange={(e: any) => {
                        handleInputChange(priceField, e, formData, setFormData);
                      }}
                      error={formData.errors[priceField]}
                      clear={() => {
                        clearInput(priceField, setFormData);
                      }}
                      infoTooltipText
                      number
                    />
                  ))}
                </div>
              );
            }
          } else {
            return (
              <Input
                key={"product-" + field}
                label={labels[field]}
                value={formData[field]}
                onChange={(e: any) => {
                  handleInputChange(field, e, formData, setFormData);
                }}
                error={formData.errors[field]}
                clear={() => {
                  clearInput(field, setFormData);
                }}
                infoTooltipText
                number
              />
            );
          }
        })}
      </div>
    </Dialog>
  );
};

export default EditVoipCallProductModal;

const selectOptionsByField: Record<string, any> = {
  country: getCountryOptions(),
  validity: ["30 days", "7 days", "5 days"].map((v) => ({
    label: v,
    key: v,
  })),
};

const toggleButtonGroupOptions = [
  {
    label: "Active",
    key: "active",
  },
  {
    label: "Inactive",
    key: "inactive",
  },
];
