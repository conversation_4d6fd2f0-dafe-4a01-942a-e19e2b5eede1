@import "../../styles/table-mixin.module.scss";

.title {
  font-size: 16px;
}

.subTitle {
  color: var(--gray-500);
  font-size: 14px;
  margin-top: 2px;
}

.tabsAndActionsContainer {
  margin-top: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .actions {
    display: flex;
    gap: 12px;
  }
}

.statusAndDateContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;

  .dateTime {
    color: var(--gray-500);
    font-size: 14px;
  }
}

.detailCardsContainer {
  margin-top: 16px;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;

  .detailCard {
    padding: 16px 12px;
    border: 1px solid var(--gray-100);
    border-radius: 16px;

    h4 {
      font-size: 14px;
      font-weight: 700;
    }
  }

  .detailCardItem {
    margin-top: 12px;
    display: flex;
    gap: 8px;

    &:not(:last-child) {
      margin-bottom: 8px;
    }
  }

  .cardItemContent {
    display: flex;
    flex-direction: column;
    gap: 2px;

    .itemTitle {
      font-size: 12px;
      color: var(--gray-500);
    }

    .itemValue {
      font-size: 14px;
    }
  }
}

.activityLogTabContainer {
  margin-top: 8px;
  display: flex;
  flex-direction: column;
  // height: 100%;
}

.activityLogTableContainer {
  @include table;

  .activityCell {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}
