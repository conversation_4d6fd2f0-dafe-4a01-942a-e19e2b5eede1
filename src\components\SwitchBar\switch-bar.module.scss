@use "../../styles/theme.scss" as *;

.main {
  width: 100%;
  background: #fff;
  border-bottom: 1px solid $lightgrey;
  padding: 0px 24px;
  font-size: 14px;
  line-height: 18px;
  display: flex;

  .option {
    height: 48px;
    padding: 0px 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    cursor: pointer;
    display: grid;
    .maskLabel {
      font-weight: 700;
      opacity: 0;
    }
    .displayLabel,
    .maskLabel {
      grid-area: 1 / 1 / 2 / 2;
    }
    &.active {
      .displayLabel {
        color: #2e70e5;
        font-weight: 700;
      }
    }
  }

  .underline {
    width: 100%;
    position: absolute;
    bottom: -2px;
    height: 3px;
    background-color: #2e70e5;
  }
}
