@use "./theme.scss" as *;

.main {
  width: 100%;
  background: #f1f1f1;
  padding: 50px 40px;
}

.topBar {
  display: flex;
  align-items: center;
}

.titleBar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 34px 0;
  h3 {
    margin: 0;
    font-weight: 700;
    font-size: 20px;
    line-height: 30px;
  }
}

.usersPanel:not(.products) {
  table {
    td:last-child,
    th:last-child {
      text-align: end;
    }
  }
}

.logs {
  overflow-x: auto;
  td,
  th {
    white-space: nowrap;
  }
}

.tableContainer {
  overflow-x: auto;
  padding-bottom: 5px;
}

.usersPanel {
  background: #fff;
  width: 100%;
  border-radius: 24px;
  margin-top: 32px;
  padding: 24px 40px;
  min-height: 746px;
  display: flex;
  flex-direction: column;
  table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    white-space: nowrap;
    tbody {
      tr {
        height: 60px;
        td {
          font-size: 12px;
          font-weight: 400;
          line-height: 21px;
          padding: 0 16px;
          border-right: 1px solid $lightgrey;
          border-bottom: 1px solid $lightgrey;
          svg {
            vertical-align: middle;
          }
        }
        td:first-child {
          padding-left: 24px;
          border-left: 1px solid $lightgrey;
        }
        td:last-child {
          padding-right: 24px;
        }
      }
      tr:last-child {
        td:first-child {
          border-bottom-left-radius: 30px;
        }
        td:last-child {
          border-bottom-right-radius: 30px;
        }
      }
      &:before {
        content: "@";
        display: block;
        line-height: 0px;
        text-indent: -99999px;
      }
    }
    thead {
      tr {
        th:first-child {
          padding-left: 24px;
          border-left: 1px solid $lightgrey;
          border-top-left-radius: 30px;
        }
        th:last-child {
          border-top-right-radius: 30px;
        }
      }
    }
    th {
      font-size: 12px;
      font-weight: 700;
      line-height: 16px;
      text-align: start;
      border-bottom: 1px solid $lightgrey;
      border-right: 1px solid $lightgrey;
      border-top: 1px solid $lightgrey;
      padding: 15px 16px;
      background-color: $lightblue;
    }
  }
}

.logHeading {
  display: flex;
  align-items: center;
  h2 {
    margin: 0;
  }
}

.products .filters {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  justify-content: space-between;
  .label {
    margin-right: 13px;
    font-size: 14px;
    line-height: 21px;
  }
}

.pagination {
  height: 75px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: auto;
}

.actionPanel {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-left: auto;
}

.actionButton {
  background: none;
  border: none;
  width: 44px;
  height: 41px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background 0.15s ease;
  border-radius: 8px;
  margin-right: 12px;
  color: #1a1a1a;
  &:last-child {
    margin-right: 0px;
  }
  &:hover {
    background: #fff;
  }
}

.noneFound {
  display: grid;
  width: 100%;
  min-height: 510px;
  align-items: center;
  justify-content: center;
  padding-top: 55px;
  img,
  h3 {
    grid-area: 1 / 1 / 2 / 2;
    font-size: 24px;
    font-weight: 700;
    line-height: 36px;
  }
  h3 {
    width: 100%;
    text-align: center;
  }
}

.skeletonContainer {
  display: flex;
  flex-direction: column;
}

.graphic {
  display: flex;
  align-items: center;
  svg {
    margin: 0 20px;
  }
}

.selectionWrapper {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.selection {
  height: 47px;
  border-radius: 1000px;
  color: $black;
  font-size: 14px;
  font-weight: 600;
  padding: 0 24px;
  display: flex;
  align-items: center;
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    color: $dark-orange;
  }

  span {
    position: relative;
    z-index: 6;
  }
}

.background {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 1000px;
  background-color: $mid-orange;
  z-index: 5;
  left: 0;
}

.activeSelection {
  cursor: auto;

  &:hover {
    color: $black;
  }
}

.createFeesAction {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-left: auto;

  p {
    font-size: 12px;
  }
}

.feesTableHeader {
  font-size: 18px;
}

.feesTableContainer {
  overflow-x: auto;
  padding-bottom: 5px;
  background: #fff;
  width: 100%;
  border-radius: 24px;
  margin-top: 32px;
  padding: 24px 24px;
  display: flex;
  flex-direction: column;

  table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0 6px;
    white-space: nowrap;
    margin-top: 8px;

    tbody {
      tr {
        height: 40px;

        &:nth-child(2n + 1) {
          background: #f7f6f6;
        }

        td {
          font-size: 14px;
          font-weight: 400;
          line-height: 21px;
          padding: 0 16px;

          svg {
            vertical-align: middle;
          }
        }

        td:first-child {
          border-radius: 12px 0 0 12px;
          padding-left: 24px;
        }

        td:last-child {
          border-radius: 0 12px 12px 0;
          padding-right: 24px;
        }
      }

      &:before {
        content: "@";
        display: block;
        line-height: 0px;
        text-indent: -99999px;
      }
    }

    thead {
      tr {
        border-bottom: 1px solid $disabled;

        th {
          font-weight: 600;
          border-bottom: 1px solid #0000141f;
        }

        th:first-child {
          padding-left: 24px;
        }
      }
    }

    th {
      font-size: 14px;
      font-weight: 500;
      line-height: 21px;
      text-align: start;
      padding: 0 16px;
      padding-bottom: 19px;
    }
  }

  .viewBtn {
    border: none;
    background: none;
    font-weight: 600;
    color: #b85e1d;
    cursor: pointer;
  }
}

.feeStatusBadge {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 82px;
  height: 29px;
  border-radius: 6px;
  font-weight: 500;
  text-transform: capitalize;
}

.editFeeBtn {
  border: none;
  background: none;
  cursor: pointer;
}
