import { Input } from "../../components/Input";
import Button from "../../components/Button";
import {
  AddUser,
  MagnifyingGlass,
  Gear,
  Pencil,
  Plus,
  SwapIcon,
  ReceiptIcon,
  Sparkle,
  Pause,
  Delete,
} from "../svgs";
import { subscriberFields } from "../../components/utils/subscribersFields";
import TableControl from "../../components/TableControl";
import { getSubscribers } from "@/components/utils/dataCreator";
import styles from "../../styles/customer-management.module.scss";
import { useState, useEffect, useRef } from "react";
import { formatDateWords } from "../../components/utils/formatDate";
import { highlightSearch } from "../../components/utils/searchAndFilter";
import { useSearchParams } from "react-router-dom";
import RadioSelect from "../../components/RadioSelect";
import UserSkeleton from "../../components/UserSkeleton";
import StatusPill from "../../components/StatusPill";
import {
  handleInputChange,
  createStateObject,
  labels,
  clearInput,
} from "../utils/InputHandlers";
import NationalSubscriberModal from "../NationalSubscriberModal/NationalSubscriberModal";
import AddSubscriberModal from "../AddSubscriberModal/AddSubscriberModal";
import { AnimatePresence } from "framer-motion";
import CollapsiblePanel from "../CollapsiblePanel";
import InsightCard from "../InsightCard";
import InsightList from "../InsightList";
import Tag from "../Tag";
import ProductsMenu from "../ProductsMenu";

const NationalCustomerPage = () => {
  const fields = ["name", "email", "iccid", "imei", "mdn", "ban"];
  const [data, setData] = useState(createStateObject(fields));
  const [showOverview, setShowOverview] = useState(true);
  const [showFilters, setShowFilters] = useState(true);
  const [customers, setCustomers] = useState(null as any);
  const [initialLoading, setInitialLoading] = useState(true);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [numberOfPages, setNumberOfPages] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchParams, setSearchParams] = useSearchParams();
  const [queryDisplay, setQueryDisplay] = useState("");
  const [showSubscriberModal, setShowSubscriberModal] = useState(false);
  const [showAddSubscriberModal, setShowAddSubscriberModal] = useState(false);
  const [activeSubscriber, setActiveSubscriber] = useState(null as any);
  const [showSearchResults, setShowSearchResults] = useState(false);

  const emptyFilters = {
    country: [],
    currency: [],
    creationTime: {
      from: null,
      to: null,
    },
    status: [],
  } as any;

  const [filters, setFilters] = useState(emptyFilters);
  const overviewPanelRef = useRef<any>(null);
  const searchPanelRef = useRef<any>(null);
  const populate = (search?: string) => {
    setInitialLoading(true);

    setTimeout(() => {
      setCustomers(getSubscribers(itemsPerPage));
      setInitialLoading(false);
    }, 500);
  };

  useEffect(() => {
    const search = searchParams.get("search");
    if (
      search &&
      Object.keys(filters).some((key: string) => {
        if (key === "creationTime") {
          return filters.creationTime.from !== null;
        } else {
          return filters[key].length > 0;
        }
      })
    ) {
      setFilters(emptyFilters);
    } else if (search) {
      populate(search);
    } else {
      populate();
    }
  }, [searchParams, currentPage, itemsPerPage, filters]);

  const formatDataItem = (item: any, key: string) => {
    if (key === "creationDate" || key === "activiationDate") {
      return formatDateWords(item[key]);
    } else if (key === "status") {
      return (
        <RadioSelect
          label={<StatusPill status={item.status} />}
          options={[
            { label: <StatusPill status="Active" />, key: "Active" },
            { label: <StatusPill status="Inactive" />, key: "Inactive" },
          ]}
          selected={item.status}
        />
      );
    } else if (key === "firstName") {
      let fullName = item.firstName + " " + item.lastName;
      return highlightSearch(fullName, "name", queryDisplay);
    } else {
      return highlightSearch(item[key], key, queryDisplay);
    }
  };

  return (
    <>
      <AnimatePresence>
        {showSubscriberModal && (
          <NationalSubscriberModal
            customer={activeSubscriber}
            close={(val: boolean) => setShowSubscriberModal(val)}
          />
        )}
      </AnimatePresence>
      <AddSubscriberModal
        show={showAddSubscriberModal}
        close={(val: boolean) => setShowAddSubscriberModal(val)}
      />
      <div className={styles.national}>
        <CollapsiblePanel
          title="Overview"
          summaryWhenClosed={
            <div style={{ marginLeft: 22 }}>
              <InsightList insights={overviewStats} />
            </div>
          }
          actionBtn={
            <Button
              style={{ marginRight: 10 }}
              onClick={(e) => {
                e.stopPropagation();
                setShowAddSubscriberModal(true);
              }}
            >
              <AddUser /> Add Subscriber
            </Button>
          }
          ref={overviewPanelRef}
        >
          <div className={styles.overview}>
            {overviewStats.map((stat, index) => (
              <InsightCard
                key={index}
                title={stat.title}
                value={stat.value.toString()}
              />
            ))}
          </div>
        </CollapsiblePanel>
      </div>
      <div style={{ marginTop: 16 }} className={styles.national}>
        <CollapsiblePanel
          title="Search Subscribers"
          summaryWhenClosed={
            <div
              style={{
                display: "flex",
                flex: 1,
                justifyContent: "space-between",
                marginLeft: 16,
                marginRight: 8,
              }}
            >
              <Tag text="3 filters applied" />
              <Button color="secondary">Clear Filters</Button>
            </div>
          }
          ref={searchPanelRef}
        >
          <div className={styles.fields}>
            {fields.map((prop) => {
              return (
                <Input
                  key={"national-search-" + prop}
                  label={labels[prop]}
                  value={data[prop]}
                  onChange={(e: any) => {
                    handleInputChange(prop, e, data, setData);
                  }}
                  error={data.errors[prop]}
                  clear={() => {
                    clearInput(prop, setData);
                  }}
                  infoTooltipText
                />
              );
            })}
          </div>
          <div className={styles.actions}>
            <Button
              color="blue"
              onClick={() => {
                searchPanelRef.current.close();
                overviewPanelRef.current.close();
                setShowSearchResults(true);
              }}
            >
              <MagnifyingGlass /> Search
            </Button>
          </div>
        </CollapsiblePanel>
      </div>
      {showSearchResults && (
        <div className={styles.panel + " " + styles.national}>
          <div className={styles.panelTopBar}>
            <h4>Subscribers</h4>
          </div>
          <>
            <div className={`${styles.tableContainer} table-scroll`}>
              <table>
                <thead>
                  <tr>
                    {subscriberFields.map((field: any) => (
                      <th key={field.label}>{field.label}</th>
                    ))}
                    <th />
                  </tr>
                </thead>
                <tbody>
                  {!initialLoading
                    ? customers.map((item: any, i: number) => {
                        if (item === null) {
                          return (
                            <tr
                              style={{
                                visibility: "hidden",
                                pointerEvents: "none",
                              }}
                              key={`subscriber-filler-${i}`}
                            ></tr>
                          );
                        } else {
                          return (
                            <tr key={`subscriber-${item.mid}`}>
                              {subscriberFields.map((field: any) => (
                                <td key={`subscriber-${item.mid}-${field.key}`}>
                                  <div
                                    style={{
                                      display: "flex",
                                      justifyContent: "flex-start",
                                    }}
                                  >
                                    {formatDataItem(item, field.key)}
                                  </div>
                                </td>
                              ))}
                              <td className={styles.actionCell}>
                                <div className={styles.actionBox}>
                                  <span>
                                    <ProductsMenu
                                      direction="left"
                                      portal={true}
                                      data={{
                                        icon: <Gear />,
                                        items: getRowMenuItems(item),
                                      }}
                                    />
                                  </span>
                                  <span
                                    className={styles.viewRowBtn}
                                    style={{ cursor: "pointer" }}
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      setActiveSubscriber(item);
                                      setShowSubscriberModal(true);
                                    }}
                                  >
                                    View
                                  </span>
                                </div>
                              </td>
                            </tr>
                          );
                        }
                      })
                    : Array.from({ length: itemsPerPage }, (v, i) => i).map(
                        (i) => (
                          <UserSkeleton
                            key={"user-skeleton-" + i}
                            noOfStandard={9}
                          />
                        )
                      )}
                </tbody>
              </table>
            </div>
            <div style={{ marginTop: "16px" }}>
              <TableControl
                show
                itemsPerPage={itemsPerPage}
                setItemsPerPage={setItemsPerPage}
                currentPage={currentPage}
                setCurrentPage={setCurrentPage}
                numberOfPages={numberOfPages}
                label="customers"
                loading={initialLoading}
              />
            </div>
          </>
        </div>
      )}
    </>
  );
};

export default NationalCustomerPage;

const overviewStats = [
  { title: "Total Subscribers", value: "63,629" },
  { title: "Active Subscriptions", value: "63,629" },
];

const getRowMenuItems = (item: any) => {
  return [
    {
      label: "Edit Subscription Details",
      icon: <Pencil />,
      onClick: () => {},
    },
    {
      label: "Change MDN",
      icon: <Pencil />,
      onClick: () => {},
    },
    {
      label: "Change Address & MDN",
      icon: <Pencil />,
      onClick: () => {},
    },
    {
      label: "Change IMEI",
      icon: <Pencil />,
      onClick: () => {},
    },
    {
      label: "Change ICCID",
      icon: <Pencil />,
      onClick: () => {},
    },
    {
      label: "Change Subscription",
      icon: <SwapIcon />,
      onClick: () => {},
    },
    {
      label: "Change Bill Cycle",
      icon: <ReceiptIcon />,
      onClick: () => {},
    },
    {
      label: "Add Top-ups",
      icon: <Plus />,
      onClick: () => {},
    },
    {
      label: "Edit Features",
      icon: <Sparkle />,
      onClick: () => {},
    },
    {
      label: "Suspend Subscription",
      icon: <Pause />,
      onClick: () => {},
    },
    {
      label: "Cancel Subscription",
      icon: <Delete />,
      onClick: () => {},
    },
  ];
};
