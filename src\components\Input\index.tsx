/*
==========================================================================================

                                        Input

Description: Input component for use accross the whole app
             with label, input and inline errors

Parameters: label (str) - label for the input
            placeholder (str) - placeholder for the input
            value (str) - input value state
            onChange (func) - function to handle input change
            error (str) - inline error for input
            onKeyDown (func) - function to call when user 
                               presses enter while focus on input
            password (bool) - whether input is a password field
            forgotPassword (bool) - whether to show forgot password link, 
                                    only used for login page
            clear (func) - function to call when clear button is pressed
            disabled (bool) - whether input is disabled
            tooltip (string) - string to display on tooltip

==========================================================================================
*/

import { useState } from "react";
import styles from "./input.module.scss";
import { Collapse } from "@mui/material";
import { Info, InfoCircle2, Pencil } from "@/components/svgs";
import { Link } from "react-router-dom";

export const Input = ({
  label,
  placeholder,
  value,
  onChange,
  error,
  onKeyDown,
  password,
  clear,
  disabled,
  readonly,
  white,
  id = null,
  number,
  noClear,
  infoTooltipText,
  style,
  passwordLink,
  rightIcon,
  editable,
  editOnClick = () => {},
}: any) => {
  const [type, setType] = useState(
    number ? "number" : password ? "password" : "text"
  );
  const [show, setShow] = useState(false);

  // Function to prevent scroll behavior in number input
  const handleWheel = (e: React.WheelEvent<HTMLInputElement>) => {
    if (type === "number") {
      e.currentTarget.blur();
    }
  };

  // Handles change from password to text on 'eye' icon click
  const handleShowChange = () => {
    if (show) {
      setType("password");
      setShow(false);
    } else {
      setType("text");
      setShow(true);
    }
  };

  return (
    <form
      style={{ width: "100%", maxWidth: "320px", ...(style || {}) }}
      autoComplete="none"
      onKeyPress={(event: any) => {
        return event.keyCode != 13;
      }}
      onSubmit={(e) => {
        e.preventDefault();
      }}
    >
      <div className={styles.inputContainer}>
        {passwordLink && (
          <Link to="/forgot-password" style={{ textDecoration: "none" }}>
            Forgot Password?
          </Link>
        )}
        <div className={styles.label}>
          <span>{label}</span>
          <span style={{ color: "#838CA0" }}>
            {infoTooltipText && <InfoCircle2 />}
          </span>
        </div>
        <Collapse in={error ? true : false}>
          <p className={styles.errorText} id={`${id}-error`}>
            <Info /> {error || <br />}
          </p>
        </Collapse>
        <div className={styles.inputWrapper}>
          <input
            id={id}
            className={`${styles.input} ${readonly && styles.readonly} ${
              password && styles.password
            } ${error && styles.error} ${noClear && styles.noClear} ${
              number && styles.numberInput
            }`}
            value={value}
            placeholder={placeholder}
            onChange={(e: any) => {
              onChange(e);
            }}
            onWheel={handleWheel}
            onKeyDown={(event) => {
              if (event.key === "Enter") {
                event.preventDefault();
                onKeyDown();
              }
            }}
            type={type}
            disabled={disabled || readonly}
          />
          <div className={styles.iconsWrapper}>
            {!noClear && (
              <img
                src="/input_clear.svg"
                className={styles.clearIcon}
                onMouseDown={(e) => {
                  e.preventDefault();
                }}
                onClick={clear}
                style={{ right: password ? 45 : 20 }}
              />
            )}
            {editable && (
              <span onClick={() => editOnClick()}>
                <Pencil />
              </span>
            )}
            {rightIcon && <div className={styles.rightIcon}>{rightIcon}</div>}
            {password && (
              <img
                src={show ? "/password_show.svg" : "/password_hide.svg"}
                className={styles.eyeIcon}
                onClick={handleShowChange}
                onMouseDown={(e) => {
                  e.preventDefault();
                }}
              />
            )}
            {/*<Fade in={error ? true : false}>
            <img
              src="/input_error.svg"
              className={styles.errorIcon}
              onMouseDown={(e) => {
                e.preventDefault();
              }}
              style={{ right: password ? 45 : 20 }}
            />
          </Fade>*/}
          </div>
        </div>
      </div>
    </form>
  );
};
