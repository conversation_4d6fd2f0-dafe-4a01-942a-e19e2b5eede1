import Dialog from "@/components/Dialog";
import { FloppyDisk, PencilCircle } from "@/components/svgs";
import {
  clearInput,
  createStateObject,
  createStateObjectWithInitialState,
  handleInputChange,
  labels,
} from "@/components/utils/InputHandlers";
import { useState } from "react";
import styles from "./edit-global-credit-modal.module.scss";
import { Input } from "@/components/Input";
import SelectInput from "@/components/SelectInput";
import ToggleButtonGroup from "@/components/ToggleButtonGroup";

type EditGlobalCreditModalProps = {
  productData: any;
  open: boolean;
  onClose: () => void;
};

const EditGlobalCreditModal = ({
  open,
  onClose,
  productData,
}: EditGlobalCreditModalProps) => {
  const fields = ["creditAmount", "currency", "status"];
  const [formData, setFormData] = useState(
    createStateObjectWithInitialState(createStateObject(fields), {
      creditAmount: productData.creditAmount,
      currency: productData.currency,
      status: productData.status,
    })
  );

  return (
    <Dialog
      open={open}
      onClose={onClose}
      size="sm"
      headerIcon={<PencilCircle />}
      headerTitle="Edit Global Credit Product"
      confirmButtonText="Save Changes"
      confirmButtonOnClick={onClose}
      confirmButtonIcon={<FloppyDisk />}
      cancelButtonText="Cancel"
    >
      <div className={styles.formContainer}>
        {fields.map((field) => {
          if (field === "creditAmount") {
            return (
              <Input
                key={"product-" + field}
                label={labels[field]}
                value={formData[field]}
                onChange={(e: any) => {
                  handleInputChange(field, e, formData, setFormData);
                }}
                error={formData.errors[field]}
                clear={() => {
                  clearInput(field, setFormData);
                }}
                infoTooltipText
                number
              />
            );
          } else if (["status", "currency"].includes(field)) {
            return (
              <ToggleButtonGroup
                key={"product-" + field}
                selected={
                  field === "status"
                    ? formData[field]
                      ? "active"
                      : undefined
                    : formData[field]
                }
                options={toggleButtonGroupOptionsByField[field]}
                label={labels[field]}
                onChange={(value) => {
                  handleInputChange(
                    field,
                    { target: { value } },
                    formData,
                    setFormData
                  );
                }}
              />
            );
          }
          return null;
        })}
      </div>
    </Dialog>
  );
};

export default EditGlobalCreditModal;

const toggleButtonGroupOptionsByField: Record<string, any> = {
  currency: [
    {
      label: "GBP",
      key: "GBP",
    },
    {
      label: "EUR",
      key: "EUR",
    },
    {
      label: "USD",
      key: "USD",
    },
  ],
  status: [
    {
      label: "Active",
      key: "active",
    },
    {
      label: "Inactive",
      key: "inactive",
    },
  ],
};
