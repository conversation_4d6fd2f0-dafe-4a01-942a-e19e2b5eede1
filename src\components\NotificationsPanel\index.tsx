import { useRef } from 'react';
import styles from './notifications-panel.module.scss';
import { useClickOutside } from '@/hooks/useClickOutside';
import { CloseCircle } from '@/components/svgs';


interface NotificationsPanelProps {
  isOpen: boolean;
  onClose: () => void;
}

const NotificationsPanel = ({ isOpen, onClose }: NotificationsPanelProps) => {
  const notificationWidgetRef = useRef<HTMLDivElement>(null);

  useClickOutside(notificationWidgetRef, onClose,
    'click' // Using click event instead of mousedown to allow button handler to stop event propagation to prevent opening and then closing the notification widget on single click
  );

  if (!isOpen) return null;

  return (
    <div
      className={styles.notificationOverlay}
      ref={notificationWidgetRef}
    >
      <div className={styles.notificationHeader}>
        <h3>Notifications</h3>
        <button className={styles.markAsReadButton}>Mark all as read</button>
        <button onClick={onClose} className={styles.closeButton}>
          <CloseCircle />
        </button>
      </div>

      <div className={`${styles.notificationList} container-scroll`}>
        {getNotificationData(20).map((notification) => (
          <div key={notification.id} className={`${styles.notificationItem} ${notification.isRead ? styles.read : ''}`}>
            <h4 className={styles.title}>{notification.title}</h4>
            <p className={styles.description}>{notification.description}</p>
            <div className={styles.notificationFooter}>
              <span className={styles.date}>{notification.date}</span>

              <button
                className={styles.deleteButton}
              // onClick={() => handleDelete(notification.id)}
              >
                Delete
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default NotificationsPanel;


const getNotificationData = (count: number) => Array.from({ length: count }, (_, index) => (
  {
    id: index,
    title: 'Cras adipiscing nullam venenatis',
    description: 'Lacinia quis faucibus enim ut pulvinar eu luctus risus. Massa sed viverra odio eget nibh sed purus risus risus molestie lorem.',
    date: '10 June, 2024',
    // first two read, rest unread
    isRead: index < 2
  }))


