import {
    clearInput,
    handleInputChange,
    labels,
    streetDirectionValues,
  } from "../utils/InputHandlers";
import { Input } from "../Input";
import inputStyles from "../Input/input.module.scss"
import { plans, topUps } from "../utils/dataCreator";
import styles from "../AddSubscriberModal/AddSubscriberModal.module.scss";
import SelectInput from "../SelectInput";

const addressFields = ["streetNumber", "streetDirection", "streetName", "state", "city", "zipCode"];
export const AddressStep = ({data, setData}: { data:any, setData: Function }) => {
    return (
        <>
        { addressFields.map((field) => {
            if (field === "streetDirection") {
                return (
                    <form>
                        <div className={inputStyles.inputContainer} role="radiogroup">
                            <div className={inputStyles.label} style={{ marginBottom: '15px' }}>Street direction</div>
                            <div className={inputStyles.directionInput}>
                            {
                                streetDirectionValues.map((val:string) => (
                                    <span
                                        className={ styles.choicesBg + ' ' + (data[field] === val ? styles.selected : '')}
                                        key={val}
                                        role="radio"
                                        aria-checked={data[field] === val}
                                        tabIndex={0}
                                        style={{ marginBottom: '8px' }}
                                        onClick={(e: any) => {
                                            handleInputChange(field, e, data, setData);
                                        }}
                                        aria-labelledby="streetDireactionRadio">
                                            {val}
                                        </span>
                                    ))
                                }
                            </div>
                            </div>
                        </form>
                    )
            } else {
                return (
                    <Input
                        key={`${field}-input`}
                        label={labels[field]}
                        value={data[field]}
                        onChange={(e: any) => {
                            handleInputChange(field, e, data, setData);
                        }}
                        error={data.errors[field]}
                        clear={() => {
                            clearInput(field, setData);
                        }}
                        infoTooltipText
                    />
                )
            }
        })}
        </>
    )
}

export const InputFieldsStep = ({data, setData, fields}: { data:any, setData: Function, fields: string[] }) => {
    return (
        <>
            {fields.map((field) => {
                if (field === "streetDirection") {
                    return (
                        <form>
                            <div className={inputStyles.inputContainer} role="radiogroup">
                                <div className={inputStyles.label} style={{ marginBottom: '15px' }}>Street direction</div>
                                    {
                                        streetDirectionValues.map((val:string) => (
                                            <span
                                                className={ styles.choicesBg + ' ' + (data[field] === val ? styles.selected : '')}
                                                key={val}
                                                role="radio"
                                                aria-checked={data[field] === val}
                                                tabIndex={0}
                                                style={{ marginBottom: '8px' }}
                                                onClick={(e: any) => {
                                                    handleInputChange(field, e, data, setData);
                                                }}
                                                aria-labelledby="streetDireactionRadio">
                                                    {val}
                                            </span>
                                        ))
                                     }
                            </div>
                        </form>
                    )
                } else if (field === 'channel') {
                    return (
                        <SelectInput
                            options={['General', 'Other Channel']}
                            label={labels[field]}
                            value={data[field]}
                            onChange={(e: any) => {
                                handleInputChange(field, e, data, setData);
                            }}
                        />
                    )
                } else {
                    return (
                        <Input
                            key={`${field}-input`}
                            label={labels[field]}
                            value={data[field]}
                            onChange={(e: any) => {
                                handleInputChange(field, e, data, setData);
                            }}
                            error={data.errors[field]}
                            clear={() => {
                                clearInput(field, setData);
                            }}
                            infoTooltipText
                        />
                    )
                }
            })}
        </>
    )
}

export const ChoicesStep = ({fieldName, data, setData, title, choices}: {fieldName: string, data:any, setData: Function, title: string, choices: string[] }) => {
    const handleState = (val:string) => {
        setData((prevState:any) => ({
            ...prevState,
            [fieldName]: val,
          }))
    }

    return (
        <>
            <p>{title}</p>
            <div className="flex">
                {
                    choices.map((choice:string) => (
                        <span key={choice} className={styles.choicesBg + ' ' + (data[fieldName] === choice ? styles.selected : '')} onClick={() => handleState(choice)}>{ choice }</span>
                    ))
                }
            </div>
        </>
    )
}

export const PlansStep = ({data, setData, type}: { data:any, setData: Function, type?: 'plans' | 'topups' }) => { 
    let selectedData = []
    if (type === 'topups') {
        selectedData = [...topUps]
    } else {
        selectedData = [...plans]
    }

    return (
        <>
            {
                selectedData.map((item: string, index: number) => {
                    const handleClick = () => {
                        handleInputChange('plan', { target: { innerText: item } }, data, setData);
                    }

                    return (
                    <div className={styles.radioPlans} key={index}>
                            <div
                                className={styles.choicesBg + ' flex'}
                                onClick={handleClick}
                                tabIndex={0}
                                role="radio"
                                aria-checked={data['plan'] === item}
                                onKeyDown={(e: any) => {
                                    if (e.key === 'Enter' || e.key === ' ') {
                                        e.preventDefault();
                                        handleClick();
                                    }
                                }}
                            >
                                <span
                                    style={{ border: data['plan'] === item ? '5px solid #2E70E5' : '' }}
                                >
                                {item}
                            </span>
                            <div>
                                <h5>{item}</h5>
                                <p>Tamet consectetur. Urna mi eros eleifend non accumsan neque. Nulla egestas ut pretium posuere tortor amet. Variu</p>
                            </div>
                        </div>
                    </div>
                    )
                })
            }
        </>
    )
}

export const ImeiField = ({data, setData, newImei, disabled }: { data:any, setData: Function, disabled?: boolean, newImei?:boolean }) => {
    let fieldName = 'imei'
    if (newImei) {
        fieldName = 'newImei'
        console.log(fieldName)
    }

    return (
        <>
            <Input
                key={`${fieldName}-input`}
                label={labels[fieldName]}
                value={data[fieldName]}
                onChange={(e: any) => {
                    handleInputChange(fieldName, e, data, setData);
                }}
                error={data.errors[fieldName]}
                clear={() => {
                    clearInput(fieldName, setData);
                }}
                disabled={disabled}
                infoTooltipText
            />
        </>
    )
}