@use "../../styles/theme.scss" as *;

.button {
  height: 40px;
  width: 100%;
  max-width: 40px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid #bfc4ce;
  background: none;
  &.noOutline,
  &.white {
    border: none;
  }
  svg {
    width: 24px;
    height: 24px;
    color: #2e70e5;
  }
  &.white {
    &:hover {
      background-color: rgba(200, 200, 200, 0.3);
    }
    svg {
      color: #fff;
    }
  }
  &:disabled {
    pointer-events: none;
    opacity: 0.5;
  }
  &:hover {
    background-color: #ebebeb;
  }
}
