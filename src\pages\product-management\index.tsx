import SwitchBar from "@/components/SwitchBar";
import Title from "@/components/Title";
import { useState } from "react";
import styles from "@/styles/product-management.module.scss";
import ATTProductManagementTab from "@/pages/product-management/att-tab";
import HutchProductManagementTab from "@/pages/product-management/hutch-tab";
import VoipProductManagementTab from "@/pages/product-management/voip-tab";
import { useSelector } from "react-redux";

const ProductManagement = () => {
  const { productType, userInfo } = useSelector((state: any) => state);
  const [activeTabId, setActiveTabId] = useState(
    productType === "voip-esim" || productType === "traveller-esim"
      ? "hutch"
      : "att"
  );

  return (
    <>
      <Title>Product Management</Title>
      {(productType === "mvne" || productType === "voip-esim") && (
        <SwitchBar
          options={
            productType === "mvne"
              ? [
                  {
                    label:
                      userInfo?.brandName === "Mobilise Communication"
                        ? "MTN"
                        : "AT&T",
                    id: "att",
                  },
                  {
                    label: "Hutch",
                    id: "hutch",
                  },
                  {
                    label: "VoIP",
                    id: "voip",
                  },
                ]
              : [
                  {
                    label: "Hutch",
                    id: "hutch",
                  },
                  {
                    label: "VoIP",
                    id: "voip",
                  },
                ]
          }
          selected={activeTabId}
          setSelected={setActiveTabId}
          layoutId="product-type-switch"
        />
      )}

      <div className={styles.main}>
        {activeTabId === "att" && <ATTProductManagementTab />}
        {activeTabId === "hutch" && <HutchProductManagementTab />}
        {activeTabId === "voip" && <VoipProductManagementTab />}
      </div>
    </>
  );
};

export default ProductManagement;
