import styles from "./user-skeleton.module.scss";
import Shimmer from "../Shimmer";

const UserSkeleton = ({ noOfStandard, showActionBar }: any) => {
  return (
    <tr className={styles.main}>
      {Array.from({ length: noOfStandard }, (v, i) => i).map((i) => (
        <td key={`skel-item-${i}`}>
          <div className={styles.box}>
            <Shimmer />
          </div>
        </td>
      ))}
      {showActionBar && (
        <td>
          <div className={styles.dataBar}>
            <div className={styles.smallBox}>
              <Shimmer />
            </div>
            <div className={styles.smallBox}>
              <Shimmer />
            </div>
            <div className={styles.smallBox}>
              <Shimmer />
            </div>
          </div>
        </td>
      )}
    </tr>
  );
};

export default UserSkeleton;
