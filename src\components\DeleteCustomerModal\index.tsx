import styles from "./delete-customer.module.scss";
import Modal from "../Modal";
import { Delete } from "../svgs";
import { useState } from "react";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { ApiDelete } from "../../pages/api/api";

const DeleteCustomerModal = ({ show, setShow, user, handleDelete }: any) => {
  const dispatch = useDispatch();

  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const deleteUser = () => {
    setLoading(true);
    ApiDelete(`/agent/delete-user`, { mid: user.mid })
      .then((response) => {
        setLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: false,
            message: `${user && user.firstName} deleted.`,
          },
        });
        navigate("/customer-management");
      })
      .catch((error) => {
        setLoading(false);
      });
  };

  return (
    <Modal
      saveButton={
        <>
          <Delete />
          Delete Account
        </>
      }
      image="/delete_user_graphic.svg"
      show={show}
      close={() => {
        setShow(false);
      }}
      proceed={deleteUser}
      loading={loading}
    >
      <div className={styles.main}>
        <h3>
          Delete {user?.firstName} {user?.lastName} Account ?
        </h3>
      </div>
    </Modal>
  );
};

export default DeleteCustomerModal;
