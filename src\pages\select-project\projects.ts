export const dummyProjects = [
  {
    id: 3,
    name: "<PERSON><PERSON><PERSON>",
    brandLogo: "new-mobilise-logo.svg",
    type: "MVNE",
    services: "SIM, eSIM, VoIP",
    providers: "AT&T, Hutch, DIDWW, Manx, BICS",
    mvnos: [
      {
        id: 1,
        name: "Orbit",
        brandLogo: "/orbit_logo.svg",
        type: "MVNO",
        services: "eSIM",
        providers: "BICS",
        productType: "traveller-esim",
      },
      {
        id: 2,
        name: "Smartroam",
        brandLogo: "/smartroam.png",
        type: "MVNO",
        services: "eSIM",
        providers: "Manx",
        productType: "traveller-esim",
      },
    ],
  },
  {
    id: 1,
    name: "Airespring",
    brandLogo: "AireSpring-Logo.png",
    type: "MVNE",
    services: "SIM, eSIM, VoIP",
    providers: "AT&T, Hutch, DIDWW",
    mvnos: [
      {
        id: 1,
        name: "<PERSON><PERSON><PERSON>",
        brandLogo: "/AireSpring-Logo.png",
        type: "MVNO",
        services: "SIM, eSIM, VoIP",
        providers: "AT&T, Hutch, DIDWW",
        productType: "mvne",
      },
      {
        id: 4,
        name: "FaithWireless",
        brandLogo: "/faithWireless_logo.png",
        type: "MVNO",
        services: "SIM, eSIM",
        providers: "AT&T",
        productType: "us-mvno",
      },
      {
        id: 4,
        name: "Call.net",
        brandLogo: "/Callnet.png",
        type: "MVNO",
        services: "SIM, eSIM",
        providers: "AT&T",
        productType: "us-mvno",
      },
      {
        id: 5,
        name: "Chrip",
        brandLogo: "/chirpMobileLogo.png",
        type: "MVNO",
        services: "SIM, eSIM",
        providers: "AT&T",
        productType: "us-mvno",
      },
      {
        id: 6,
        name: "WestBold",
        brandLogo: "/WestboldLogo.png",
        type: "MVNO",
        services: "SIM, eSIM",
        providers: "AT&T",
        productType: "us-mvno",
      },
      {
        id: 7,
        name: "S-Mobile",
        brandLogo: "/S-mobilelogo.jpg",
        type: "MVNO",
        services: "SIM, eSIM",
        providers: "AT&T",
        productType: "us-mvno",
      },
      {
        id: 8,
        name: "AYO",
        brandLogo: "/AYO_Logo.jpg",
        type: "MVNO",
        services: "SIM, eSIM",
        providers: "AT&T",
        productType: "us-mvno",
      },
      {
        id: 9,
        name: "SkyWire",
        brandLogo: "/Skywire-logo.png",
        type: "MVNO",
        services: "SIM, eSIM",
        providers: "AT&T",
        productType: "us-mvno",
      },
      {
        id: 10,
        name: "RAZA",
        brandLogo: "/Raza_logo.png",
        type: "MVNO",
        services: "SIM, eSIM",
        providers: "AT&T",
        productType: "us-mvno",
      },
      {
        id: 11,
        name: "Activate Wireless",
        brandLogo: "/activate_wireless_logo.jpeg",
        type: "MVNO",
        services: "SIM, eSIM",
        providers: "AT&T",
        productType: "us-mvno",
      },
      {
        id: 12,
        name: "PREVI",
        brandLogo: "/PreviLogo.png",
        type: "MVNO",
        services: "SIM, eSIM",
        providers: "AT&T",
        productType: "us-mvno",
      },
      {
        id: 13,
        name: "Proper Connections",
        brandLogo: "/proper-mobile.png",
        type: "MVNO",
        services: "SIM, eSIM",
        providers: "AT&T",
        productType: "us-mvno",
      },
      {
        id: 14,
        name: "Black Wireless",
        brandLogo: "/BlackWireless.png",
        type: "MVNO",
        services: "SIM, eSIM",
        providers: "AT&T",
        productType: "us-mvno",
      },
      {
        id: 15,
        name: "Ring Mobile",
        brandLogo: "/ringMobile.jpg",
        type: "MVNO",
        services: "SIM, eSIM",
        providers: "AT&T",
        productType: "us-mvno",
      },
      {
        id: 16,
        name: "Werks Mobile",
        brandLogo: "/werks.jpg",
        type: "MVNO",
        services: "SIM, eSIM",
        providers: "AT&T",
        productType: "us-mvno",
      },
      {
        id: 17,
        name: "Fast Net",
        brandLogo: "/fastnet_logo.png",
        type: "MVNO",
        services: "SIM, eSIM",
        providers: "AT&T",
        productType: "us-mvno",
      },
      {
        id: 18,
        name: "iCatholic",
        brandLogo: "/iCatholic_logo.png",
        type: "MVNO",
        services: "SIM, eSIM",
        providers: "AT&T",
        productType: "us-mvno",
      },
      {
        id: 19,
        name: "EZ Mobile",
        brandLogo: "/ezmobile_logo.jpg",
        type: "MVNO",
        services: "SIM, eSIM",
        providers: "AT&T",
        productType: "us-mvno",
      },
    ],
  },
  {
    id: 2,
    name: "Mobilise Communication",
    brandLogo: "Mobilise Communication.svg",
    type: "MVNE",
    services: "SIM, eSIM, VoIP",
    providers: "MTN, Hutch, DIDWW",
    mvnos: [
      {
        id: 1,
        name: "Mobilise Communication",
        brandLogo: "/Mobilise Communication.svg",
        type: "MVNO",
        services: "SIM, eSIM, VoIP",
        providers: "MTN, Hutch, DIDWW",
        productType: "mvne",
      },
      {
        id: 2,
        name: "Gist Mobile",
        brandLogo: "/gist_logo.png",
        type: "MVNO",
        services: "eSIM, VoIP",
        providers: "Hutch, DIDWW",
        productType: "voip-esim",
      },
    ],
  },
  {
    id: 4,
    name: "Telecall",
    brandLogo: "telecallBg.png",
    type: "MVNE",
    services: "SIM, eSIM, VoIP",
    providers: "AT&T, Hutch, DIDWW",
    mvnos: [
      {
        id: 1,
        name: "Telecall",
        brandLogo: "/telecallBg.png",
        type: "MVNO",
        services: "SIM, eSIM, VoIP",
        providers: "AT&T, Hutch, DIDWW",
        productType: "mvne",
      },
      {
        id: 4,
        name: "Trill Mobile",
        brandLogo: "/trill_logo.png",
        type: "MVNO",
        services: "SIM, eSIM",
        providers: "AT&T",
        productType: "us-mvno",
      },
    ],
  },
];
