import styles from "./edit-user.module.scss";
import Modal from "../Modal";
import { FloppyDisk } from "../svgs";
import { Input } from "../Input";
import { useEffect, useState } from "react";
import { validateAll } from "indicative/src/Validator";
import { useDispatch } from "react-redux";
import { ApiPatch } from "../../pages/api/api";
import {
  clearInput,
  createStateObject,
  displayErrors,
  getMessages,
  getRules,
  handleInputChange,
  labels,
  placeholders,
} from "../utils/InputHandlers";

const fields = ["firstName", "lastName"];
const rules = getRules(fields);
const messages = getMessages(fields);

const EditProfile = ({ show, setShow, user, repopulateUser }: any) => {
  console.log(user);
  const dispatch = useDispatch();

  const [data, setData] = useState(createStateObject(fields, " "));

  // Populate with current user's data
  useEffect(() => {
    if (user && show) {
      setData({
        ...data,
        firstName: user.firstName,
        lastName: user.lastName,
      });
    }
  }, [user, show]);

  // Reset modal data when closed
  const reset = () => {
    setTimeout(() => {
      setData(createStateObject(fields, " "));
    }, 300);
    setLoading(false);
    setShow(false);
  };

  // Handles creation of new user
  const editUser = () => {
    const testData = {
      firstName: data.firstName.trim(),
      lastName: data.lastName.trim(),
    };

    validateAll(testData, rules, messages)
      .then((response) => {
        setLoading(true);
        ApiPatch("/agent/edit/profile", {
          firstName: testData.firstName,
          lastName: testData.lastName,
        })
          .then((response) => {
            repopulateUser();
            setLoading(false);
            setShow(false);
            dispatch({
              type: "notify",
              payload: {
                error: false,
                message: response.data.message,
              },
            });
          })
          .catch((error) => {
            setLoading(false);
            dispatch({
              type: "notify",
              payload: {
                error: true,
                message: error.response.data.message,
              },
            });
          });
      })
      .catch((errors) => {
        displayErrors(errors, setData);
      });
  };

  const [loading, setLoading] = useState(false);

  return (
    <Modal
      saveButton={
        <>
          <FloppyDisk />
          Save Changes
        </>
      }
      image="/edit_user_graphic.svg"
      show={show}
      setShow={setShow}
      proceed={editUser}
      close={reset}
      loading={loading}
    >
      <div className={`${styles.main} normal-select-input`}>
        <h3>Edit Details</h3>
        {fields.map((prop) => (
          <Input
            key={`edit-user-${prop}`}
            label={labels[prop]}
            placeholder={placeholders[prop]}
            value={data[prop]}
            onChange={(e: any) => {
              handleInputChange(prop, e, data, setData);
            }}
            error={data.errors[prop]}
            onKeyDown={editUser}
            clear={() => {
              clearInput(prop, setData);
            }}
            disabled={loading}
            white
          />
        ))}
      </div>
    </Modal>
  );
};

export default EditProfile;
