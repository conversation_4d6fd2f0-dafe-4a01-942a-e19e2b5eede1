@use "./../../../../styles/theme.scss" as *;
@import "./../../../../styles/mixins.module.scss";
@import "./../../../../styles/table-mixin.module.scss";

.searchPanelForm {
  @include searchPanelForm
}

.panel {
  @include panel;
  @include table;

  margin-top: 16px;
}

.tableContainer {
  overflow: auto;
  padding-bottom: 5px;
  margin-top: 20px;
  border-radius: 16px;

  tr:hover .tableRowActions {
    opacity: 100   !important;
  }

  .tableRowActions {
    opacity: 0;
    display: flex;
    align-items: center;
    color: var(--primary-500);

    .viewRowBtn {
      color: var(--button-tertiary-text);
      font-size: 14px;
      font-weight: 700;
      text-decoration: underline;
      cursor: pointer;
    }

    span {
      width: 40px;
      height: 40px;
      display: flex;
      justify-content: center;
      align-items: center;
      color: var(--primary-500);
      cursor: pointer;

      &:hover {
        color: var(--primary-800);
      }
    }
  }
}