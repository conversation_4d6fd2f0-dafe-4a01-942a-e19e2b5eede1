@use "../../styles/theme.scss" as *;

.container {
  width: 100%;
  border-radius: 12px;
  border: 0.5px solid rgba(0, 0, 0, 0.3);
  background: #fdfcfb;
  box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.05);
  cursor: pointer;
  overflow: hidden;
  &.selected {
    border: 3px solid $orange;
    cursor: auto;
    .data {
      padding: 13.5px;
      padding-bottom: 16px;
    }
    .price {
      padding: 9.5px 21.5px;
      padding-top: 12px;
    }
  }
}

.data {
  padding: 16px;
}

.item {
  display: flex;
  align-items: center;
  font-size: 12px;
  margin-bottom: 16px;
  &:last-of-type {
    margin-bottom: 0px;
  }
  svg {
    margin-right: 8px;
    vertical-align: middle;
    width: 24px;
    height: 24px;
  }
}

.price {
  width: 100%;
  background-color: $orange;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 12px 24px;
  font-size: 14px;
  line-height: 21px;
  font-weight: 600;
}
