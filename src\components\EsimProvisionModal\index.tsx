import styles from "./esim-provision.module.scss";
import Modal from "../Modal";
import { FloppyDisk } from "../svgs";
import { Input } from "../Input";
import SelectInput from "../SelectInput";
import { useEffect, useState } from "react";
import { validateAll } from "indicative/src/Validator";
import { useDispatch } from "react-redux";
import { ApiPatch, ApiPostAuth } from "../../pages/api/api";
import Toggle from "../Toggle";
import {
  clearInput,
  createStateObject,
  displayErrors,
  handleInputChange,
  labels,
  placeholders,
} from "../utils/InputHandlers";
import StatusSwitch from "../StatusSwitch";
import CodecsSwitch from "../CodecsSwitch";

const EsimProvisionModal = ({
  show,
  setShow,
  provider,
  resetActiveUser,
  page,
  repopulateUsers,
}: any) => {
  const dispatch = useDispatch();
  const [PageState, setPageState] = useState<string[]>([]);

  console.log("pagessss ", page);
  console.log("provider", provider);

  let providerinputs = ["provisionAmount"];
  //   let ratesinputes = ["buyRate", "sellRate"];

  const InputsChange = () => {
    console.log("inputChange");
    setPageState(providerinputs);
  };

  console.log("pagestates ", PageState);

  const [data, setData] = useState(createStateObject(providerinputs));
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    InputsChange();
  }, [page]);

  // Populate with current data
  useEffect(() => {
    if (provider && show) {
      setData({
        ...data,
        provisionAmount: provider.provisionAmount,
      });
    }
  }, [provider, show]);

  // Reset modal data when closed
  const reset = () => {
    setTimeout(() => {
      setData(createStateObject(providerinputs));
    }, 300);
    setLoading(false);
    setShow(false);
  };

  // Handles creation of new user
  const editUser = () => {
    const testData = {
      provisionAmount: data.provisionAmount.trim(),
    };
    console.log("testData", testData);

    // *Todo: We will validate the data after getting API's

    // validateAll(testData, rules, messages)
    //   .then((response) => {
    //     setLoading(true);

    //     // ApiPatch("/users/edit", {
    //     //   mode: testData.mode,
    //     //   inboundIP: testData.inboundIP,
    //     //   outboundIP: testData.outboundIP,
    //     // })
    //     //   .then((response) => {
    //     //     repopulateUsers();
    //     //     setLoading(false);
    //     //     setShow(false);
    //     //     dispatch({
    //     //       type: "notify",
    //     //       payload: {
    //     //         error: false,
    //     //         message: response.data.message,
    //     //       },
    //     //     });
    //     //   })
    //     //   .catch((error) => {
    //     //     setLoading(false);
    //     //     dispatch({
    //     //       type: "notify",
    //     //       payload: {
    //     //         error: true,
    //     //         message: error.response.data.message,
    //     //       },
    //     //     });
    //     //   });
    //   })
    //   .catch((errors) => {
    //     displayErrors(errors, setData);
    //   });
  };

  return (
    <Modal
      saveButton={<>Provision eSIM Batch</>}
      image="/bulk_edit_confirm_graphic.svg"
      show={show}
      setShow={setShow}
      proceed={editUser}
      close={setShow}
      // onClose={() => setShow(false)}
      loading={loading}
    >
      <div className={`${styles.main} `}>
        <h3>Provision eSIM Batch</h3>

        {PageState.map((prop: any) => (
          <>
            {console.log("props", prop)}
            <div className={styles.modalContent}>
              <div>
                <p>
                  {" "}
                  {!provider?.esimsProvisioned ? null : (
                    <>Available for provisioning:</>
                  )}
                </p>
                <p>
                  {" "}
                  {!provider?.availableEsimsProvisioned ? null : (
                    <>eSIMs in provisioned pool:</>
                  )}
                </p>
              </div>
              <div>
                <p>{provider?.esimsProvisioned}</p>
                <p>{provider?.availableEsimsProvisioned}</p>
              </div>
            </div>
            <Input
              key="provisionAmount"
              label="Provision Amount"
              placeholder={placeholders[prop]}
              value={data[prop]}
              onChange={(e: any) => {
                handleInputChange(prop, e, data, setData);
              }}
              error={data.errors[prop]}
              onKeyDown={editUser}
              clear={() => {
                clearInput(prop, setData);
              }}
              disabled={loading}
              white
            />
          </>
        ))}
      </div>
    </Modal>
  );
};

export default EsimProvisionModal;
