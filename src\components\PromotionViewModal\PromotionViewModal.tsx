import { motion } from "framer-motion";
import styles from "./promotionv-view.module.scss";
import BlobSwitchBar from "../BlobSwitchBar";
import { useEffect, useState } from "react";
import UserSkeleton from "@/components/UserSkeleton";
import Button from "../Button";
import {
  ArrowsClockwise,
  Calendar,
  Calendar12,
  CalendarTick,
  Close,
  Coin,
  CreditCard,
  Delete,
  Export,
  Gift,
  PercentCircle,
  PercentSign,
  Receipt,
} from "../svgs";
import {
  activityFields,
  promotionsHistoryFields,
  detailsIcons,
} from "@/pages/promotions/promotionsFields";
import { formatDateWithTime } from "../utils/formatDate";
import { getPromotionHistory, getPromotionLogs } from "../utils/dataCreator";
import TableControl from "../TableControl";
import StatusPill from "../StatusPill";
import { labels } from "../utils/InputHandlers";
import AddEditPromotionModal from "../AddEditPromotionModal/AddEditPromotionModal";
import DeletePromotionModal from "../DeletePromotionModal/DeletePromotionModal";

const detailsFields = [
  "type",
  "feeDiscount",
  "promocode",
  "coderesuable",
  "amount",
  "applyTo",
  "timesUsed",
  "creationDate",
  "expiryDate",
];

const icons = {
  type: <PercentSign />,
  feeDiscount: <Receipt />,
  promocode: <Gift />,
  coderesuable: <ArrowsClockwise />,
  amount: <Coin />,
  applyTo: <CreditCard />,
  timesUsed: <CreditCard />,
  creationDate: <CalendarTick />,
  expiryDate: <Calendar12 />,
} as any;

const PromotionViewModal = ({
  show,
  promotion,
  type,
  close,
}: {
  show: boolean;
  promotion: any;
  type: string;
  close: Function;
}) => {
  const [mainSelected, setMainSelected] = useState("overview");
  const [fields, setFields] = useState(promotionsHistoryFields);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [loading, setLoading] = useState(true);
  const [promotionModalMode, setPromotionModalMode] = useState<
    "" | "edit" | "delete"
  >("");
  const [numberOfPages, setNumberOfPages] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [rows, setRows] = useState([]);

  useEffect(() => {
    if (mainSelected === "history") {
      setFields(promotionsHistoryFields);
      populate(getPromotionHistory(itemsPerPage));
    } else if (mainSelected === "logs") {
      setFields(activityFields);
      populate(getPromotionLogs(itemsPerPage));
    }
  }, [mainSelected, currentPage]);

  const populate = (data: any) => {
    setLoading(true);
    setRows([]);
    setTimeout(() => {
      setRows(data);
      setLoading(false);
    }, 500);
  };

  const sections = [
    {
      id: "overview",
      label: "Overview",
    },
    {
      id: "history",
      label: "Usage History",
    },
    {
      id: "logs",
      label: "Activity Logs",
    },
  ];

  const formatDataItem = (item: any, key: string) => {
    if (key === "date") {
      return formatDateWithTime(item[key]);
    } else {
      return item[key];
    }
  };

  const formatOverviewData = (key: string) => {
    if (key === "creationDate" || key === "expiryDate") {
      return formatDateWithTime(promotion[key]);
    } else if (key === "promocode") {
      return promotion["code"];
    } else if (key === "amount") {
      return "$" + promotion[key];
    } else if (key === "feeDiscount") {
      return "Regulatory";
    } else {
      return promotion[key];
    }
  };

  return (
    <>
      {promotion && promotionModalMode === "edit" && (
        <AddEditPromotionModal
          show={promotionModalMode}
          promotion={promotion}
          setShow={() => setPromotionModalMode("")}
          repopulate={() => populate(15)}
        />
      )}
      {promotionModalMode === "delete" && (
        <DeletePromotionModal
          show={promotionModalMode}
          promotion={promotion}
          setShow={() => setPromotionModalMode("")}
          repopulate={() => populate(15)}
        />
      )}
      <motion.div
        className={styles.container}
        key="promotion-modal"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
      >
        <div className={styles.modal}>
          <div className={styles.title}>{promotion.name}</div>
          <div className={styles.close} onClick={() => close()}>
            <Close />
          </div>
          <div className={styles.menuBar}>
            <BlobSwitchBar
              options={sections}
              selected={mainSelected}
              setSelected={setMainSelected}
              layoutId="promotion-main-selection"
            />
            <div className={styles.actionButtons}>
              {mainSelected === "overview" ? (
                <>
                  <Button
                    style={{ marginRight: "10px" }}
                    color="customerActionBlue"
                    onClick={() => setPromotionModalMode("edit")}
                  >
                    Edit Promotion
                  </Button>
                  <Button
                    color="customerActionRed"
                    onClick={() => setPromotionModalMode("delete")}
                  >
                    <Delete /> Delete Promotion
                  </Button>
                </>
              ) : (
                <Button color="customerActionBlue">
                  <Export /> Export to CSV
                </Button>
              )}
            </div>
          </div>
          <div>
            {mainSelected === "overview" ? (
              <div className={styles.overview}>
                <div
                  className={`flex flex-justify-content-between ${styles.head}`}
                >
                  <StatusPill status={promotion.status === "Active"} />
                  <div>
                    <p>
                      Creation Date:{" "}
                      {formatDateWithTime(promotion.creationDate)}
                    </p>
                    <p>
                      Last Modified: {formatDateWithTime(promotion.expiryDate)}
                    </p>
                  </div>
                </div>
                <div className={styles.detailsContainer}>
                  <p className={styles.title}>Promotion details</p>
                  <div className={styles.details}>
                    {detailsFields.map((key: string) => {
                      if (
                        key === "feeDiscount" &&
                        promotion["type"] !== "Fee Discount"
                      ) {
                        return null;
                      } else {
                        return (
                          <div key={key} className={styles.feature}>
                            <div className={styles.icon}>{icons[key]}</div>
                            <div className={styles.data}>
                              <p className={styles.title}>
                                {labels[key] === "Created on"
                                  ? "Start Date"
                                  : labels[key]}
                              </p>
                              <p className={styles.value}>
                                {formatOverviewData(key)}
                              </p>
                            </div>
                          </div>
                        );
                      }
                    })}
                  </div>
                </div>
              </div>
            ) : (
              <>
                <div className={`${styles.tableContainer} table-scroll`}>
                  <table>
                    <thead>
                      <tr>
                        {fields.map((field: any) => (
                          <th>{field.label}</th>
                        ))}
                      </tr>
                    </thead>
                    <tbody>
                      {!loading
                        ? rows.map((item: any, i: number) => {
                            return (
                              <tr
                                key={`promo-${i}`}
                                style={{ cursor: "pointer" }}
                              >
                                {fields.map((field: any) => (
                                  <td key={`promotion-${i}-${field.key}`}>
                                    <div
                                      style={{
                                        display: "flex",
                                        justifyContent: "flex-start",
                                      }}
                                    >
                                      {formatDataItem(item, field.key)}
                                    </div>
                                  </td>
                                ))}
                              </tr>
                            );
                          })
                        : Array.from({ length: itemsPerPage }, (v, i) => i).map(
                            (i) => (
                              <UserSkeleton
                                key={"order-skeleton-" + i}
                                noOfStandard={8}
                              />
                            )
                          )}
                    </tbody>
                  </table>
                </div>
                <div style={{ marginTop: "16px" }}>
                  <TableControl
                    show
                    itemsPerPage={itemsPerPage}
                    setItemsPerPage={(val: any) => setItemsPerPage(val)}
                    currentPage={currentPage}
                    setCurrentPage={setCurrentPage}
                    numberOfPages={numberOfPages}
                    label="history-logs"
                    loading={loading}
                  />
                </div>
              </>
            )}
          </div>
        </div>
      </motion.div>
    </>
  );
};

export default PromotionViewModal;
