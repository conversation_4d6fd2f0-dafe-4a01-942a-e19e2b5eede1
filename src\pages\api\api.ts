import axios, { CancelTokenSource } from "axios";

const queryCors = import.meta.env.VITE_APP_BE_CRM;

const headers = {
  Accept: "application/json",
  apikey: import.meta.env.VITE_APP_BE_CRM_APIKEY,
  companycode: import.meta.env.VITE_APP_BE_CRM_COMCOD,
  operatingsystem: "crm",
  version: "",
};

export const ApiPostAuth = (
  url: string,
  parameters?: { [propName: string]: any },
  cancelToken?: CancelTokenSource
) =>
  axios.post(queryCors + url, parameters, {
    headers: {
      Authorization: `Bearer ${localStorage.getItem("token") || ""}`,
      ...headers,
    },
    cancelToken: cancelToken?.token,
  });

export const ApiFirstPostAuth = (
  url: string,
  parameters?: { [propName: string]: any },
  Authorization?: string,
  cancelToken?: CancelTokenSource
) =>
  axios.post(queryCors + url, parameters, {
    headers: {
      Authorization: `Bearer ${Authorization || ""}`,
      ...headers,
    },
    cancelToken: cancelToken?.token,
  });

export const ApiPost = (
  url: string,
  parameters: { [propName: string]: any },
  cancelToken?: CancelTokenSource
) =>
  axios.post(queryCors + url, parameters, {
    headers: headers,
    cancelToken: cancelToken?.token,
  });

export const ApiPut = (
  url: string,
  parameters?: { [propName: string]: any },
  cancelToken?: CancelTokenSource
) =>
  axios.put(queryCors + url, parameters, {
    headers: {
      Authorization: `Bearer ${localStorage.getItem("token") || ""}`,
      ...headers,
    },
    cancelToken: cancelToken?.token,
  });

export const ApiPatch = (
  url: string,
  parameters?: { [propName: string]: any },
  cancelToken?: CancelTokenSource
) =>
  axios.patch(queryCors + url, parameters, {
    headers: {
      Authorization: `Bearer ${localStorage.getItem("token") || ""}`,
      ...headers,
    },
    cancelToken: cancelToken?.token,
  });

export const ApiGet = (url: string, signal?: any) =>
  axios.get(queryCors + url, {
    headers: {
      Authorization: `Bearer ${localStorage.getItem("token") || ""}`,
      ...headers,
    },
    signal: signal,
  });

export const ApiGetActivity = (id: any) =>
  axios.get(queryCors + "/activity/mvno", {
    headers: {
      Authorization: `Bearer ${localStorage.getItem("token") || ""}`,
      mvnoId: id,
      ...headers,
    },
  });

export const ApiGetNoAuth = (url: string, cancelToken?: CancelTokenSource) =>
  axios.get(queryCors + url, {
    headers: headers,
    cancelToken: cancelToken?.token,
  });

export const ApiDelete = (
  url: string,
  parameters: { [propName: string]: any }
) =>
  axios.request({
    url: queryCors + url,
    method: "delete",
    headers: {
      Authorization: `Bearer ${localStorage.getItem("token") || ""}`,
      ...headers,
    },
    data: parameters,
  });
