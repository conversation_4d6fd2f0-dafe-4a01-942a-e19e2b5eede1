import { InfoCircle, InfoCircle2 } from "../svgs";
import styles from "./status-pill.module.scss";

export const statuses = [
  "Inactive",
  "Active",
  "Suspended",
  "TBS",
  "Cancelled",
  "No Subscription",
];

const StatusPill = ({
  status,
  hover,
  customStatus,
  styling,
  click,
  icon,
  text,
  color,
  additionalText,
}: any) => {
  return (
    <div
      onClick={() => click()}
      className={`${styles.main} ${hover && styles.hover} ${color && styles[color]} ${
        status === true
          ? styles.active
          : status === false
            ? styles.inactive
            : status
              ? styles[status.replace(/\s/g, "").toLowerCase()]
              : ""
      }`}
    >
      {status === true
        ? "Active"
        : status === false
          ? "Inactive"
          : status !== ""
            ? status
            : ""}
      {text && text}
      {additionalText && (
        <>
          <br />
          {" " + additionalText}
        </>
      )}
      {icon && <InfoCircle />}
    </div>
  );
};

export default StatusPill;
