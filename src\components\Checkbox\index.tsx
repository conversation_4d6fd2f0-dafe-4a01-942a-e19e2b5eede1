/*
==========================================================================================

                                        Checkbox

Description: Reusable checkbox component

Parameters: checked (bool) - whether checkbox is checked or not
            onClick (func) - handle click on checkbox
            error (str) - inline error message
            size (num) - default = 27 - size of box in px
            disabled (bool) - whether checkbox is disabled or not

==========================================================================================
*/

import styles from "./checkbox.module.scss";

const Checkbox = ({
  checked,
  onClick,
  disabled,
  size,
  indeterminate,
  id = "",
}: any) => {
  return (
    <div className={styles.container} style={{ width: size ? size : "auto" }}>
      <input
        type="checkbox"
        className={styles.checkbox}
        onChange={() => {}}
        checked={checked}
        disabled={disabled}
      />
      <div
        className={`${styles.box} ${indeterminate && styles.indeterminate} ${
          disabled && styles.disabled
        } ${checked && styles.checked}`}
        onClick={(e) => {
          onClick && e.stopPropagation();
          !disabled && onClick && onClick();
        }}
        id={id}
      >
        <div className={`${styles.tick} ${checked && styles.tickChecked}`}>
          <svg
            width="10"
            height="8"
            viewBox="0 0 10 8"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M9.125 1.25L3.62188 6.5L0.875 3.875"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </div>
      </div>
    </div>
  );
};

export default Checkbox;
