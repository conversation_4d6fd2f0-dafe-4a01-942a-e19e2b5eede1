import { useState } from "react";
import styles from "./add-traveller-customer-product-modal.module.scss";
import {
  createStateObject,
  handleInputChange,
  labels,
} from "../utils/InputHandlers";
import Dialog from "../Dialog";
import { PlusCircle } from "../svgs";
import ToggleButtonGroup from "../ToggleButtonGroup";
import PlanCard from "../PlanCard";
import { getCountryOptions } from "../utils/getCountryOptions";
import CheckboxDropdownInput from "../CheckboxDropdownInput";
import ReactDOMServer from "react-dom/server";

type AddTravellerCustomerProductModalProps = {
  open: boolean;
  onClose: () => void;
  nextStepReview?: boolean;
  onContinue: () => void;
};

const AddTravellerCustomerProductModal = ({
  open,
  onClose,
  nextStepReview,
  onContinue,
}: AddTravellerCustomerProductModalProps) => {
  const [step, setStep] = useState<"addProduct" | "review">("addProduct");

  const headerTitleByStep = {
    addProduct: "Add Product",
    review: "Review",
  };

  const headerSubtitleByStep = {
    addProduct:
      "To add a product to the account, first select a country, then choose a product type, and finally, pick the product you want to add.",
    review:
      "The following product will be added to [<EMAIL>]’s account. ",
  };

  const fields = ["country", "productType"];
  const [formData, setFormData] = useState(createStateObject(fields));
  const [selectedPlanIndex, setSelectedPlanIndex] = useState<number | null>(
    null
  );

  const showPlansField = !!formData["productType"];
  const canContinue = selectedPlanIndex !== null;

  const handleContinue = () => {
    if (step === "addProduct" && nextStepReview) {
      setStep("review");
    } else {
      onContinue?.();
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      size="sm"
      headerIcon={<PlusCircle />}
      headerTitle={headerTitleByStep[step]}
      headerSubtitle={headerSubtitleByStep[step]}
      showFooter={canContinue}
      confirmButtonText="Continue"
      confirmButtonOnClick={handleContinue}
      cancelButtonText="Cancel"
      cancelButtonOnClick={onClose}
    >
      {step === "addProduct" && (
        <div className={styles.formContainer}>
          {fields.map((field) => {
            if (field === "country") {
              return (
                <CheckboxDropdownInput
                  key={"add-" + field}
                  options={getCountryOptions().map((opt) => ({
                    label: opt.label,
                    value: opt.value,
                  }))}
                  label={labels[field]}
                  selected={formData[field]}
                  onChange={(value: any) => {
                    handleInputChange(
                      field,
                      { target: { value } },
                      formData,
                      setFormData,
                      "select"
                    );
                  }}
                  searchOption={field === "country"}
                  error={formData.errors[field]}
                  infoTooltipText
                />
              );
            } else {
              return (
                <ToggleButtonGroup
                  options={[
                    { key: "combo", label: "Combo" },
                    { key: "data", label: "Data" },
                    { key: "virtualNumber", label: "Virtual Number" },
                  ]}
                  onChange={(value) => {
                    handleInputChange(
                      field,
                      { target: { value } },
                      formData,
                      setFormData
                    );
                  }}
                  selected={formData[field]}
                />
              );
            }
          })}
          {showPlansField && (
            <div className={styles.plans}>
              <p className={styles.label}>Plans</p>
              {Array(4)
                .fill(0)
                .map((_, i) => (
                  <PlanCard
                    key={i}
                    selected={i === selectedPlanIndex}
                    onSelect={() => setSelectedPlanIndex(i)}
                  />
                ))}
            </div>
          )}
        </div>
      )}
      {step === "review" && (
        <div className={styles.reviewContainer}>
          <div className={styles.reviewStepContainer}>
            <div className={styles.planCardPreview}>
              <p className={styles.productTypeTag}>{formData["productType"]}</p>
              <div className={styles.row}>
                <div className={styles.productCountryContainer}>
                  <img
                    src="https://public-gist-bucket.s3.eu-north-1.amazonaws.com/flag-icons/png/GB.png"
                    width={30}
                    height={30}
                  />
                  <div className={styles.countryNameAndOffersContainer}>
                    <p className={styles.countryName}>United Kingdom</p>
                    <p className={styles.offers}>5GB, 100 mins, 100 SMS</p>
                  </div>
                </div>

                <p className={styles.price}>£10.00</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </Dialog>
  );
};

export default AddTravellerCustomerProductModal;
