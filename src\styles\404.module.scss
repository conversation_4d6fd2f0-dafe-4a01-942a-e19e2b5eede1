@use "./theme.scss" as *;

.main {
  background: $off-white;
  padding: 90px;
  height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;

  .logos {
    display: flex;
    align-items: center;
  }

  .logo {
    width: 147px;
    margin-right: 33px;
  }
}

.error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  margin: 0 auto;
  h5 {
    font-size: 82px;
    line-height: 150%;
    font-weight: 700;
    margin: 0;
  }
  .notFound {
    font-size: 24px;
    font-weight: 500;
    line-height: 150%;
    margin-bottom: 16px;
  }
  .text {
    margin-bottom: 24px;
  }
}
