import Dialog from "@/components/Dialog";
import { FloppyDisk, PencilCircle } from "@/components/svgs";
import {
  clearInput,
  createStateObject,
  createStateObjectWithInitialState,
  handleInputChange,
  labels,
} from "@/components/utils/InputHandlers";
import { useState } from "react";
import styles from "./edit-combo-product-modal.module.scss";
import { Input } from "@/components/Input";
import SelectInput from "@/components/SelectInput";
import ToggleButtonGroup from "@/components/ToggleButtonGroup";
import { getCountryOptions } from "../utils/getCountryOptions";

type EditComboProductModalProps = {
  productData: any;
  open: boolean;
  onClose: () => void;
};

const EditComboProductModal = ({
  open,
  onClose,
  productData,
}: EditComboProductModalProps) => {
  const fields = [
    "country",
    "validity",
    "dataAllowance",
    "smsAllowance",
    "callAllowance",
    "usdPrice",
    "gbpPrice",
    "eurPrice",
    "status",
  ];
  const [formData, setFormData] = useState(
    createStateObjectWithInitialState(createStateObject(fields), {
      country: productData.country,
      validity: productData.validity,
      dataAllowance: productData.dataAllowance,
      smsAllowance: productData.smsAllowance,
      callAllowance: productData.callAllowance,
      usdPrice: productData.usdPrice,
      gbpPrice: productData.gbpPrice,
      eurPrice: productData.eurPrice,
      status: productData.status,
    })
  );

  return (
    <Dialog
      open={open}
      onClose={onClose}
      size="sm"
      headerIcon={<PencilCircle />}
      headerTitle="Edit Combo Product"
      confirmButtonText="Save Changes"
      confirmButtonOnClick={onClose}
      confirmButtonIcon={<FloppyDisk />}
      cancelButtonText="Cancel"
    >
      <div className={styles.formContainer}>
        {fields.map((field) => {
          if (["country", "validity"].includes(field)) {
            return (
              <SelectInput
                key={"product-" + field}
                options={selectOptionsByField[field]}
                label={labels[field]}
                selected={formData[field]}
                onChange={(e: any) => {
                  handleInputChange(
                    field,
                    { target: { value: e } },
                    formData,
                    setFormData
                  );
                }}
                error={formData.errors[field]}
                infoTooltipText
              />
            );
          } else if (["status"].includes(field)) {
            return (
              <ToggleButtonGroup
                key={"product-" + field}
                selected={formData[field] ? "active" : undefined}
                options={[
                  {
                    label: "Active",
                    key: "active",
                  },
                  {
                    label: "Inactive",
                    key: "inactive",
                  },
                ]}
                label={labels[field]}
                onChange={(value) => {
                  handleInputChange(
                    field,
                    { target: { value } },
                    formData,
                    setFormData
                  );
                }}
              />
            );
          } else {
            if (["usdPrice", "gbpPrice", "eurPrice"].includes(field)) {
              if (field === "usdPrice") {
                return (
                  <div key={"price-fields"} className={styles.priceFields}>
                    {["usdPrice", "gbpPrice", "eurPrice"].map((priceField) => (
                      <Input
                        key={"product-" + priceField}
                        label={labels[priceField]}
                        value={formData[priceField]}
                        onChange={(e: any) => {
                          handleInputChange(
                            priceField,
                            e,
                            formData,
                            setFormData
                          );
                        }}
                        error={formData.errors[priceField]}
                        clear={() => {
                          clearInput(priceField, setFormData);
                        }}
                        infoTooltipText
                        number
                      />
                    ))}
                  </div>
                );
              }
              return null;
            }

            return (
              <Input
                key={"product-" + field}
                label={labels[field]}
                value={formData[field]}
                onChange={(e: any) => {
                  handleInputChange(field, e, formData, setFormData);
                }}
                error={formData.errors[field]}
                clear={() => {
                  clearInput(field, setFormData);
                }}
                infoTooltipText
                number={[
                  "dataAllowance",
                  "smsAllowance",
                  "callAllowance",
                ].includes(field)}
              />
            );
          }
        })}
      </div>
    </Dialog>
  );
};

export default EditComboProductModal;

const selectOptionsByField: Record<string, any> = {
  country: getCountryOptions(),
  validity: ["30days", "7days", "5days", "1day"],
};
