@use "./theme.scss" as *;

.main {
  width: 100%;
  padding: 32px 32px 50px 32px;
  background: #f6f9fe;
  min-height: calc(100vh - 88px);
  h3 {
    font-size: 20px;
    font-weight: 700;
  }
  .backLink {
    color: #000;
    &:hover {
      text-decoration: underline;
    }
  }
}

.container {
  max-width: 1440px;
  margin: 0 auto;
}

.projects {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(314px, 1fr));
  grid-column-gap: 24px;
  grid-row-gap: 24px;
  padding: 15px 0;
}
