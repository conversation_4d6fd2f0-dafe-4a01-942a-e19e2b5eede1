@import '../../styles/mixins.module.scss';

.notificationOverlay {
  max-height: 720px;
  background-color: white;
  box-shadow: 0px 0px 40px 0px rgba(0, 0, 0, 0.15);
  border-radius: 24px;
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
} 


.notificationHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 16px;
}

.markAsReadButton {
  @include buttonReset;
  color: var(--button-tertiary-text);
  font-size: 14px;
  font-weight: 700;
  margin-left: 12px;
  margin-right: auto;
}

.closeButton {  
  @include buttonReset;
  background-color: transparent;
  border: none;
}

.notificationList {
  flex: 1;
  overflow-y: auto;
}

.notificationItem {
  display: flex;
  flex-direction: column;
  padding-block: 16px;
  padding-inline: 24px;
  border-top: 1px solid var(--gray-100);

  &.read {
    background-color: var(--primary-50);
  }

  .title {
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 700;
  }

  .description {
    color: var(--gray-600);
    font-size: 14px;
    font-weight: 400;
    margin-top: 4px;
  }

  .notificationFooter {
    display: flex;
    margin-top: 8px;
    height: 32px;
    justify-content: space-between;
    align-items: center;

    .date {
      color: rgba(62, 68, 81, 1);
      font-size: 14px;
      font-weight: 400;
    }

    .deleteButton {
      @include buttonReset;
      color: var(--button-tertiary-text);
      font-size: 14px;
      font-weight: 700;
    }
  }  
}



