import styles from "./sidebar.module.scss";
import {
  Dashboard,
  Ticketing,
  Hash,
  Box,
  Subscribers,
  Tag,
  UserStar,
  LifeRing,
  Bell,
  Settings,
  Promotions,
  LogOut,
  LogoFull,
  LogoSmall,
  ClockArrowsBack,
  Stack,
  LineChart,
  User,
  ChevronRight,
  Database,
  ExclaimCircle,
} from "../svgs";
import { useSelector } from "react-redux";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { useEffect, useRef, useState } from "react";
import NotificationsPanel from "@/components/NotificationsPanel";
import { useClickOutside } from "@/hooks/useClickOutside";
import Button from "../Button";
import { useMediaQuery } from "@mui/material";
import Dialog from "../Dialog";

const Sidebar = () => {
  const { userInfo, productType } = useSelector((state: any) => state);
  const location = useLocation();
  const navigate = useNavigate();
  const [openDropdown, setOpenDropdown] = useState("");
  const [isSidebarHovered, setIsSidebarHovered] = useState(false);
  const [isSidebarLocked, setIsSidebarLocked] = useState(false);
  const [showLogoutModal, setShowLogoutModal] = useState(false);

  const isSidebarExpanded = isSidebarLocked || isSidebarHovered;

  const sidebarContainerRef = useRef<HTMLDivElement>(null);
  const isTouchDevice = useMediaQuery("(pointer: coarse)");

  useEffect(() => {
    if (!isSidebarExpanded) {
      setOpenDropdown("");
    }
  }, [isSidebarExpanded]);

  // For tablet/touch displays so that menu is hidden on tapping outside
  useClickOutside(
    sidebarContainerRef,
    () => {
      setIsSidebarLocked(false);
      setIsSidebarHovered(false);
    },
    "click"
  );

  const [isNotificationWidgetOpen, setIsNotificationWidgetOpen] =
    useState(false);

  // close notification panel on route change
  useEffect(() => {
    if (isNotificationWidgetOpen) {
      setIsNotificationWidgetOpen(false);
      setIsSidebarLocked(false);
    }
  }, [location.pathname]);

  const handleCloseLogoutModal = () => {};

  const handleToggleNotificationWidget = (
    e: React.MouseEvent<HTMLButtonElement>
  ) => {
    e.stopPropagation();
    setIsNotificationWidgetOpen(!isNotificationWidgetOpen);
    setIsSidebarLocked(!isSidebarLocked);
  };

  const Page = ({ Icon, title, url }: any) => (
    <Link to={url} style={{ textDecoration: "none" }}>
      <div
        className={`${styles.page} ${styles.pageLink} ${location.pathname === url && styles.pageActive}`}
      >
        <Icon />
        <span className={styles.title}>{title}</span>
      </div>
    </Link>
  );

  const MenuTitle = ({
    Icon,
    title,
    menuType,
  }: {
    Icon?: any;
    title: string;
    menuType: string;
  }) => (
    <div
      className={`${styles.page} ${
        openDropdown === menuType && styles.pageActive
      }`}
      onMouseEnter={(e) => {
        e.stopPropagation();
        setOpenDropdown(menuType);
      }}
      onMouseLeave={() => {
        setOpenDropdown("closed");
      }}
    >
      <Icon />
      <span className={styles.title}>{title}</span> <ChevronRight />
    </div>
  );

  return (
    <div className={styles.sidebarContainer} ref={sidebarContainerRef}>
      <div
        className={`${styles.sidebar} container-scroll ${isSidebarExpanded ? styles.open : styles.closed}`}
        onMouseEnter={() => {
          // Do nothing if touch device to prevent bug with sidebar not closing when user taps outside for touch devices
          if (isTouchDevice) return;

          setIsSidebarHovered(true);
        }}
        onMouseLeave={() => {
          // Do nothing if touch device to prevent bug with sidebar not closing when user taps outside for touch devices
          if (isTouchDevice) return;

          setIsSidebarHovered(false);
        }}
      >
        <div className={styles.logoContainer}>
          {isSidebarExpanded ? <LogoFull /> : <LogoSmall />}
        </div>
        <div className={styles.mvnoLogoOverflow}>
          <div className={styles.mvnoLogoContainer}>
            <img src={userInfo?.brandLogo} className={styles.mvnoLogo} />
          </div>
        </div>
        {/* Top sidebar expand button for touch displays */}
        {openDropdown === "operations" && (
          <div
            className={styles.sideMenuContainer}
            onMouseEnter={(e) => {
              e.stopPropagation();
              setOpenDropdown("operations");
            }}
            onMouseLeave={() => {
              setOpenDropdown("closed");
            }}
            style={{ top: 69 }}
          >
            <div className={styles.sideMenu}>
              <div className={styles.groupLinks}>
                <Page Icon={Ticketing} title="Tickets" url="/tickets" />
                <Page
                  Icon={Subscribers}
                  title="Customer Managment"
                  url="/customer-management"
                />
                {userInfo?.role === 1 && (
                  <Page Icon={Box} title="Order Managment" url="/orders" />
                )}
                <Page Icon={Promotions} title="Promotions" url="/promotions" />
              </div>
            </div>
          </div>
        )}
        {openDropdown === "admin" && (
          <div
            className={styles.sideMenuContainer}
            onMouseEnter={(e) => {
              e.stopPropagation();
              setOpenDropdown("admin");
            }}
            onMouseLeave={() => {
              setOpenDropdown("closed");
            }}
            style={{ top: 116 }}
          >
            <div className={styles.sideMenu}>
              <div className={styles.groupLinks}>
                {userInfo?.role === 1 && (
                  <Page
                    Icon={Tag}
                    title="Product Managment"
                    url="/product-management"
                  />
                )}
                <Page
                  Icon={Stack}
                  title="Stock Managment"
                  url="/stock-managment"
                />
              </div>
            </div>
          </div>
        )}
        {openDropdown === "roles" && (
          <div
            className={styles.sideMenuContainer}
            onMouseEnter={(e) => {
              e.stopPropagation();
              setOpenDropdown("roles");
            }}
            onMouseLeave={() => {
              setOpenDropdown("closed");
            }}
            style={{ top: 212 }}
          >
            <div className={styles.sideMenu}>
              <div className={styles.groupLinks}>
                {(productType === "us-mvno" || productType === "mvne") && (
                  <Page
                    Icon={Hash}
                    title="Channel Managment"
                    url="/channel-management"
                  />
                )}
                {userInfo?.role === 1 && (
                  <Page
                    Icon={UserStar}
                    title="User Management"
                    url="/user-management"
                  />
                )}
                <Page
                  Icon={ClockArrowsBack}
                  title="Activity Logs"
                  url="/activity-logs"
                />
              </div>
            </div>
          </div>
        )}
        <div
          className={`${styles.toggleExpandButton} ${isSidebarExpanded ? styles.isExpanded : ""}`}
          onClick={() => {
            setIsSidebarHovered(false);
            setIsSidebarLocked(isSidebarExpanded ? false : true);
          }}
        >
          <ChevronRight />
        </div>
        {/* Bottom sidebar expand button for touch displays */}
        <div
          className={`${styles.toggleExpandButton} ${styles.bottom} ${isSidebarExpanded ? styles.isExpanded : ""}`}
          onClick={() => {
            setIsSidebarHovered(false);
            setIsSidebarLocked(isSidebarExpanded ? false : true);
          }}
        >
          <ChevronRight />
        </div>
        <div className={styles.pages}>
          <div className={styles.pageSection}>
            <Page Icon={Dashboard} title="Dashboard" url="/dashboard" />
            <MenuTitle
              title="Operations"
              menuType="operations"
              Icon={Settings}
            />
            <MenuTitle title="Inventory" menuType="admin" Icon={Database} />
            <Page Icon={LineChart} title="Analytics" url="/reporting/charts" />
            <MenuTitle title="Access & Logs" menuType="roles" Icon={User} />
            {
              productType === 'mvne' && (
                <Page Icon={LifeRing} title="Accounts" url="/accounts" />
              )
            }
            <Page Icon={LifeRing} title="Support" url="/support" />
          </div>
          {isSidebarExpanded && (
            <div className={styles.pageSection}>
              <div className={styles.userSection}>
                <div className={styles.userData}>
                  <div>
                    <p>Lola Strawberry</p>
                    <span>Admin</span>
                  </div>
                  <button
                    type="button"
                    onClick={handleToggleNotificationWidget}
                    className={styles.notificationBellButton}
                  >
                    <Bell />
                  </button>
                </div>
                <div className={styles.userData}>
                  <Link to="/settings">
                    <Button color="secondary">
                      <Settings /> Settings
                    </Button>
                  </Link>
                  <Button
                    color="secondary"
                    onClick={() => setShowLogoutModal(true)}
                  >
                    <LogOut /> Logout
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
      {isNotificationWidgetOpen && (
        <div className={styles.notificationsPanelContainer}>
          <NotificationsPanel
            isOpen={isNotificationWidgetOpen}
            onClose={() => {
              setIsNotificationWidgetOpen(false);
              setIsSidebarLocked(false);
            }}
          />
        </div>
      )}
      {showLogoutModal && (
        <Dialog
          open={showLogoutModal}
          onClose={() => setShowLogoutModal(false)}
          headerTitle="Logging out?"
          headerIcon={<ExclaimCircle />}
          confirmButtonText="Yes, Logout"
          confirmButtonOnClick={() => {
            navigate("/login");
            setShowLogoutModal(false);
          }}
          size="sm"
        />
      )}
    </div>
  );
};

export default Sidebar;
