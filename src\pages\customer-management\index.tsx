import { Add<PERSON><PERSON>, ArrowsC<PERSON>wise, Cog, Gear, MagnifyingGlass, PeopleCircle, Plugs, RefreshCircle, X } from "../../components/svgs";
import styles from "../../styles/customer-management.module.scss";
import { useCallback, useEffect, useRef, useState } from "react";
import { formatDateWords } from "../../components/utils/formatDate";
import TableControl from "../../components/TableControl";
import StatusPill from "../../components/StatusPill";
import { customerFields } from "../../components/utils/customerFields";
import UserSkeleton from "../../components/UserSkeleton";
import RadioSelect from "../../components/RadioSelect";
import SendNotificationModal from "../../components/SendNotificationModal";
import { useNavigate } from "react-router-dom";
import parsePhoneNumber from "libphonenumber-js";
import { countryListAlpha2 } from "../../components/utils/countryList";
import { getCustomers } from "@/components/utils/dataCreator";
import Title from "@/components/Title";
import SwitchBar from "@/components/SwitchBar";
import TravellerCustomerPage from "@/components/TravellerCustomerPage";
import { AnimatePresence } from "framer-motion";
import NationalCustomerPage from "@/components/NationalCustomerPage/nationalCustomerpage";
import CollapsiblePanel from "@/components/CollapsiblePanel";
import InsightList from "@/components/InsightList";
import InsightCard from "@/components/InsightCard";
import Button from "@/components/Button";
import Tag from "@/components/Tag";
import {
  clearInput,
  createStateObject,
  handleInputChange,
  labels,
} from "@/components/utils/InputHandlers";
import { Input } from "@/components/Input";
import AddTravellerSubscriberModal from "@/components/AddTravellerSubscriberModal";
import { useSelector } from "react-redux";
import { getCountryOptions } from "@/components/utils/getCountryOptions";
import CheckboxDropdownInput from "@/components/CheckboxDropdownInput";
import ProductsMenu from "@/components/ProductsMenu";
const CountryDisplay = ({ country }: any) => {
  return (
    <div className={styles.country}>
      <div
        className={styles.flag}
        style={{
          backgroundImage: `url(https://public-gist-bucket.s3.eu-north-1.amazonaws.com/flag-icons/png/${country}.png)`,
        }}
      />
      {
        countryListAlpha2.filter(
          (countryItem: any) => country === countryItem.code
        )[0]?.name
      }
    </div>
  );
};

const CustomerManagement = () => {
  const { productType } = useSelector((state: any) => state);
  const [customers, setCustomers] = useState(null as any);

  const [initialLoading, setInitialLoading] = useState(true);

  const navigate = useNavigate();

  const [customerType, setCustomerType] = useState(
    productType === "us-mvno" ? "national" : "traveller"
  );

  const overviewPanelRef = useRef<any>(null);
  const searchPanelRef = useRef<any>(null);
  const searchFields = ["name", "email", "simNo", "country", "status"];
  const [showSearchResults, setShowSearchResults] = useState(false);
  const [formData, setFormData] = useState(createStateObject(searchFields));

  const formatDataItem = (item: any, key: string) => {
    if (key === "country") {
      const phone = parsePhoneNumber(
        item.phoneNumber[0] === "+" ? item.phoneNumber : "+" + item.phoneNumber
      ) as any;

      if (phone && phone.country) {
        return <CountryDisplay country={phone.country} />;
      }
    } else if (key === "creationTime") {
      return formatDateWords(item[key]);
    } else if (key === "status") {
      return (
        <RadioSelect
          label={<StatusPill status={item.status} />}
          options={[
            { label: <StatusPill status="Active" />, key: "Active" },
            { label: <StatusPill status="Inactive" />, key: "Inactive" },
          ]}
          selected={item.status}
          onChange={() => { }}
        />
      );
    } else if (key === "firstName") {
      let fullName = item.firstName + " " + item.lastName;
      return fullName;
    } else if (key === "phoneNumber") {
      const phone = parsePhoneNumber(
        item.phoneNumber[0] === "+" ? item.phoneNumber : "+" + item.phoneNumber
      ) as any;
      return phone?.formatInternational();
    } else {
      return item[key];
    };
  }

  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [numberOfPages, setNumberOfPages] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  const populate = (search?: string) => {
    setInitialLoading(true);

    setTimeout(() => {
      setCustomers(getCustomers(itemsPerPage));
      setInitialLoading(false);
    }, 500);
  };

  useEffect(() => {
    populate();
  }, [currentPage, itemsPerPage]);

  const renderAddSubscriberButton = useCallback(() => (
    <Button onClick={(e) => {
      e.stopPropagation()
      setShowAddSubscriberModal(true)
    }}>
      <AddUser />
      Add Subscriber
    </Button>
  ), [])


  const [sendNotification, setSendNotification] = useState(false);

  const [activeCustomer, setActiveCustomer] = useState(null as any);
  const [showCustomerModal, setShowCustomerModal] = useState(false);

  const [showAddSubscriberModal, setShowAddSubscriberModal] = useState(false)

  return (
    <>
      <AnimatePresence>
        {showCustomerModal && (
          <TravellerCustomerPage
            show={showCustomerModal}
            customer={activeCustomer}
            close={(val: boolean) => setShowCustomerModal(val)}
          />
        )}
      </AnimatePresence>
      {showAddSubscriberModal && (
        <AddTravellerSubscriberModal
          open={showAddSubscriberModal}
          onClose={() => setShowAddSubscriberModal(false)}
        />
      )}
      <SendNotificationModal
        show={sendNotification}
        setShow={setSendNotification}
      />
      <Title>Customer Management</Title>
      {productType === "mvne" && (
        <SwitchBar
          options={[
            {
              label: "Traveller",
              id: "traveller",
            },
            {
              label: "National",
              id: "national",
            },
          ]}
          selected={customerType}
          setSelected={setCustomerType}
          layoutId="customer-type-switch"
        />
      )}
      <div className={styles.main}>
        {customerType === "traveller" && (
          <>
            {/* Overview */}
            <CollapsiblePanel
              title="Overview"
              summaryWhenClosed={
                <div className={styles.overviewSummaryWrapper}>
                  <InsightList insights={overviewStats} />
                  {renderAddSubscriberButton()}
                </div>
              }
              headerWhenOpen={
                <div className={styles.overviewHeaderOpenWrapper}>
                  {renderAddSubscriberButton()}
                </div>
              }
              ref={overviewPanelRef}
            >
              <div className={styles.overview}>
                {overviewStats.map((stat, index) => (
                  <InsightCard key={index} {...stat} />
                ))}
              </div>
            </CollapsiblePanel>

            {/* Search */}
            <div style={{ marginTop: 16 }}>
              <CollapsiblePanel
                title="Search Subscriptions"
                summaryWhenClosed={
                  <div style={{ display: 'flex', flex: 1, justifyContent: 'space-between', alignItems: 'center', marginLeft: 16, marginRight: 8 }}>
                    <Tag text="3 filters applied" />
                    <Button color="secondary">Clear Filters</Button>
                  </div>
                }
                ref={searchPanelRef}
              >
                <div className={styles.searchPanelForm}>
                  <div className={styles.fields}>
                    {searchFields.map((prop) => {
                      if (["country", "status"].includes(prop)) {
                        return (
                          <CheckboxDropdownInput
                            key={"traveller-customer-" + prop}
                            options={selectOptionsByField[prop]}
                            label={labels[prop]}
                            selected={formData[prop]}
                            onChange={(values) => {
                              handleInputChange(
                                prop,
                                values,
                                formData,
                                setFormData,
                                'select'
                              );
                            }}
                            searchOption={prop === 'country'}
                            error={formData.errors[prop]}
                            infoTooltipText
                        />
                        );
                      } else {
                        return (
                          <Input
                            key={"customers-" + prop}
                            label={labels[prop]}
                            value={formData[prop]}
                            onChange={(e: any) => {
                              handleInputChange(prop, e, formData, setFormData);
                            }}
                            error={formData.errors[prop]}
                            clear={() => {
                              clearInput(prop, setFormData);
                            }}
                            infoTooltipText
                          />
                        );
                      }
                    })}
                  </div>
                  <div className={styles.actions}>
                    <Button
                      color="blue"
                      onClick={() => {
                        searchPanelRef.current.close();
                        overviewPanelRef.current.close();
                        setShowSearchResults(true);
                      }}
                    >
                      <MagnifyingGlass /> Search
                    </Button>
                  </div>
                </div>
              </CollapsiblePanel>
            </div>

            {/* Table */}
            {showSearchResults && <div className={styles.panel} style={{ marginTop: 16 }}>
              <div className={styles.panelTopBar}>
                <div className={styles.tableHeader}>
                  <h4>Subscriptions</h4>
                  <span>(8 results)</span>
                </div>
              </div>
              <>
                <div className={`${styles.tableContainer} table-scroll`}>
                  <table>
                    <thead>
                      <tr>
                        {customerFields.map((field: any) => (
                          <th>{field.label}</th>
                        ))}
                        <th />
                      </tr>
                    </thead>
                    <tbody>
                      {!initialLoading
                        ? customers.map((item: any, i: number) => {
                          if (item === null) {
                            return (
                              <tr
                                style={{
                                  visibility: "hidden",
                                  pointerEvents: "none",
                                }}
                                key={`customer-filler-${i}`}
                              ></tr>
                            );
                          } else {
                            return (
                              <tr
                                key={`customer-${item.mid}`}
                              >
                                {customerFields.map((field: any) => (
                                  <td
                                    key={`customer-${item.mid}-${field.key}`}
                                  >
                                    <div
                                      style={{
                                        display: "flex",
                                        justifyContent: "flex-start",
                                      }}
                                    >
                                      {formatDataItem(item, field.key)}
                                    </div>
                                  </td>
                                ))}
                                <td className={styles.actionCell}>
                                  <div className={styles.actionBox}>
                                    <span>
                                      <ProductsMenu
                                        direction="left"
                                        portal
                                        data={{
                                          icon: <Cog />,
                                          items: getRowMenuItems(item)
                                        }}
                                      />
                                    </span>
                                    <span
                                      className={styles.viewRowBtn}
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        setActiveCustomer(item);
                                        setShowCustomerModal(true);
                                      }}
                                      style={{ cursor: "pointer" }}
                                    >
                                      View
                                    </span>
                                  </div>
                                </td>
                              </tr>
                            );
                          }
                        })
                        : Array.from({ length: itemsPerPage }, (v, i) => i).map(
                          (i) => (
                            <UserSkeleton
                              key={"user-skeleton-" + i}
                              noOfStandard={9}
                            />
                          )
                        )}
                    </tbody>
                  </table>
                </div>
                <div style={{ marginTop: "16px" }}>
                  <TableControl
                    show
                    itemsPerPage={itemsPerPage}
                    setItemsPerPage={setItemsPerPage}
                    currentPage={currentPage}
                    setCurrentPage={setCurrentPage}
                    numberOfPages={numberOfPages}
                    label="customers"
                    loading={initialLoading}
                  />
                </div>
              </>
            </div>}
          </>
        )}
        {customerType === "national" && <NationalCustomerPage />}
      </div>
    </>
  );
};

export default CustomerManagement;

const overviewStats = [
  { title: "Total Subscribers", value: "63,629", icon: <PeopleCircle /> },
  { title: "Active Subscriptions", value: "63,629", icon: <RefreshCircle /> },
];

const selectOptionsByField: Record<string, any> = {
  country: getCountryOptions(),
  status: [
    { label: "Active", value: "active" },
    { label: "Inactive", value: "Inactive" },
  ],
};

const getRowMenuItems = (item: any) => {
  return [
    {
      label: "Turn OFF Auto-Renew",
      icon: <ArrowsClockwise />,
      onClick: () => { }
    },
    {
      label: "Deactivate Virtual Number",
      icon: <Plugs />,
      onClick: () => { }
    },
    {
      label: "Cancel Plan",
      icon: <X width={20} height={20} />,
      onClick: () => { }
    }
  ]
}
