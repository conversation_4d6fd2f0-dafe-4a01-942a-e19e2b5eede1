import React, { useEffect } from "react";
import {
  BrowserRouter as Router,
  Route,
  Routes,
  Navigate,
  Outlet,
  useLocation,
} from "react-router-dom";
import ScrollToTop from "./components/ScrollToTop";
import { AnimatePresence, motion } from "framer-motion";
import Layout from "./components/Layout";
import { useDispatch, useSelector } from "react-redux";
import { CircularProgress } from "@mui/material";
import Promotions from "./pages/promotions";
import Notification from "@/components/Notification";
import Accounts from "./pages/accounts";
const root = getComputedStyle(document.getElementById("root")!);

const loading = (
  <div
    style={{
      display: "flex",
      width: "100%",
      height: "100vh",
      alignItems: "center",
      justifyContent: "center",
      background: "#f1f1f1",
      opacity: 0.5,
    }}
  >
    <CircularProgress
      style={{
        width: 50,
        height: 50,
        color: root.getPropertyValue("--orange"),
      }}
    />
  </div>
);

const pageVariants = {
  initial: {
    opacity: 0,
  },
  in: {
    opacity: 1,
  },
  out: {
    opacity: 0,
  },
};

const pageTransition = {
  type: "tween",
  ease: "easeInOut",
  duration: 0.3,
};

const LoginLayout = () => {
  const { pathname } = useLocation();
  const dispatch = useDispatch();
  const { notifications, closeResetMessage } = useSelector(
    (state: any) => state
  );

  useEffect(() => {
    if (!closeResetMessage) {
      dispatch({
        type: "set",
        closeResetMessage: true,
      });
    } else {
      dispatch({
        type: "set",
        notifications: [],
      });
    }
  }, [pathname]);

  return (
    <motion.div
      key={pathname}
      initial="initial"
      animate="in"
      variants={pageVariants}
      transition={pageTransition}
    >
      <div className="notification-wrapper login-notification-wrapper">
        <AnimatePresence>
          {notifications.map((notification: any) => (
            <Notification
              id={notification.id}
              key={notification.id}
              message={notification.message}
              error={notification.error}
            />
          ))}
        </AnimatePresence>
      </div>
      <Outlet />
    </motion.div>
  );
};

const MainLayout = () => {
  const { pathname } = useLocation();
  const dispatch = useDispatch();
  const { notifications, closeLoginMessage } = useSelector(
    (state: any) => state
  );

  useEffect(() => {
    if (!closeLoginMessage) {
      dispatch({
        type: "set",
        closeLoginMessage: true,
      });
    } else {
      dispatch({
        type: "set",
        notifications: [],
      });
    }
  }, [pathname]);

  return (
    <Layout>
      <motion.div
        key={pathname}
        initial="initial"
        animate="in"
        variants={pageVariants}
        transition={pageTransition}
      >
        <div className="notification-wrapper">
          <AnimatePresence>
            {notifications.map((notification: any) => (
              <Notification
                id={notification.id}
                key={notification.id}
                message={notification.message}
                error={notification.error}
              />
            ))}
          </AnimatePresence>
        </div>
        <Outlet />
      </motion.div>
    </Layout>
  );
};

const Protected = ({ children }: any) => {
  const dispatch = useDispatch();
  const { isLoggedIn } = useSelector((state: any) => state);
  /*if (!isLoggedIn) {
    if (localStorage.getItem("token") && localStorage.getItem("crmUserInfo")) {
      let jwt;
      try {
        jwt = jwt_decode(localStorage.getItem("token")!) as any;
      } catch (e) {
        localStorage.clear();
        return <Navigate to="/login" replace />;
      }
      if ("exp" in jwt && jwt.exp * 1000 > new Date().getTime()) {
        dispatch({
          type: "set",
          isLoggedIn: true,
        });
        return children;
      } else {
        localStorage.clear();
        return <Navigate to="/login" replace />;
      }
    } else {
      return <Navigate to="/login" replace />;
    }
  }*/
  return children;
};

function MyApp() {
  // Pages
  const Login = React.lazy(() => import("./pages/login"));
  const FirstLogin = React.lazy(() => import("./pages/new-login"));
  const ForgotPassword = React.lazy(() => import("./pages/forgot-password"));
  const ResetPassword = React.lazy(() => import("./pages/reset-password"));
  const Dashboard = React.lazy(() => import("./pages/dashboard"));
  const Tickets = React.lazy(() => import("./pages/tickets"));
  const UserManagement = React.lazy(() => import("./pages/user-management"));
  const CustomerManagement = React.lazy(
    () => import("./pages/customer-management")
  );
  const NumberManagement = React.lazy(
    () => import("./pages/number-management")
  );
  const ProductManagement = React.lazy(
    () => import("./pages/product-management")
  );
  const Reporting = React.lazy(() => import("./pages/reporting"));
  const NotFound = React.lazy(() => import("./pages/404"));
  const SelectProject = React.lazy(() => import("./pages/select-project"));
  const Orders = React.lazy(() => import("./pages/orders"));
  const ChannelManagement = React.lazy(
    () => import("./pages/channel-management")
  );
  const StockManagment = React.lazy(() => import("./pages/stock-managment"));
  const TableTest = React.lazy(() => import("./pages/table-test"));
  const Support = React.lazy(() => import("./pages/support"));
  const ActivityLogs = React.lazy(() => import("./pages/activity-logs"));
  const Settings = React.lazy(() => import("./pages/settings"));

  return (
    <Router>
      <React.Suspense fallback={loading}>
        <ScrollToTop>
          <Routes>
            <Route element={<LoginLayout />}>
              <Route path="*" element={<NotFound />} />
              <Route path="/" element={<Navigate to="/login" replace />} />
              <Route path="/login" element={<Login />} />
              <Route path="/new-login" element={<FirstLogin />} />
              <Route path="/forgot-password" element={<ForgotPassword />} />
              <Route path="/reset-password" element={<ResetPassword />} />
              <Route
                path="/select-project/:mvne?"
                element={<SelectProject />}
              />
            </Route>
            <Route
              element={
                /*<Protected>*/
                <MainLayout />
                /*</Protected>*/
              }
            >
              <Route path="/dashboard" element={<Dashboard />} />
              <Route path="/tickets" element={<Tickets />} />
              <Route path="/user-management" element={<UserManagement />} />
              <Route path="/number-management" element={<NumberManagement />} />

              <Route
                path="/customer-management"
                element={<CustomerManagement />}
              />
              <Route
                path="/product-management"
                element={<ProductManagement />}
              />
              <Route path="/accounts" element={<Accounts />} />
              <Route path="/promotions" element={<Promotions />} />
              <Route path="/reporting/:section" element={<Reporting />} />
              <Route path="/orders" element={<Orders />} />
              <Route path="/stock-managment" element={<StockManagment />} />
              <Route
                path="/channel-management"
                element={<ChannelManagement />}
              />
              <Route path="/activity-logs" element={<ActivityLogs />} />
              <Route path="/settings" element={<Settings />} />
              <Route path="/support" element={<Support />} />
              <Route path="/table-test" element={<TableTest />} />
            </Route>
          </Routes>
        </ScrollToTop>
      </React.Suspense>
    </Router>
  );
}

export default MyApp;
