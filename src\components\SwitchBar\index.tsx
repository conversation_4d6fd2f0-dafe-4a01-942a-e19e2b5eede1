import { motion } from "framer-motion";
import styles from "./switch-bar.module.scss";

type Option = {
  label: string;
  id: string | number;
};

type PropsType = {
  options: Option[];
  selected: string | number;
  setSelected: Function;
  layoutId: string;
};

const SwitchBar = ({ options, selected, setSelected, layoutId }: PropsType) => {
  return (
    <div className={styles.main}>
      {options.map((option: Option) => (
        <div
          className={`${styles.option} ${selected === option.id ? styles.active : ""}`}
          onClick={() => {
            setSelected(option.id);
          }}
        >
          <span className={styles.maskLabel}>{option.label}</span>
          <span className={styles.displayLabel}>{option.label}</span>
          {selected === option.id && (
            <motion.div className={styles.underline} layoutId={layoutId} />
          )}
        </div>
      ))}
    </div>
  );
};

export default SwitchBar;
