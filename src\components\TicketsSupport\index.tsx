import styles from "./tickets-support.module.scss";
import { ArrowRight, PaperPlane, Plus } from "../svgs";

import { Divider } from "@mui/material";
import Tooltip from "../Tooltip";

import Button from "../Button";

import TicketType from "../TicketType";
import Priority from "../Priority";
import Category from "../Category";
import StatusBadge from "../StatusBadge";
import Initials from "../Initials";

const TicketsSupport = ({ data }: any) => {
  return (
    <div
      style={{ display: "flex", gap: "32px", width: "100%", height: "913px" }}
    >
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: "32px",
          width: "45%",
          height: "913px",
        }}
      >
        <div
          className={styles.panel}
          style={{
            display: "flex",
            flexDirection: "column",
            alignItems: "start",
            gap: "24px",
            height: "auto",
          }}
        >
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              gap: "4px",
              width: "100%",
            }}
          >
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <div
                style={{
                  height: "auto",
                  fontSize: "24px",
                  fontWeight: "700",
                }}
              >
                Hans Down
              </div>
              <Button color="quaternary">
                Customer Summary <ArrowRight />
              </Button>
            </div>
            <div><EMAIL></div>
          </div>
          <Divider style={{ width: "100%" }} />
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              width: "100%",
            }}
          >
            <div
              style={{
                display: "flex",
                gap: "12px",
                flexDirection: "column",
                width: "100%",
              }}
            >
              <div
                style={{ display: "flex", gap: "8px", flexDirection: "column" }}
              >
                <div
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    width: "100%",
                  }}
                >
                  <div style={{ display: "flex", gap: "12px" }}>
                    <TicketType status="DID Request" />
                    <div>#4444</div>
                  </div>
                  <div>DD/MM/YYY</div>
                </div>
                <div>{`<Subject goes here unable to activate>`}</div>
              </div>

              <div
                style={{ display: "flex", gap: "8px", flexDirection: "column" }}
              >
                <div>Issue:</div>
                <div>
                  Hi, I’ve downloaded a bundle but when I go to my mobile data
                  settings, breathe sim does not come up. The only option is to
                  ‘add esim’ and it then asks me for a QR code which I don’t
                  have. I have an iPhone 11 on iOS 16. What should I do?
                </div>
              </div>
              <div style={{ display: "flex", gap: "12px" }}>
                <Priority priority="High" />
                <Category category="Finance" />
                <Tooltip show text="Hans Down">
                  <Initials>HD</Initials>
                </Tooltip>
                <div>
                  <StatusBadge status="Open" />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            gap: "24px",
            height: "auto",
            overflow: "auto",
          }}
          className={styles.panel}
        >
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <div
              style={{
                fontSize: "20px",
                fontWeight: "700",
              }}
            >
              Notes
            </div>
            <div
              style={{ display: "flex", gap: "8px", alignItems: "center" }}
              className={styles.plus}
            >
              <Plus />
              Add Notes
            </div>
          </div>
          <div
            style={{
              display: "flex",
              padding: "16px 24px",
              flexDirection: "column",
              alignItems: "flex-start",
              gap: "10px",
              alignSelf: "stretch",
              borderRadius: "8px",
              background: "#F7F6F6",
            }}
          >
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "flex-start",
                alignSelf: "stretch",
              }}
            >
              <div>By: Mira Gouse</div>
              <div>DD/MM/YYYY</div>
            </div>
            <div>
              Lorem ipsum dolor sit amet consectetur. Lobortis sed risus in
              cras. Sit cras varius vulputate tortor suscipit. Duis enim
              bibendum ac nunc. Praesent eget posuere ac molestie tincidunt
              faucibus senectus nunc. Tincidunt senectus nullam mauris fermentum
              tortor. Quis felis egestas porttitor augue nibh malesuada viverra
              morbi. Lobortis arcu tellus arcu ipsum. Elit interdum at in ornare
              a enim aliquet dictum. At consequat ut vestibulum.
            </div>
          </div>
        </div>
      </div>
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          width: "60%",
          height: "913px",
        }}
      >
        <div className={styles.topRow}></div>
        <div className={styles.panel}>
          <>
            <div style={{ display: "flex", flexDirection: "column" }}>
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  marginBottom: "24px",
                }}
              >
                <div className={styles.title}>Correspondence</div>
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    gap: "8px",
                  }}
                  className={styles.plus}
                >
                  <PaperPlane /> Send New Email
                </div>
              </div>
              <Divider />

              <div
                style={{
                  display: "flex",
                  paddingBottom: "0px",
                  flexDirection: "column",
                  justifyContent: "flex-end",
                  alignItems: "flex-start",
                  flex: "1 0 0",
                  minHeight: "740px",
                }}
              >
                <div
                  style={{
                    width: "550px",
                    background: "#CBCAF9",
                    padding: "8px 24px",
                    borderRadius: "12px",
                    alignSelf: "flex-start",
                    marginBottom: "8px",
                    display: "flex",
                    flexDirection: "column",
                    gap: "8px",
                  }}
                >
                  <div
                    style={{
                      display: "flex",
                      gap: "10px",
                      justifyContent: "space-between",
                      alignItems: "center",
                    }}
                  >
                    <div style={{ fontSize: "14px", fontWeight: "600" }}>
                      Problem logging in
                    </div>
                    <div style={{ display: "flex", gap: "8px" }}>
                      <div>12:30</div>
                      <div>3 Apr 2023</div>
                    </div>
                  </div>
                  I am writing to report an issue with logging in to my account.
                  I have been using the same login credentials, but I am unable
                  to access my account. I have also tried resetting my password
                  several times, but I have not received any email with the
                  password reset link. Can you please help me resolve this issue
                  as soon as possible? I need to access my account urgently.
                </div>
                <div
                  style={{
                    width: "550px",
                    background: "#FEEBD4",
                    padding: "8px 24px",
                    borderRadius: "12px",
                    alignSelf: "flex-end",
                    display: "flex",
                    flexDirection: "column",
                    gap: "8px",
                  }}
                >
                  <div
                    style={{
                      display: "flex",
                      gap: "10px",
                      justifyContent: "space-between",
                      alignItems: "center",
                    }}
                  >
                    <div style={{ fontSize: "14px", fontWeight: "600" }}>
                      Re: Problem logging in
                    </div>
                    <div style={{ display: "flex", gap: "8px" }}>
                      <div>12:30</div>
                      <div>3 Apr 2023</div>
                    </div>
                  </div>
                  Hello Omar, Thank you for contacting us regarding the login
                  issue you are experiencing with your account. We apologize for
                  the inconvenience caused and we will be happy to assist you
                  with resolving this issue. Regards, Customer Support
                </div>
              </div>
            </div>
          </>
        </div>
      </div>
    </div>
  );
};

export default TicketsSupport;
