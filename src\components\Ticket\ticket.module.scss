@use "../../styles/theme.scss" as *;

@mixin resizing($bp1) {
}

.main {
  width: auto;
  padding: 22.5px 32px;
  border-radius: 12px;
  background: #f7f6f6;
  cursor: pointer;
  &.disabled {
    pointer-events: none;
    cursor: auto;
  }
  &.ticketOpen {
    width: max-content;
  }
}

.summary {
  display: flex;
  align-items: center;
}

.overview {
  padding-left: 24px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-right: 16px;
  .top {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 12px;
    line-height: 18px;
    .id {
      color: #000;
    }
    .date {
      color: #797979;
    }
  }
  .bottom {
    display: flex;
    align-items: center;
    gap: 16px;
    font-size: 14px;
    line-height: 21px;
  }
}

.expand {
  margin-left: 25px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  &.rotate {
    transform: rotate(180deg);
  }
}

.body {
  display: flex;
  gap: 20px;
  padding-top: 20.5px;
  padding-bottom: 8px;
  padding-left: 48px;
  .left {
    width: 100%;
    max-width: 736px;
    .issueHeading {
      margin: 0;
      font-size: 12px;
      line-height: 18px;
      margin-bottom: 8px;
      font-weight: 400;
    }
    .issueContent {
      font-size: 14px;
      line-height: 21px;
    }
  }
}

.dataBar {
  display: flex;
  align-items: center;
  margin-left: auto;
  gap: 12px;
}
.customerSummary {
  display: flex;
  align-items: center;
  gap: 8px;
  color: $orange;
  font-size: 14px;
  font-weight: 500;
  line-height: 21px;
  height: 24px;
  transition: all 0.2s ease;
  white-space: nowrap;
  cursor: pointer;
  margin-left: auto;
  margin-top: auto;
  text-decoration: none;
  &:hover {
    color: $dark-orange;
  }
}
