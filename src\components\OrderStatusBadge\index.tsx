import { InfoCircle, InfoCircle2 } from "@/components/svgs";
import styles from "./status-badge.module.scss";
import { orderStatuses } from "@/components/utils/dataCreator";

export const convertStatus = (str: string, flat: boolean) => {
  switch (str) {
    case "ICCIDREQUIRED":
      return "ICCID Required";
    case "BANCHANGE":
      return "BAN Change";
    case "READY":
      return "Ready";
    case "ICCIDANDPORTINREQUIRED":
      return flat ? "ICCID & Port in Required" : <>ICCID & Port in Required</>;
    case "DETAILSREQUIRED":
      return (
        <span style={{ display: "flex", alignItems: "center", gap: 4 }}>
          <span>Details Required</span>
          <span>
            <InfoCircle2 />
          </span>
        </span>
      );
    case "PORTINREQUIRED":
      return flat ? "Port in Required" : <>Port in Required</>;
    default:
      return "";
  }
};

type OrderStatusBadgeProps = {
  status: (typeof orderStatuses)[number];
  flat?: boolean;
};

const OrderStatusBadge = ({ status, flat = false }: OrderStatusBadgeProps) => {
  return (
    <div
      className={`${styles.main} ${
        convertStatus(status, flat) === "ICCID Required"
          ? styles.iccid
          : convertStatus(status, flat) === "Ready"
            ? styles.ready
            : convertStatus(status, flat) === "BAN Change"
              ? styles.banChange
              : status === "PORTINREQUIRED"
                ? styles.portInRequired
                : status === "DETAILSREQUIRED"
                  ? styles.detailsRequired
                  : styles.default
      }`}
    >
      {convertStatus(status, flat)}
    </div>
  );
};

export default OrderStatusBadge;
