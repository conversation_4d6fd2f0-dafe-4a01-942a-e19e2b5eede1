@use "../../styles/theme.scss" as *;

.form {
  width: 100%;
  display: flex;
  flex-direction: column;
  h2 {
    font-size: 30px;
    font-weight: 700;
    line-height: 38px;
    margin-bottom: 6px;
    color: #061632
  }
  .tag {
    margin: 0 0 24px 0;
    font-size: 14px;
    color: #667085
  }
  .notReceived {
    margin: 31px 0 0 0;
  }
}

.notReceived {
  display: flex;
  align-items: center;
  color: #061632;
  font-weight: 700;
}

.resend {
  background: none;
  border: none;
  color: $black;
  font-weight: 600;
  margin-left: 10px;
  padding: 0;
  font-size: 16px;
  line-height: 24px;
  cursor: pointer;
  transition: color 0.1 ease;
  &:hover {
    color: $orange;
  }
}
