@use "../../styles/theme.scss" as *;

.sidebarContainer {
  display: flex;
  position: fixed;
  left: 0px;
  top: 0px;
  z-index: 90;
}

.notificationsPanelContainer {
  margin-left: 12px;
  padding-block: 24px;
  width: 367px;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  height: 100vh;
}

.sidebar {
  position: relative;
  height: 100vh;
  width: 48px;
  background: #fff;
  box-shadow: 2px 0px 8px 0px #0000000d;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: width 0.3s ease;
  overflow: visible;
  padding: 5px 0px 8px 0px;

  .sideMenuContainer {
    position: absolute;
    left: 159px;
    top: 90px;
    padding: 100px;
    .sideMenu {
      width: 250px;
      border-radius: 24px;
      background: #fff;
      box-shadow: 0px 0px 20px 0px #00000026;
      overflow: hidden;
      pointer-events: all;
    }

    // disable pointer events on mobile devices so taps outside(but close to) the popup menu will close it.
    // works in tandem with the pointer-events rule in .sideMenu
    @media (pointer: coarse) {
      pointer-events: none;
    }
  }
}

.open {
  width: 252px;
  border-radius: 0px 16px 16px 0px;
}

.logoContainer {
  position: relative;
  width: 100%;
}

.mvnoLogoOverflow {
  margin-left: auto;
  overflow: hidden;
  display: flex;
  align-items: center;
  height: 55px;
  width: 100%;
  padding: 0px 4px;
  .mvnoLogoContainer {
    display: flex;
    align-items: flex-start;
    width: 100%;
    .mvnoLogo {
      max-width: 100%;
      max-height: 50px;
    }
  }
}

.pages {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  padding-top: 16px;

  // extra padding for sidebar expand button only for tablets/touch devices
  /*@media (pointer: coarse), (max-width: 1200px) {
    padding-top: 32px;
  }*/

  .pageSection {
    display: flex;
    flex-direction: column;
    gap: 8px;
    width: 100%;
    overflow-y: auto;
    .groupLinks {
      .page {
        padding: 12px 32px;
        align-items: center;
        .title {
          margin-left: 17px;
        }
      }
    }
    .userSection {
      background-color: #e8f0fc;
      border-radius: 20px;
      padding: 15px 10px;
      margin: 0 10px;
      display: flex;
      flex-direction: column;
      gap: 16px;

      .userData {
        display: flex;
        justify-content: space-between;

        align-items: center;
        div {
          margin-bottom: 8px;
          span {
            color: #667085;
            font-size: 12px;
          }
          p {
            color: #061632;
            font-size: 14px;
          }
        }
        a {
          text-decoration: none;
        }
        .notificationBellButton {
          background-color: var(--primary-100);
          color: #2e70e5;
          border-radius: 50%;
          width: 32px;
          height: 32px;
          padding: 7px;
          cursor: pointer;
          border: none;

          &:hover {
            background-color: var(--primary-200);
          }

          &:active {
            scale: 0.9;
          }
        }
        button {
          height: 36px;
          padding: 0 12px;
          font-size: 14px;
          margin-right: 3px;
          svg {
            width: 16px;
          }
        }
      }
    }
  }
}

.closed {
  width: 48px;
  .pages {
    .pageSection {
      .groupLinks {
        .page {
          padding: 12px 16px;
        }
      }
    }
  }
  svg {
    margin-left: 4px;
  }
  .page {
    .title {
      opacity: 0;
    }
  }
}

.page {
  flex-shrink: 0;
  height: 40px;
  width: 100%;
  display: grid;
  grid-template-columns: 16px auto auto;
  padding: 8px 16px;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  overflow: hidden;
  color: #061632;
  position: relative;
  &.pageActive {
    color: #2e70e5;
    background-color: #d6e3fa;
    cursor: auto;
    svg {
      stroke: #2e70e5 !important;
    }
  }
  .title {
    margin-left: 8px;
    opacity: 1;
    font-size: 14px;
    line-height: 18px;
  }
  .head {
    font-size: 12px;
    color: #838ca0;
  }
  &.pageLink {
    &:hover {
      background-color: #d6e3fa;
    }
  }
  span {
    position: relative;
    z-index: 6;
  }
  svg {
    width: 20px;
    height: 20px;
    transition: all 0.2s ease;
    margin-left: auto;
    position: relative;
    z-index: 6;
    stroke: #061632;
  }
}

.toggleExpandButton {
  @media (pointer: fine), (min-width: 1200px) {
    display: none;
  }

  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-block: 16px;
  width: 24px;
  height: 34px;
  background: white;
  border-radius: 4px;
  right: -20px;
  top: 44px;
  color: #061632;
  box-shadow: 2px 0px 8px 0px #0000000d;

  &.bottom {
    top: auto;
    bottom: 24px;
  }

  svg {
    width: 18px;
    height: 18px;
    position: relative;
    left: 2px;
  }

  &.isExpanded {
    svg {
      transform: rotate(180deg);
    }
  }

  // mask box-shadow from showing in area that overlaps with sidebar
  &::after {
    content: "";
    position: absolute;
    left: -5px;
    height: 110%;
    width: 10px;
    background: white;
    z-index: 10;
  }
}
