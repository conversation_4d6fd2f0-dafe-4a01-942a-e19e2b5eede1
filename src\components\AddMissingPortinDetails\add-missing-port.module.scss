@use "../../styles/theme.scss" as *;

.main {
  margin: auto;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.eligible {
  border: 2px solid $success;
  color: $success;
  margin-bottom: 12px;
  align-items: center;
  padding: 12px 16px;
  border-radius: 8px;
  display: grid;
  grid-template-columns: 32px 1fr;
  grid-column-gap: 12px;
}
.inputsGrid {
  display: grid;
  width: 389px;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: repeat(5, auto);
  grid-column-gap: 16px;
  grid-row: 16px;
  form:nth-child(1) {
    grid-area: 1/1/2/2;
  }
  form:nth-child(2) {
    grid-area: 1/2/2/3;
  }
  form:nth-child(3) {
    grid-area: 2/1/3/3;
  }
  form:nth-child(4) {
    grid-area: 3/1/4/3;
  }
}
.oldNumber {
  width: 430px;
}
.notEligible {
  border: 2px solid $urgent;
  margin-bottom: 24px;
  color: $urgent;
  .topText {
    margin-bottom: 6px;
    color: $placeholder;
  }
  .bottomText {
    color: $placeholder;
    font-size: 12px;
    line-height: 18px;
  }
}
.eligible {
  border: 2px solid $success;
  color: $success;
  margin-bottom: 12px;
  align-items: center;
}

.eligible,
.notEligible {
  padding: 12px 16px;
  border-radius: 15px;
  display: grid;
  grid-template-columns: 32px 1fr;
  grid-column-gap: 12px;
}
