import Dialog from "@/components/Dialog";
import { PlusCircle } from "@/components/svgs";
import {
  clearInput,
  createStateObject,
  handleInputChange,
  labels,
} from "@/components/utils/InputHandlers";
import { useState } from "react";
import styles from "./create-global-credit-modal.module.scss";
import { Input } from "@/components/Input";
import ToggleButtonGroup from "@/components/ToggleButtonGroup";
import SelectInput from "@/components/SelectInput";

type CreateGlobalCreditModalProps = {
  open: boolean;
  onClose: () => void;
};

const CreateGlobalCreditModal = ({
  open,
  onClose,
}: CreateGlobalCreditModalProps) => {
  const fields = ["creditAmount", "currency", "status"];
  const [formData, setFormData] = useState(createStateObject(fields));

  return (
    <Dialog
      open={open}
      onClose={onClose}
      size="sm"
      headerIcon={<PlusCircle />}
      headerTitle="Create Global Credit Product"
      confirmButtonText="Create Product"
      confirmButtonOnClick={onClose}
      cancelButtonText="Cancel"
    >
      <div className={styles.formContainer}>
        {fields.map((field) => {
          if (field === "creditAmount") {
            return (
              <Input
                key={"product-" + field}
                label={labels[field]}
                value={formData[field]}
                onChange={(e: any) => {
                  handleInputChange(field, e, formData, setFormData);
                }}
                error={formData.errors[field]}
                clear={() => {
                  clearInput(field, setFormData);
                }}
                infoTooltipText
                number
              />
            );
          } else if (["status", "currency"].includes(field)) {
            return (
              <ToggleButtonGroup
                key={"product-" + field}
                selected={formData[field]}
                options={toggleButtonGroupOptionsByField[field]}
                label={labels[field]}
                onChange={(value) => {
                  handleInputChange(
                    field,
                    { target: { value } },
                    formData,
                    setFormData
                  );
                }}
              />
            );
          }
          return null;
        })}
      </div>
    </Dialog>
  );
};

export default CreateGlobalCreditModal;

const toggleButtonGroupOptionsByField: Record<string, any> = {
  currency: [
    {
      label: "GBP",
      key: "GBP",
    },
    {
      label: "EUR",
      key: "EUR",
    },
    {
      label: "USD",
      key: "USD",
    },
  ],
  status: [
    {
      label: "Active",
      key: "active",
    },
    {
      label: "Inactive",
      key: "inactive",
    },
  ],
};
