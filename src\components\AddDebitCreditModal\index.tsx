import styles from "./debit-credit.module.scss";
import Modal from "../Modal";
import { Export, FloppyDisk } from "../svgs";
import { Input } from "../Input";
import SelectInput from "../SelectInput";
import { useEffect, useState } from "react";
import { validateAll } from "indicative/src/Validator";
import { useDispatch } from "react-redux";
import { ApiPatch, ApiPostAuth } from "../../pages/api/api";
import Toggle from "../Toggle";
import {
  clearInput,
  createStateObject,
  displayErrors,
  getMessages,
  getRules,
  handleInputChange,
  labels,
  placeholders,
} from "../utils/InputHandlers";
import StatusSwitch from "../StatusSwitch";
import CodecsSwitch from "../CodecsSwitch";
import { UploadButton } from "../UploadButton";
import TypeSwitch from "../TypeSwitch";

const creditFields = ["creditAmount", "creditReason"];
const debitFields = ["debitAmount", "debitReason"];
const rulesCredit = getRules(creditFields);
const messagesCredit = getMessages(creditFields);
const rulesDebit = getRules(debitFields);
const messagesDebit = getMessages(debitFields);

const DebitCreditModal = ({
  show,
  setShow,
  page,
  email,
  clearContainer,
  populate,
}: any) => {
  const dispatch = useDispatch();

  const [data, setData] = useState(
    createStateObject(page === "credit" ? creditFields : debitFields)
  );
  const [loading, setLoading] = useState(false);

  const addCreditDebit = () => {
    if (page === "credit") {
      setLoading(true);

      const creditData = {
        emailId: email,
        credit: Number(data.creditAmount),
        reason: data.creditReason,
      };
      validateAll(data, rulesCredit, messagesCredit)
        .then((response) => {
          ApiPostAuth("/customer/credit", creditData)
            .then((response) => {
              dispatch({
                type: "notify",
                payload: {
                  error: false,
                  message: response.data.message,
                },
              });
              setShow(false);
              setLoading(false);
              populate();
            })
            .catch((error) => {
              dispatch({
                type: "notify",
                payload: {
                  error: true,
                  message: error.response.data.message,
                },
              });
              setLoading(false);
            });
        })
        .catch((errors) => {
          displayErrors(errors, setData);
          setLoading(false);
        });
    } else {
      setLoading(true);

      const debitData = {
        amount: data.debitAmount,
        reason: data.debitReason,
        emailId: email,
      };
      validateAll(data, rulesDebit, messagesDebit)
        .then((response) => {
          ApiPostAuth("/customer/debit", debitData)
            .then((response) => {
              dispatch({
                type: "notify",
                payload: {
                  error: false,
                  message: response.data.message,
                },
              });
              setShow(false);
              setLoading(false);
              populate();
            })
            .catch((error) => {
              dispatch({
                type: "notify",
                payload: {
                  error: true,
                  message: error.response.data.message,
                },
              });
              setLoading(false);
            });
        })
        .catch((errors) => {
          displayErrors(errors, setData);
          setLoading(false);
        });
    }
  };

  return (
    <Modal
      clearContainer={clearContainer}
      saveButton={
        <>
          <FloppyDisk />
          Save Changes
        </>
      }
      image="/edit_user_graphic.svg"
      show={show}
      setShow={setShow}
      proceed={addCreditDebit}
      close={() => setShow(false)}
      onClose={() => setShow(false)}
      loading={loading}
    >
      <div className={`${styles.main} normal-select-input`}>
        <h3>{page === "credit" ? "Add Credit" : "Debit Account"}</h3>
        {(page === "credit" ? creditFields : debitFields).map((prop) => (
          <Input
            key={`${prop}`}
            label={labels[prop]}
            placeholder={placeholders[prop]}
            value={data[prop]}
            onChange={(e: any) => {
              handleInputChange(prop, e, data, setData);
            }}
            error={data.errors[prop]}
            onKeyDown={addCreditDebit}
            clear={() => {
              clearInput(prop, setData);
            }}
            disabled={loading}
            white
          />
        ))}
      </div>
    </Modal>
  );
};

export default DebitCreditModal;
