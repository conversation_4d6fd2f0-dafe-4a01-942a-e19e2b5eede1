import { Visa, MasterCard } from "@/components/svgs";
import styles from "./payment-method-display.module.scss";

type PaymentMethodDisplayProps = {
  type: string;
  lastDigits?: string | number;
};

const PaymentMethodDisplay = ({
  type,
  lastDigits,
}: PaymentMethodDisplayProps) => {
  const renderPaymentIcon = () => {
    switch (type.toLowerCase()) {
      case "visa":
        return <Visa />;
      case "mastercard":
        return <MasterCard />;
      default:
        return null;
    }
  };

  return (
    <div className={styles.container}>
      {renderPaymentIcon() || (
        <span className={styles.text}>
          {type.charAt(0).toUpperCase() + type.slice(1).toLowerCase()}
        </span>
      )}
      {lastDigits && (
        <>
          <span className={styles.dots}>••••</span>
          <span className={styles.digits}>{lastDigits}</span>
        </>
      )}
    </div>
  );
};

export default PaymentMethodDisplay;
