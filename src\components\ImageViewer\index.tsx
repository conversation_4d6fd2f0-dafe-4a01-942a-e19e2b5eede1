import React, { useState } from "react";
import { Dialog } from "@mui/material";

function ImageViewer({ imageUrl, altText, style }: any) {
  const [open, setOpen] = useState(false);

  const handleClose = () => {
    setOpen(false);
  };

  const handleOpen = () => {
    setOpen(true);
  };

  return (
    <div>
      <img
        src={imageUrl}
        alt={altText}
        style={{ cursor: "pointer", ...style }}
        onClick={handleOpen}
      />

      <Dialog open={open} onClose={handleClose}>
        <img src={imageUrl} alt={altText} style={{ width: "100%" }} />
      </Dialog>
    </div>
  );
}

export default ImageViewer;
