import { useCallback, useEffect, useRef, useState } from "react";
import styles from './global-credit-tab.module.scss';
import CollapsiblePanel from "@/components/CollapsiblePanel";
import InsightList from "@/components/InsightList";
import Button from "@/components/Button";
import { MagnifyingGlass, Plus, Delete, Export, Pencil } from "@/components/svgs";
import InsightCard from "@/components/InsightCard";
import { clearInput, createStateObject, handleInputChange, labels } from "@/components/utils/InputHandlers";
import Tag from "@/components/Tag";
import SelectInput from "@/components/SelectInput";
import { getHutchGlobalCreditProducts } from "@/components/utils/dataCreator";
import UserSkeleton from "@/components/UserSkeleton";
import TableControl from "@/components/TableControl";
import { globalCreditFields } from "@/components/utils/globalCreditFields";
import { formatDateWords } from "@/components/utils/formatDate";
import StatusPill from "@/components/StatusPill";
import CreateGlobalCreditModal from "@/components/CreateGlobalCreditModal";
import EditGlobalCreditModal from "@/components/EditGlobalCreditModal";
import DeleteGlobalCreditModal from "@/components/DeleteGlobalCreditModal";
import CheckboxDropdownInput from "@/components/CheckboxDropdownInput";

const HutchGlobalCreditTab = () => {
  const searchFields = ["creditAmount", "currency", "status"];
  const [data, setData] = useState(createStateObject(searchFields));

  const overviewPanelRef = useRef<any>(null);
  const searchPanelRef = useRef<any>(null);
  const [showSearchResults, setShowSearchResults] = useState(false);

  const [loading, setLoading] = useState(true);
  const [products, setProducts] = useState<any[]>([]);
  const [itemsPerPage, setItemsPerPage] = useState(7);
  const [numberOfPages, setNumberOfPages] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);

  const [showCreateProductModal, setShowCreateProductModal] = useState(false);
  const [activelyEditedProduct, setActivelyEditedProduct] = useState(null);
  const [activelyDeletedProduct, setActivelyDeletedProduct] = useState(null);

  const populate = (itemsPerPage: number) => {
    setLoading(true);
    setTimeout(() => {
      setProducts(getHutchGlobalCreditProducts(itemsPerPage))
      setLoading(false)
    }, 500)
  }

  useEffect(() => {
    populate(itemsPerPage)
  }, [itemsPerPage, currentPage])

  const formatDataItem = (item: any, key: string) => {
    if (key === "dateAdded") {
      return formatDateWords(item[key])
    }

    if (key === "status") {
      return <StatusPill status={item[key] ? "Active" : "Inactive"} color={item[key] ? "active" : "inactive"} />
    }

    if (!item[key]) {
      return '-'
    }

    return item[key]
  }

  const renderCreateProductButton = useCallback(() => (
    <Button onClick={(e) => {
      e.stopPropagation()
      setShowCreateProductModal(true)
    }}>
      <Plus />
      Create Product
    </Button>
  ), [])

  return (
    <>
      {showCreateProductModal && (
        <CreateGlobalCreditModal
          open={showCreateProductModal}
          onClose={() => setShowCreateProductModal(false)}
        />
      )}

      {!!activelyEditedProduct && (
        <EditGlobalCreditModal
          onClose={() => setActivelyEditedProduct(null)}
          open={!!activelyEditedProduct}
          productData={activelyEditedProduct}
        />
      )}

      {!!activelyDeletedProduct && (
        <DeleteGlobalCreditModal
          onClose={() => setActivelyDeletedProduct(null)}
          open={!!activelyDeletedProduct}
          productData={activelyDeletedProduct}
        />
      )}

      {/* Overview */}
      <CollapsiblePanel
        title="Overview"
        summaryWhenClosed={
          <div className={styles.overviewSummaryWrapper}>
            <InsightList insights={overviewStats} />
            {renderCreateProductButton()}
          </div>
        }
        headerWhenOpen={
          <div className={styles.overviewHeaderOpenWrapper}>
            {renderCreateProductButton()}
          </div>
        }
        ref={overviewPanelRef}
      >
        <div className={styles.overview}>
          {overviewStats.map((stat, index) => (
            <InsightCard key={index} title={stat.title} value={stat.value.toString()} />
          ))}
        </div>
      </CollapsiblePanel>

      {/* Search */}
      <div style={{ marginTop: 16 }}>
        <CollapsiblePanel
          title="Search Global Credit Products"
          summaryWhenClosed={
            <div style={{ display: 'flex', flex: 1, justifyContent: 'space-between', marginLeft: 16, marginRight: 8 }}>
              <Tag text="3 filters applied" />
              <Button color="secondary">Clear Filters</Button>
            </div>
          }
          ref={searchPanelRef}
        >
          <div className={styles.searchPanelForm}>
            <div className={styles.fields}>
              {searchFields.map((prop) => (
                <CheckboxDropdownInput
                  key={"promotions-" + prop}
                  options={selectOptionsByField[prop]}
                  label={labels[prop]}
                  selected={data[prop]}
                  onChange={(values) => {
                  handleInputChange(
                      prop,
                      values,
                      data,
                      setData,
                      'select'
                  )}}
                  error={data.errors[prop]}
                  infoTooltipText
                />
              ))}
            </div>
            <div className={styles.actions}>
              <Button
                color="blue"
                onClick={() => {
                  searchPanelRef.current.close()
                  overviewPanelRef.current.close()
                  setShowSearchResults(true)
                }}
              ><MagnifyingGlass /> Search</Button>
            </div>
          </div>
        </CollapsiblePanel>
      </div>

      {/* Table */}
      <div className={styles.panel}>
        <div className={styles.panelTopBar}>
          <h4>Global Credit Products</h4>
          <div className={styles.actions}>
            <Button color="secondary">
              <Export /> Export to CSV
            </Button>
          </div>
        </div>

        <div className={`${styles.tableContainer} table-scroll`}>
          <table>
            <thead>
              <tr>
                {globalCreditFields.map((field: any) => (
                  <th key={field.key}>{field.label}</th>
                ))}
                <th></th>
              </tr>
            </thead>
            <tbody>
              {!loading
                ? products.map((item: any, i: number) => (
                  <tr key={`product-${i}`}>
                    {globalCreditFields.map((field: any) => (
                      <td key={`product-${i}-${field.key}`}>
                        <div style={{ display: "flex", justifyContent: "flex-start" }}>
                          {formatDataItem(item, field.key)}
                        </div>
                      </td>
                    ))}
                    <td style={{ width: 100 }}>
                      <div className={styles.tableRowActions}>
                        <span onClick={() => setActivelyEditedProduct(item)}>
                          <Pencil />
                        </span>
                        <span onClick={() => setActivelyDeletedProduct(item)}>
                          <Delete />
                        </span>
                      </div>
                    </td>
                  </tr>
                ))
                : Array.from({ length: itemsPerPage }, (v, i) => i).map(
                  (i) => (
                    <UserSkeleton
                      key={"product-skeleton-" + i}
                      noOfStandard={globalCreditFields.length}
                    />
                  )
                )}
            </tbody>
          </table>
        </div>
        <div style={{ marginTop: "16px" }}>
          <TableControl
            show
            itemsPerPage={itemsPerPage}
            setItemsPerPage={(val: any) => setItemsPerPage(val)}
            currentPage={currentPage}
            setCurrentPage={setCurrentPage}
            numberOfPages={numberOfPages}
            label="products"
            loading={loading}
          />
        </div>
      </div>
    </>
  )
}

export default HutchGlobalCreditTab;

const overviewStats = [
  { title: 'Total Products', value: '45,234' },
  { title: 'Active Products', value: '38,129' },
  { title: 'Inactive Products', value: '7,105' },
]

const selectOptionsByField: Record<string, any> = {
  creditAmount: ["5", "10", "15", "20", "25", "30", "35"].map(v => ({
    label: `${v} Credits`,
    value: v
  })),
  currency: ["GBP", "EUR", "USD"].map(v => ({
    label: v,
    value: v
  })),
  status: [
    { label: "Active", value: "active" },
    { label: "Inactive", value: "Inactive" },
  ],
}; 