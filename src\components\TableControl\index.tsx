import { useEffect, useState } from "react";
import Pagination from "../Pagination";
import styles from "./table-control.module.scss";

const TableControl = ({
  itemsPerPage,
  setItemsPerPage,
  currentPage,
  setCurrentPage,
  numberOfPages,
  label,
  show,
  loading = false,
}: any) => {
  const offset =
    currentPage === 0 || currentPage === 1
      ? 1
      : itemsPerPage * (currentPage - 1) + 1;
  const total = numberOfPages * itemsPerPage;

  const [endCountInRow, setEndCountInRow] = useState(itemsPerPage);

  useEffect(() => {
    setEndCountInRow(currentPage * itemsPerPage);
  }, [currentPage, itemsPerPage]);

  return (
    <div className={styles.pagination}>
      <div className={styles.ticketsPerPage}>
        <p>Rows per page</p>
        <select
          value={itemsPerPage}
          onChange={(e) => setItemsPerPage(Number(e.target.value))}
        >
          {[5, 10, 15, 45, 75, 100].map((itemsCount) => (
            <option value={itemsCount}>{itemsCount}</option>
          ))}
        </select>
        <p>
          {offset}-{endCountInRow} of {total}
        </p>
      </div>

      <Pagination
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        numberOfPages={numberOfPages}
        loading={loading}
      />
    </div>
  );
};

export default TableControl;
