@import './mixins.module.scss';
@import "./table-mixin.module.scss";

.main {
  padding: 16px;
}

.panel {
  @include panel;
  @include table;

  margin-top: 16px;
}

.overview {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.fields {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-top: 20px
}
.actions {
  display: flex;
  justify-content: end;
}

.tableContainer {
  overflow: auto;
  padding-bottom: 5px;
  margin-top: 20px;
  border-radius: 16px;

  .viewRowBtn {
    color: var(--button-tertiary-text);
    font-size: 14px;
    font-weight: 700;
    text-decoration: underline;
    cursor: pointer;
    visibility: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    span {
      width: 57px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  tr:first-of-type .viewRowBtn {
    visibility: visible;
  }
  td {
    width: 12%;
  }
  tr:hover .viewRowBtn{
    visibility: visible;
  }
}

