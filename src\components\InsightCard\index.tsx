import React from "react";
import styles from "./insight-card.module.scss";
import { SparklesCircle } from "@/components/svgs";

type StatCardProps = {
  title: string;
  value: string;
  icon?: React.ReactNode;
};

const InsightCard = (props: StatCardProps) => {
  const { title, icon = <SparklesCircle />, value } = props;

  return (
    <div className={styles.card}>
      <div className={styles.icon}>{icon}</div>
      <div className={styles.content}>
        <h3 className={styles.title}>{title}</h3>
        <p className={styles.value}>{value}</p>
      </div>
    </div>
  );
};

export default InsightCard;
