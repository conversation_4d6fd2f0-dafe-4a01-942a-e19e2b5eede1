import Button from "@/components/Button";
import CollapsiblePanel from "@/components/CollapsiblePanel";
import Tag from "@/components/Tag";
import { clearInput, createStateObject, handleInputChange, labels } from "@/components/utils/InputHandlers";
import { useEffect, useRef, useState } from "react";
import styles from "./fees-tab.module.scss"
import { Input } from "@/components/Input";
import SelectInput from "@/components/SelectInput";
import { Export, MagnifyingGlass, Settings } from "@/components/svgs";
import UserSkeleton from "@/components/UserSkeleton";
import TableControl from "@/components/TableControl";
import { formatDateWithTime, formatDateWords } from "@/components/utils/formatDate";
import StatusPill from "@/components/StatusPill";
import EditFeeModal from "@/components/EditFeeModal";
import DeleteFeeModal from "@/components/DeleteFeeModal";
import FeeDetailModal from "@/components/FeeDetailModal";
import CheckboxDropdownInput from "@/components/CheckboxDropdownInput";


type FeesTabProps = {
  feesData: any
}

const FeesTab = ({ feesData }: FeesTabProps) => {
  const searchFields = ["name", "type", "status"]
  const [data, setData] = useState(createStateObject(searchFields));

  const searchPanelRef = useRef<any>(null);
  // const [showSearchResults, setShowSearchResults] = useState(false);

  const [loading, setLoading] = useState(true);
  const [fees, setFees] = useState<any[]>([]);

  const populate = (itemsPerPage: number) => {
    setLoading(true);

    setTimeout(() => {
      setFees(feesData)
      setLoading(false)
    }, 500)
  }

  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [numberOfPages, setNumberOfPages] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);

  useEffect(() => {
    populate(itemsPerPage)
  }, [itemsPerPage, currentPage])

  const formatDataItem = (item: any, key: string) => {
    if (key === "createdAt") {
      return formatDateWithTime(item[key])
    }

    if (key === "status") {
      const status = item[key]
      return <StatusPill status={status ? "Active" : "Inactive"} color={status ? "active" : "inactive"} />
    }

    if (key === "amount") {
      return `$${(item[key] || 0).toFixed(2)}`
    }

    else {
      return item[key]
    }
  }

  const [activelyEditedFee, setActivelyEditedFee] = useState(null)
  const [activelyDeletedFee, setActivelyDeletedFee] = useState(null)
  const [activelyViewedFee, setActivelyViewedFee] = useState(null)


  return (
    <>
      {!!activelyEditedFee && (
        <EditFeeModal
          onClose={() => setActivelyEditedFee(null)}
          open={!!activelyEditedFee}
          feeData={activelyEditedFee}
        />
      )}
      {!!activelyDeletedFee && (
        <DeleteFeeModal
          onClose={() => setActivelyDeletedFee(null)}
          open={!!activelyDeletedFee}
          feeData={activelyDeletedFee}
        />
      )}
      {!!activelyViewedFee && (
        <FeeDetailModal
          feeData={activelyViewedFee}
          open={!!activelyViewedFee}
          onClose={() => setActivelyViewedFee(null)}
          onEditFee={() => setActivelyEditedFee(activelyViewedFee)}
          onDeleteFee={() => setActivelyDeletedFee(activelyViewedFee)}
        />)}

      {/* Search */}
      <div style={{ marginTop: 4 }}>
        <CollapsiblePanel
          title="Search Fees"
          summaryWhenClosed={
            <div style={{ display: 'flex', flex: 1, justifyContent: 'space-between', marginLeft: 16, marginRight: 8 }}>
              <Tag text="3 filters applied" />
              <Button color="secondary">Clear Filters</Button>
            </div>
          }
          ref={searchPanelRef}
        >
          <div className={styles.searchPanelForm}>
            <div className={styles.fields}>
              {searchFields.map((prop) => {
                if (["name"].includes(prop)) {
                  return (
                    <Input
                      key={"att-" + prop}
                      label={labels[prop]}
                      value={data[prop]}
                      onChange={(e: any) => {
                        handleInputChange(prop, e, data, setData);
                      }}
                      error={data.errors[prop]}
                      clear={() => {
                        clearInput(prop, setData);
                      }}
                      infoTooltipText
                    />)
                } else if (["type", "status"].includes(prop)) {
                  return (
                    <CheckboxDropdownInput
                      key={"promotions-" + prop}
                      options={selectOptionsByField[prop]}
                      label={labels[prop]}
                      selected={data[prop]}
                      onChange={(values) => {
                      handleInputChange(
                          prop,
                          values,
                          data,
                          setData,
                          'select'
                      )}}
                      error={data.errors[prop]}
                      infoTooltipText
                    />
                  )
                }
              })}
            </div>
            <div className={styles.actions}>
              <Button
                color="blue"
                onClick={() => {
                  searchPanelRef.current.close()
                  // setShowSearchResults(true)

                }}
              ><MagnifyingGlass /> Search</Button>
            </div>
          </div>
        </CollapsiblePanel>
      </div>

      {/* Table */}
      {<div className={styles.panel}>
        <div className={styles.panelTopBar}>
          <h4>Fees</h4>
          <div className={styles.actions}>
            <Button color="secondary">
              <Export /> Export to CSV
            </Button>
          </div>
        </div>

        <div className={`${styles.tableContainer} table-scroll`}>
          <table>
            <thead>
              <tr>
                {feesColumns.map((field: any) => (
                  <th>{field.label}</th>
                ))}
                <th></th>
              </tr>
            </thead>
            <tbody>
              {!loading
                ? fees.map((item: any, i: number) => {
                  return (
                    <tr key={`fee-${i}`}
                    >
                      {feesColumns.map((field: any) => (
                        <td key={`fee-${i}-${field.key}`}>
                          <div style={{ display: "flex", justifyContent: "flex-start" }}>
                            {formatDataItem(item, field.key)}
                          </div>
                        </td>
                      ))}
                      <td style={{ width: 100 }}>
                        <div className={styles.tableRowActions}>
                          <span
                            onClick={() => setActivelyEditedFee(item)}
                          >
                            <Settings />
                          </span>
                          <span
                            className={styles.viewRowBtn}
                            onClick={() => { setActivelyViewedFee(item) }}
                          >
                            View
                          </span>
                        </div>
                      </td>
                    </tr>
                  )
                }) : Array.from({ length: itemsPerPage }, (v, i) => i).map(
                  (i) => (
                    <UserSkeleton
                      key={"order-skeleton-" + i}
                      noOfStandard={6}
                    />
                  )
                )}
            </tbody>
          </table>
        </div>
        <div style={{ marginTop: "16px" }}>
          <TableControl
            show
            itemsPerPage={itemsPerPage}
            setItemsPerPage={(val: any) => setItemsPerPage(val)}
            currentPage={currentPage}
            setCurrentPage={setCurrentPage}
            numberOfPages={numberOfPages}
            label="orders"
            loading={loading}
          />
        </div>
      </div>}
    </>
  )
}

export default FeesTab

const selectOptionsByField: Record<string, any> = {
  type: [
    { label: "Regulatory", value: "Regulatory" },
    { label: "Activation", value: "Activation" },
  ],
  status: [
    { label: "Active", value: "active" },
    { label: "Inactive", value: "Inactive" },
  ],
}

const feesColumns = [
  {
    label: 'Type',
    key: 'type'
  },
  {
    label: 'Name',
    key: 'name'
  },
  {
    label: 'Amount',
    key: 'amount'
  },
  {
    label: 'Creation Date',
    key: 'createdAt'
  },
  {
    label: 'Status',
    key: 'status'
  }
]