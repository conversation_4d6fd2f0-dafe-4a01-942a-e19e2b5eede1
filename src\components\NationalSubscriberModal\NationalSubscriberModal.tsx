import { Customer } from "@/types/customer";
import styles from "../TravellerCustomerPage/traveller-customer.module.scss";
import SwitchBar from "../SwitchBar";
import { useState } from "react";
import Button from "../Button";
import { Close, Delete, Pencil } from "../svgs";
import BlobSwitchBar from "../BlobSwitchBar";
import { SwitchTransition, CSSTransition } from "react-transition-group";
import NationalSubscriptionsTab from "../NationalSubscriptionsTab/NationalSubscriptionsTab";
import TransactionHistory from "../TransactionHistory";
import EditAccountModal from "../EditAccountModal/EditAccountModal";

type PropTypes = {
  customer: Customer;
  close: Function
};

const NationalSubscriberModal = ({ customer, close }: PropTypes) => {
  const [selected, setSelected] = useState("manage");
  const [mainSelected, setMainSelected] = useState("subscriptions");
  const [showEditAccountModal, setShowEditAccountModal] = useState(false)

  const sections = [
    {
      id: "subscriptions",
      label: "Subscriptions",
    },
    {
      id: "transactions",
      label: "Transaction History",
    }
  ];

  return (
    <div className={`${styles.container}`}>
      <div className={styles.modal}>
        <EditAccountModal show={showEditAccountModal} close={(val:boolean) => setShowEditAccountModal(val)} />
        <div className={styles.leftColumn}>
          <img
            className={styles.profilePicture}
            src={customer?.profilePicture}
          />
          <div className={styles.name}>
            {customer?.firstName} {customer?.lastName}
          </div>
          <SwitchBar
            options={[
              {
                id: "manage",
                label: "Manage",
              },
              {
                id: "account",
                label: "Account Info",
              },
            ]}
            selected={selected}
            setSelected={setSelected}
            layoutId="customer-sidebar"
          />
          <div className={styles.tabsContainer}>
            {selected === "manage" && <div className={styles.manageTab}>
              <Button color="customerActionBlue" onClick={() => setShowEditAccountModal(true)}>
                <Pencil />
                Edit Account Details
              </Button>
              <Button color="customerActionRed">
                <Delete />
                Delete Account
              </Button>
            </div>}
          </div>
        </div>
        <div className={styles.mainSection + ' modal-scroll'}>
          <BlobSwitchBar
            options={sections}
            selected={mainSelected}
            setSelected={setMainSelected}
            layoutId="subscriber-main-selection"
          />
          <div className={styles.close} onClick={() => close(false)}>
            <Close />
          </div>
          <SwitchTransition>
            <CSSTransition
              key={mainSelected}
              addEndListener={(node, done) =>
                node.addEventListener("transitionend", done, false)
              }
              classNames="fade"
            >
              {mainSelected === "subscriptions" ? (
                <NationalSubscriptionsTab customer={customer} />
              ) : (
                <TransactionHistory />
              )}
            </CSSTransition>
          </SwitchTransition>
        </div>
      </div>
    </div>
  );
};

export default NationalSubscriberModal;
