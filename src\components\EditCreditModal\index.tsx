import styles from "./edit-credit-modal.module.scss";
import Modal from "../Modal";
import { AddUser, FloppyDisk } from "../svgs";
import { Input } from "../Input";
import SelectInput from "../SelectInput";
import { useEffect, useState } from "react";
import { validateAll } from "indicative/src/Validator";
import { useDispatch } from "react-redux";
import {
  clearInput,
  createStateObject,
  displayErrors,
  getMessages,
  getRules,
  handleInputChange,
  labels,
  placeholders,
} from "../utils/InputHandlers";
import StatusSwitch from "../StatusSwitch";
import SelectDropdown from "../SelectDropdown";

const fields = ["currency", "creditAmount"];
const rules = getRules(fields);
const messages = getMessages(fields);

const EditCreditModal = ({ show, setShow, selectedProduct }: any) => {
  const dispatch = useDispatch();

  const [data, setData] = useState(createStateObject(fields));

  // Reset modal data when closed
  const reset = () => {
    setData(createStateObject(fields));
    setLoading(false);
  };

  const [loading, setLoading] = useState(false);
  const [status, setStatus] = useState(false);

  const addProduct = () => {};

  useEffect(() => {
    if (selectedProduct) {
      setData({
        ...data,
        currency: {
          label: selectedProduct.currency,
          value: selectedProduct.currency,
        },
        creditAmount: {
          label: selectedProduct.amount,
          value: selectedProduct.amount,
        },
        errors: {
          currency: "",
          creditAmount: "",
        },
      });
    }
  }, [selectedProduct]);

  const currencyOptions = [
    {
      label: "GBP",
      value: "GBP",
    },
    {
      label: "USD",
      value: "USD",
    },
    {
      label: "EUR",
      value: "EUR",
    },
  ];

  const amountOptions = [
    {
      label: "5",
      value: "5",
    },
    {
      label: "10",
      value: "10",
    },
    {
      label: "20",
      value: "20",
    },
    {
      label: "50",
      value: "50",
    },
    {
      label: "100",
      value: "100",
    },
  ];

  return (
    <Modal
      saveButton={
        <>
          <FloppyDisk />
          Save Changes
        </>
      }
      image="/edit_user_graphic.svg"
      show={show}
      setShow={setShow}
      proceed={addProduct}
      close={() => {
        setShow(false);
        setTimeout(() => {
          reset();
        }, 300);
      }}
      loading={loading}
      scroll={false}
    >
      <div className={styles.main}>
        <h3>Edit Global Credit</h3>
        <div className={styles.inputs}>
          <div style={{ width: 144, marginRight: 12 }}>
            <SelectDropdown
              value={data.currency}
              error={data.errors.currency}
              onChange={(newValue: any) => {
                setData({
                  ...data,
                  currency: newValue,
                  errors: { ...data.errors, currency: "" },
                });
              }}
              disabled={loading}
              placeholder="Currency"
              options={currencyOptions}
            />
          </div>
          <div style={{ width: 200 }}>
            <SelectDropdown
              value={data.creditAmount}
              error={data.errors.creditAmount}
              onChange={(newValue: any) => {
                setData({
                  ...data,
                  creditAmount: newValue,
                  errors: { ...data.errors, creditAmount: "" },
                });
              }}
              disabled={loading}
              placeholder="Credit Amount"
              options={amountOptions}
            />
          </div>
        </div>
        <StatusSwitch status={status} setStatus={setStatus} />
      </div>
    </Modal>
  );
};

export default EditCreditModal;
