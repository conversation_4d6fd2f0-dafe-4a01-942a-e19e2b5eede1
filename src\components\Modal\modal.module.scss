@use "../../styles/theme.scss" as *;

.container {
  width: 100%;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 0 30px;
  background: rgba(0, 0, 0, 0.6);
  &.clear {
    background: none;
  }
}

.customBtn {
  display: flex;
  flex-grow: 1;
  justify-content: end;
}

.modal {
  max-width: 1350px;
  min-width: 450px;
  border-radius: 17px;
  background: #fff;
  display: flex;
  justify-content: end;
  padding: 9px 0px;
  grid-template-columns: 1fr 705px;
  .circledIcon {
    @include circledIcon;
    margin-bottom: 10px;
    &.success {
      color: #1a5625;
      stroke: #1a5625;
      background: #dcf4e0;
    }
  }
  .icon {
    width: 48px;
    height: 48px;
    padding: 12px;
    margin-top: 10px;
  }
  .closeBtn {
    position: absolute;
    width: 40px;
    height: 40px;
    right: 10px;
    top: 20px;
    .close {
      @include closeButton;
    }
  }
}

.imgContainer {
  height: 90vh;
  max-height: 662px;
  max-width: calc(90vh * 0.92);
  width: 100%;
}

.illustration {
  height: 100%;
  width: auto;
  box-shadow: 20px 0px 40px rgba(0, 95, 210, 0.1);
}

.main {
  width: 100%;
  height: 100%;
  max-width: 500px;
  max-height: 662px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  padding: 32px;
  position: relative;
}

.close.fullSize {
  position: absolute;
  svg {
    width: 20px;
    height: 20px;
    margin: 0;
  }
}

.content {
  align-self: center;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  font-size: 14px;
  color: #667085;
  &.fullSize {
    margin-top: 0;
  }
  h3 {
    font-weight: 700;
    font-size: 24px;
    line-height: 36px;
    text-align: center;
  }
}

.buttons {
  display: flex;
  margin-top: 15px;
  button {
    height: 48px;
  }
}

.title {
  margin: 0 0 24px 0;
  width: 100%;
  font-weight: 700;
  font-size: 20px;
  color: #061632;
  line-height: 36px;
}

.subtitle {
  width: 100%;
  font-weight: 400;
  font-size: 14px;
  line-height: 21px;
  color: #667085
}
