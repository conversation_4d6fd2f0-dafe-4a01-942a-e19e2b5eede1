import { faker } from "@faker-js/faker";
import { v4 as uuid } from "uuid";
import { countryListAlpha2 } from "./countryList";


export const priorities = [4, 3, 2, 1];
export const categories = [
  "Mobilise",
  "Technical Support",
  "Finance",
  "Product Management",
];
export const packages = [1, 5, 10, 20, 50, 100, 75, 150, 200, 250, 300, 350, 400, 450, 500, 550, 600, 650, 700, 750]
export const projectType = ['MVNO', 'Reseller', 'Affiliate'];
export const projectStatus = ['Active', 'Suspended', 'Pending', 'Deactivated']
export const assignees = [
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "Malwina Roczniak",
];
export const statuses = [2, 3, 4, 5];
export const types = [
  "Support",
  "Support",
  "Support",
  "Support",
  "DID Request",
  "Porting",
];

export const ticketsTypes = ["Billing", "Support", "Payment", "Maintenance", "Invoice"]

const success = ["Success", "Failed"]

const availabilities = ["Available", "Pending", "Registered"];
const type = ["SMS", "Call"];
const total_cost = ["Success", "Fail"];
const eSimStatuses = [
  "Available",
  "Registered",
  "Installed",
  "Enabled",
  "Disabled",
  "Deleted",
];

const providers = [
  "Alpha Provisioning Services", "Omega Mobile Systems", "Theta Provisioning Platform", "Zeta SIM Services",
  "Epsilon Connectivity", "Delta Profile Management", "Gamma eSIM Solutions", "Beta Secure Systems"
]

const portinRequestStatuses = [
  "Completed",
  "Cancelled",
  "Failed",
  "In progress",
  "Confirmed"
]
const period = [7, 14, 13];

const currencies = ["GBP", "EUR", "USD"];
const active = ["Active", "Inactive"];
const codecs = ["Yes", "No"];
const activity = [
  "Logged In",
  "Logged Out",
  "Add a Subscriber",
  "Edited a user",
  "Disabled a product",
];
const source = [
  "CRM User",
  "Aradial API Call",
  "Scheduler & Customer"
]

export const features = 
[
  {
    "name": "APEX 128Kbps",
    "code": "APEX128",
    "description": "When added to select plans this SOC throttles data usage from the device to 128Kbps.  Does not treat tethering usage.  Compatible with APEX Exclusive.",
    "classificationName": "throttle",
    "status": "Active"
  },
  {
    "name": "APEX 256Kbps",
    "code": "APEX256",
    "description": "When added to select plans this SOC throttles data usage from the device to 256Kbps.  Does not treat tethering usage. Compatible with APEX Exclusive.",
    "classificationName": "throttle",
    "status": "Inactive"
  },
  {
    "name": "APEX 512Kbps",
    "code": "APEX512",
    "description": "When added to select plans this SOC throttles data usage from the device to 512Kbps.  Does not treat tethering usage. Compatible with APEX Exclusive.",
    "classificationName": "throttle",
    "status": "Active"
  },
  {
    "name": "APEX 3Mbps",
    "code": "APEX3",
    "description": "When added to select plans this SOC throttles data usage from the device to 3Mbps.  Does not treat tethering usage.  Compatible with APEX Exclusive.",
    "classificationName": "throttle",
    "status": "Inactive"
  },
  {
    "name": "APEX 6Mbps",
    "code": "APEX6",
    "description": "When added to select plans this SOC throttles data usage from the device to 6Mbps.  Does not treat tethering usage.  Compatible with APEX Exclusive.",
    "classificationName": "throttle",
    "status": "Active"
  },
  {
    "name": "APEX 12Mbps",
    "code": "APEX12",
    "description": "When added to select plans this SOC throttles data usage from the device to 12Mbps.  Does not treat tethering usage.  Compatible with APEX Exclusive.",
    "classificationName": "throttle",
    "status": "Inactive"
  },
  {
    "name": "Data Block ",
    "code": "APEXBLOCK",
    "description": "Blocks all data when added.  To restore data, remove SOC.  Does not fall off automatically at start of next bill cycle.",
    "classificationName": "throttle",
    "status": "Active"
  },
  {
    "name": "Mobile Security Call Protect Block",
    "code": "SMRTBLOCK",
    "description": "Mobile Security Call Protect Block",
    "classificationName": "feature",
    "status": "Inactive"
  },
  {
    "name": "Caller ID Block",
    "code": "CIBL",
    "description": "Blocks name and number on caller id",
    "classificationName": "feature",
    "status": "Active"
  },
  {
    "name": "Day Pass for Resale including roaming",
    "code": "AZIRRLHDF",
    "description": "AT&T International Day Pass for Resale*",
    "classificationName": "bolton",
    "status": "Inactive"
  },
  {
    "name": "PassPort Data/Voice 10GB MRC",
    "code": "APIDV10GB",
    "description": "Must be added with API250VM to enable data on smartphones",
    "classificationName": "bolton",
    "status": "Active"
  },
  {
    "name": "International Data Bolt On 1GB Voice including roaming",
    "code": "AZIR100VM",
    "description": "Must be added with AZIRDV1GB to enable voice on smartphones",
    "classificationName": "bolton",
    "status": "Inactive"
  },
  {
    "name": "Business World Connect Advantage",
    "code": "APEXILD",
    "description": "International Long Distance Business Advantage Plan",
    "classificationName": "bolton",
    "status": "Active"
  },
  {
    "name": "PassPort DataOnly 10GB",
    "code": "AZIDX10GB",
    "description": "For data only devices",
    "classificationName": "bolton",
    "status": "Inactive"
  },
  {
    "name": "PassPort Voice/Data 10GB MRC",
    "code": "API250VM",
    "description": "Must be added with APIDV10GB to enable voice on smartphones",
    "classificationName": "bolton",
    "status": "Active"
  },
  {
    "name": "International Data Bolt On 1GB Data",
    "code": "AZIRDV1GB",
    "description": "Must be added with API250VM to enable data on smartphones.",
    "classificationName": "bolton",
    "status": "Inactive"
  },
  {
    "name": "Block International Roaming Except Mexico/Canada",
    "code": "NIRMAPEX",
    "description": "Block International Roaming Except Mexico/Canada - Compatible with all APEX plans (APEX Unlimited, APEX Mobile Select, APEX Exclusive, and Custom Plans).  ",
    "classificationName": "feature",
    "status": "Active"
  },
  {
    "name": "Message Suppression",
    "code": "TRKSOCV56",
    "description": "Blocks DUCCS notice for international alerts and other automated AT&T messages",
    "classificationName": "feature",
    "status": "Inactive"
  },
  {
    "name": "International Long Distance Block",
    "code": "ZZNOILD2",
    "description": "Blocks international LD calls from the US.  Must delete ILDSMXCAO bundled SOC when adding blocking SOC for block to occur.  Blocks calls to Canada & Mexico even if included in price plan.",
    "classificationName": "feature",
    "status": "Active"
  },
  {
    "name": "Blocking international texting",
    "code": "NOILDTEXT",
    "description": "Blocks international text from the US.",
    "classificationName": "feature",
    "status": "Inactive"
  },
  {
    "name": "ActiveArmour Basic",
    "code": "SMARTBCRU",
    "description": "AT&T ActiveArmour Basic blocks spam and fraud calls when activated.  Add this SOC to the line and the end user will receive a text to activate the service",
    "classificationName": "feature",
    "status": "Active"
  },
  {
    "name": "Stream Saver",
    "code": "DSABR2",
    "description": "Video Management",
    "classificationName": "feature",
    "status": "Inactive"
  }
]

const subscriberActivity = [
  "Edit Subscription",
  "Upgrade Subscription",
  "Downgrade Subscription",
  "Suspend Subscription",
  "Cancel Subscription",
  "Renew Subscription",
  "Transfer Subscription",
  "Terminate Subscription",
  "Porting in",
]
const mode = ["HTTP", "HTTPS"];

const prefixes = ["+12", "+44", "+1", "+39", "+32", "+91", "+86", "+81", "+33", "+61"];

// const availabilities = ['Available', 'Pending', 'Registered']
const numberType = ["Mobile", "Landline"];
const numberActive = ["Active", "Deactivated"];
const transaction_Item = ["Combo", "Data", "Did Number", "Credit"];
const activity_Item = ["Combo", "Global Credit", "N/A"];
export const plans = ['APEX 1GB LTE iPhone 2.32/GB Coverage no tether', 'APEX 5GB LTE SAMSUNG 3/GB Coverage tether', 'APEX 10GB LTE PIXEL 5/GB no Coverage no tether', 'APEX 50GB LTE iphone 30/GB no Coverage tether included', 'APEX 100GB LTE iphone 50/GB Coverage tether included']
export const topUps = ['Top-up 1 GB Offer', 'Top-up 2 GB Offer', 'Top-up 5 GB Offer', 'Top-up 10 GB Offer', 'Top-up 20 GB Offer']

const createNumber = () => {
  let alpha2 = faker.location.countryCode("alpha-2");
  return {
    // id: faker.string.uuid(),
    number: faker.phone.number("+44 #### ######"),
    country: {
      countryCode: alpha2,
      countryName: countryListAlpha2.filter(
        (country: any) => country.code === alpha2
      )[0]?.name,
      iconURL: `https://public-gist-bucket.s3.eu-north-1.amazonaws.com/flag-icons/png/${alpha2}.png`,
    },
    availability: availabilities[faker.number.int(2)],
    registeredTo: faker.internet.email(),
    type: numberType[faker.number.int(1)],
    period: period[faker.number.int(2)],
    purchasedOn: faker.date.past({ years: 1 }),
    status: numberActive[faker.number.int(1)],
  };
};

export const getNumbers = () => {
  let response = [] as any;
  Array.from({ length: 100 }).forEach(() => {
    response.push(createNumber());
  });
  return response;
};

const createPortedNumber = () => {
  let alpha2 = faker.location.countryCode("alpha-2");
  return {
    id: faker.string.uuid(),
    userId: faker.string.numeric(10),
    email: faker.internet.email(),
    portedMsisdn: faker.string.numeric(15),
    phone: faker.phone.number("### ### ###"),
    country: {
      countryCode: alpha2,
      countryName: countryListAlpha2.filter(
        (country: any) => country.code === alpha2
      )[0]?.name,
      iconURL: `https://public-gist-bucket.s3.eu-north-1.amazonaws.com/flag-icons/png/${alpha2}.png`,
    },
    city: faker.location.city(),
    status: portinRequestStatuses[faker.number.int(4)],
    zipNo: faker.location.zipCode()
  };
};

export const getPortedNumbers = (amount:number) => {
  let response = [] as any;
  Array.from({ length: amount }).forEach(() => {
    response.push(createPortedNumber());
  });
  return response;
};

const customerTickets = () => {
  const type = Math.floor(Math.random() * 6);

  return {
    id: uuid(),
    type: types[type],
    date: faker.date.past({ years: 1 }),
    subject: "Subject goes here unable to activate",
    name: faker.person.firstName() + " " + faker.person.lastName(),
    priority:
      type === 4 || type === 5
        ? null
        : priorities[Math.floor(Math.random() * 4)],
    category:
      type === 4 || type === 5
        ? null
        : categories[Math.floor(Math.random() * 4)],
    assignee: assignees[Math.floor(Math.random() * 4)],
    status: statuses[Math.floor(Math.random() * 4)],
    porting:
      type === 5
        ? {
            phoneNumber: faker.phone.number(),
            country: faker.location.country(),
            pacCode: 43521,
          }
        : null,
    didRequest:
      type === 4
        ? {
            address:
              faker.location.streetAddress() +
              ", " +
              faker.location.city() +
              ", " +
              faker.location.zipCode(),
          }
        : null,
    body: "Hi, I've downloaded a bundle but when I go to my mobile data settings, breathe sim does not come up. The only option is to 'add esim' and it then asks me for a QR code which I don't have. I have an iPhone 11 on iOS 16. What should I do?",
  };
};
export const getCustomerTickets = () => {
  let response = [] as any;
  Array.from({ length: 5 }).forEach(() => {
    response.push(customerTickets());
  });
  return response;
};
const createTicket = () => {
  const date = faker.date.past({ years: 1 })
  return {
    id: Math.floor(Math.random() * 10000),
    creationDate: date,
    subject: "Subject goes here unable to activate",
    type: ticketsTypes[Math.floor(Math.random() * 4)],
    description:
      "Hi, I've downloaded a bundle but when I go to my mobile data settings, breathe sim does not come up. The only option is to 'add esim' and it then asks me for a QR code which I don't have. I have an iPhone 11 on iOS 16. What should I do?",
    assignee: faker.person.fullName(),
    priority: priorities[Math.floor(Math.random() * 4)],
    status: statuses[Math.floor(Math.random() * 4)],
    dueDate: faker.date.between({ from: date, to: new Date() })
  };
};

export const getTickets = (items: number) => {
  let response = [] as any;
  Array.from({ length: items ? items : 200 }).forEach(() => {
    response.push(createTicket());
  });
  return response;
};

const createCustomer = () => {
  let alpha2 = faker.location.countryCode("alpha-2");
  return {
    mid: uuid(),
    firstName: faker.person.firstName(),
    lastName: faker.person.lastName(),
    email: faker.internet.email(),
    phoneNumber: faker.phone.number("+## ### ### ###"),
    country: countryListAlpha2.filter(
      (country: any) => country.code === alpha2
    )[0]?.name,
    currency: currencies[faker.number.int(2)],
    credit: faker.number.int(400),
    status: active[faker.number.int(1)],
    creationTime: faker.date.past({ years: 1 }),
    profilePicture: "/profile_pic.png"
  };
};

export const getCustomers = (amount: number) => {
  let response = [] as any;
  Array.from({ length: amount }).forEach(() => {
    response.push(createCustomer());
  });
  return response;
};

const createSubscriber = () => { 
  let size = faker.number.int(1000)
  return {
    mid: uuid(),
    firstName: faker.person.firstName(),
    lastName: faker.person.lastName(),
    subscriberNo: faker.number.int({ min: 1000000, max: 9999999 }),
    iccid: faker.number.int({ min: 100000000000000, max: 9999999999999999 }),
    imei: faker.number.int({ min: 100000000000000, max: 9999999999999999 }),
    email: faker.internet.email(),
    size: size,
    phone: faker.phone.number("+## ### ### ###"),
    status: active[faker.number.int(1)],
    creationDate: faker.date.past({ years: 1 }),
    activiationDate: faker.date.past({ years: 1 }),
    product: `APEX ${size}GB LTE`,
    profilePicture: "/profile_pic.png",
    street: faker.location.streetAddress(),
    city: faker.location.city(),
    state: faker.location.state(),
    zipNo: faker.location.zipCode()
  };
}

export const getSubscribers = (amount: number) => {
  let response = [] as any;
  Array.from({ length: amount }).forEach(() => {
    response.push(createSubscriber());
  });
  return response;
};

const createPromotion = () => {
  let maxuses = faker.number.int(200)
  let timesUsed = faker.number.int(200)
  let coderesuable = ["Yes", "No"][faker.number.int(1)]
  return {
    type: ["Fee Discount", "Discount"][faker.number.int(1)],
    name: 'PromoName',
    maxuses,
    timesUsed,
    amount: faker.finance.amount({min: 3, max: 50}),
    currency: '$',
    applyTo: ["Total", "Subtotal"][faker.number.int(1)],
    coderesuable, 
    code: faker.random.alpha({ count: faker.datatype.number({ min: 4, max: 5 }) }) + faker.datatype.number({ min: 100, max: 999 }),
    creationDate: faker.date.past({ years: 1 }),
    expiryDate: faker.date.future({ years: 1}),
    discountType: ["Fixed", "Percentage"][faker.number.int(1)],
    status: active[faker.number.int(1)]
  }
}

export const getPromotions = (amount: number) => {
  let response = [] as any;
  Array.from({ length: amount }).forEach(() => {
    response.push(createPromotion());
  });
  return response;
};

const createPromotionHistory = () => {
  return {
    email: faker.internet.email(),
    orderNo: faker.number.int().toString(),
    date: faker.date.past({ years: 1 }),
    name: faker.person.fullName()
  }
}

export const getPromotionHistory = (amount: number) => {
  let response = [] as any;
  Array.from({ length: amount }).forEach(() => {
    response.push(createPromotionHistory());
  });
  return response;
};

const createPromotionLogs = () => {
  return {
    email: faker.internet.email(),
    date: faker.date.past({ years: 1 }),
    activity: ["Status: Inactive → Active", "Status: Active → Inactive", "Name: prev → newName", "Amount: $3.99 → $2.99"][faker.number.int(1)]
  }
}

export const getPromotionLogs = (amount: number) => {
  let response = [] as any;
  Array.from({ length: amount }).forEach(() => {
    response.push(createPromotionLogs());
  });
  return response;
};

const createProvider = () => {
  return {
    name: providers[faker.number.int(providers.length - 1)],
    prefix: prefixes[faker.number.int(prefixes.length - 1)],
    mode: mode[faker.number.int(1)],
    inBoundIp: "**************",
    outBoundIp: "**************",
    allowCodecs: codecs[faker.number.int(1)],
    providerType: type[faker.number.int(1)],
    status: active[faker.number.int(1)],
  };
};

export const getProviders = (size: number) => {
  let response = [] as any;
  Array.from({ length: size }).forEach(() => {
    response.push(createProvider());
  });
  return response;
};

const createESim = () => {
  let alpha2 = faker.location.countryCode("alpha-2");
  return {
    msisdn: faker.string.numeric(5),
    iccid: faker.string.numeric(3),
    availability: availabilities[faker.number.int(2)],
    country: {
      countryCode: alpha2,
      countryName: countryListAlpha2.filter(
        (country: any) => country.code === alpha2
      )[0]?.name,
      iconURL: `https://public-gist-bucket.s3.eu-north-1.amazonaws.com/flag-icons/png/${alpha2}.png`,
    },
    registeredTo: faker.internet.email(),
    period: period[faker.number.int(2)],
    status: eSimStatuses[faker.number.int(5)],
  };
};

export const getESims = () => {
  let response = [] as any;
  Array.from({ length: 100 }).forEach(() => {
    response.push(createESim());
  });
  return response;
};

const createRates = () => {
  let alpha2 = faker.location.countryCode("alpha-2");

  return {
    country: {
      countryCode: alpha2,
      countryName: countryListAlpha2.filter(
        (country: any) => country.code === alpha2
      )[0]?.name,
      iconURL: `https://public-gist-bucket.s3.eu-north-1.amazonaws.com/flag-icons/png/${alpha2}.png`,
    },
    name: faker.person.firstName() + " " + faker.person.lastName(),
    types: type[faker.number.int(1)],
    buyRate: faker.string.numeric(5),
    sellRate: faker.string.numeric(5),
    currency: currencies[faker.number.int(2)],
    dateAdded: faker.date.past({ years: 1 }),
    status: active[faker.number.int(1)],
  };
};

export const getRates = () => {
  let response = [] as any;
  Array.from({ length: 100 }).forEach(() => {
    response.push(createRates());
  });
  return response;
};
const createESimProvision = () => {
  return {
    esimsProvisioned: "30000",
    availableEsimsProvisioned: "15000",
    provisionAmount: "100",
  };
};

export const getESimProvision = () => {
  let response = createESimProvision();

  return response;
};
const createActivityLog = () => {
  return {
    dateTime: faker.date.past({ years: 1 }),
    email: faker.internet.email(),
    activity: subscriberActivity[faker.number.int(8)],
    source: source[faker.number.int(2)],
    status: success[faker.number.int(1)]
  };
};

export const getActivityLog = (size: any) => {
  let response = [] as any;
  Array.from({ length: size }).forEach(() => {
    response.push(createActivityLog());
  });
  return response;
};

const customerTransactions = () => {
  return {
    trans_id: faker.number.int().toString(),
    order_id: faker.number.int().toString(),
    date: faker.date.past().toLocaleDateString("en-US"),
    result: total_cost[faker.number.int(1)],
    item: transaction_Item[faker.number.int(1)],
    price: "20",
  };
};
export const getCustomerTransactions = () => {
  let response = [] as any;
  Array.from({ length: 60 }).forEach(() => {
    response.push(customerTransactions());
  });
  return response;
};

const customerActivity = () => {
  return {
    type: type[faker.number.int(1)],
    source: "123456",
    destination: "DD MM YY",
    total_cost: "0.2",
    item: activity_Item[faker.number.int(1)],
  };
};

export const getCustomerActivity = () => {
  let response = [] as any;
  Array.from({ length: 60 }).forEach(() => {
    response.push(customerActivity());
  });
  return response;
};

const VoiceMailCustomer = () => {
  const alpha2 = faker.location.countryCode("alpha-2");

  return {
    country: {
      countryCode: alpha2,
      iconURL: `https://public-gist-bucket.s3.eu-north-1.amazonaws.com/flag-icons/png/${alpha2}.png`,
    },
    voicemailName: "Voicemail Name Here",
    number: faker.number.binary(255),
    time: faker.number.int({ min: 10, max: 100 }),
    status: active[faker.number.int(1)],
  };
};

export const getVoiceMailCustomer = () => {
  let response = [] as any;
  Array.from({ length: 3 }).forEach(() => {
    response.push(VoiceMailCustomer());
  });
  return response;
};

const createNumberESim = () => {
  let alpha2 = faker.location.countryCode("alpha-2");
  return {
    // id: faker.string.uuid(),
    msisdn: faker.string.numeric(5),
    iccid: faker.string.numeric(3),
    availability: availabilities[faker.number.int(2)],
    country: {
      countryCode: alpha2,
      countryName: countryListAlpha2.filter(
        (country: any) => country.code === alpha2
      )[0]?.name,
      iconURL: `https://public-gist-bucket.s3.eu-north-1.amazonaws.com/flag-icons/png/${alpha2}.png`,
    },
    registeredTo: faker.internet.email(),
    period: period[faker.number.int(2)],
    purchasedOn: faker.date.past({ years: 1 }),

    status: eSimStatuses[faker.number.int(5)],
  };
};

export const getNumberESims = () => {
  let response = [] as any;
  Array.from({ length: 50 }).forEach(() => {
    response.push(createNumberESim());
  });
  return response;
};

const createCreditProduct = (i: number) => {
  const amounts = [5, 10, 20, 50, 100];
  return {
    planId: uuid(),
    credit: amounts[i],
    prices: [
      {
        currencyCode: "USD",
        cost: faker.number.int(100),
      },
      {
        currencyCode: "GBP",
        cost: faker.number.int(100),
      },
      {
        currencyCode: "EUR",
        cost: faker.number.int(100),
      },
    ],
    dateAdded: faker.date.past({ years: 1 }),
    status: true,
  };
};

const createComboProduct = () => {
  let alpha2 = faker.location.countryCode("alpha-2");
  return {
    planId: uuid(),
    planName: countryListAlpha2.filter(
      (country: any) => country.code === alpha2
    )[0]?.name,
    validity: 30,
    dataAllowance: faker.number.int(100),
    smsAllowance: faker.number.int(300),
    voiceAllowance: faker.number.int(100),
    prices: [
      {
        currencyCode: "USD",
        cost: faker.number.int(100),
      },
      {
        currencyCode: "GBP",
        cost: faker.number.int(100),
      },
      {
        currencyCode: "EUR",
        cost: faker.number.int(100),
      },
    ],
    dateAdded: faker.date.past({ years: 1 }),
    status: true,
  };
};

const createDataProduct = () => {
  let alpha2 = faker.location.countryCode("alpha-2");
  return {
    planId: uuid(),
    planName: countryListAlpha2.filter(
      (country: any) => country.code === alpha2
    )[0]?.name,
    validity: 30,
    dataAllowance: faker.number.int(100),
    prices: [
      {
        currencyCode: "USD",
        cost: faker.number.int(100),
      },
      {
        currencyCode: "GBP",
        cost: faker.number.int(100),
      },
      {
        currencyCode: "EUR",
        cost: faker.number.int(100),
      },
    ],
    dateAdded: faker.date.past({ years: 1 }),
    status: true,
  };
};

const createSMSProduct = () => {
  let alpha2 = faker.location.countryCode("alpha-2");
  return {
    planId: uuid(),
    planName: countryListAlpha2.filter(
      (country: any) => country.code === alpha2
    )[0]?.name,
    validity: 30,
    smsAllowance: faker.number.int(300),
    prices: [
      {
        currencyCode: "USD",
        cost: faker.number.int(100),
      },
      {
        currencyCode: "GBP",
        cost: faker.number.int(100),
      },
      {
        currencyCode: "EUR",
        cost: faker.number.int(100),
      },
    ],
    dateAdded: faker.date.past({ years: 1 }),
    status: true,
  };
};

const createVoiceProduct = () => {
  let alpha2 = faker.location.countryCode("alpha-2");
  return {
    planId: uuid(),
    planName: countryListAlpha2.filter(
      (country: any) => country.code === alpha2
    )[0]?.name,
    validity: 30,
    voiceAllowance: faker.number.int(100),
    smsAllowance: faker.number.int(300),
    prices: [
      {
        currencyCode: "USD",
        cost: faker.number.int(100),
      },
      {
        currencyCode: "GBP",
        cost: faker.number.int(100),
      },
      {
        currencyCode: "EUR",
        cost: faker.number.int(100),
      },
    ],
    dateAdded: faker.date.past({ years: 1 }),
    status: true,
  };
};

export const orderStatuses = [
  "ICCIDREQUIRED",
  "BANCHANGE",
  "READY",
  // "ICCIDANDPORTINREQUIRED",
  "DETAILSREQUIRED",
  "PORTINREQUIRED"
] as const

const createOrder = () => {
  return {
    orderNumber: faker.number.int(100000),
    dateTime: faker.date.past(),
    subscriber: `${faker.person.firstName()} ${faker.person.lastName()}`,
    simType: faker.helpers.arrayElement(["eSIM", "SIM"]),
    price: faker.number.int(100),
    product: `${faker.word.adjective()} ${faker.word.noun()}`,
    status: faker.helpers.arrayElement(orderStatuses),
  };
}

export const getOrders = (amount: number) => {
  let response = [] as any;
  Array.from({ length: amount }).forEach(() => {
    response.push(createOrder());
  });
  return response;
}

const createAccount = () => {
  return {
    id: uuid(),
    name: faker.company.name(),
    type: projectType[Math.floor(Math.random() * 3)],
    primaryContact: faker.internet.email(),
    creationDate: faker.date.past({ years: 1 }),
    lastModifiedDate: faker.date.past({ years: 1 }),
    status: projectStatus[Math.floor(Math.random() * 4)],
    details: ''
  }
}

export const getAccounts = (amount: number) => {
  let response = [] as any;
  Array.from({ length: amount }).forEach(() => {
    response.push(createAccount());
  });
  return response;
}

const createStockDidNumbers = () => {
  let alpha2 = faker.location.countryCode("alpha-2");
  let status = ['Available', 'Pending', 'Registered'][Math.floor(Math.random() * 3)]
  return {
    didNumber: faker.number.int(10000000000000000000),
    country: status === 'Available' ? '-' : {
      countryCode: alpha2,
      countryName: countryListAlpha2.filter(
        (country: any) => country.code === alpha2
      )[0]?.name,
      iconURL: `https://public-gist-bucket.s3.eu-north-1.amazonaws.com/flag-icons/png/${alpha2}.png`,
    },
    availability: status,
    email:  status === 'Available' ? '-' : faker.internet.email(),
    type: numberType[Math.floor(Math.random() * 2)],
    period: faker.number.int(30) + ' Days',
    date: new Date(),
    status: active[Math.floor(Math.random() * 2)],
  }
}

export const getStockDidNumbers = (size: number) => {
  let stockDidNumbers = [] as any;
  Array.from({ length: size }).forEach((i: any) => {
    stockDidNumbers.push(createStockDidNumbers());
  });
  return stockDidNumbers;
};

const createStockRates = () => {
  let alpha2 = faker.location.countryCode("alpha-2");
  return {
    country: {
      countryCode: alpha2,
      countryName: countryListAlpha2.filter(
        (country: any) => country.code === alpha2
      )[0]?.name,
      iconURL: `https://public-gist-bucket.s3.eu-north-1.amazonaws.com/flag-icons/png/${alpha2}.png`,
    },
    name: faker.helpers.arrayElement(providers),
    type: types[Math.floor(Math.random() * 2)],
    buyRate: faker.number.float(10).toFixed(2),
    sellRate: faker.number.float(10).toFixed(2),
    currency: currencies[faker.number.int(2)],
    date: new Date(),
    status: active[Math.floor(Math.random() * 2)],
  }
}

export const getStockRates = (size: number) => {
  let stockRates = [] as any;
  Array.from({ length: size }).forEach((i: any) => {
    stockRates.push(createStockRates());
  });
  return stockRates;
};

const createStockeSIM = () => {
  let alpha2 = faker.location.countryCode("alpha-2");
  let status = ['Available', 'Pending', 'Registered'][Math.floor(Math.random() * 3)]
  return {
    iccid: faker.number.int(10000000000000000000),
    carrier: 'AT&T',
    email:  status === 'Available' ? '-' : faker.internet.email(),
    msisdn: faker.phone.number("+44 #### ######"),
    availability: status,
    provider: providers[Math.floor(Math.random() * providers.length - 1)],
    country: status === 'Available' ? '-' : {
          countryCode: alpha2,
          countryName: countryListAlpha2.filter(
            (country: any) => country.code === alpha2
          )[0]?.name,
          iconURL: `https://public-gist-bucket.s3.eu-north-1.amazonaws.com/flag-icons/png/${alpha2}.png`,
    },
    uploaded: new Date(),
    activationCode: ['ESIM-', 'ACTCODE-'][Math.floor(Math.random() * 2)] + faker.number.int(10000000000000000000),
  };
};

export const getStockESIM = (size: number) => {
  let stockEsim = [] as any;
  Array.from({ length: size }).forEach((i: any) => {
      stockEsim.push(createStockeSIM());
  });
  return stockEsim;
};

const createProduct = (type: "wholesale" | "retail") => {
  const families = [
    "Velocity Suite",
    "Harmony Hub",
    "Rhythm Box",
    "Melody Maker",
    "Symphony Source",
    "Tempo Tracker",
    "Beat Generator",
  ];

  const retailNames = [
    "Velocity Pro",
    "Harmony Sync",
    "Pixel Fusion",
    "Zenith Elite",
    "Luminous Spark",
    "Tranquil Wave",
    "Eclipse Horizon",
  ];

  const billingOptions = ["Flat", "First GB", "Additional Charges"];

  const hasRetail = type === "retail" || faker.datatype.boolean();
  const retailName = hasRetail ? faker.helpers.arrayElement(retailNames) : null;
  const retailPrice = hasRetail ? faker.commerce.price({ min: 5, max: 20, dec: 2 }) : null;

  const retailOnlyFields = type === "retail" ? {
    status: faker.datatype.boolean(),
  } : {}

  return {
    id: uuid(),
    family: faker.helpers.arrayElement(families),
    name: faker.helpers.arrayElement(retailNames),
    retailName: retailName,
    sizeGB: faker.number.int({ min: 1, max: 12 }),
    wholesalePrice: faker.commerce.price({ min: 2, max: 10, dec: 2 }),
    retailPrice: retailPrice,
    creationDate: faker.date.between({ from: new Date(2024, 10, 26), to: new Date(2024, 11, 3) }),
    talkText: faker.helpers.arrayElement([null, "Unlimited"]),
    approach: faker.helpers.arrayElement(["Overage", "Flat"]),
    billing: faker.helpers.arrayElement(billingOptions),
    ...retailOnlyFields
  };
};

export const getProducts = (amount: number, type: "wholesale" | "retail") => {
  let response = [] as any[];
  Array.from({ length: amount }).forEach(() => {
    response.push(createProduct(type));
  });
  return response;
};


const createHutchProduct = () => {

  return {
    productName: `${faker.commerce.productAdjective()} ${faker.commerce.product()}`,
    description: faker.commerce.productDescription(),
    carrier: 'Hutch',
    dataAllowance: faker.number.int({ min: 1, max: 24 }),
    country: countryListAlpha2[faker.number.int({ min: 0, max: countryListAlpha2.length - 1 })],
    validity: faker.helpers.arrayElement(['30days', '7days', '5days', '1day']),
    usdPrice: faker.commerce.price({ min: 2.49, max: 14.99, dec: 2 }),
    euroPrice: faker.commerce.price({ min: 2.49, max: 14.99, dec: 2 }),
    gbpPrice: faker.commerce.price({ min: 2.49, max: 14.99, dec: 2 }),
    dateAdded: faker.date.past({ years: 1 }),
    status: faker.datatype.boolean()
  }
}

export const getHutchProducts = (amount: number) => {
  let response = [] as any;
  Array.from({ length: amount }).forEach(() => {
    response.push(createHutchProduct());
  });
  return response;
};

export const getHutchComboProducts = (count: number) => {
  return Array.from({ length: count }, (_, i) => ({
    id: i + 1,
    country: countryListAlpha2[faker.number.int({ min: 0, max: countryListAlpha2.length - 1 })],
    validity: faker.helpers.arrayElement(["30 days", "7 days", "5 days"]),
    dataAllowance: faker.number.int({ min: 1, max: 10 }),
    smsAllowance: (Math.floor(Math.random() * 8) * 100 + 100).toString(),
    callAllowance: (Math.floor(Math.random() * 8) * 100 + 100).toString(),
    usdPrice: faker.commerce.price({ min: 10, max: 100, dec: 2 }),
    gbpPrice: faker.commerce.price({ min: 8, max: 80, dec: 2 }),
    eurPrice: faker.commerce.price({ min: 9, max: 90, dec: 2 }),
    dateAdded: faker.date.past({ years: 1 }),
    status: faker.datatype.boolean(),
  }));
};

export const getHutchGlobalCreditProducts = (count: number) => {
  return Array.from({ length: count }, (_, i) => ({
    id: i + 1,
    creditAmount: ((i + 1) * 5).toString(),
    currency: faker.helpers.arrayElement(["GBP", "EUR", "USD"]),
    dateAdded: faker.date.past({ years: 1 }),
    status: faker.datatype.boolean(),
  }));
};

export const getVoipCallProducts = (count: number) => {
  return Array.from({ length: count }, (_, i) => ({
    id: i + 1,
    country: countryListAlpha2[faker.number.int({ min: 0, max: countryListAlpha2.length - 1 })],
    validity: faker.helpers.arrayElement(["30 days", "7 days", "5 days"]),
    callAllowance: (Math.floor(Math.random() * 8) * 100 + 100).toString(),
    usdPrice: faker.commerce.price({ min: 10, max: 100, dec: 2 }),
    gbpPrice: faker.commerce.price({ min: 8, max: 80, dec: 2 }),
    eurPrice: faker.commerce.price({ min: 9, max: 90, dec: 2 }),
    dateAdded: faker.date.past({ years: 1 }),
    status: faker.datatype.boolean(),
  }));
};

export const getVoipSmsProducts = (count: number) => {
  return Array.from({ length: count }, (_, i) => ({
    id: i + 1,
    country: countryListAlpha2[faker.number.int({ min: 0, max: countryListAlpha2.length - 1 })],
    validity: faker.helpers.arrayElement(["30 days", "7 days", "5 days"]),
    smsAllowance: (Math.floor(Math.random() * 8) * 100 + 100).toString(),
    usdPrice: faker.commerce.price({ min: 10, max: 100, dec: 2 }),
    gbpPrice: faker.commerce.price({ min: 8, max: 80, dec: 2 }),
    eurPrice: faker.commerce.price({ min: 9, max: 90, dec: 2 }),
    dateAdded: faker.date.past({ years: 1 }),
    status: faker.datatype.boolean(),
  }));
};

const createTransaction = () => {
  const paymentTypes = ["visa", "mastercard", "credit"];
  const itemTypes = ["Data", "SMS", "Combo", "Account Credit", "Virtual Number"];
  const results = ["Success", "Failed"];

  const type = faker.helpers.arrayElement(paymentTypes);
  const result = faker.helpers.arrayElement(results);

  return {
    transactionId: faker.number.int({ min: 1000, max: 9999 }),
    time: faker.date.recent({ days: 30 }).toISOString().replace('T', ' ').slice(0, 19),
    result,
    itemPurchased: faker.helpers.arrayElement(itemTypes),
    price: faker.commerce.price({ min: 2, max: 100, dec: 2 }),
    orderId: faker.number.int({ min: 10000, max: 99999 }),
    type,
    lastdigits: type === 'credit' ? undefined : faker.number.int({ min: 1000, max: 9999 })
  };
};

export const getTransactions = (count: number) => {
  return Array.from({ length: count }, () => createTransaction());
};

const createCallSmsLog = () => {
  const isCall = faker.datatype.boolean();
  const type = isCall ? "Call" : "SMS";
  const chargeMethods = ["Combo", "Credit"];
  const chargeMethod = faker.helpers.arrayElement(chargeMethods);

  let totalCost = faker.helpers.arrayElement([
    faker.number.float({ min: 0, max: 10, precision: 0.01 }).toFixed(2),
    'N/A'
  ]);

  return {
    type,
    source: faker.helpers.arrayElement([
      faker.phone.number("+1##########"),
      faker.phone.number("+44##########")
    ]),
    destination: faker.helpers.arrayElement([
      faker.phone.number("+1##########"),
      faker.phone.number("+44##########"),
      faker.phone.number("+234#########")
    ]),
    time: faker.date.recent({ days: 365 }).toISOString().replace('T', ' ').slice(0, 19),
    totalCost,
    chargeMethod
  };
};

export const getCallSmsLogs = (count: number) => {
  return Array.from({ length: count }, () => createCallSmsLog());
};