import { faker } from "@faker-js/faker";
import SearchBar from "../SearchBar";
import styles from "./customer-notes.module.scss";
import RichTextEditor from "../RichTextEditor";
import { formatDateWithTime } from "../utils/formatDate";

const CustomerNotesSection = () => {
  const exampleNote = () => ({
    name: "<PERSON> Lemon",
    content:
      "Checked with the support team—confirmed multiple eSIMs can be managed under one account. Sent detailed instructions to the customer on setup.",
    date: faker.date.recent({ days: 100 }),
  });

  const notes = Array.from({ length: 10 }).map(() => exampleNote());

  return (
    <>
      <div className={styles.panelTopBar}>
        <h4>Notes</h4>
        <div className={styles.actions}>
          <SearchBar placeholder="Search" />
        </div>
      </div>
      <div className={`${styles.notesContainer} modal-scroll`}>
        <RichTextEditor />
        {notes.map((note) => (
          <div className={styles.note}>
            <div className={styles.circle} />
            <div>
              <div className={styles.top}>
                <div>{note.name}</div>
                <div>{formatDateWithTime(note.date)}</div>
              </div>
              <div
                className={styles.content}
                dangerouslySetInnerHTML={{ __html: note.content }}
              />
            </div>
          </div>
        ))}
      </div>
    </>
  );
};

export default CustomerNotesSection;
