import { useDispatch, useSelector } from "react-redux";
import Menu from "../Menu";
import styles from "./page-title.module.scss";
import { logOut } from "../utils/logOut";
import { useNavigate } from "react-router-dom";
import { LogOut, User } from "../svgs";

const PageTitle = ({ title }: any) => {
  const { userInfo } = useSelector((state: any) => state);
  const dispatch = useDispatch();
  const navigate = useNavigate();

  return (
    <div className={styles.top}>
      <h2>{title}</h2>
      <Menu
        data={{
          label: userInfo ? userInfo.firstName : "",
          items: [
            {
              label: "Profile",
              icon: <User />,
              link: "/user-profile",
            },
            {
              label: "Logout",
              icon: <LogOut />,
              onClick: () => {
                logOut(dispatch, navigate);
              },
            },
          ],
        }}
      />
    </div>
  );
};

export default PageTitle;
