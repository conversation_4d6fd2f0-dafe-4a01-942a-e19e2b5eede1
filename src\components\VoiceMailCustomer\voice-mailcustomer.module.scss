@use "../../styles/theme.scss" as *;


.modalContent {
    display: flex;
    flex-direction: column;
    gap: 12px;
    justify-content: center;
    border: 1px solid #DFE2E7;
    border-radius: 12px;
    padding: 18px;
    margin-bottom: 12px; 
    div {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 16px;
    }
    .description {
      display: block;
      p {
        font-size: 14px;
        font-weight: bold;
      }
      span {
        font-size: 12px;
        color: #838CA0
      }
    }
  }

  .flag {
    background-size: cover;
    background-position: center;
    width: 30px;
    height: 30px;
    border-radius: 1000px;
  }

  .delete{
    color: #2E70E5;
    margin-bottom: 5px;
    &:hover{
      cursor: pointer;
    }
  }