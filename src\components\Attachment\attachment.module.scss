@use "../../styles/theme.scss" as *;

.main {
  border-radius: 16px;
  width: 100%;
  max-width: 163px;
  margin-right: 20px;
  position: relative;
  cursor: pointer;
  .image {
    background: $lightblue;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 16px 16px 0px 0px;
    border: 1px solid $lightgrey;
    padding: 45px 0px 40px 0px;
    .actions {
      position: absolute;
      top: 5px;
      right: 9px;
      display: none;
      span {
        width: 32px;
        height: 32px;
        background-color: #fff;
        border-radius: 8px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        margin-left: 5px;
        svg {
          width: 24px;
          height: 24px;
          color: #2E70E5;
        }
      }
    }
    &:hover {
      background: #061632;
      transition: all 0.5s ease;
      .actions {
        display: block;
      }
    }
  }
  .info {
    padding: 10px 12px;
    border: 1px solid $lightgrey;
    border-top: none;
    border-radius: 0px 0px 16px 16px;
    .filename {
      color: #061632;
      font-size: 14px;
      line-height: 18px;
      margin-bottom: 2px;
    }
    .date {
      color: #838ca0;
      font-size: 12px;
      line-height: 15px;
    }
  }
}
