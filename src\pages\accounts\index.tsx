import Title from "@/components/Title";
import styles from "@/styles/accounts.module.scss"
import InsightList from "@/components/InsightList";
import CollapsiblePanel from "@/components/CollapsiblePanel";
import InsightCard from "@/components/InsightCard";
import { handleInputChange, createStateObject, labels, clearInput } from "@/components/utils/InputHandlers";
import { useEffect, useRef, useState } from "react";
import { formatDateWithTime } from "@/components/utils/formatDate";
import UserSkeleton from "@/components/UserSkeleton";
import TableControl from "@/components/TableControl";
import CheckboxDropdownInput from "@/components/CheckboxDropdownInput";
import { accountsFields } from "@/components/utils/accountsFields";
import Tag from "@/components/Tag";
import { Input } from "@/components/Input";
import Button from "@/components/Button";
import { Export, MagnifyingGlass } from "@/components/svgs";
import { getAccounts } from "@/components/utils/dataCreator";
import StatusPill from "@/components/StatusPill";

const Accounts = () => {
    const overviewPanelRef = useRef<any>(null);
    const searchPanelRef = useRef<any>(null);
    const searchAccountsFields = ["accountType", "name", "status", "creationDate"]

    const [data, setData] = useState(createStateObject(searchAccountsFields));
    const [showSearchResults, setShowSearchResults] = useState(false);
    const [loading, setLoading] = useState(true);
    const [accounts, setAccounts] = useState([]);

    const [itemsPerPage, setItemsPerPage] = useState(10);
    const [numberOfPages, setNumberOfPages] = useState(10);
    const [currentPage, setCurrentPage] = useState(1);

    const populate = (itemsPerPage: number) => {
        setLoading(true);
    
        setTimeout(() => {
          setAccounts(getAccounts(itemsPerPage))
          setLoading(false)
        }, 500)
    }

    useEffect(() => {
        populate(itemsPerPage)
    }, [itemsPerPage, currentPage])

    const formatDataItem = (item: any, key: string) => {
        if (key === "creationDate" || key === "lastModifiedDate") {
          return formatDateWithTime(item[key])
        } else if (key === 'status') {
            return <StatusPill status={item[key]} color={item[key] === 'Deactivated' && 'inactive' } />
        } else if (key === 'type') {
            return <StatusPill 
                        status={item[key]} 
                        color={item[key] === 'MVNO' ? 'confirmed' : 'active' } />
        } else if (key === 'details') {
            return (
                <>
                 <span>Subscribers: 3500</span>
                 <span>eSIMs: 5000</span>
                </>
            )
        } else {
          return item[key]
        }
      }

    return (
        <>
            <Title>Accounts</Title>
            <div className={styles.main}>
                <CollapsiblePanel
                    title="Overview"
                    summaryWhenClosed={
                        <div style={{ marginLeft: 22 }}>
                            <InsightList insights={overviewStats} />
                        </div>
                    }
                    ref={overviewPanelRef}>
                    <div className={styles.overview}>
                        {overviewStats.map((stat, index) => (
                        <InsightCard key={index} title={stat.title} value={stat.value.toString()} />
                        ))}
                    </div>
                </CollapsiblePanel>

                {/* Search orders */}
                <div style={{ marginTop: 16 }}>
                    <CollapsiblePanel
                        title="Search Accounts"
                        summaryWhenClosed={
                        <div style={{ display: 'flex', flex: 1, justifyContent: 'space-between', marginLeft: 16, marginRight: 8 }}>
                            <Tag text="3 filters applied" />
                            <Button color="secondary">Clear Filters</Button>
                        </div>
                        }
                        ref={searchPanelRef}>
                        <div className={styles.fields}>
                            {searchAccountsFields.map((prop) => {
                                if (["name", "creationDate"].includes(prop)) {
                                return (
                                    <Input
                                        key={"orders-" + prop}
                                        label={labels[prop]}
                                        value={data[prop]}
                                        onChange={(e: any) => {
                                            handleInputChange(prop, e, data, setData);
                                        }}
                                        error={data.errors[prop]}
                                        clear={() => {
                                            clearInput(prop, setData);
                                        }}
                                        infoTooltipText
                                    />)
                                } else if (["accountType", "status"].includes(prop)) {
                                return (
                                    <CheckboxDropdownInput
                                        key={"orders-" + prop}
                                        options={selectOptionsByField[prop]}
                                        label={labels[prop]}
                                        selected={data[prop]}
                                        onChange={(values) => {
                                            handleInputChange(
                                            prop,
                                            values,
                                            data,
                                            setData,
                                            'select'
                                            );
                                        }}
                                        error={data.errors[prop]}
                                        infoTooltipText
                                    />
                                );
                                }
                            })}
                        </div>
                        <div className={styles.actions}>
                            <Button
                                color="blue"
                                onClick={() => {
                                searchPanelRef.current.close()
                                overviewPanelRef.current.close()
                                setShowSearchResults(true)
                                }}
                            ><MagnifyingGlass /> Search</Button>
                        </div>
                    </CollapsiblePanel>
                </div>

                {/* Accounts Table*/}
                {showSearchResults && <div className={styles.panel}>
                    <div className={styles.panelTopBar}>
                        <h4>Accounts</h4>
                        <div className={styles.actions}>
                            <Button color="secondary">
                                <Export /> Export to CSV
                            </Button>
                        </div>
                    </div>

                    <div className={`${styles.tableContainer} table-scroll`}>
                        <table>
                        <thead>
                            <tr>
                                {accountsFields.map((field: any) => (
                                    <th key={field.key}>{field.label}</th>
                                ))}
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>
                            {!loading
                            ? accounts.map((item: any, i: number) => {
                                return (
                                <tr key={`accounts-${item.id}`} style={{ cursor: 'pointer' }}>
                                    {accountsFields.map((field: any) => (
                                        <td key={`accounts-${item.id}-${field.key}`}>
                                            <div style={{ display: "flex", justifyContent: "flex-start" }}>
                                                {formatDataItem(item, field.key)}
                                            </div>
                                        </td>
                                    ))}
                                    <td>
                                    <span
                                        className={styles.viewRowBtn}>
                                        Actions
                                    </span>
                                    </td>
                                </tr>
                                )
                            }) : Array.from({ length: itemsPerPage }, (v, i) => i).map(
                                (i) => (
                                <UserSkeleton
                                    key={"order-skeleton-" + i}
                                    noOfStandard={8}
                                />
                                )
                            )}
                        </tbody>
                        </table>
                    </div>
                    <div style={{ marginTop: "16px" }}>
                        <TableControl
                            show
                            itemsPerPage={itemsPerPage}
                            setItemsPerPage={(val: any) => setItemsPerPage(val)}
                            currentPage={currentPage}
                            setCurrentPage={setCurrentPage}
                            numberOfPages={numberOfPages}
                            label="orders"
                            loading={loading}
                        />
                    </div>
                </div>}
            </div>
        </>
    )
}

export default Accounts

const overviewStats = [
    { title: 'MVNOs', value: '7,245' },
    { title: 'Resellers', value: '3,591' },
    { title: 'Affiliates', value: '59' },
    { title: 'Active Subscribers', value: '248,624' }
]

const selectOptionsByField: Record<string, any> = {
    accountType: [
      { label: "MVNO", value: "mvno" },
      { label: "Reseller", value: "reseller" },
      { label: "Affiliate", value: "affiliate" },

    ],
    status: [
      { label: "Active", value: "active" },
      { label: "Pending", value: "pending" },
      { label: "Suspended", value: "suspended" },
      { label: "Deactivated", value: "deactivated" }
    ]
  }