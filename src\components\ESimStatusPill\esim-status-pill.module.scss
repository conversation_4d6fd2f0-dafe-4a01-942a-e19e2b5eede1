@use "../../styles/theme.scss" as *;

.main {
  padding: 4px 12px;
  font-size: 14px;
  border-radius: 6px;
  width: auto;
  font-weight: 500 !important;
  display: inline-block;
  &.hover:hover {
    box-shadow: 0 0 0 6px #fff;
  }
  &.status-0 {
    background-color: $esim-available;
    color: $esim-available-text;
  }
  &.status-1 {
    background-color: $esim-registered;
    color: $esim-registered-text;
  }
  &.status-2 {
    background-color: $esim-installed;
    color: $esim-installed-text;
  }
  &.status-3 {
    background-color: $esim-enable;
    color: $esim-enable-text;
  }
  &.status-4 {
    background-color: $esim-disabled;
    color: $esim-disabled-text;
  }
  &.status-5 {
    background-color: $esim-deleted;
    color: $esim-deleted-text;
  }
}
