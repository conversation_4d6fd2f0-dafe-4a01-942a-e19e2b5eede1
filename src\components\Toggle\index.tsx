import Shimmer from "../Shimmer";
import styles from "./toggle.module.scss";

const Toggle = ({
  style,
  on,
  onChange = () => {},
  disabled,
  id = "",
  readonly,
}: any) => {
  return (
    <div
      className={`${styles.main} ${id}`}
      style={{
        ...style,
        background: on ? "var(--primary-200)" : "var(--gray-100)",
        cursor: disabled || readonly ? "auto" : "pointer",
        opacity: disabled || readonly ? 0.5 : 1,
      }}
      onClick={(e) => {
        e.stopPropagation();
        if (!disabled) {
          onChange();
        }
      }}>
      {disabled && <Shimmer />}
      <div className={styles.thumb} style={{ right: on ? 2 : 17, background: on ? '#2E70E5' : '#838CA0' }} />
    </div>
  );
};

export default Toggle;
