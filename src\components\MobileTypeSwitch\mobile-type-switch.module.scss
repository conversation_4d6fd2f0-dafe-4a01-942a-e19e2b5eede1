@use "../../styles/theme.scss" as *;

.container {
  position: relative;
  display: flex;
  height: 50px;
  border-radius: 50px;
  background: #f2f2f2;
  margin-bottom: 13px;
  overflow: hidden;
}

.label {
  font-size: 12px;
  line-height: 16px;
  margin-bottom: 8px;
  padding-left: 16px;
  color: $placeholder;
}

.role {
  width: 50%;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: $black;
  position: relative;
  z-index: 1000;
  cursor: pointer;
}

.thumb {
  width: 50%;
  height: 50px;
  border-radius: 50px;
  background-color: $orange;
  position: absolute;
  z-index: 2000;
  top: 0px;
  left: 0px;
  transition: all 0.4s ease;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  box-shadow: 0px 4px 6px 0px rgba(0, 0, 0, 0.15);
  &.right {
    left: 50%;
    background-color: $primary;
    color: $off-white;
  }
}
