import styles from "./role-switch.module.scss";

const RoleSwitch = ({ role, setRole, noTransition }: any) => {
  return (
    <div>
      <div className={styles.label}>Role</div>
      <div className={styles.container}>
        <div
          onClick={() => {
            setRole(2);
          }}
          className={`${styles.role} ${role === 2 && styles.active}`}
        >
          Agent
        </div>
        <div
          onClick={() => {
            setRole(1);
          }}
          className={`${styles.role} ${role === 1 && styles.active}`}
        >
          Admin
        </div>
      </div>
    </div>
  );
};

export default RoleSwitch;
