import styles from "./delete-user-modal.module.scss";
import { WarningCircle } from "../svgs";
import { useState } from "react";
import { useDispatch } from "react-redux";
import { ApiDelete } from "src/pages/api/api";
import StatusPill from "../StatusPill";
import Dialog from "../Dialog";

const DeleteUserModal = ({ show, setShow, user, repopulateUsers }: any) => {
  const dispatch = useDispatch();

  const [loading, setLoading] = useState(false);

  const handleSuccess = () => {
    dispatch({
      type: "notify",
      payload: {
        error: false,
        message: 'User has been deleted successfully',
      },
    });
    setShow(false)
    repopulateUsers()
  }

  const deleteUser = () => {
    setLoading(true);
    ApiDelete(`/agent/${user.mid}`, {})
      .then((response) => {
        setLoading(false);
        setShow(false);
        repopulateUsers();
        dispatch({
          type: "notify",
          payload: {
            error: false,
            message: response.data.message,
          },
        });
      })
      .catch((error) => {
        setLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: error.response.data.message,
          },
        });
      });
  };

  return (
    <Dialog
      confirmButtonText="Yes, Delete User"
      headerTitle="Delete User?"
      headerSubtitle="Once removed, it cannot be recovered."
      confirmButtonVariant="customerActionRed"
      headerIcon={<WarningCircle />}
      open={show}
      onClose={() => {
        setShow(false);
      }}
      confirmButtonOnClick={() => handleSuccess()}
      size="sm"
      cancelButtonText="Cancel">
      <div className={styles.main}>
        <div className={styles.table}>
          <div className={styles.tableRow}>
            <div className={styles.labels}>
              { user?.firstName && (<p>First name</p>) }
            </div>
            <div className={styles.value}>
              {user?.firstName }
            </div>
          </div>
          <div className={styles.tableRow}>
            <div className={styles.labels}>
              { user?.lastName && (<p>First name</p>) }
            </div>
            <div className={styles.value}>
              {user?.lastName }
            </div>
          </div>
          <div className={styles.tableRow}>
            <div className={styles.labels}>
              { user?.email && (<p>Email Address</p>) }
            </div>
            <div className={styles.value}>
              {user?.email }
            </div>
          </div>
          <div className={styles.tableRow}>
            <div className={styles.labels}>
              { user?.role && (<p>Role</p>) }
            </div>
            <div className={styles.value}>
              { user?.role === 1 ? 'Admin' : 'Agent' }
            </div>
          </div>
          <div className={styles.tableRow}>
            <div className={styles.labels}>
              { user?.status && (<p>Status</p>) }
            </div>
            <div className={styles.value}>
              <StatusPill status={user?.status === 'Active'} />
            </div>
          </div>
        </div>
      </div>
    </Dialog>
  );
};

export default DeleteUserModal;
