import styles from "./select.module.scss";
import { ControlledMenu, MenuItem, useMenuState } from "@szhsin/react-menu";
import "@szhsin/react-menu/dist/index.css";
import "@szhsin/react-menu/dist/transitions/slide.css";
import { ChevronDown } from "../svgs";
import { useRef } from "react";
import { Tick } from "../svgs";
import { Fade } from "@mui/material";

const Select = ({ options, selected, onChange, disabled }: any) => {
  const ref = useRef(null);

  const [menuProps, toggleMenu] = useMenuState({ transition: true });

  return (
    <div className={styles.box}>
      <div
        ref={ref}
        className={`${styles.menuButton} ${disabled && styles.disabled} ${
          menuProps.state === "open" || menuProps.state === "opening"
            ? styles.iconOpen
            : styles.iconClosed
        }`}
        onClick={() => toggleMenu(true)}
      >
        {selected}
        <ChevronDown />
      </div>
      <ControlledMenu
        {...menuProps}
        anchorRef={ref}
        onClose={() => toggleMenu(false)}
        align="start"
        viewScroll="initial"
        position="initial"
        onItemClick={(e) => (e.keepOpen = true)}
      >
        {options.map((item: any) => (
          <MenuItem
            className={`${styles.menuItem} ${
              selected === item && styles.selected
            }`}
            onClick={() => {
              onChange(item);
            }}
            key={item}
          >
            {item}
            <Fade in={selected === item}>
              <div style={{ height: 24 }}>
                <Tick />
              </div>
            </Fade>
          </MenuItem>
        ))}
      </ControlledMenu>
    </div>
  );
};

export default Select;
