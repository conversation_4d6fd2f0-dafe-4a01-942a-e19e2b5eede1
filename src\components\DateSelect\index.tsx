import { useEffect, useState } from "react";
import MonthPicker from "../MonthPicker";
import { Calendar, CaretDown, CaretUp } from "../svgs";
import { formatDate, isDateNow } from "../utils/dateHelpers";
import styles from "./date-select.module.scss";
import {
  getMonthArray,
  getMonthDays,
  getMonthFirstDay,
  zeroPad,
} from "../utils/dateHelpers";

const now = new Date();
const daysLetters = ["M", "T", "W", "T", "F", "S", "S"];

const DateSelect = ({
  dateType,
  fromDate,
  setFromDate,
  untilDate,
  setUntilDate,
  today,
  showDate = true,
}: any) => {
  const [month, setMonth] = useState(now.getMonth() + 1);
  const [year, setYear] = useState(now.getFullYear());
  const [displayMonths, setDisplayMonths] = useState([] as any);

  const [hoverDate, setHoverDate] = useState(null as any);

  useEffect(() => {
    const days = getMonthDays(month, year);
    const first = getMonthFirstDay(month, year);
    const newMonthArray = getMonthArray(
      Array.from({ length: days }, (v, i) => ({
        type: "main",
        value: i + 1,
        date: new Date(`${year}/${zeroPad(month)}/${zeroPad(i + 1)}`),
      })),
      days,
      first,
      month,
      year
    );
    setDisplayMonths(newMonthArray);
  }, [month, year]);

  return (
    <>
      <div className={styles.input}>
        <Calendar />
        {showDate && (
          <div className={styles.text}>
            {dateType === "specific" ? (
              fromDate === null ? (
                "DD/MM/YYYY"
              ) : (
                formatDate(fromDate)
              )
            ) : (
              <>
                {fromDate ? formatDate(fromDate) : "DD/MM/YYYY"}
                {" - "}
                {untilDate ? formatDate(untilDate) : "DD/MM/YYYY"}
              </>
            )}
          </div>
        )}
      </div>
      <div className={styles.mainDatePicker}>
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            padding: "12px 26px 0 26px",
          }}
        >
          <MonthPicker
            activeMonth={month}
            setActiveMonth={setMonth}
            activeYear={year}
            setActiveYear={setYear}
          />
          <input
            style={{
              position: "absolute",
              opacity: 0,
              zIndex: -1,
            }}
            id="close-month-menu"
          />
          <div className={styles.prevNext}>
            <div
              className={styles.prev}
              onClick={() => {
                if (month === 1) {
                  setMonth(12);
                  setYear(year - 1);
                } else {
                  setMonth(month - 1);
                }
              }}
            >
              <CaretUp />
            </div>
            <div
              className={styles.next}
              onClick={() => {
                if (month === 12) {
                  setMonth(1);
                  setYear(year + 1);
                } else {
                  setMonth(month + 1);
                }
              }}
            >
              <CaretDown />
            </div>
          </div>
        </div>
        <div className={styles.calendar}>
          <div className={styles.days}>
            {daysLetters.map((letter: any, i: any) => (
              <div key={i} className={styles.letter}>
                {letter}
              </div>
            ))}
          </div>
          <div className={styles.datesGrid}>
            {displayMonths.map((day: any) => (
              <div
                className={styles.cellContainer}
                onMouseEnter={() => {
                  setHoverDate(day.date);
                }}
                onMouseLeave={() => {
                  setHoverDate(null as any);
                }}
                key={`${day.type}-date-${day.date.getTime()}`}
              >
                <div
                  className={`${styles.gridCell} ${
                    untilDate && dateType === "range"
                      ? day.date.getTime() >= fromDate.getTime() &&
                        day.date.getTime() <= untilDate.getTime()
                        ? styles.highlight
                        : ""
                      : fromDate &&
                          hoverDate &&
                          !untilDate &&
                          dateType === "range"
                        ? day.date.getTime() >= fromDate.getTime() &&
                          day.date.getTime() <= hoverDate.getTime() &&
                          hoverDate.getTime() !== fromDate.getTime()
                          ? styles.highlight
                          : ""
                        : ""
                  } ${
                    fromDate
                      ? fromDate.getTime() === day.date.getTime()
                        ? styles.curveLeft
                        : ""
                      : ""
                  } ${
                    untilDate
                      ? untilDate.getTime() === day.date.getTime()
                        ? styles.curveRight
                        : ""
                      : hoverDate && !untilDate
                        ? hoverDate.getTime() === day.date.getTime()
                          ? styles.curveRight
                          : ""
                        : ""
                  }`}
                />
                <div
                  className={`${styles.day} ${
                    day.date > today && styles.disable
                  } ${isDateNow(day.date) && styles.now} ${
                    day.type === "pad" && styles.pad
                  } ${
                    fromDate
                      ? fromDate.getTime() === day.date.getTime()
                        ? styles.active
                        : untilDate
                          ? untilDate.getTime() === day.date.getTime()
                            ? styles.active
                            : ""
                          : ""
                      : ""
                  }`}
                  onClick={() => {
                    if (
                      fromDate === null ||
                      (fromDate && untilDate) ||
                      dateType === "specific"
                    ) {
                      setFromDate(day.date);
                      setUntilDate(null as any);
                    } else if (day.date.getTime() < fromDate.getTime()) {
                      setUntilDate(fromDate);
                      setFromDate(day.date);
                    } else {
                      setUntilDate(day.date);
                    }
                  }}
                >
                  {day.value}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </>
  );
};

export default DateSelect;
