@import "../../styles/table-mixin.module.scss";

.title {
  font-size: 16px;
}

.tabsAndActionsContainer {
  margin-top: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .actions {
    display: flex;
    gap: 12px;
  }
}

.statusAndDateContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;

  .dateTime {
    color: var(--gray-500);
    font-size: 14px;
  }
}

.detailsCardContainer {
  margin-top: 4px;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.activityLogTabContainer {
  margin-top: 8px;
  display: flex;
  flex-direction: column;
  // height: 100%;
}

.activityLogTableContainer {
  @include table;

  .activityCell {
    display: flex;
    align-items: center;
    gap: 8px;

    .activityName {
      color: var(--text-secondary);
    }
  }
}
