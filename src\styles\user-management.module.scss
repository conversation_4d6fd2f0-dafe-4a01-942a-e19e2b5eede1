@use "./theme.scss" as *;
@import "./table-mixin.module.scss";
@import "./mixins.module.scss";

.main {
  width: 100%;
  padding: 24px;
}

.overview {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.panel {
  @include panel;
  @include table;

  margin-top: 16px;
}

.fields {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-top: 20px;
}

.actions {
  display: flex;
  justify-content: end;
}

.usersPanel:not(.products) {
  table {
    td:last-child,
    th:last-child {
      text-align: end;
    }
  }
}

.tableContainer {
  overflow: auto;
  padding-bottom: 5px;
  margin-top: 20px;
  border-radius: 16px;
  table {
    tbody {
      td {
        width: 17.5%;
        &:last-of-type {
          padding: 0;
        }
      }
    }
  }

  .viewRowBtn {
    color: var(--button-tertiary-text);
    font-size: 14px;
    font-weight: 700;
    text-decoration: underline;
    cursor: pointer;
    visibility: hidden;
  }

  tr:first-of-type .viewRowBtn {
    visibility: visible;
  }

  tr:hover .viewRowBtn {
    visibility: visible;
  }
}
