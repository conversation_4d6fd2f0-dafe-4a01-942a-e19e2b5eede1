import styles from "./notification.module.scss";
import { CheckCircle, Close, Tick, Warning<PERSON><PERSON> } from "../svgs";
import { useEffect } from "react";
import { useDispatch } from "react-redux";
import { motion } from "framer-motion";

const Notification = ({ id, error, message }: any) => {
  const dispatch = useDispatch();

  let timeout: any;

  const onClose = () => {
    if (timeout) clearTimeout(timeout);
    dispatch({
      type: "closeNotification",
      payload: id,
    });
  };

  useEffect(() => {
    timeout = setTimeout(() => {
      onClose()
    }, 5000);
  }, []);

  return (
    <motion.div
      layout
      initial={{ opacity: 0, y: -150 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -150 }}
      transition={{
        type: "tween",
        ease: "easeInOut",
        duration: 0.3,
      }}
      key={id}
    >
      <div className={`${styles.main} ${error && styles.error}`}>
        <div className={styles.content}>
          {
            error ? <WarningCross /> : <CheckCircle />
          }
          <div>
            <h5>{!error ? 'Success!' : 'Error'}</h5>
            <p className={styles.message}>
              {message
                ? message
                : error
                ? "An unexpected error occurred"
                : "Success!"}
            </p>
          </div>
        </div>
        <button className={styles.close} onClick={onClose}>
          <Close color="#2E70E5" />
        </button>
      </div>
    </motion.div>
  );
};

export default Notification;
