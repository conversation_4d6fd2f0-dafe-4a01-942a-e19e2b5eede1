.main {
  background: #d6e3fa;
  padding: 4px 6px;
  font-size: 12px;
  line-height: 15px;
  display: inline-flex;
  color: #061632;
  border-radius: 200px;
  margin: 8px 0;
  .option {
    height: 32px;
    padding: 0px 16px;
    border-radius: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    position: relative;
    cursor: pointer;
    display: grid;
    margin-right: 8px;
    &:last-of-type {
      margin-right: 0px;
    }
    &:hover {
      background-color: #d6e3fa;
    }
    .maskLabel {
      font-weight: 700;
      opacity: 0;
    }
    .displayLabel {
      position: relative;
      z-index: 80;
    }
    .displayLabel,
    .maskLabel {
      grid-area: 1 / 1 / 2 / 2;
    }
    &.active {
      cursor: default;
      &:hover {
        background-color: initial;
      }
      .displayLabel {
        font-weight: 700;
      }
    }
  }

  .underline {
    width: 100%;
    top: 0px;
    left: 0px;
    right: 0px;
    bottom: 0px;
    position: absolute;
    background-color: #adc8f5;
    border-radius: 200px;
    z-index: 70;
  }
}
