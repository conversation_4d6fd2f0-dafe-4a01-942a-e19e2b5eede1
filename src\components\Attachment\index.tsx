import { AttachmentImage, Delete, Download } from "../svgs";
import styles from "./attachment.module.scss";

const Attachment = ({ data, showDeleteModal }: any) => {
  return (
    <div className={styles.main}>
      <div className={styles.image}>
        <AttachmentImage />
        <div className={styles.actions}>
          <span>
            <Download />
          </span>
          <span onClick={showDeleteModal}>
            <Delete />
          </span>
        </div>
      </div>
      <div className={styles.info}>
        <div className={styles.filename}>filename.format</div>
        <div className={styles.date}>DD MMM YYYY, HH:MM</div>
      </div>
    </div>
  );
};

export default Attachment;
