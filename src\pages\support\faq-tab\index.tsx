import { useState } from 'react';
import styles from "./faq-tab.module.scss";
import { Collapse } from '@mui/material';
import { ChevronDownLg } from '@/components/svgs';

const FAQTab = () => {
  // Create array of 16 items
  const items = [...Array(16)].map((_, index) => ({
    question: "How to activate a subscriber",
    answer: "Meat broccoli meat tomato red stuffed dolor. Personal Hawaiian dolor chicken buffalo mouth spinach. Pork broccoli garlic ipsum hand mayo. Large platter and pepperoni."
  }));

  // Split items into two columns
  const midPoint = Math.ceil(items.length / 2);
  const leftColumnItems = items.slice(0, midPoint);
  const rightColumnItems = items.slice(midPoint);

  return (
    <div className={styles.panel}>
      <h2>FAQs</h2>
      <div className={styles.columnsContainer}>
        <div className={styles.column}>
          {leftColumnItems.map((item, index) => (
            <FAQItem
              key={`left-${index}`}
              question={item.question}
              answer={item.answer}
            />
          ))}
        </div>
        <div className={styles.column}>
          {rightColumnItems.map((item, index) => (
            <FAQItem
              key={`right-${index}`}
              question={item.question}
              answer={item.answer}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default FAQTab;

interface FAQItemProps {
  question: string;
  answer: string;
}

const FAQItem = ({ question, answer }: FAQItemProps) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className={styles.faqItem}>
      <button
        className={styles.questionButton}
        onClick={() => setIsOpen(!isOpen)}
      >
        <span>{question}</span>
        <div className={`${styles.arrow} ${isOpen ? styles.rotated : ''}`}>
          <ChevronDownLg />
        </div>
      </button>
      <Collapse in={isOpen}>
        <div className={styles.answer}>
          {answer}
        </div>
      </Collapse>
    </div>
  );
};
