@use "./theme.scss" as *;
@import "./table-mixin.module.scss";
@import "./mixins.module.scss";

.main {
  padding: 16px;
}

.overview {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.overviewSummaryWrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-left: 22px;
  margin-right: 12px;
}

.overviewHeaderOpenWrapper {
  display: flex;
  justify-content: flex-end;
  margin-right: 12px;
}

.searchPanelForm {
  @include searchPanelForm;
}

.topRow {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 18px;
  h3 {
    font-size: 20px;
    line-height: 30px;
    font-weight: 700;
  }
  .buttons {
    display: flex;
    align-items: center;
  }
}

.tableHeader {
  display: flex;
  align-items: center;
  gap: 8px;

  span {
    font-size: 12px;
  }
}

.national {
  margin-bottom: 15px;
  .panelTopBar {
    p {
      font-size: 14px;
      color: #667085;
      span {
        color: #061632;
        font-weight: 700;
      }
    }
  }
  .subscriptionFigures {
    display: flex;
    column-gap: 15px;
    > div {
      display: flex;
      align-items: center;
      background: #f1f6fd;
      padding: 20px;
      border-radius: 23px;
      .subscriptionIcon {
        background: #d6e3fa;
        padding: 8px;
        border-radius: 50%;
        height: 40px;
        margin-right: 10px;
        svg {
          color: #1857c3;
          stroke: #1857c3;
        }
      }
      .subscriptionData {
        p {
          font-size: 14px;
          color: #667085;
          margin-bottom: 4px;
        }
        span {
          font-size: 20px;
          font-weight: 700;
        }
      }
    }
  }
  .fields {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-top: 20px;
  }
  .actions {
    display: flex;
    justify-content: end;
  }
}

.panel {
  @include panel;
  @include table;
  margin-top: 16px;
  .panelTopBar {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .actions {
      width: 60%;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      .collapseArrows {
        margin-left: 15px;
        cursor: pointer;
      }
    }
    h4 {
      font-size: 16px;
      font-weight: 700;
      line-height: 22px;
    }
  }
}

.tableContainer {
  overflow: auto;
  padding-bottom: 5px;
  margin-top: 20px;

  .actionCell {
    width: 40px;
    padding: 0;
  }

  .actionBox {
    cursor: pointer;
    visibility: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0px;
    color: var(--button-tertiary-text);
  }

  .viewRowBtn {
    font-size: 14px;
    font-weight: 700;
    text-decoration: underline;
    height: 40px;
    width: 57px;
    display: flex;
    align-items: center;
    cursor: pointer;
    justify-content: center;
  }

  tr:first-of-type .actionBox {
    visibility: visible;
  }

  tr:hover .actionBox {
    visibility: visible;
  }
}

.country {
  display: grid;
  grid-template-columns: 16px 1fr;
  grid-column-gap: 6px;
  align-items: center;
  font-weight: 400;
  .flag {
    background-size: cover;
    background-position: center;
    width: 16px;
    height: 16px;
    border-radius: 1000px;
  }
}

.ticketsLink {
  color: inherit;
  text-decoration: none;
  margin-right: 12px;
  font-weight: 400;
  &:hover {
    color: $orange;
  }
}

.breadcrumbs {
  display: flex;
  align-items: center;
  color: $black;
  font-size: 14px;
  font-weight: 600;
  .ticketId {
    white-space: nowrap;
  }
}

.actionBox {
  display: grid;
  align-items: center;
  grid-template-columns: auto auto;
  grid-column-gap: 5px;
  span {
    &:first-of-type {
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.noResults {
  margin: 0 auto;
  display: grid;
  align-items: center;
  position: relative;
  .searchImage {
    grid-area: 1 / 1 / 2 / 2;
    height: 80%;
  }
  .text {
    grid-area: 1 / 1 / 2 / 2;
    display: flex;
    flex-direction: column;
    align-items: center;
    h4 {
      font-size: 24px;
      line-height: 36px;
      margin-bottom: 12px;
    }
    p {
      font-size: 12px;
      line-height: 18px;
      margin-bottom: 40px;
    }
  }
}

.actionButton {
  background: none;
  border: none;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background 0.15s ease;
  border-radius: 8px;
  &:last-child {
    margin-right: 0px;
  }
  &:hover {
    background: #fff;
  }
}

.PortingTitle {
  color: #000;
  font-family: Poppins;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.PortingSubtitle {
  color: #000;
  font-family: Poppins;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.imagepanel {
  flex: 1;
  border-radius: 24px;
  background: #fff;
  padding: 24px;
  height: fit-content;
  &:hover {
    box-shadow: 0px 0px 30px 0px rgba(8, 7, 87, 0.1);
  }
}
