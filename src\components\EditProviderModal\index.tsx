import Dialog from "@/components/Dialog";
import { PencilCircle, FloppyDisk } from "@/components/svgs";
import styles from "./edit-provider-modal.module.scss";
import StatusPill from "@/components/StatusPill";
import { Input } from "@/components/Input";
import { useState } from "react";
import { clearInput, createStateObject, createStateObjectWithInitialState, handleInputChange, labels } from "@/components/utils/InputHandlers";
import ToggleButtonGroup from "../ToggleButtonGroup";

type EditProviderModalProps = {
  open: boolean;
  onClose: () => void;
  data: any;
};

const EditProviderModal = ({
  open,
  onClose,
  data
}: EditProviderModalProps) => {
  const fields = ["mode", "inBoundIp", "outBoundIp", "allowCodecs", "status"];

  const [formData, setFormData] = useState(createStateObjectWithInitialState(createStateObject(fields), {
    mode: data?.mode || "",
    inBoundIp: data?.inBoundIp || "",
    outBoundIp: data?.outBoundIp || "",
    allowCodecs: data?.allowCodecs,
    status: data?.status
  }));

  return (
    <Dialog
      open={open}
      onClose={onClose}
      size="sm"
      headerIcon={<PencilCircle />}
      headerTitle="Edit Provider"
      confirmButtonText="Save Changes"
      confirmButtonOnClick={onClose}
      confirmButtonIcon={<FloppyDisk />}
      cancelButtonText="Cancel"
    >
      <div className={styles.tableContainer}>
        <table className={styles.table}>
          <tbody>
            {data?.name && (
              <tr>
                <td className={styles.label}>Provider</td>
                <td className={styles.value}>{data.name}</td>
              </tr>
            )}
            {data?.prefix && (
              <tr>
                <td className={styles.label}>Prefix</td>
                <td className={styles.value}>{data.prefix}</td>
              </tr>
            )}
            {data?.providerType && (
              <tr>
                <td className={styles.label}>Type</td>
                <td className={styles.value}>{data.providerType}</td>
              </tr>
            )}
            {data?.status && (
              <tr>
                <td className={styles.label}>Status</td>
                <td className={styles.value}>
                  <StatusPill
                    status={data.status === 1 ? "Active" : "Inactive"}
                    color={data.status === 1 ? "active" : "inactive"}
                  />
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
      <div className={styles.formContainer}>
        {fields.map(field => {
          if (["status", "allowCodecs"].includes(field)) {
            return (
              <div key={field} style={{ marginBottom: 16 }}>
                <ToggleButtonGroup
                  label={labels[field]}
                  options={toggleOptionsByField[field as keyof typeof toggleOptionsByField]}
                  selected={formData[field]}
                  onChange={(e: any) => handleInputChange(field, e, formData, setFormData, "select")}
                />
              </div>
            )
          }

          return (
            <Input
              key={field}
              label={labels[field]}
              value={formData[field]}
              onChange={(e: any) => handleInputChange(field, e, formData, setFormData)}
              error={formData.errors[field]}
              clear={() => clearInput(field, setFormData)}
              infoTooltipText
            />
          )
        })}
      </div>
    </Dialog>
  );
};

export default EditProviderModal;

const toggleOptionsByField = {
  status: [
    { label: "Active", key: "Active" },
    { label: "Inactive", key: "Inactive" }
  ],
  allowCodecs: [
    { label: "Yes", key: "Yes" },
    { label: "No", key: "No" }
  ]
}; 