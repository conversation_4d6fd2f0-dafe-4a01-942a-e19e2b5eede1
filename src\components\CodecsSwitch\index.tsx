import { SwitchTransition, CSSTransition } from "react-transition-group";
import styles from "./codecs-switch.module.scss";

const CodecsSwitch = ({ role, setRole }: any) => {
  return (
    <div>
      <div className={styles.label}>Allow Codecs</div>
      <div className={styles.container}>
        <div
          onClick={() => {
            setRole(true);
          }}
          className={styles.role}
        >
          Yes
        </div>
        <div
          onClick={() => {
            setRole(false);
          }}
          className={styles.role}
        >
          No
        </div>
        <div className={`${styles.thumb} ${role === false && styles.right}`}>
          <SwitchTransition>
            <CSSTransition
              key={role}
              addEndListener={(node, done) =>
                node.addEventListener("transitionend", done, false)
              }
              classNames="fade"
            >
              <div>{role === true ? <>Yes</> : <>No</>}</div>
            </CSSTransition>
          </SwitchTransition>
        </div>
      </div>
    </div>
  );
};

export default CodecsSwitch;
