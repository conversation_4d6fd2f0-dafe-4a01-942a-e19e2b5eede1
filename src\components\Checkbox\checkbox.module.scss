@use "../../styles/theme.scss" as *;

.container {
  width: 45px;
  height: 24px;
  padding: 3.75px;
}
.box {
  border: 1px solid #838CA0;
  border-radius: 2px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.1s ease;
  width: 16.5px;
  height: 16.6px;
  color: white;
  &.disabled {
    cursor: auto;
    &.indeterminate {
      &:hover {
        background: #2E70E5;
        border-color: #2E70E5;
        .tick {
          opacity: 1;
        }
      }
    }
    &:hover {
      background: none;
      border-color: #9b9b9b;
      .tick {
        opacity: 0;
      }
    }
  }
  &.indeterminate {
    background: $faded-orange;
    border-color: $faded-orange;
    .tick {
      opacity: 1;
    }
  }
  &:hover {
    // background: #2E70E5;
    border-color: #2E70E5;
    .tick {
      opacity: 1;
    }
  }
  svg {
    width: 100%;
  }
}
.checked {
  background: #2E70E5;
  border-color: #2E70E5;
  &:hover {
    border-color: #2E70E5;
    background: #2E70E5;
  }
}
.checkbox {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}
.tick {
  opacity: 0;
  transition: all 0.1s ease;
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  &.tickChecked {
    opacity: 1;
  }
}
