import Dialog from "@/components/Dialog";
import {
  WarningCircle,
  Visa,
  MasterCard,
  HandCoinsCircle,
} from "@/components/svgs";
import styles from "./refund-transaction-modal.module.scss";
import {
  clearInput,
  createStateObject,
  handleInputChange,
  labels,
} from "@/components/utils/InputHandlers";
import { useState } from "react";
import { Input } from "@/components/Input";
import formatDate, { formatDateWithTime } from "@/components/utils/formatDate";

type RefundTransactionModalProps = {
  onClose: () => void;
  open: boolean;
  transactionData: any;
};

const RefundTransactionModal = ({
  onClose,
  open,
  transactionData,
}: RefundTransactionModalProps) => {
  const fields = ["amount", "reason"];
  const [formData, setFormData] = useState(createStateObject(fields));

  return (
    <Dialog
      open={open}
      onClose={onClose}
      size="sm"
      headerIcon={<HandCoinsCircle />}
      headerTitle="Refund Transaction"
      headerSubtitle={`Process a refund for [Subscriber Name] ([Subscriber Email]). Review the transaction details below before proceeding.`}
      confirmButtonText="Process Refund"
      confirmButtonOnClick={onClose}
      cancelButtonText="Cancel"
    >
      <div className={styles.tableContainer}>
        <table className={styles.table}>
          <tbody>
            <tr>
              <td className={styles.label}>Transaction ID</td>
              <td className={styles.value}>{transactionData.transactionId}</td>
            </tr>
            <tr>
              <td className={styles.label}>Order ID</td>
              <td className={styles.value}>{transactionData.orderId}</td>
            </tr>
            <tr>
              <td className={styles.label}>Date & Time</td>
              <td className={styles.value}>
                {formatDateWithTime(transactionData.time)}
              </td>
            </tr>
            <tr>
              <td className={styles.label}>Item</td>
              <td className={styles.value}>{transactionData.itemPurchased}</td>
            </tr>
            <tr>
              <td className={styles.label}>Amount (GBP)</td>
              <td className={styles.value}>{transactionData.price}</td>
            </tr>
            <tr>
              <td className={styles.label}>Payment Method</td>
              <td className={styles.value}>
                <div
                  style={{ display: "flex", alignItems: "center", gap: "8px" }}
                >
                  {transactionData.type === "visa" ? <Visa /> : <MasterCard />}
                  •••• {transactionData.lastdigits}
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div className={styles.formContainer}>
        {fields.map((field) => (
          <Input
            key={field}
            label={field === "amount" ? "Amount to Refund" : labels[field]}
            value={formData[field]}
            onChange={(e: any) =>
              handleInputChange(field, e, formData, setFormData)
            }
            error={formData.errors[field]}
            clear={() => clearInput(field, setFormData)}
            infoTooltipText
          />
        ))}
      </div>
    </Dialog>
  );
};

export default RefundTransactionModal;
