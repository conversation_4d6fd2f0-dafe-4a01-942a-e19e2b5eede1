.label {
  margin-bottom: 4px;
  font-size: 14px;
}

.toggleButtonGroup {
  display: flex;
  gap: 0.5rem;
}

.button {
  padding: 14px 16px;
  border: 1px solid var(--textField-border-primary);
  border-radius: 16px;
  background-color: var(--textField-background-primary);
  color: var(--primary-900);
  cursor: pointer;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.button:hover {
  background-color: var(--primary-100);
}

.selected {
  background-color: var(--primary-500);
  color: #fff;
  pointer-events: none;
}
