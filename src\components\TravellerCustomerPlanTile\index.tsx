import PlanDataBar from "../PlanDataBar";
import ProductsMenu from "../ProductsMenu";
import { CallCircle, Chat, Cog, Phone, ArrowsClockwise, Signal, Plugs, PlugsConnected, X, ExclaimCircle, WarningCircle } from "../svgs";
import { TravellerProductStates } from "../utils/travellerProductStates";
import styles from './traveller-customer-plan-tile.module.scss'
import { useState } from "react";
import Dialog from "../Dialog";

type TravellerCustomerPlanTileProps = {
  data: TravellerProductStates
};

const TravellerCustomerPlanTile = ({
  data
}: TravellerCustomerPlanTileProps) => {

  const hasMobileNumber = data.type !== 'data';

  const hasDataPlan = data.type === 'combo' || data.type === 'data'
  const hasSMSPlan = data.type === 'combo' || data.type === 'vn-mins-sms'
  const hasCallPlan = data.type === 'combo' || data.type === 'vn-mins-sms'

  const showMenuButton = !data.expired && !data.cancelled;

  const getFooterStatusObject = () => {
    if (data.autoRenew === true) {
      return {
        bgColor: "var(--primary-50)",
        text: "Renews on 24 Apr 2024"
      }
    }
    else if (data.autoRenew === false) {
      return {
        bgColor: "var(--systemStatus-red-50)",
        text: "Expires on 12 Apr 2024"
      }
    }
    else if (data.expired) {
      return {
        bgColor: "var(--gray-50)",
        text: "Expired on 10 Dec 2022"
      }
    }
    else if (data.cancelled) {
      return {
        bgColor: "var(--systemStatus-red-100)",
        text: "Cancelled on 10 Dec 2022"
      }
    }
  }

  const getMenuItems = () => {
    const showTurnOffAutoRenew = data.autoRenew;
    const showTurnOnAutoRenew = data.autoRenew === false;
    const showDeactivateVN = data.type !== 'data' && !data.vnInactive;
    const showActivateVN = data.type !== 'data' && data.vnInactive;
    const showCancelPlan = !data.cancelled && !data.expired;

    return [
      showTurnOffAutoRenew && {
        label: "Turn OFF Auto-Renew",
        icon: <ArrowsClockwise />,
        onClick: () => setTurnOffAutoRenewModal(true),
      },
      showTurnOnAutoRenew && {
        label: "Turn ON Auto-renew",
        icon: <ArrowsClockwise />,
        onClick: () => setTurnOnAutoRenewModal(true)
      },
      showDeactivateVN && {
        label: "Deactivate Virtual Number",
        icon: <Plugs />,
        onClick: () => setDeactivateVNModal(true)
      },
      showActivateVN && {
        label: "Activate Virtual Number",
        icon: <PlugsConnected />,
        onClick: () => setActivateVNModal(true)
      },
      showCancelPlan && {
        label: "Cancel Plan",
        icon: <X width={20} height={20} />,
        onClick: () => setCancelPlanModal(true)
      }
    ].filter(Boolean)
  }

  const [showTurnOnAutoRenewModal, setTurnOnAutoRenewModal] = useState(false);
  const [showTurnOffAutoRenewModal, setTurnOffAutoRenewModal] = useState(false);
  const [showCancelPlanModal, setCancelPlanModal] = useState(false);
  const [showDeactivateVNModal, setDeactivateVNModal] = useState(false);
  const [showActivateVNModal, setActivateVNModal] = useState(false);

  return (
    <>
      <TurnOnAutoRenewConfirmModal
        open={showTurnOnAutoRenewModal}
        onClose={() => setTurnOnAutoRenewModal(false)}
      />
      <TurnOffAutoRenewConfirmModal
        open={showTurnOffAutoRenewModal}
        onClose={() => setTurnOffAutoRenewModal(false)}
      />
      <CancelPlanConfirmModal
        open={showCancelPlanModal}
        onClose={() => setCancelPlanModal(false)}
      />
      <DeactivateVNConfirmModal
        open={showDeactivateVNModal}
        onClose={() => setDeactivateVNModal(false)}
      />
      <ActivateVNConfirmModal
        open={showActivateVNModal}
        onClose={() => setActivateVNModal(false)}
      />

      <div className={styles.container}>
        <div className={styles.topSection}>
          <div className={styles.badgesContainer}>
            <Badge variant="info" text={planTypeTextMap[data.type]} />

            {data.active === true && <Badge variant="success" text="Active" />}
            {data.active === false && <Badge variant="neutral" text="Inactive" />}
            {data.dataActive && <Badge variant="success" text="Data Active" />}
            {data.vnInactive === true && <Badge variant="neutral" text="VN Inactive" />}
            {data.expired && <Badge variant="neutral" text="Expired" />}
            {data.cancelled && <Badge variant="error" text="Cancelled" />}
          </div>

          {showMenuButton && (
            <div className={styles.menu}>
              <ProductsMenu
                data={{
                  icon: <Cog />,
                  items: getMenuItems()
                }}
              />
            </div>
          )}
        </div>

        <div className={styles.productAndCountrySection}>
          <div className={styles.productCountryContainer}>
            <img
              src="https://public-gist-bucket.s3.eu-north-1.amazonaws.com/flag-icons/png/GB.png"
              width={30}
              height={30}
            />
            <div className={styles.countryNameAndOffersContainer}>
              <p className={styles.countryName}>United Kingdom</p>
              <p className={styles.offers}>5GB, 100 mins, 100 SMS</p>
            </div>
          </div>

          <p className={styles.date}>12/05/24</p>
        </div>

        {hasMobileNumber && (
          <div className={styles.mobileNumberSection}>
            <div className={styles.icon}>
              <CallCircle />
            </div>
            <div className={styles.text}>
              <p className={styles.label}>Mobile Number</p>
              <p className={styles.phoneNumber}>0751234567890</p>
            </div>
          </div>
        )}

        <div className={styles.planUsageSection}>
          {hasDataPlan && (
            <PlanDataBar
              Icon={Signal}
              displayText={"2.4GB Remaining"}
              percentage={"52%"}
              faded={false}
            grey={data.expired || data.cancelled}
            />
        )}
        {hasSMSPlan &&
          (<PlanDataBar
          Icon={Chat}
          displayText={"92 SMS"}
          percentage={"8%"}
            faded={data.vnInactive}
          grey={data.expired || data.cancelled}
        />
          )}
        {hasCallPlan &&
          (<PlanDataBar
            Icon={Phone}
            displayText={"24 Mins"}
            percentage={"77%"}
            faded={data.vnInactive}
            grey={data.expired || data.cancelled}
        />
          )}
      </div>

      <div className={styles.bottomSection} style={{ background: getFooterStatusObject()?.bgColor }}>
        {getFooterStatusObject()?.text}
      </div>
    </div>
    </>
  );
};

export default TravellerCustomerPlanTile;

type BadgeProps = {
  variant: "success" | "error" | "neutral" | "info";
  text: string;
}

const Badge = ({
  variant,
  text
}: BadgeProps) => {
  return (
    <div className={styles.badge} style={badgeColorMap[variant]}>
      {text}
    </div>
  )
}

const badgeColorMap: Record<BadgeProps["variant"], {
  background: string;
  color: string;
}> = {
  success: {
    background: "var(--systemStatus-green-100)",
    color: "var(--systemStatus-green-800)",
  },
  error: {
    background: "var(--systemStatus-red-100)",
    color: "var(--systemStatus-red-800)",
  },
  info: {
    background: "var(--primary-50)",
    color: "var(--primary-900)",
  },
  neutral: {
    background: "var(--gray-100)",
    color: "var(--gray-800)",
  }
}

const planTypeTextMap = {
  "combo": "Combo",
  "vn-mins-sms": "Virtual Number",
  "vn-only": "Virtual Number",
  "data": "Data"
} as any

type TurnOnAutoRenewConfirmModalProps = {
  open: boolean;
  onClose: () => void;
}

const TurnOnAutoRenewConfirmModal = (props: TurnOnAutoRenewConfirmModalProps) => {

  return (
    <Dialog
      open={props.open}
      onClose={props.onClose}
      size="sm"
      headerIcon={<ExclaimCircle />}
      headerTitle="Turn ON Auto-Renew?"
      headerSubtitle="Auto-renew will be activated for [Plan Name]. The plan will renew automatically upon expiry or when the data allowance is fully consumed."
      confirmButtonText="Yes, Turn ON Auto-Renew"
      confirmButtonOnClick={props.onClose}
      cancelButtonText="Cancel"
    />
  )
}

type TurnOffAutoRenewConfirmModalProps = {
  open: boolean;
  onClose: () => void;
}

const TurnOffAutoRenewConfirmModal = (props: TurnOffAutoRenewConfirmModalProps) => {

  return (
    <Dialog
      open={props.open}
      onClose={props.onClose}
      size="sm"
      headerIcon={<ExclaimCircle />}
      headerTitle="Turn OFF Auto-Renew?"
      headerSubtitle="Auto-renew will be deactivated for [Plan Name]. Once disabled, the plan will not renew automatically, and the user will need to purchase a new plan manually."
      confirmButtonText="Yes, Turn OFF Auto-Renew"
      confirmButtonOnClick={props.onClose}
      cancelButtonText="Cancel"
    />
  )
}

type CancelPlanConfirmModalProps = {
  open: boolean;
  onClose: () => void;
}

const CancelPlanConfirmModal = (props: CancelPlanConfirmModalProps) => {
  return (
    <Dialog
      open={props.open}
      onClose={props.onClose}
      size="sm"
      headerIcon={<WarningCircle />}
      headerTitle="Cancel Plan?"
      headerSubtitle={
        <>
          [Plan Name]
          <br />
          <br />
          <span>Once canceled, this plan will no longer be active, and any remaining data or benefits may be lost. This action cannot be undone.</span>
        </>
      }
      confirmButtonText="Yes, Cancel Plan"
      confirmButtonOnClick={props.onClose}
      confirmButtonVariant="destructive"
      cancelButtonText="No, Keep Plan"
    />
  )
}

type DeactivateVNConfirmModalProps = {
  open: boolean;
  onClose: () => void;
}

const DeactivateVNConfirmModal = (props: DeactivateVNConfirmModalProps) => {
  return (
    <Dialog
      open={props.open}
      onClose={props.onClose}
      size="sm"
      headerIcon={<ExclaimCircle />}
      headerTitle="Deactivate Virtual Number?"
      headerSubtitle={
        <>
          Deactivate the virtual number [+Virtual Number]. Once deactivated:
          <br />
          <br />
          <ul style={{ paddingLeft: 18 }}>
            <li>The user will lose access to this number and will no longer be able to send or receive calls and messages.</li>
            <li>If auto-renew is not enabled, the number will be marked for deletion and may not be recoverable.</li>
          </ul>
        </>
      }
      confirmButtonText="Yes, Deactivate Virtual Number"
      confirmButtonOnClick={props.onClose}
      cancelButtonText="Cancel"
    />
  )
}

type ActivateVNConfirmModalProps = {
  open: boolean;
  onClose: () => void
}
const ActivateVNConfirmModal = (props: ActivateVNConfirmModalProps) => {
  return (
    <Dialog
      open={props.open}
      onClose={props.onClose} size="sm"
      headerIcon={<ExclaimCircle />}
      headerTitle="Activate Virtual Number?"
      headerSubtitle="Activate the virtual number [+Virtual Number]. Once activated, the user will regain access to this number and can send and receive calls and messages."
      confirmButtonText="Yes, Activate Virtual Number"
      confirmButtonOnClick={props.onClose}
      cancelButtonText="Cancel"
    />
  )
}












  ;
