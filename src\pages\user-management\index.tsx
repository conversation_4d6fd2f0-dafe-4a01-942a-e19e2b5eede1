import styles from "../../styles/user-management.module.scss";
import {
  Plus,
  MagnifyingGlass,
  Export,
  Settings,
  Pencil,
  Envelope,
  SendPassword,
  Delete,
} from "../../components/svgs";
import { useState, useEffect, useRef } from "react";
import { useDispatch } from "react-redux";
import Button from "../../components/Button";
import RoleBadge from "../../components/RoleBadge";
import AddEditUserModal from "../../components/AddUserModal";
import NotifyMailsModal from "@/components/NotifyMailsModal";
import UserSkeleton from "../../components/UserSkeleton";
import DeleteUserModal from "../../components/DeleteUserModal";
import StatusPill from "../../components/StatusPill";
import CollapsiblePanel from "@/components/CollapsiblePanel";
import InsightCard from "@/components/InsightCard";
import { ApiPatch } from "../api/api";
import InsightList from "@/components/InsightList";
import TableControl from "../../components/TableControl";
import { getUsers, userFields } from "./dummyData";
import Title from "@/components/Title";
import Tag from "@/components/Tag";
import { Input } from "@/components/Input";
import {
  clearInput,
  handleInputChange,
  labels,
  createStateObject,
} from "@/components/utils/InputHandlers";
import SelectInput from "@/components/SelectInput";
import ProductsMenu from "@/components/ProductsMenu";
import CheckboxDropdownInput from "@/components/CheckboxDropdownInput";

const UserManagement = () => {
  const searchUserFields = ["name", "email", "role", "status", "channel"];
  const dispatch = useDispatch();
  const [data, setData] = useState(createStateObject(searchUserFields));
  const [showSearchResults, setShowSearchResults] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);

  const repopulateUsers = () => {
    setInitialLoading(true);
    setActiveUser(null);
    const users = getUsers(usersPerPage);
    setUsersData(users);
    setFilteredUsers(users);
    setInitialLoading(false);
  };

  useEffect(repopulateUsers, []);

  const [usersData, setUsersData] = useState([] as any);
  const overviewPanelRef = useRef<any>(null);
  const searchPanelRef = useRef<any>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [usersPerPage, setUsersPerPage] = useState(10);

  const [filteredUsers, setFilteredUsers] = useState(usersData);

  /***********   Add User     ***********/

  const [showAddEditUserModal, setShowAddEditUserModal] = useState(false);

  const [showNotifyMailsModal, setShowNotifyMailsModal] = useState<
    "" | "password" | "welcome"
  >("");

  const [activeUser, setActiveUser] = useState(null as any);

  const formatDataItem = (item: any, key: string) => {
    if (key === "status") {
      return <StatusPill status={item[key] === "Active"} />;
    } else if (key === "role") {
      return <RoleBadge role={item[key]} />;
    } else {
      return item[key];
    }
  };

  /********       Delete User         **********/

  const [showDeleteUserModal, setShowDeleteUserModal] = useState(false);

  // Handles deleting user
  const handleModal = (
    user: any,
    action: "delete" | "edit" | "password" | "welcome"
  ) => {
    setActiveUser(user);
    if (action === "delete") {
      setShowDeleteUserModal(true);
    } else if (action === "edit") {
      setShowAddEditUserModal(true);
    } else {
      setShowNotifyMailsModal(action);
    }
  };

  const handleChangeRole = (userDetails: any, newRole: any) => {
    ApiPatch(`/agent/${userDetails.mid}/edit`, {
      active: userDetails.active,
      firstName: userDetails.firstName,
      lastName: userDetails.lastName,
      email: userDetails.email,
      roleId: newRole,
    })
      .then((response) => {
        repopulateUsers();
        dispatch({
          type: "notify",
          payload: {
            error: false,
            message: response.data.message,
          },
        });
      })
      .catch((error) => {
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: error.response.data.message,
          },
        });
      });
  };
  const handleChangeStatus = (userDetails: any, newStatus: any) => {
    ApiPatch(`/agent/${userDetails.mid}/edit`, {
      active: newStatus,
      firstName: userDetails.firstName,
      lastName: userDetails.lastName,
      email: userDetails.email,
      roleId: userDetails.roleId,
    })
      .then((response) => {
        repopulateUsers();
        dispatch({
          type: "notify",
          payload: {
            error: false,
            message: response.data.message,
          },
        });
      })
      .catch((error) => {
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: error.response.data.message,
          },
        });
      });
  };

  return (
    <>
      <Title>User Managment</Title>
      <div className={styles.main}>
        <AddEditUserModal
          show={showAddEditUserModal}
          setShow={setShowAddEditUserModal}
          repopulateUsers={repopulateUsers}
          currentUser={activeUser}
        />
        <NotifyMailsModal
          show={showNotifyMailsModal}
          setShow={setShowNotifyMailsModal}
          user={activeUser}
        />
        <DeleteUserModal
          show={showDeleteUserModal}
          setShow={setShowDeleteUserModal}
          user={activeUser}
          repopulateUsers={repopulateUsers}
        />
        <CollapsiblePanel
          title="Overview"
          summaryWhenClosed={
            <div style={{ marginLeft: 22 }}>
              <InsightList insights={overviewStats} />
            </div>
          }
          actionBtn={
            <Button
              style={{ marginRight: 10 }}
              onClick={(e) => {
                e.stopPropagation();
                setShowAddEditUserModal(true);
              }}
            >
              <Plus /> Create user
            </Button>
          }
          ref={overviewPanelRef}
        >
          <div className={styles.overview}>
            {overviewStats.map((stat, index) => (
              <InsightCard
                key={index}
                title={stat.title}
                value={stat.value.toString()}
              />
            ))}
          </div>
        </CollapsiblePanel>

        <div style={{ marginTop: 16 }}>
          <CollapsiblePanel
            title="Search Users"
            summaryWhenClosed={
              <div
                style={{
                  display: "flex",
                  flex: 1,
                  justifyContent: "space-between",
                  marginLeft: 16,
                  marginRight: 8,
                }}
              >
                <Tag text="3 filters applied" />
                <Button color="secondary">Clear Filters</Button>
              </div>
            }
            ref={searchPanelRef}
          >
            <div className={styles.fields}>
              {searchUserFields.map((prop) => {
                if (["name", "email"].includes(prop)) {
                  return (
                    <Input
                      key={"user-" + prop}
                      label={labels[prop]}
                      value={data[prop]}
                      onChange={(e: any) => {
                        handleInputChange(prop, e, data, setData);
                      }}
                      error={data.errors[prop]}
                      clear={() => {
                        clearInput(prop, setData);
                      }}
                      infoTooltipText
                    />
                  );
                } else if (["role", "status", "channel"].includes(prop)) {
                  return (
                    <CheckboxDropdownInput
                      key={`user-${prop}`}
                      options={selectOptionsByField[prop]}
                      label={labels[prop]}
                      selected={data[prop]}
                      onChange={(values) => {
                        handleInputChange(
                          prop,
                          values,
                          data,
                          setData,
                          "select"
                        );
                      }}
                      error={data.errors[prop]}
                      infoTooltipText
                    />
                  );
                }
              })}
            </div>
            <div className={styles.actions}>
              <Button
                color="blue"
                onClick={() => {
                  searchPanelRef.current.close();
                  overviewPanelRef.current.close();
                  setShowSearchResults(true);
                }}
              >
                <MagnifyingGlass /> Search
              </Button>
            </div>
          </CollapsiblePanel>
        </div>

        {showSearchResults && (
          <div className={styles.panel}>
            <div className={styles.panelTopBar}>
              <h4>Users</h4>
              <div className={styles.actions}>
                <Button color="secondary">
                  <Export /> Export to CSV
                </Button>
              </div>
            </div>

            <div className={`${styles.tableContainer} table-scroll`}>
              <table>
                <thead>
                  <tr>
                    {userFields.map((field: any) => (
                      <th>{field.label}</th>
                    ))}
                    <th></th>
                  </tr>
                </thead>
                <tbody>
                  {!initialLoading
                    ? usersData.map((item: any, i: number) => {
                        return (
                          <tr
                            key={`order-${item.orderNumber}`}
                            style={{ cursor: "pointer" }}
                          >
                            {userFields.map((field: any) => (
                              <td
                                key={`order-${item.orderNumber}-${field.key}`}
                              >
                                <div
                                  style={{
                                    display: "flex",
                                    justifyContent: "flex-start",
                                  }}
                                >
                                  {formatDataItem(item, field.key)}
                                </div>
                              </td>
                            ))}
                            <td>
                              <span
                                className={
                                  styles.viewRowBtn + " settingsUserMenu"
                                }
                              >
                                <ProductsMenu
                                  data={{
                                    icon: <Settings />,
                                    items: [
                                      {
                                        label: "Edit details",
                                        icon: <Pencil />,
                                        onClick: () =>
                                          handleModal(item, "edit"),
                                      },
                                      {
                                        label: "Re-send Welcome Email",
                                        icon: <Envelope />,
                                        onClick: () =>
                                          handleModal(item, "welcome"),
                                      },
                                      {
                                        label: "Send Password reset link",
                                        icon: <SendPassword />,
                                        onClick: () =>
                                          handleModal(item, "password"),
                                      },
                                      {
                                        label: "Delete user",
                                        icon: <Delete />,
                                        onClick: () =>
                                          handleModal(item, "delete"),
                                      },
                                    ],
                                  }}
                                />
                              </span>
                            </td>
                          </tr>
                        );
                      })
                    : Array.from({ length: usersPerPage }, (v, i) => i).map(
                        (i) => (
                          <UserSkeleton
                            key={"order-skeleton-" + i}
                            noOfStandard={8}
                          />
                        )
                      )}
                </tbody>
              </table>
            </div>
            <div style={{ marginTop: "16px" }}>
              <TableControl
                show
                itemsPerPage={usersPerPage}
                setItemsPerPage={(val: any) => setUsersPerPage(val)}
                currentPage={currentPage}
                setCurrentPage={setCurrentPage}
                numberOfPages={Math.ceil(usersData.length / usersPerPage)}
                label="users"
                loading={initialLoading}
              />
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default UserManagement;

const overviewStats = [
  { title: "Total Channels", value: "63,629" },
  { title: "Active Channels", value: "63,629" },
  { title: "Inactive Channels", value: "63,629" },
];

const selectOptionsByField: Record<string, any> = {
  role: ["Admin", "Agent"].map((v) => ({
    label: v,
    value: v,
  })),
  channel: [
    "London",
    "General",
    "Dublin",
    "Birmingham",
    "Manchester",
    "Glasgow",
  ].map((v) => ({
    label: v,
    value: v,
  })),
  status: ["Active", "Inactive"].map((v) => ({
    label: v,
    value: v,
  })),
};
