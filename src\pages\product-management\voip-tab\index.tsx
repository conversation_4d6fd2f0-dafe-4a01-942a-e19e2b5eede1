import BlobSwitchBar from "@/components/BlobSwitchBar";
import { useState } from "react";
import VoipCallTab from "./call-tab";
import VoipSmsTab from "./sms-tab";

const VoipProductManagementTab = () => {
  const [activeTabId, setActiveTabId] = useState("call");

  return (
    <div>
      <BlobSwitchBar
        options={[
          {
            label: "Call",
            id: "call",
          },
          {
            label: "SMS",
            id: "sms",
          },
        ]}
        selected={activeTabId}
        setSelected={setActiveTabId}
        layoutId="voip-product-management-tabs"
      />

      {activeTabId === "call" && <VoipCallTab />}
      {activeTabId === "sms" && <VoipSmsTab />}
    </div>
  );
};

export default VoipProductManagementTab;
