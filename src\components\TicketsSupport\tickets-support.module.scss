@use "../../styles/theme.scss" as *;
@import "../../styles/mixins.module.scss";

.panel {
  @include stripedTablePanel;
  height: 842px;
  // max-height: calc(100vh - 196px);
  thead {
    position: sticky;
    top: 0px;
    left: 0px;
    background: #fff;
  }
}
.plus {
  font-size: 16px;
  font-weight: 600;

  &:hover {
    color: $dark-orange;
    cursor: pointer;
  }
}
.country {
  display: grid;
  grid-template-columns: 24px 1fr;
  grid-column-gap: 8px;
  align-items: center;
  font-weight: 400;
  .flag {
    background-size: cover;
    background-position: center;
    width: 24px;
    height: 24px;
    border-radius: 1000px;
  }
}

.details {
  color: #4d4d4d;
  font-family: Poppins;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.subdetails {
  color: var(--primary-pallete-black, #1a1a1a);
  font-family: Poppins;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.showmore {
  color: var(--primary-pallete-black, #1a1a1a);
  font-family: Poppins;
  font-size: 12px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  cursor: pointer;
}

.div_notes {
  display: flex;
  padding: 4px;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
  align-self: stretch;
  width: 100%;
  background-color: #f2f2f2;
  border-radius: 4px;
}

.notes {
  display: flex;
  justify-content: space-between;
  padding: 8px 10px;
  align-items: flex-start;
  gap: 10px;
  align-self: stretch;
  &:hover {
    background-color: $light-orange;
    cursor: pointer;
    border-radius: 4px;
  }
}
.icons {
  background-color: #f5f5f5;
  padding: 6px;
  display: flex;
  border-radius: 4px;

  &:hover {
    background-color: $light-orange;
    cursor: pointer;
  }
}
.selection {
  height: 47px;
  border-radius: 1000px;
  color: $black;
  font-size: 14px;
  padding: 0 24px;
  display: flex;
  align-items: center;
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
  &:hover {
    color: $dark-orange;
  }
  span {
    position: relative;
    z-index: 6;
  }
}
.subSelection {
  height: 47px;
  border-radius: 1000px;
  color: $black;
  font-size: 14px;
  padding: 0 24px;
  display: flex;
  align-items: center;
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
  &:hover {
    color: $dark-orange;
  }
  span {
    position: relative;
    z-index: 6;
  }
}

.activeSelection {
  text-shadow: 0px 0px 0.5px $black;
  cursor: auto;
  &:hover {
    color: $black;
  }
}

.topRow {
  display: flex;
  justify-content: space-between;
  width: 100%;
  // margin-bottom: 18px;

  .selectionWrapper {
    display: flex;
    align-items: center;
  }

  .buttons {
    display: flex;
    align-items: center;
    gap: 12px;
  }
}
.background {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 1000px;
  background-color: $light-orange;
  z-index: 5;
  left: 0;
}

.tableContainer {
  overflow: auto;
  padding-bottom: 15px;
  padding-right: 15px;
  position: relative;
}

.title {
  font-family: Poppins;
  font-size: 20px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
}
