import styles from "./show-notes.module.scss";
import Modal from "../Modal";
import { Delete } from "../svgs";
import { useState } from "react";

const ShowNotesModal = ({ show, setShow, provider }: any) => {
  const [loading, setLoading] = useState(false);

  // Populate with current data

  const deleteNote = () => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
      setShow(false);
    }, 2000);
  };

  return (
    <Modal
      saveButton={
        <>
          <Delete />
          Delete Note
        </>
      }
      image="/edit_user_graphic.svg"
      show={show}
      setShow={setShow}
      proceed={deleteNote}
      close={setShow}
      // onClose={() => setShow(false)}
      loading={loading}
    >
      <div className={`${styles.main} `}>
        <h3>Note details</h3>

        <div className={styles.modalContent}>
          {/* <div style={{ display: "flex", justifyContent: "space-between" }}>
            <div className={styles.subdetails}>
              By : <span className={styles.details}>{provider?.name}</span>
            </div>
            <div className={styles.subdetails}>{provider?.date}</div>
          </div> */}

          <p style={{ fontSize: "14px", fontWeight: "400" }}>
            {provider?.note}
          </p>
        </div>
      </div>
    </Modal>
  );
};

export default ShowNotesModal;
