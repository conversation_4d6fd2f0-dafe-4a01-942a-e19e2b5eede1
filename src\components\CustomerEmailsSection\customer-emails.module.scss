.panelTopBar {
  width: 100%;
  display: flex;
  align-items: center;
  margin-top: 16px;
  .actions {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
  h4 {
    font-size: 16px;
    font-weight: 700;
    line-height: 22px;
  }
}

.emailsContainer {
  width: 100%;
  margin-top: 16px;
  height: calc(100% - 120px);
}

.emailContainer {
  border: 1px solid #dfe2e7;
  max-width: 670px;
  border-radius: 16px;
  margin-bottom: 10px;
  .emailSubject {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 13px 16px;
    .subjectText {
      font-size: 14px;
      font-weight: 700;
      line-height: 18px;
    }
  }
  .dateContainer {
    display: flex;
    align-items: center;
    .date {
      font-size: 14px;
      font-weight: 400;
      line-height: 18px;
      color: #838ca0;
    }
    .mainExpand {
      margin-left: 16px;
      color: #2e70e5;
      display: flex;
      align-items: center;
      transition: transform 0.2s ease;
      cursor: pointer;
      &.open {
        transform: rotate(180deg);
      }
    }
  }
}

.repliesContainer {
  padding: 2px 10px 10px 10px;
  border-top: 1px solid #dfe2e7;
  .replyContainer {
    border: 1px solid #dfe2e7;
    width: 100%;
    border-radius: 16px;
    margin-top: 8px;
    .replyTop {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 7px 16px;
      font-size: 14px;
      font-weight: 400;
      line-height: 18px;
      .left,
      .right {
        display: flex;
        align-items: center;
      }
      .left {
        .circle {
          background-color: #80a9ef;
          width: 24px;
          height: 24px;
          border-radius: 50px;
          margin-right: 12px;
        }
        .name {
          color: #525a6b;
          margin-right: 6px;
        }
        .date {
          color: #838ca0;
        }
      }
      .right {
        .mainExpand {
          margin-left: 12px;
          color: #2e70e5;
          display: flex;
          align-items: center;
          transition: transform 0.2s ease;
          cursor: pointer;
          &.open {
            transform: rotate(180deg);
          }
        }
      }
    }
    .replyContent {
      border-top: 1px solid #dfe2e7;
      padding: 20px 16px 16px 16px;
      font-size: 14px;
      font-weight: 400;
      line-height: 18px;
      color: #061632;
    }
  }
}
