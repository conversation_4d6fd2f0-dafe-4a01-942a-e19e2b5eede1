import { useCallback, useEffect, useRef, useState } from "react";
import styles from "./call-tab.module.scss";
import CollapsiblePanel from "@/components/CollapsiblePanel";
import InsightList from "@/components/InsightList";
import Button from "@/components/Button";
import {
  MagnifyingGlass,
  Plus,
  Delete,
  Export,
  Pencil,
} from "@/components/svgs";
import InsightCard from "@/components/InsightCard";
import {
  clearInput,
  createStateObject,
  handleInputChange,
  labels,
} from "@/components/utils/InputHandlers";
import Tag from "@/components/Tag";
import SelectInput from "@/components/SelectInput";
import { countryListAlpha2 } from "@/components/utils/countryList";
import { getVoipCallProducts } from "@/components/utils/dataCreator";
import UserSkeleton from "@/components/UserSkeleton";
import TableControl from "@/components/TableControl";
import { voipCallProductFields } from "@/components/utils/voipCallProductFields";
import { formatDateWords } from "@/components/utils/formatDate";
import CountryDisplay from "@/components/CountryDisplay";
import StatusPill from "@/components/StatusPill";
import CreateVoipCallProductModal from "@/components/CreateVoipCallProductModal";
import EditVoipCallProductModal from "@/components/EditVoipCallProductModal";
import DeleteVoipCallProductModal from "@/components/DeleteVoipCallProductModal";
import { getCountryOptions } from "@/components/utils/getCountryOptions";
import CheckboxDropdownInput from "@/components/CheckboxDropdownInput";

const VoipCallTab = () => {
  const searchFields = ["callAllowance", "country", "status", "validity"];
  const [data, setData] = useState(createStateObject(searchFields));

  const overviewPanelRef = useRef<any>(null);
  const searchPanelRef = useRef<any>(null);
  const [showSearchResults, setShowSearchResults] = useState(false);

  const [loading, setLoading] = useState(true);
  const [products, setProducts] = useState<any[]>([]);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [numberOfPages, setNumberOfPages] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  const [showCreateProductModal, setShowCreateProductModal] = useState(false);
  const [activelyEditedProduct, setActivelyEditedProduct] = useState(null);
  const [activelyDeletedProduct, setActivelyDeletedProduct] = useState(null);

  const populate = (itemsPerPage: number) => {
    setLoading(true);
    setTimeout(() => {
      setProducts(getVoipCallProducts(itemsPerPage));
      setLoading(false);
    }, 500);
  };

  useEffect(() => {
    populate(itemsPerPage);
  }, [itemsPerPage, currentPage]);

  const formatDataItem = (item: any, key: string) => {
    if (key === "dateAdded") {
      return formatDateWords(item[key]);
    }

    if (key === "status") {
      return (
        <StatusPill
          status={item[key] ? "Active" : "Inactive"}
          color={item[key] ? "active" : "inactive"}
        />
      );
    }

    if (key === "country") {
      return <CountryDisplay country={item[key].code} width={16} height={16} />;
    }

    if (key === "callAllowance") {
      return `${item[key]} Mins`;
    }

    if (!item[key]) {
      return "-";
    }

    return item[key];
  };

  const renderCreateProductButton = useCallback(
    () => (
      <Button
        onClick={(e) => {
          e.stopPropagation();
          setShowCreateProductModal(true);
        }}
      >
        <Plus />
        Create Product
      </Button>
    ),
    []
  );

  return (
    <>
      {showCreateProductModal && (
        <CreateVoipCallProductModal
          open={showCreateProductModal}
          onClose={() => setShowCreateProductModal(false)}
        />
      )}

      {!!activelyEditedProduct && (
        <EditVoipCallProductModal
          onClose={() => setActivelyEditedProduct(null)}
          open={!!activelyEditedProduct}
          productData={activelyEditedProduct}
        />
      )}

      {!!activelyDeletedProduct && (
        <DeleteVoipCallProductModal
          onClose={() => setActivelyDeletedProduct(null)}
          open={!!activelyDeletedProduct}
          productData={activelyDeletedProduct}
        />
      )}

      {/* Overview */}
      <CollapsiblePanel
        ref={overviewPanelRef}
        title="Overview"
        headerWhenOpen={
          <div className={styles.overviewHeaderOpenWrapper}>
            {renderCreateProductButton()}
          </div>
        }
        summaryWhenClosed={
          <div className={styles.overviewSummaryWrapper}>
            <InsightList insights={overviewStats} />
            {renderCreateProductButton()}
          </div>
        }
      >
        <div className={styles.overview}>
          {overviewStats.map((stat, index) => (
            <InsightCard
              key={index}
              title={stat.title}
              value={stat.value.toString()}
            />
          ))}
        </div>
      </CollapsiblePanel>

      {/* Search */}
      <div style={{ marginTop: 16 }}>
        <CollapsiblePanel
          ref={searchPanelRef}
          title="Search Call Products"
          summaryWhenClosed={
            <div
              style={{
                display: "flex",
                flex: 1,
                justifyContent: "space-between",
                marginLeft: 16,
                marginRight: 8,
              }}
            >
              <Tag text="3 filters applied" />
              <Button color="secondary">Clear Filters</Button>
            </div>
          }
        >
          <div className={styles.searchPanelForm}>
            <div className={styles.fields}>
              {searchFields.map((prop) => (
                <CheckboxDropdownInput
                  key={"call-tab-" + prop}
                  options={selectOptionsByField[prop]}
                  label={labels[prop]}
                  selected={data[prop]}
                  onChange={(values) => {
                    handleInputChange(prop, values, data, setData, "select");
                  }}
                  searchOption={prop === "country"}
                  error={data.errors[prop]}
                  infoTooltipText
                />
              ))}
            </div>
            <div className={styles.actions}>
              <Button
                color="blue"
                onClick={() => {
                  searchPanelRef.current.close();
                  overviewPanelRef.current.close();
                  setShowSearchResults(true);
                }}
              >
                <MagnifyingGlass /> Search
              </Button>
            </div>
          </div>
        </CollapsiblePanel>
      </div>

      {/* Table */}
      <div className={styles.panel}>
        <div className={styles.panelTopBar}>
          <h4>Call Products</h4>
          <div className={styles.actions}>
            <Button color="secondary">
              <Export /> Export to CSV
            </Button>
          </div>
        </div>

        <div className={`${styles.tableContainer} table-scroll`}>
          <table>
            <thead>
              <tr>
                {voipCallProductFields.map((field: any) => (
                  <th key={field.key}>{field.label}</th>
                ))}
                <th></th>
              </tr>
            </thead>
            <tbody>
              {!loading
                ? products.map((item: any, i: number) => (
                    <tr key={`product-${i}`}>
                      {voipCallProductFields.map((field: any) => (
                        <td key={`product-${i}-${field.key}`}>
                          <div
                            style={{
                              display: "flex",
                              justifyContent: "flex-start",
                            }}
                          >
                            {formatDataItem(item, field.key)}
                          </div>
                        </td>
                      ))}
                      <td style={{ width: 100 }}>
                        <div className={styles.tableRowActions}>
                          <span onClick={() => setActivelyEditedProduct(item)}>
                            <Pencil />
                          </span>
                          <span onClick={() => setActivelyDeletedProduct(item)}>
                            <Delete />
                          </span>
                        </div>
                      </td>
                    </tr>
                  ))
                : Array.from({ length: itemsPerPage }, (v, i) => i).map((i) => (
                    <UserSkeleton
                      key={"product-skeleton-" + i}
                      noOfStandard={voipCallProductFields.length}
                    />
                  ))}
            </tbody>
          </table>
        </div>
        <div style={{ marginTop: "16px" }}>
          <TableControl
            show
            itemsPerPage={itemsPerPage}
            setItemsPerPage={(val: any) => setItemsPerPage(val)}
            currentPage={currentPage}
            setCurrentPage={setCurrentPage}
            numberOfPages={numberOfPages}
            label="products"
            loading={loading}
          />
        </div>
      </div>
    </>
  );
};

export default VoipCallTab;

const overviewStats = [
  { title: "Total Products", value: "63,629" },
  { title: "Active Products", value: "63,629" },
  { title: "Inactive Products", value: "63,629" },
  { title: "Countries", value: "63,629" },
];

const selectOptionsByField: Record<string, any> = {
  callAllowance: ["100", "200", "300", "400", "500", "600", "700", "800"].map(
    (v) => ({
      label: v,
      value: v,
    })
  ),
  country: getCountryOptions(),
  status: [
    { label: "Active", value: "active" },
    { label: "Inactive", value: "Inactive" },
  ],
  validity: [
    { label: "30 days", value: "30" },
    { label: "7 days", value: "7" },
    { label: "5 days", value: "5" },
  ],
};
