export const getProductFields = (type: "wholesale" | "retail") => {
  const retailOnlyFields =
    type === "retail"
      ? [
          {
            label: "Status",
            key: "status",
          },
        ]
      : [];

  return [
    {
      label: "Family",
      key: "family",
    },
    {
      label: "Wholesale Name",
      key: "name",
    },
    {
      label: "Retail Name",
      key: "retailName",
    },
    {
      label: "Size (GB)",
      key: "sizeGB",
    },
    {
      label: "Wholesale Price",
      key: "wholesalePrice",
    },
    {
      label: "Retail Price",
      key: "retailPrice",
    },
    {
      label: "Creation Date",
      key: "creationDate",
    },
    {
      label: "Talk & Text",
      key: "talkText",
    },
    {
      label: "Approach",
      key: "approach",
    },
    {
      label: "Billing",
      key: "billing",
    },
    ...retailOnlyFields,
  ];
};
