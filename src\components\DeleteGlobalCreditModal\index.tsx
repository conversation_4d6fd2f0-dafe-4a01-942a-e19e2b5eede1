import Dialog from "@/components/Dialog";
import { WarningCircle } from "@/components/svgs";
import styles from './delete-global-credit-modal.module.scss'
import StatusPill from "@/components/StatusPill";

type DeleteGlobalCreditModalProps = {
  onClose: () => void;
  open: boolean;
  productData: any;
}

const DeleteGlobalCreditModal = ({
  onClose,
  open,
  productData,
}: DeleteGlobalCreditModalProps) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      size="sm"
      headerIcon={<WarningCircle />}
      headerTitle="Delete Product?"
      headerSubtitle="Once removed, it cannot be recovered"
      confirmButtonText="Yes, Delete Product"
      confirmButtonOnClick={onClose}
      confirmButtonVariant="destructive"
      cancelButtonText="Cancel"
    >
      <div className={styles.tableContainer}>
        <table className={styles.table}>
          <tbody>
            <tr>
              <td className={styles.label}>Credit Amount</td>
              <td className={styles.value}>{productData.creditAmount} {productData.currency}</td>
            </tr>
            <tr>
              <td className={styles.label}>Status</td>
              <td className={styles.value}>
                <StatusPill status={productData.status ? "Active" : "Inactive"} color={productData.status ? "active" : "inactive"} />
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </Dialog>
  )
}

export default DeleteGlobalCreditModal 