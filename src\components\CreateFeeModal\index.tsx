import Dialog from "@/components/Dialog";
import { PlusCircle } from "@/components/svgs";
import {
  clearInput,
  createStateObject,
  handleInputChange,
  labels,
} from "@/components/utils/InputHandlers";
import { useState } from "react";
import styles from "./create-fee-modal.module.scss";
import { Input } from "@/components/Input";
import SelectInput from "@/components/SelectInput";
import ToggleButtonGroup from "@/components/ToggleButtonGroup";

type CreateFeeModalProps = {
  open: boolean;
  onClose: () => void;
};

const CreateFeeModal = ({ open, onClose }: CreateFeeModalProps) => {
  const fields = ["type", "name", "amount", "status"];
  const [formData, setFormData] = useState(createStateObject(fields));

  return (
    <Dialog
      open={open}
      onClose={onClose}
      size="sm"
      headerIcon={<PlusCircle />}
      headerTitle="Create Fee"
      confirmButtonText="Create Fee"
      confirmButtonOnClick={onClose}
      cancelButtonText="Cancel"
    >
      <div className={styles.formContainer}>
        {fields.map((field) => {
          if (["type"].includes(field)) {
            return (
              <SelectInput
                key={"fee-" + field}
                options={selectOptionsByField[field]}
                label={labels[field]}
                value={formData[field]}
                onChange={(e: any) => {
                  handleInputChange(field, e, formData, setFormData);
                }}
                error={formData.errors[field]}
                infoTooltipText
              />
            );
          } else if (["status"].includes(field)) {
            return (
              <ToggleButtonGroup
                selected={formData[field]}
                options={[
                  {
                    label: "Active",
                    key: "active",
                  },
                  {
                    label: "Inactive",
                    key: "inactive",
                  },
                ]}
                label={labels[field]}
                onChange={(value) => {
                  handleInputChange(
                    field,
                    { target: { value } },
                    formData,
                    setFormData
                  );
                }}
              />
            );
          } else {
            return (
              <Input
                key={"field-" + field}
                label={labels[field]}
                value={formData[field]}
                onChange={(e: any) => {
                  handleInputChange(field, e, formData, setFormData);
                }}
                error={formData.errors[field]}
                clear={() => {
                  clearInput(field, setFormData);
                }}
                infoTooltipText
              />
            );
          }
        })}
      </div>
    </Dialog>
  );
};

export default CreateFeeModal;

const selectOptionsByField: Record<string, any> = {
  type: ["Regulatory", "Activation"],
};
