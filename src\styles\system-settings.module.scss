@use "./theme.scss" as *;
@import "./mixins.module.scss";

.top {
  display: flex;
  background: #fff;
  margin-left: -24px;
  padding: 0 24px 0 48px;
  height: 80px;
  align-items: center;
  justify-content: space-between;
  transition: all 0.5s ease;
  h2 {
    margin: 0;
    font-weight: 700;
    font-size: 20px;
    line-height: 30px;
  }
}

.main {
  width: 100%;
  background: #f1f1f1;
  padding: 24px;
}

.topRow {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 18px;

  .selectionWrapper {
    display: flex;
    align-items: center;
  }

  .buttons {
    display: flex;
    align-items: center;
    gap: 12px;
  }
}

.selection {
  height: 47px;
  border-radius: 1000px;
  color: $black;
  font-size: 14px;
  padding: 0 24px;
  display: flex;
  align-items: center;
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
  &:hover {
    color: $dark-orange;
  }
  span {
    position: relative;
    z-index: 6;
  }
}

.background {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 1000px;
  background-color: $light-orange;
  z-index: 5;
  left: 0;
}

.activeSelection {
  text-shadow: 0px 0px 0.5px $black;
  cursor: auto;
  &:hover {
    color: $black;
  }
}

.panel {
  @include stripedTablePanel;
  thead {
    background: #fff;
  }
}

.tableContainer {
  overflow: auto;
}

.actionPanel {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-left: auto;
}

.actionButton {
  background: none;
  border: none;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background 0.15s ease;
  border-radius: 8px;
  margin-right: 12px;
  &:last-child {
    margin-right: 0px;
  }
  &:hover {
    background: #fff;
  }
}
.country {
  display: grid;
  grid-template-columns: 24px 1fr;
  grid-column-gap: 8px;
  align-items: center;
  font-weight: 400;
  .flag {
    background-size: cover;
    background-position: center;
    width: 24px;
    height: 24px;
    border-radius: 1000px;
  }
}

.eSims_available {
  padding: 12px 24px;
  border-radius: 0 25px 25px 0px; /* top-left, top-right, bottom-right, bottom-left */
}
.eSims_provisioned {
  padding: 12px 24px;
  border-radius: 25px 0 0 25px; /* top-left, top-right, bottom-right, bottom-left */
}
.textEllipsis {
  white-space: nowrap; /* Prevent text from wrapping to next line */
  overflow: hidden; /* Hide any overflowing content */
  text-overflow: ellipsis; /* Display ellipsis (...) when content overflows */
  max-width: 500px;
}
