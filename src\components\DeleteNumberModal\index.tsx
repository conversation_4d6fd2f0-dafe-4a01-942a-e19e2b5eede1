import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import Modal from "../Modal";
import { Delete } from "../svgs";
import styles from "./delete-number-modal.module.scss";
import {
  numbersModalFields,
  eSimModalFields,
} from "../utils/numberModalFields";
import AvailabilityPill from "../AvailabilityPill";
import { ApiDelete } from "../../pages/api/api";
import { formatNumber } from "../utils/formatNumber";

const CountryDisplay = ({ country }: any) => {
  return (
    <div className={styles.country}>
      <div
        className={styles.flag}
        style={{ backgroundImage: `url(${country.iconURL})` }}
      />
      {country.countryName}
    </div>
  );
};

const DeleteNumberModal = ({
  show,
  setShow,
  multiNumber,
  selection,
  number,
  remove,
  esim,
  repopulate,
}: any) => {
  const Multinumbers = multiNumber?.map((item: any) => {
    return item.number.replace(/\s+/g, "").replace("+", "");
  });

  const dispatch = useDispatch();

  const [loading, setLoading] = useState(false);

  const deleteNumber = () => {
    let didNumbers =
      Multinumbers.length > 0
        ? Multinumbers
        : [number.replace(/\s+/g, "").replace("+", "")];

    setLoading(true);
    ApiDelete("/customer/number", {
      didNumbers: didNumbers,
    })
      .then((res) => {
        setLoading(false);
        setShow(false);
        dispatch({
          type: "notify",
          payload: {
            error: false,
            message: `${
              multiNumber.length > 0 ? multiNumber.length : number
            } eSims Deleted.`,
          },
        });
        repopulate();
      })
      .catch((err) => {
        setLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: err?.response?.data?.message,
          },
        });
      });
  };
  const deleteEsims = () => {
    setLoading(true);
    ApiDelete("/agent/esim-number", {
      msisdn: esim.msisdn,
      mid: esim.mid,
    })
      .then((res) => {
        setLoading(false);
        setShow(false);
        dispatch({
          type: "notify",
          payload: {
            error: false,
            message: res.data.message,
          },
        });
        repopulate();
      })
      .catch((err) => {
        setLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: err?.response?.data?.message,
          },
        });
      });
  };

  useEffect(() => {
    console.log(number);
  }, [number]);

  return (
    <Modal
      saveButton={
        <>
          <Delete />
          Yes, Delete {selection === "e-sims" ? "eSIM" : "DID"} Number
        </>
      }
      cancelButton="No"
      image="/delete_user_graphic.svg"
      show={show}
      close={() => {
        setShow(false);
      }}
      proceed={selection === "e-sims" ? deleteEsims : deleteNumber}
      loading={loading}
    >
      <>
        {selection === "e-sims" && multiNumber.length === 0 && (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              height: "100%",
            }}
          >
            <h3>Delete eSIM Number {esim?.msisdn} ?</h3>
          </div>
        )}
        {selection === "did-numbers" && multiNumber.length === 0 && (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              height: "100%",
            }}
          >
            <h3>
              Delete DID Number{" "}
              {typeof number === "string" ? formatNumber(number) : ""}?
            </h3>
          </div>
        )}

        {selection === "did-numbers" && multiNumber.length > 0 && (
          <div className={styles.panel}>
            <h3 style={{ marginBottom: "32px" }}>
              Delete {multiNumber?.length} DID Number
              {multiNumber?.length > 1 && "s"}?
            </h3>
            <div className={`${styles.tableContainer} table-scroll`}>
              {multiNumber?.length > 0 && (
                <table>
                  <thead>
                    <tr>
                      {selection === "did-numbers" &&
                        numbersModalFields.map((field: any) => (
                          <th key={field.key}>{field.label}</th>
                        ))}
                      <th />
                    </tr>
                  </thead>
                  <tbody>
                    {multiNumber.map((item: any) => (
                      <tr key={selection === "did-numbers" && item?.number}>
                        <td>
                          {selection === "did-numbers" &&
                            formatNumber(item?.number)}
                        </td>
                        <td>
                          <CountryDisplay country={item.country} />
                        </td>
                        <td>
                          <AvailabilityPill status={item.availability} />
                        </td>
                        <td
                          className={styles.remove}
                          onClick={() => remove(item)}
                        >
                          remove
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              )}
            </div>
          </div>
        )}
        {selection === "e-sims" && multiNumber.length > 0 && (
          <div className={styles.panel}>
            <h3 style={{ marginBottom: "32px" }}>
              Delete {multiNumber?.length} eSIMs ?
            </h3>
            <div className={`${styles.tableContainer} table-scroll`}>
              {multiNumber?.length > 0 && (
                <table>
                  <thead>
                    <tr>
                      {selection === "e-sims" &&
                        eSimModalFields.map((field: any) => (
                          <th key={field.key}>{field.label}</th>
                        ))}

                      <th />
                    </tr>
                  </thead>
                  <tbody>
                    {multiNumber.map((item: any) => (
                      <tr key={selection === "e-sims" && item?.msisdn}>
                        <td>{selection === "e-sims" && item?.msisdn}</td>
                        <td>
                          <CountryDisplay country={item.country} />
                        </td>
                        <td>
                          <AvailabilityPill status={item.availability} />
                        </td>
                        <td
                          className={styles.remove}
                          onClick={() => remove(item)}
                        >
                          remove
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              )}
            </div>
          </div>
        )}
      </>
    </Modal>
  );
};

export default DeleteNumberModal;
