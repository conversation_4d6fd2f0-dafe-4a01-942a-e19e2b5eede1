import styles from "../../styles/login.module.scss";
import { Input } from "../../components/Input";
import Button from "../../components/Button";
import { Link } from "react-router-dom";
import { LogoFull } from "../../components/svgs";
import { validateAll } from "indicative/src/Validator";
import { useState } from "react";
import { useDispatch } from "react-redux";
import $ from "jquery";
import { ApiPost } from "../api/api";

const rules = {
  email: "required|email",
};

const messages = {
  "email.required":
    "Email address is required.",
  "email.email":
    "Please enter a valid email address. Example: <EMAIL>",
};

const ForgotPassword = () => {
  const dispatch = useDispatch();

  const [email, setEmail] = useState({
    value: "",
    error: "",
  });

  const [loading, setLoading] = useState(false);

  const handleChange = (e: any) => {
    setEmail({
      value: e.target.value,
      error: "",
    });
  };

  const sendEmail = () => {
    const data = {
      email: email.value.trim(),
    };

    validateAll(data, rules, messages)
      .then((response) => {
        setLoading(true);
        ApiPost("/agent/forgotpassword", {
          emailId: data.email,
        })
          .then((response: any) => {
            setLoading(false);
            dispatch({
              type: "notify",
              payload: {
                error: false,
                message: response.data.message,
              },
            });
            setEmail({
              value: "",
              error: "",
            });
          })
          .catch((error: any) => {
            setLoading(false);
            dispatch({
              type: "notify",
              payload: {
                error: true,
                message: error.response.data.message,
              },
            });
          });
      })
      .catch((error) => {
        setEmail({
          ...email,
          error: error[0].message,
        });
        $("#forgot-email").trigger("focus");
      });
  };

  return (
    <div className={styles.container}>
      <div className={styles.main}>
        <div className={styles.logos}>
          <Link to="/login">
            <LogoFull />
          </Link>
        </div>
        <div className={styles.formContainer}>
          <div className={styles.form}>
            <h2 style={{ marginBottom: 12 }}>Forgot Password</h2>
            <p style={{ margin: "0 0 32px 0" }}>
              Enter the email address you signed up with. We’ll send you an email with a link to get back into your account.
            </p>
            <div style={{ width: "100%" }}>
              <Input
                label="Email Address"
                placeholder="Enter your email"
                value={email.value}
                error={email.error}
                onChange={(e: any) => {
                  handleChange(e);
                }}
                onKeyDown={sendEmail}
                disabled={loading}
                clear={() => {
                  setEmail({ value: "", error: "" });
                }}
                id="forgot-email"
              />
            </div>
            <Button
              style={{ height: 50 }}
              color="primary"
              loading={loading}
              onClick={sendEmail}>
              Send me the recovery link
            </Button>
          </div>
        </div>
      </div>
      <img src="/login.jpg" className={styles.graphic} />
    </div>
  );
};

export default ForgotPassword;
