import { SwitchTransition, CSSTransition } from "react-transition-group";
import styles from "./type-switch.module.scss";

const TypeSwitch = ({ status, setStatus }: any) => {
  return (
    <div>
      <div className={styles.label}>Types</div>
      <div className={styles.container}>
        <div
          onClick={() => {
            setStatus("Call");
          }}
          className={styles.role}
        >
          Call
        </div>
        <div
          onClick={() => {
            setStatus("SMS");
          }}
          className={styles.role}
        >
          SMS
        </div>
        <div className={`${styles.thumb} ${status === "SMS" && styles.right}`}>
          <SwitchTransition>
            <CSSTransition
              key={status}
              addEndListener={(node, done) =>
                node.addEventListener("transitionend", done, false)
              }
              classNames="fade"
            >
              <div>{status === "Call" ? <>Call</> : <>SMS</>}</div>
            </CSSTransition>
          </SwitchTransition>
        </div>
      </div>
    </div>
  );
};

export default TypeSwitch;
