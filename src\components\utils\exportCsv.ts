import { statuses } from "../StatusPill";

// Encode quotes and commas to prevent column mix up
const encodeCommasAndQuotes = (string: string) => {
  return '"' + string.replaceAll('"', '""') + '"';
};

// Export all subs to csv file
export const exportCsv = (fields: any, data: any, dispatch: any) => {
  let csvContent = `data:text/csv;charset=utf-8,${fields.map(
    (fieldItem: any) => fieldItem.labelStr
  )}\n`;
  data.forEach((sub: any) => {
    fields.forEach((fieldItem: any) => {
      if (fieldItem.key === "subscriberNumberStatus") {
        csvContent += encodeCommasAndQuotes(statuses[sub[fieldItem.key]]) + ",";
      } else {
        csvContent +=
          encodeCommasAndQuotes(sub[fieldItem.key].toString()) + ",";
      }
    });
    csvContent += "\n";
  });
  const encodedUri = encodeURI(csvContent);
  const link = document.createElement("a");
  link.setAttribute("href", encodedUri);
  link.setAttribute("download", "tickets.csv");
  document.body.appendChild(link);

  link.click();

  dispatch({
    type: "notify",
    payload: {
      message: ".CSV file exported.",
      error: false,
    },
  });
};

// Export all subs to csv file
export const exportCsvPlainFields = (
  fields: any,
  data: any,
  dispatch: any,
  filename: string
) => {
  let csvContent = `data:text/csv;charset=utf-8,${fields}\n`;
  data.forEach((item: any) => {
    fields.forEach((fieldItem: any) => {
      item[fieldItem] = item[fieldItem] || "";
      csvContent += encodeCommasAndQuotes(item[fieldItem].toString()) + ",";
    });
    csvContent += "\n";
  });
  const encodedUri = encodeURI(csvContent);
  const link = document.createElement("a");
  link.setAttribute("href", encodedUri);
  link.setAttribute("download", filename + ".csv");
  document.body.appendChild(link);

  link.click();

  dispatch({
    type: "notify",
    payload: {
      message: ".CSV file exported.",
      error: false,
    },
  });
};
