import styles from "./modal.module.scss";
import Button from "../Button";
import { Fade } from "@mui/material";
import { Close } from "../svgs";

const Modal = ({
	show,
	close,
	proceed = () => {
		console.log("hello3");
	},
	loading,
	image = "",
	icon,
	iconColor,
	saveButton,
	cancelButton = "Cancel",
	cancelBtnFn,
	saveBtnColor = 'primary',
	closeBtnColor = 'secondary',
	customButton,
	customBtnFn,
	customBtnColor = 'secondary',
	children,
	onClose = () => {},
	scroll = true,
	fullSize,
	title = null,
	subtitle = null,
	clearContainer,
}: any) => {
	return (
		<Fade in={show} unmountOnExit>
			<div className={`${styles.container} ${clearContainer && styles.clear}`}>
				<div className={styles.modal}>
					{image.length ? (
						<div className={styles.imgContainer}>
							<img className={styles.illustration} src={image} />
						</div>
					) : (
						""
					)}
					<div className={styles.main + ' ' + (scroll && "modal-scroll")}>
						<div className={styles.closeBtn}>
							<div className={`${styles.close} ${fullSize && styles.fullSize}`}
								onClick={() => {
									onClose();
									close();
								}}>
								<Close />
							</div>
						</div>
						{
							icon && (
								<div className={`${styles.circledIcon} ${styles[iconColor]} ${styles.icon}`}>{icon}</div>
							)
						}
						{title !== null && (
							<h2 style={{ marginBottom: subtitle !== null ? 10 : 0 }} className={styles.title}>
								{title}
							</h2>
						)}
						{subtitle !== null && <div className={styles.subtitle}>{subtitle}</div>}
						{
							children && (
								<div className={`${styles.content} ${fullSize && styles.fullSize}`}>{children}</div>
							)
						}
						{
							(saveButton || customButton || cancelButton) && (
								<div className={styles.buttons}>
									{saveButton && (
										<Button style={{ minWidth: "initial", marginRight: cancelButton ? '10px' : '0' }} onClick={proceed} loading={loading} color={saveBtnColor}>
											{saveButton}
										</Button>
									)}
									{
									customButton && (
										<Button style={{ minWidth: "initial", marginRight: saveButton ? '15px' : '0' }} 
										onClick={ () => {
											customBtnFn ? customBtnFn() : proceed()
										}} loading={loading} color={customBtnColor}>
											{customButton}
										</Button>
									)
									}
									{cancelButton && (
										<div className={customButton && styles.customBtn}>
											<Button
												style={{ minWidth: "initial" }}
												disabled={loading}
												onClick={() => {
													cancelBtnFn ? cancelBtnFn() :
													onClose();
													close();
												}}
												color={closeBtnColor}>
												{cancelButton}
											</Button>
		
										</div>
									)}
								</div>
							)
						}
					</div>
				</div>
			</div>
		</Fade>
	);
};

export default Modal;
