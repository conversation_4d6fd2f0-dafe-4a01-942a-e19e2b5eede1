@use "../../styles/theme.scss" as *;

.container {
  position: fixed;
  top: 0px;
  left: 0px;
  z-index: 100;
  width: 100%;
  height: 100vh;
  background: rgba(12, 44, 100, 0.7);
  align-items: center;
  justify-content: center;
  display: flex;
}

.closeButton {
  width: 30px;
  height: 30px;
  border-radius: 100px;
  background-color: #e8f0fc;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.tabsContentContainer {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: start;
  min-height: 0;
  flex-grow: 1;
}

.modal {
  width: calc(100% - 32px);
  max-width: 1302px;
  background: #fff;
  border-radius: 24px;
  display: flex;
  height: 702px;
}

.grid_3 {
  width: 100%;
  padding-top: 16px;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-row-gap: 11px;
  grid-column-gap: 11px;
}

.leftColumn {
  width: 100%;
  max-width: 260px;
  border-right: 1px solid $lightgrey;
  text-align: center;
  padding-top: 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;

  .profilePicture {
    width: 80px;
    margin-bottom: 12px;
  }
  .name {
    font-size: 20px;
    font-weight: 700;
    line-height: 28px;
    margin-bottom: 6px;
  }
  .creditBalance {
    margin: 10px 16px 8px 16px;
    width: calc(100% - 32px);
    background: #f1f6fd99;
    padding: 12px 16px;
    text-align: start;
    border-radius: 16px;

    .text {
      color: #525a6b;
      margin-bottom: 18px;
      font-size: 14px;
      line-height: 18px;
    }

    .creditAmount {
      color: #061632;
      font-size: 20px;
      font-weight: 700;
      line-height: 28px;
    }
  }

  .tabsContainer {
    width: 100%;
    min-height: 0;
  }

  .manageTab {
    padding: 12px;
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .accountTab {
    overflow-y: auto;
    overflow-x: hidden;
    height: 100%;
    padding: 12px;
  }

  .accountDetails {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-top: 12px;
    text-align: left;
    text-overflow: break-word;

    .detailItem {
      display: flex;
      gap: 8px;

      .icon {
        flex: 0 0 35px;
        @include circledIcon;
        padding: 0;
        height: 35px;

        svg {
          width: 16px;
          height: 16px;
        }
      }

      .textContainer {
        display: flex;
        min-width: 0;
        flex-direction: column;
        gap: 12px;

        .text {
          font-size: 14px;
          word-wrap: break-word;
          display: flex;
          flex-direction: column;
          gap: 2px;
          line-height: 18px;

          .label {
            color: var(--gray-600);
          }
        }
      }
    }
  }
}
.mainSection {
  padding: 12px 16px;
  width: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: start;
  .close {
    @include circledIcon;
    position: absolute;
    top: 10px;
    right: 20px;
    cursor: pointer;
    svg {
      width: 20px;
    }
  }
}

.toggleContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 16px;
  border-radius: 54px;
  background: var(--primary-50);

  .title {
    font-size: 14px;
    font-weight: 700;
    color: var(--button-secondary-text);
  }

  .toggle {
    span {
      font-size: 12px;
    }

    display: flex;
    align-items: center;
    gap: 8px;
    .text {
      font-size: 14px;
      color: var(--gray-600);
    }
  }
}
