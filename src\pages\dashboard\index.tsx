import styles from "../../styles/dashboard.module.scss";
import DailyHighlight from "@/components/DailyHighlight";
import PageTitle from "@/components/Title";
import { faker } from "@faker-js/faker";
import moment from "moment";
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  PointElement,
  LineElement,
  Filler,
  ScriptableContext,
} from "chart.js";
import { Bar, Doughnut, Line } from "react-chartjs-2";
import {
  formatChartDate,
  formatChartDateLabel,
} from "@/components/utils/formatDate";
import { useState } from "react";

ChartJS.register(
  ArcElement,
  Tooltip,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  PointElement,
  LineElement,
  Filler
);

const constantOptions = {
  responsive: true,
  scales: {
    x: {
      ticks: {
        color: "#667085",
        font: {
          size: 12,
          family: "Roboto",
        },
        autoSkip: true,
        callback(val: any, index: any): string {
          return formatChartDateLabel(
            (this as any).getLabelForValue(val)
          ) as any;
        },
      },
      border: {
        display: false,
      },
      grid: {
        color: "rgba(181, 181, 181, 0)",
      },
    },
    y: {
      ticks: {
        color: "#667085",
        font: {
          size: 12,
          family: "Roboto",
        },
        autoSkip: true,
        beginAtZero: true,
        callback: (value: number) => {
          if (value % 1 === 0) {
            return value;
          }
        },
      },
      border: { dash: [3, 2], display: false },
      grid: {
        color: "#BFC4CE",
        drawTicks: false,
        display: true,
      },
      beginAtZero: true,
    },
  },
  plugins: {
    tooltip: {
      callbacks: {
        title: (tooltipItem: any, data: any) => {
          return formatChartDate(tooltipItem[0].label);
        },
      },
    },
  },
} as any;

const Dashboard = () => {
  const [timeRange, setTimeRange] = useState("week");

  const highlights = [
    {
      title: "Activations",
      number: 4569,
      percentChange: 46.7,
      red: false,
    },
    {
      title: "Total Active Subscribers",
      number: 621032,
      percentChange: -2.0,
      red: true,
    },
    {
      title: "Data Block",
      number: 7823,
      percentChange: -3.1,
      red: true,
    },
    {
      title: "Pending Ports",
      number: 36,
      percentChange: 34.2,
      red: true,
    },
    {
      title: "Pending Activations",
      number: 732,
      percentChange: 18.3,
      red: true,
    },
    {
      title: "Porting Errors",
      number: 21,
      percentChange: -12.4,
      red: false,
    },
    {
      title: "Provisioning Errors",
      number: 342,
      percentChange: -23.6,
      red: false,
    },
    {
      title: "Active Roaming Day-Pass Subs",
      number: 651,
      percentChange: 22.6,
      red: false,
    },
    {
      title: "Int. Calling Bolt-on Subs",
      number: 8745,
      percentChange: -6.3,
      red: true,
    },
    {
      title: "Cancelled Activations",
      number: 125,
      percentChange: 22.6,
      red: false,
    },
    {
      title: "Suspended Activations",
      number: 110,
      percentChange: -6.3,
      red: true,
    },
  ];

  const generateChartData = (range: string) => {
    return {
      labels: Array.from({ length: range === "week" ? 7 : 30 }).map((x, i) =>
        moment()
          .subtract(range === "week" ? 6 - i : 29 - i, "d")
          .format("YYYY-MM-DD")
      ),
      datasets: [
        {
          label: "",
          data: Array.from({ length: range === "week" ? 7 : 30 }).map((i) =>
            faker.number.int(800)
          ),
          fill: true,
          backgroundColor: (context: ScriptableContext<"line">) => {
            const ctx = context.chart.ctx;
            const gradient = ctx.createLinearGradient(0, 0, 0, 200);
            gradient.addColorStop(0, "rgba(87, 141, 234, 0.4)");
            gradient.addColorStop(1, "rgba(87, 141, 234, 0)");
            return gradient;
          },
          borderColor: "#2E70E5",
          borderWidth: 1.5,
          pointStyle: false as any,
        },
      ],
    };
  };

  const generateBarChart = (range: string) => {
    return {
      labels: Array.from({ length: range === "week" ? 7 : 30 }).map((x, i) =>
        moment()
          .subtract(range === "week" ? 6 - i : 29 - i, "d")
          .format("YYYY-MM-DD")
      ),
      datasets: [
        {
          label: "",
          data: Array.from({ length: range === "week" ? 7 : 30 }).map((i) =>
            faker.number.int(1000)
          ),
          fill: true,
          backgroundColor: "#2E70E5",
          borderRadius: 10000,
          barThickness: 16,
        },
        {
          label: "",
          data: Array.from({ length: range === "week" ? 7 : 30 }).map((i) =>
            faker.number.int(1000)
          ),
          fill: true,
          backgroundColor: "#E6AA2E",
          borderRadius: 10000,
          barThickness: 16,
        },
      ],
    };
  };

  const generateDoubleLine = (range: string) => {
    return {
      labels: Array.from({ length: range === "week" ? 7 : 30 }).map((x, i) =>
        moment()
          .subtract(range === "week" ? 6 - i : 29 - i, "d")
          .format("YYYY-MM-DD")
      ),
      datasets: [
        {
          label: "SIM Cards",
          data: Array.from({ length: range === "week" ? 7 : 30 }).map((i) =>
            faker.number.int(800)
          ),
          fill: true,
          backgroundColor: (context: ScriptableContext<"line">) => {
            const ctx = context.chart.ctx;
            const gradient = ctx.createLinearGradient(0, 0, 0, 200);
            gradient.addColorStop(0, "rgba(230, 170, 46, 0.4)");
            gradient.addColorStop(1, "rgba(230, 170, 46, 0)");
            return gradient;
          },
          borderColor: "#E6AA2E",
          borderWidth: 1.5,
          pointStyle: false as any,
        },
        {
          label: "eSIMs",
          data: Array.from({ length: range === "week" ? 7 : 30 }).map((i) =>
            faker.number.int(800)
          ),
          fill: true,
          backgroundColor: (context: ScriptableContext<"line">) => {
            const ctx = context.chart.ctx;
            const gradient = ctx.createLinearGradient(0, 0, 0, 200);
            gradient.addColorStop(0, "rgba(87, 141, 234, 0.4)");
            gradient.addColorStop(1, "rgba(87, 141, 234, 0)");
            return gradient;
          },
          borderColor: "#2E70E5",
          borderWidth: 1.5,
          pointStyle: false as any,
        },
      ],
    };
  };

  const ChartItem = ({ title }: any) => {
    return (
      <div className={styles.chartContainer}>
        <h4 className={styles.chartTitle}>{title}</h4>
        <Line
          options={{
            ...constantOptions,
            elements: {
              line: {
                tension: 0.4,
              },
            },
          }}
          data={generateChartData(timeRange)}
        />
      </div>
    );
  };

  const chartTitles = [
    "Activations",
    "Total Active Subscribers",
    "Data Block",
    "Pending Ports",
    "Pending Activations",
    "Porting Errors",
    "Provisioning Errors",
    "Active Roaming Day-Pass Subs",
    "Int. Calling Bolt-on Subs",
  ];

  return (
    <>
      <PageTitle>Dashboard</PageTitle>
      <div className={styles.main}>
        <div className={styles.daily}>
          <h3 className={styles.title}>Daily Highlights</h3>
          <div className={styles.itemsContainer}>
            {highlights.map((highlight: any) => (
              <DailyHighlight
                title={highlight.title}
                number={highlight.number}
                time="Yesterday"
                percentChange={highlight.percentChange}
                red={highlight.red}
              />
            ))}
          </div>
        </div>
        <div className={styles.insights}>
          <div className={styles.topBar}>
            <h3 className={styles.title}>Insights</h3>
            <div className={styles.timeFrameButtons}>
              <div
                className={`${styles.timeButton} ${timeRange === "week" ? styles.active : ""}`}
                onClick={() => {
                  setTimeRange("week");
                }}
                style={{ marginRight: 8 }}
              >
                Week
              </div>
              <div
                className={`${styles.timeButton} ${timeRange === "month" ? styles.active : ""}`}
                onClick={() => {
                  setTimeRange("month");
                }}
              >
                Month
              </div>
            </div>
          </div>
          <div className={styles.itemsContainer}>
            {chartTitles.map((title: string) => (
              <ChartItem title={title} />
            ))}
            <div className={styles.chartContainer}>
              <div className={styles.top}>
                <h4 className={styles.chartTitle}>eSIM vs SIM Cards</h4>
                <div className={styles.legend}>
                  <div
                    className={styles.legendItem}
                    style={{ marginBottom: 1 }}
                  >
                    <div
                      className={styles.dot}
                      style={{ backgroundColor: "#578DEA" }}
                    />
                    <div>eSIM: 123,456</div>
                  </div>
                  <div className={styles.legendItem}>
                    <div
                      className={styles.dot}
                      style={{ backgroundColor: "#E6AA2E" }}
                    />
                    <div>SIM Cards: 345,678</div>
                  </div>
                </div>
              </div>
              <Line
                options={{
                  ...constantOptions,
                  elements: {
                    line: {
                      tension: 0.4,
                    },
                  },
                }}
                data={generateDoubleLine(timeRange)}
              />
            </div>
            <div
              className={styles.chartContainer}
              style={{ gridColumn: "span 2" }}
            >
              <h4 className={styles.chartTitle}>Upgrades & Downgrades</h4>
              <Bar
                options={{ ...constantOptions, aspectRatio: 4.3 }}
                width="100%"
                data={generateBarChart(timeRange)}
              />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Dashboard;
