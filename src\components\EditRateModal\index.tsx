import Dialog from "@/components/Dialog";
import { PencilCircle, FloppyDisk } from "@/components/svgs";
import styles from "./edit-rate-modal.module.scss";
import CountryDisplay from "@/components/CountryDisplay";
import StatusPill from "@/components/StatusPill";
import { formatDateWithTime } from "@/components/utils/formatDate";
import { Input } from "@/components/Input";
import { useState } from "react";
import {
  clearInput,
  createStateObject,
  createStateObjectWithInitialState,
  handleInputChange,
  labels,
} from "@/components/utils/InputHandlers";

type EditRateModalProps = {
  open: boolean;
  onClose: () => void;
  data: any;
};

const EditRateModal = ({ open, onClose, data }: EditRateModalProps) => {
  const fields = ["buyRate", "sellRate"];

  const [formData, setFormData] = useState(
    createStateObjectWithInitialState(createStateObject(fields), {
      buyRate: data?.buyRate || "",
      sellRate: data?.sellRate || "",
    })
  );

  return (
    <Dialog
      open={open}
      onClose={onClose}
      size="sm"
      headerIcon={<PencilCircle />}
      headerTitle="Edit Rate"
      confirmButtonText="Save Changes"
      confirmButtonOnClick={onClose}
      confirmButtonIcon={<FloppyDisk />}
      cancelButtonText="Cancel"
    >
      <div className={styles.tableContainer}>
        <table className={styles.table}>
          <tbody>
            <tr>
              <td className={styles.label}>Country</td>
              <td className={styles.value}>
                <CountryDisplay country={data.country.countryCode} />
              </td>
            </tr>
            <tr>
              <td className={styles.label}>Provider</td>
              <td className={styles.value}>{data.name}</td>
            </tr>
            <tr>
              <td className={styles.label}>Type</td>
              <td className={styles.value}>{data.type}</td>
            </tr>
            <tr>
              <td className={styles.label}>Date Added</td>
              <td className={styles.value}>{formatDateWithTime(data.date)}</td>
            </tr>
            <tr>
              <td className={styles.label}>Status</td>
              <td className={styles.value}>
                <StatusPill
                  status={data.status === 1 ? "Active" : "Inactive"}
                  color={data.status === 1 ? "active" : "inactive"}
                />
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div className={styles.formContainer}>
        {fields.map((field) => (
          <Input
            key={field}
            label={labels[field]}
            value={formData[field]}
            onChange={(e: any) =>
              handleInputChange(field, e, formData, setFormData)
            }
            error={formData.errors[field]}
            clear={() => clearInput(field, setFormData)}
            infoTooltipText
          />
        ))}
      </div>
    </Dialog>
  );
};

export default EditRateModal;
