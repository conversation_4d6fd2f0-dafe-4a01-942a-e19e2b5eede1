import Dialog from "@/components/Dialog";
import { FloppyDisk, PencilCircle } from "@/components/svgs";
import { clearInput, createStateObject, createStateObjectWithInitialState, handleInputChange, labels } from "@/components/utils/InputHandlers";
import { useState } from "react";
import { Input } from "@/components/Input";


type EditSettingsUserModalProps = {
  userData: any;
  open: boolean;
  onClose: () => void;
}

const EditSettingsUserModal = ({
  open,
  onClose,
  userData
}: EditSettingsUserModalProps) => {
  const fields = ["firstName", "lastName", "email"];
  const [formData, setFormData] = useState(createStateObjectWithInitialState(createStateObject(fields), {
    ...userData,
  }))

  return (
    <Dialog
      open={open}
      onClose={onClose}
      size="sm"
      headerIcon={<PencilCircle />}
      headerTitle="Edit Details"
      confirmButtonText="Save Changes"
      confirmButtonOnClick={onClose}
      confirmButtonIcon={<FloppyDisk />}
      cancelButtonText="Cancel"
    >
      {fields.map(field => {
        return (
          <Input
            key={"user-" + field}
            label={labels[field]}
            value={formData[field]}
            onChange={(e: any) => {
              handleInputChange(field, e, formData, setFormData)
            }}
            error={formData.errors[field]}
            clear={() => {
              clearInput(field, setFormData)
            }}
            infoTooltipText
          />
        )
      })}
    </Dialog>
  )
}

export default EditSettingsUserModal