import { useEffect, useState } from "react";
import NumberSkeleton from "../NumberSkeleton";
import TableControl from "../TableControl";
import styles from "./portins-table.module.scss";
import { ApiGet } from "src/pages/api/api";
import { useDispatch } from "react-redux";
import { portedNumberFields } from "../utils/portedNumberFields";
import SearchSelect from "../SearchSelect";
import Search from "../Search";
import DatePicker from "../DatePicker";
import { padArrayToLength } from "../utils/padArray";
import AvailabilityPill from "../AvailabilityPill";
import { formatNumber } from "../utils/formatNumber";
import { countryListAlpha2 } from "../utils/countryList";
import { filterList } from "../utils/searchAndFilter";
import PortedNumberModal from "../PortedNumberModal";
import PortinStatus from "../PortinStatus";
import { getPortedNumbers } from "../utils/dummy-numbers";

const CountryDisplay = ({ country }: any) => {
  return (
    <div className={styles.country}>
      <div
        className={styles.flag}
        style={{
          backgroundImage: `url(https://public-gist-bucket.s3.eu-north-1.amazonaws.com/flag-icons/png/${countryListAlpha2.find((item: any) => item.name.toLowerCase() === country.toLowerCase())?.code}.png)`,
        }}
      />
      {country}
    </div>
  );
};

const emptyFilters: { [key: string]: any[] } = {
  status: [],
  country: [],
  number: [],
  availability: [],
  registeredTo: [],
  period: [],
  purchasedOn: [],
  type: [],
  city: [],
};

const PortinsTable = () => {
  const dispatch = useDispatch();

  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [filteredNumbers, setFilteredNumbers] = useState([] as any);
  const [initialLoading, setInitialLoading] = useState(false);
  const [filters, setFilters] = useState(emptyFilters);

  const [activeNumber, setActiveNumber] = useState(null as any);

  const [showModal, setShowModal] = useState(false);

  const [QueryDisplay, setQueryDisplay] = useState("");
  const [dateRange, setDateRange] = useState({
    start: null,
    end: null,
  } as any);

  const [numbers, setNumbers] = useState([] as any);

  const repopulateNumbers = () => {
    /*setInitialLoading(true);
    ApiGet("/agent/portins").then((response) => {
      setNumbers(response.data);
      setInitialLoading(false);
    });*/
    setNumbers(getPortedNumbers(itemsPerPage));
  };

  useEffect(repopulateNumbers, []);

  useEffect(() => {
    setFilteredNumbers(filterList(numbers, filters));
    setCurrentPage(1);
  }, [numbers, filters]);

  const formatDataItem = (item: any, key: string) => {
    if (key === "country") {
      return <CountryDisplay country={item.country} />;
    } else if (key === "msisdn") {
      return formatNumber(item.msisdn);
    } else if (key === "status") {
      return <PortinStatus status={item.status} />;
    } else {
      return item[key] || "-";
    }
  };

  const getAllOfType = (all: any, key: string) => {
    let allUsers = [] as any;
    all.forEach((log: any) => {
      if (key === "number") {
        console.log(log[key]);
      }
      if (key === "country") {
        if (!allUsers.some((item: any) => item.key === log[key])) {
          allUsers.push({
            key: log[key],
            label: <CountryDisplay country={log[key]} />,
          });
        }
      } else {
        if (!allUsers.some((item: any) => item.key === log[key])) {
          if (key === "status") {
            allUsers.push({
              key: log[key],
              label: <PortinStatus status={log[key]} />,
            });
          } else if (key === "availability") {
            allUsers.push({
              key: log[key],
              label: <AvailabilityPill status={log[key]} />,
            });
          } else {
            allUsers.push({ key: log[key], label: log[key] });
          }
        }
      }
    });
    console.log(allUsers);
    return allUsers;
  };

  return (
    <div className={styles.panel}>
      <PortedNumberModal
        show={showModal}
        setShow={setShowModal}
        activeNumber={activeNumber}
      />
      <div className={`${styles.tableContainer} table-scroll`}>
        <table>
          <thead>
            <tr>
              {portedNumberFields.map((field) => {
                if (
                  field.filter === "search-select" ||
                  field.filter === "select"
                ) {
                  return (
                    <th>
                      <SearchSelect
                        label={field.label}
                        selected={filters[field.key]}
                        options={getAllOfType(numbers, field.key)}
                        setSelected={(state: any) => {
                          setFilters({
                            ...filters,
                            [field.key]: state,
                          });
                        }}
                        grid={
                          field.key === "country" ||
                          field.key === "city" ||
                          field.key === "number"
                        }
                        twoColumnGrid={
                          field.key === "country" ||
                          field.key === "city" ||
                          field.key === "number"
                        }
                        search={field.filter === "search-select"}
                      />
                    </th>
                  );
                } else if (field.filter === "search") {
                  return (
                    <th>
                      <Search
                        label={field.label}
                        data={filteredNumbers}
                        setFilteredData={setFilteredNumbers}
                        setQueryDisplay={setQueryDisplay}
                        placeholder={`Search ${field.label}`}
                      />
                    </th>
                  );
                } else if (field.filter === "date") {
                  return (
                    <th>
                      <DatePicker
                        label={<div>{field.label}</div>}
                        field={field}
                        masterFrom={dateRange.start}
                        masterUntil={dateRange.end}
                        onChange={(newFrom: Date, newUntil: Date) => {
                          setDateRange({
                            start: newFrom,
                            end: newUntil,
                          });
                        }}
                        reports={false}
                        setFilteredData={setFilteredNumbers}
                        Data={numbers}
                      />
                    </th>
                  );
                } else {
                  return <th>{field.label}</th>;
                }
              })}

              <th />
            </tr>
          </thead>
          <tbody>
            {!initialLoading
              ? padArrayToLength(
                  filteredNumbers.slice(
                    (currentPage - 1) * itemsPerPage,
                    currentPage * itemsPerPage
                  ),
                  itemsPerPage,
                  null
                ).map((item: any) => {
                  if (item === null) {
                    return (
                      <tr
                        style={{
                          visibility: "hidden",
                          pointerEvents: "none",
                        }}
                      ></tr>
                    );
                  } else {
                    return (
                      <tr
                        style={{ cursor: "pointer" }}
                        onClick={() => {
                          setActiveNumber(item);
                          setShowModal(true);
                        }}
                      >
                        {portedNumberFields.map((key: any) => (
                          <td>
                            <div
                              style={{
                                display: "flex",
                                justifyContent: "flex-start",
                              }}
                            >
                              {formatDataItem(item, key.key)}
                            </div>
                          </td>
                        ))}
                      </tr>
                    );
                  }
                })
              : Array.from({ length: itemsPerPage }, (v, i) => i).map((i) => (
                  <NumberSkeleton
                    key={"number-skeleton-" + i}
                    noOfStandard={7}
                  />
                ))}
          </tbody>
        </table>
      </div>
      <div style={{ marginTop: "16px" }}>
        <TableControl
          show={true}
          itemsPerPage={itemsPerPage}
          setItemsPerPage={setItemsPerPage}
          currentPage={currentPage}
          setCurrentPage={setCurrentPage}
          numberOfPages={Math.ceil(filteredNumbers.length / itemsPerPage)}
          label="numbers"
        />
      </div>
    </div>
  );
};

export default PortinsTable;
