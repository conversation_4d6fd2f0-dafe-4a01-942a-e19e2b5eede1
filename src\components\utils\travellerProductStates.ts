// todo: remove complex types
type UnionToIntersection<U> = (U extends any ? (k: U) => void : never) extends (
  k: infer I
) => void
  ? I
  : never;

type MergeUnion<U> = {
  [K in keyof UnionToIntersection<U>]: K extends keyof U
    ? U[K]
    : UnionToIntersection<U>[K] | undefined;
};

export type TravellerProductStates = MergeUnion<
  (typeof travellerProductStates)[0]
>;

const commonFields = {
  country: "GB",
  mobileNumber: "0751234567890",
  totalGB: 5,
  usedGB: 2.4,
  totalSMS: 100,
  usedSMS: 92,
  totalMins: 100,
  usedMins: 23,
  createdOn: "12-05-2024",
  renewsOn: "24-04-2024",
  expiresOn: "12-04-2024",
};

export const travellerProductStates = [
  // Combo plan, active, auto-renew on
  {
    type: "combo",
    ...commonFields,
    active: true,
    autoRenew: true,
    renewsOn: "24-04-2024",
  },
  // Combo plan, active, auto-renew off
  {
    type: "combo",
    ...commonFields,
    active: true,
    autoRenew: false,
    expiresOn: "12-04-2024",
  },
  // Combo plan, data active, VN inactive, auto-renew on
  {
    type: "combo",
    ...commonFields,
    dataActive: true,
    vnInactive: true,
    autoRenew: true,
    renewsOn: "24-04-2024",
  },
  // Combo plan, data active, VN inactive, auto-renew off
  {
    type: "combo",
    ...commonFields,
    dataActive: true,
    vnInactive: true,
    autoRenew: false,
    expiresOn: "12-04-2024",
  },
  // Combo plan, expired
  {
    type: "combo",
    ...commonFields,
    expired: true,
    expiredOn: "10-12-2022",
  },
  // Combo plan, cancelled
  {
    type: "combo",
    ...commonFields,
    cancelled: true,
    cancelledOn: "10-12-2022",
  },

  // VN + mins + sms plan, active, auto-renew on
  {
    type: "vn-mins-sms",
    ...commonFields,
    active: true,
    autoRenew: true,
    renewsOn: "24-04-2024",
  },
  // VN + mins + sms plan, active, auto-renew off
  {
    type: "vn-mins-sms",
    ...commonFields,
    active: true,
    autoRenew: false,
    expiresOn: "12-04-2024",
  },
  // VN + mins + sms plan, inactive, auto-renew on
  {
    type: "vn-mins-sms",
    ...commonFields,
    active: false,
    autoRenew: true,
    renewsOn: "24-04-2024",
  },
  // VN + mins + sms plan, inactive, auto-renew off
  {
    type: "vn-mins-sms",
    ...commonFields,
    active: false,
    autoRenew: false,
    expiresOn: "12-04-2024",
  },
  // VN + mins + sms plan, expired
  {
    type: "vn-mins-sms",
    ...commonFields,
    expired: true,
    expiredOn: "10-12-2022",
  },
  // VN + mins + sms plan, cancelled
  {
    type: "vn-mins-sms",
    ...commonFields,
    cancelled: true,
    cancelledOn: "10-12-2022",
  },

  // Data plan, active, auto-renew on
  {
    type: "data",
    ...commonFields,
    active: true,
    autoRenew: true,
    renewsOn: "24-04-2024",
  },
  // Data plan, active, auto-renew off
  {
    type: "data",
    ...commonFields,
    active: true,
    autoRenew: false,
    expiresOn: "12-04-2024",
  },
  // Data plan, expired
  {
    type: "data",
    ...commonFields,
    expired: true,
    expiredOn: "10-12-2022",
  },
  // Data plan, cancelled
  {
    type: "data",
    ...commonFields,
    cancelled: true,
    cancelledOn: "10-12-2022",
  },

  // VN-only plan, active, auto-renew on
  {
    type: "vn-only",
    ...commonFields,
    active: true,
    autoRenew: true,
    renewsOn: "24-04-2024",
  },
  // VN-only plan, active, auto-renew off
  {
    type: "vn-only",
    ...commonFields,
    active: true,
    autoRenew: false,
    expiresOn: "12-04-2024",
  },
  // VN-only plan, inactive, auto-renew on
  {
    type: "vn-only",
    ...commonFields,
    active: false,
    autoRenew: true,
    renewsOn: "24-04-2024",
  },
  // VN-only plan, inactive, auto-renew off
  {
    type: "vn-only",
    ...commonFields,
    active: false,
    autoRenew: false,
    expiresOn: "12-04-2024",
  },
  // VN-only plan, expired
  {
    type: "vn-only",
    ...commonFields,
    expired: true,
    expiredOn: "10-12-2022",
  },
  // VN-only plan, cancelled
  {
    type: "vn-only",
    ...commonFields,
    cancelled: true,
    cancelledOn: "10-12-2022",
  },
];
