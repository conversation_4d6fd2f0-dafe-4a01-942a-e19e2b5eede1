import { faker } from "@faker-js/faker";
import moment from "moment";
import { formatReportsDate } from "./formatDate";

export const getRegistrations = (range: any) => {
  let data = {} as any;
  Array.from({ length: 100 }).forEach(() => {
    const date = formatReportsDate(
      faker.date.between({ from: range.start, to: range.end })
    );
    data[date] = {
      "9:00": faker.number.int(200),
      "12:00": faker.number.int(200),
      "15:00": faker.number.int(200),
      "18:00": faker.number.int(200),
    };
  });
  return data;
};

export const getTransactions = () => {
  return Array.from({ length: faker.number.int(200) }).map((x, i) => ({
    amount: faker.number.int(50),
    currencyCode: ["GBP", "USD", "EUR"][faker.number.int(2)],
  }));
};

export const convertRange = (range: string) => {
  switch (range) {
    case "last day":
      return 1;
    case "last 7 days":
      return 7;
    case "last month":
      return 30;
    case "last 3 months":
      return 90;
    case "last 6 months":
      return 180;
    case "last 12 months":
      return 365;
  }
};

export const sortByDate = (a: string, b: string) => {
  const aTime = new Date(a).getTime();
  const bTime = new Date(b).getTime();
  return aTime - bTime;
};

export const squashDataPoints = (data: any) => {
  const days = Object.keys(data);

  if (days.length > 30) {
    let weeks = {} as any;
    days.forEach((day: any) => {
      const weekStart = moment(day).startOf("week").format("YYYY-MM-DD");
      let daySum = 0;
      Object.keys(data[day]).forEach((time: string) => {
        daySum += data[day][time];
      });
      if (weekStart in weeks) {
        weeks[weekStart] += daySum;
      } else {
        weeks[weekStart] = daySum;
      }
    });

    if (Object.keys(weeks).length > 30) {
      let months = {} as any;
      days.forEach((day: any) => {
        const monthStart = moment(day).startOf("month").format("YYYY-MM-DD");
        let daySum = 0;
        Object.keys(data[day]).forEach((time: string) => {
          daySum += data[day][time];
        });
        if (monthStart in months) {
          months[monthStart] += daySum;
        } else {
          months[monthStart] = daySum;
        }
      });
      return months;
    } else {
      return weeks;
    }
  } else {
    if (days.length >= 3) {
      let dayTotals = {} as any;
      days.forEach((day: any) => {
        let daySum = 0;
        Object.keys(data[day]).forEach((time: string) => {
          daySum += data[day][time];
        });
        dayTotals[day] = daySum;
      });
      console.log(dayTotals);
      return dayTotals;
    } else {
      let timeTotals = {} as any;
      days.forEach((day: any) => {
        Object.keys(data[day]).forEach((time: string) => {
          timeTotals[`${day}T${time.padStart(5, "0")}`] = data[day][time];
        });
      });
      return timeTotals;
    }
  }
};
