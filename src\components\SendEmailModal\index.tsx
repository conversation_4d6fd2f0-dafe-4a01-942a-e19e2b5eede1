import { motion } from "framer-motion";
import styles from "./send-email-modal.module.scss";
import IconButton from "../IconButton";
import { FullScreen, Minimize, X } from "../svgs";
import Button from "../Button";
import { useState } from "react";

const SendEmailModal = ({ setShow }: any) => {
  const [expanded, setExpanded] = useState(false);

  return (
    <motion.div
      className={`${styles.container} ${expanded && styles.expanded}`}
      key="customer-modal"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      <div className={styles.top}>
        <div className={styles.title}>Create email</div>
        <div className={styles.actions}>
          <IconButton
            style={{ marginRight: 4 }}
            white
            onClick={() => {
              setExpanded((prev: boolean) => !prev);
            }}
          >
            {expanded ? <Minimize /> : <FullScreen />}
          </IconButton>
          <IconButton
            white
            onClick={() => {
              setShow(false);
            }}
          >
            <X />
          </IconButton>
        </div>
      </div>
      <div className={styles.mainEmail}>
        <div className={styles.row}>
          <div className={styles.circle} />
          Emma Lemon
        </div>
        <div className={styles.row}>
          To:
          <div className={styles.toEmail}>
            Sophie Apple (<EMAIL>)
          </div>
        </div>
        <div className={styles.row}>
          <input placeholder="Add a subject..." />
        </div>
        <div className={styles.body}>
          <textarea placeholder="Body" className="modal-scroll" />
        </div>
      </div>
      <div className={styles.bottom}>
        <Button>Send Email</Button>
      </div>
    </motion.div>
  );
};

export default SendEmailModal;
