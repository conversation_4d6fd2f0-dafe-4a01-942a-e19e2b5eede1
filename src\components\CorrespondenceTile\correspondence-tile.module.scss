@use "../../styles/theme.scss" as *;

.notes {
  background: $off-white;
  box-shadow: 0px 0px 30px 0px rgba(8, 7, 87, 0.1);
  border-radius: 24px;
  padding: 24px 0;
  .title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24px 24px 24px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
    margin-bottom: 24px;
    h4 {
      font-size: 20px;
      line-height: 30px;
    }
  }
  .notesContainer {
    padding: 0 24px;
  }
  .note {
    border-radius: 20px;
    padding: 24px;
    margin-bottom: 16px;
    max-width: 550px;
    &.from {
      background-color: $open;
      margin-right: auto;
    }
    &.to {
      background-color: #feebd4;
      margin-left: auto;
    }
    &:last-of-type {
      margin-bottom: 0px;
    }
    .top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 10px;
      font-size: 14px;
      line-height: 21px;
    }
    .subject {
      font-weight: 600;
    }
    .date {
      color: #474747;
    }
    .noteContent {
      font-size: 14px;
      line-height: 21px;
    }
  }
}
