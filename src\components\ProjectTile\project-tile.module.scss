@use "../../styles/theme.scss" as *;

a {
  text-decoration: none;
}
.main {
  width: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  border-radius: 16px;
  border: 1px solid var(--primary-100, #d6e3fa);
  cursor: pointer;
  position: relative;
  transition: background 0.2s ease;
  height: 240px;
  &.dark {
    background: #000;
    &:hover {
      background: #1d1d1d;
    }
  }
  .img {
    max-width: 100px;
    max-height: 80px;
    transition: all 0.3s ease;
  }
  .logoSection {
    background-color: #e8f0fc;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 130px;
  }
  .details {
    padding: 15px 25px;
    .company {
      display: flex;
      justify-content: space-between;
      border-bottom: 0.5px solid #d6e3fa;
      align-items: center;
      padding-bottom: 10px;
      h5 {
        font-weight: 700;
        font-size: 16px;
        color: #061632;
        margin: 0;
      }
      .mvne {
        background-color: #fff0cc;
        color: #664700;
        font-size: 12px;
        padding: 7px 11px;
        border-radius: 8px;
      }
      .mvno {
        background-color: #e8f0fc;
        color: #0c2c64;
        font-size: 12px;
        padding: 7px 11px;
        border-radius: 8px;
      }
    }
    .data {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      justify-content: space-between;
      align-items: flex-start;
      padding-top: 10px;
      .title {
        color: #838ca0;
        font-size: 12px;
        font-weight: 400;
        padding-bottom: 3px;
      }
      .value {
        color: #061632;
        font-size: 12px;
        font-weight: 400;
      }
    }
  }
}

.overlay {
  position: absolute;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: all 0.4s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  color: $off-white;
  background-color: $light-primary;
  font-size: 24px;
  font-weight: 500;
  z-index: 10;
  border-radius: 4px;
  &:hover {
    opacity: 1;
  }
}
