@use "../../styles/theme.scss" as *;

.container {
  display: grid;
  grid-template-rows: 1fr;
  align-items: center;
  color: #061632;
  font-size: 14px;
  position: relative;
  width: 100%;
  max-width: 313px;
  grid-column-gap: 12px;
  .numberDisplay {
    border: 1px solid #DFE2E7;
    width: 97%;
    height: 48px;
    line-height: 49px;
    cursor: text;
    text-align: center;
    border-radius: 15px;
  }
}

.input {
  border: none;
  font-size: 26px;
  width: 97%;
  height: 48px;
  line-height: 30px;
  text-align: center;
  background-color: #F1F6FD;
  outline: none;
  top: 1px;
  border-radius: 15px;
  &:focus {
    border: 2px solid #2E70E5;
  }
}
