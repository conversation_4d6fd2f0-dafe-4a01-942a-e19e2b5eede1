@use "./theme.scss" as *;

.container {
  height: 100vh;
  display: flex;
  justify-content: space-between;
  background: $off-white;

  @media screen and (max-width: 1024px) {
    flex-direction: column;
  }
}

.graphic {
  margin: 15px;
}

.version {
  font-size: 14px;
  color: #838ca0;
}

.logos {
  display: flex;
  align-items: center;
  margin-left: -17px;
}

.main {
  padding: 50px 32px;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  width: fit-content;
  margin-inline: auto;
  max-width: 392px;

  form,
  button {
    max-width: 320px;
  }
}

.formContainer {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
}

.forgotPassword {
  width: 100%;
  display: flex;
  justify-content: center;
}

.form {
  width: 100%;
  display: flex;
  flex-direction: column;
  h2 {
    font-size: 30px;
    font-weight: 700;
    line-height: 30px;
    margin-bottom: 10px;
    color: #061632;

    @media screen and (max-width: 1024px) {
      padding-top: 8px;
    }
  }
  > p {
    font-size: 14px;
    color: #667085;
    margin-bottom: 25px;
  }
}
