import { useEffect, useState } from "react";
import Menu from "../Menu";
import SearchBar from "../SearchBar";
import { LogOut, User } from "../svgs";
import styles from "./global-search-section.module.scss";
import { logOut } from "../utils/logOut";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import FullPageLoading from "../FullPageLoading";
import { ApiGet, ApiPostAuth } from "../../pages/api/api";
import { isNumeric, validateEmail } from "../utils/CardDetailsCheckers";

const GlobalSearchSection = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { userInfo, ticketOpen } = useSelector((state: any) => state);

  const [searchParams, setSearchParams] = useSearchParams();

  const [loading, setLoading] = useState(false);

  const [searchQuery, setSearchQuery] = useState("");

  useEffect(() => {
    const search = searchParams.get("search");
    if (search) {
      setSearchQuery(search);
    } else {
      setSearchQuery("");
    }
  }, [searchParams]);

  const submitSearch = () => {
    navigate("/customer-management?search=" + searchQuery);
  };

  return (
    <div className={`${styles.topBar} ${ticketOpen && styles.ticketOpen}`}>
      <FullPageLoading loading={loading} />
      <SearchBar
        query={searchQuery}
        setQuery={setSearchQuery}
        onSubmit={submitSearch}
        placeholder="Search for a customer via email or MSISDN"
        id="global-search"
      />
      <div style={{ width: 50 }} />
      <Menu
        data={{
          label: userInfo ? userInfo.firstName : "",
          items: [
            {
              label: "Profile",
              icon: <User />,
              link: "/user-profile",
            },
            {
              label: "Logout",
              icon: <LogOut />,
              link: "/login",
              onClick: () => {
                logOut(dispatch, navigate);
              },
            },
          ],
        }}
      />
    </div>
  );
};

export default GlobalSearchSection;
