@use "./../../styles/theme.scss" as *;
@import "./../../styles/mixins.module.scss";
@import "./../../styles/table-mixin.module.scss";

.panel {
  @include panel;
  @include table;
  height: 100%;
  padding-bottom: 22px;
}


.reportFiltersHeader {
  h2 {
    margin-top: 6px;
    font-size: 20px;
    font-weight: 700;
  }

  p {
    margin-top: 8px;
    font-size: 14px;
    color: var(--gray-500);
  }
}

.reportFiltersForm {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 24px;
}

.reportFiltersFormRow {
  h5 {
    font-size: 14px;
    font-weight: 400;
    color: var(--text-primary);
    margin-bottom: 4px;

  }
}

.reportFiltersButtonWrapper {
  margin-top: 8px;
}



.tableContainer {
  overflow: auto;
  padding-bottom: 5px;
  margin-top: 16px;
  border-radius: 16px;
}

.panelTopBar {
  display: flex;
  justify-content: space-between;
  align-items: center;

  h4 {
    margin: 0;
    font-weight: 600;
    font-size: 20px;
    line-height: 30px;
    color: var(--gray-900);
  }

  .actions {
    display: flex;
    gap: 8px;
  }
} 