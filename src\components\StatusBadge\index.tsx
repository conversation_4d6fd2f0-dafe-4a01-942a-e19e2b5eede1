import styles from "./status-badge.module.scss";

export const convertStatus = (status: number) => {
  switch (status) {
    case 2:
      return "Open";
    case 3:
      return "Pending";
    case 4:
      return "Resolved";
    case 5:
      return "Closed";
    default:
      return "";
  }
};

const StatusBadge = ({ status }: any) => {
  return (
    <div
      className={`${styles.main} ${
        status === "Open"
          ? styles.open
          : status === "Pending"
            ? styles.pending
            : status === "Resolved"
              ? styles.resolved
              : styles.closed
      }`}
    >
      {status}
    </div>
  );
};

export default StatusBadge;
