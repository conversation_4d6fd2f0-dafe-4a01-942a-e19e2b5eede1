import styles from './esim-status-pill.module.scss'

export const eSimStatuses = [
  'Available',
  'Registered',
  'Installed',
  'Enabled',
  'Disabled',
  'Deleted',
]

const ESimStatusPill = ({ status, hover }: any) => {
  return (
    <div
      className={`${styles.main} ${hover && styles.hover} ${
        styles[`status-${eSimStatuses.indexOf(status)}`]
      }`}
    >
      {status !== '' ? status : 'No Status'}
    </div>
  )
}

export default ESimStatusPill
