@use "../../styles/theme.scss" as *;

.menuButton {
  display: flex;
  align-items: center;
  border: none;
  font-size: 14px;
  font-weight: 600;
  line-height: 21px;
  gap: 4px;
  cursor: pointer;
  transition: color 0.2s ease, background-color 0.2s ease;
  -webkit-user-select: none; /* Safari */
  -ms-user-select: none; /* IE 10 and IE 11 */
  user-select: none;
  &:hover {
    color: $orange;
    &.background {
      color: $black;
      background: $light-orange;
    }
  }
  &.background {
    height: 32px;
    background-color: #f7f6f6;
    padding: 5.5px 6px 5.5px 12px;
    border-radius: 6px;
    font-weight: 500;
    margin-right: 12px;
  }
  svg {
    display: inline;
    vertical-align: middle;
    transition: transform 0.2s ease;
    margin-left: 5px;
  }
  &.iconOpen {
    svg {
      transform: rotate(180deg);
    }
  }
}

.menu {
  width: 338px;
  background: $orange;
  box-shadow: 0px 10px 20px rgba(62, 29, 107, 0.2);
  border-radius: 8px;
  padding: 24px 26px;
  display: flex;
  flex-direction: column;
  &.yearMenu {
    background: $black;
  }
}

.months {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-column-gap: 27px;
  grid-row-gap: 27px;
  margin-top: 35px;
  padding: 0 7px;
  .month {
    width: 44px;
    height: 44px;
    border-radius: 1000px;
    display: flex;
    align-items: center;
    color: #fff;
    font-size: 12px;
    line-height: 24px;
    justify-content: center;
    cursor: pointer;
    &.active {
      background: #fff;
      color: $black;
      cursor: auto;
      &:hover {
        background: #fff;
      }
    }
    &:hover {
      background: rgba(255, 255, 255, 0.2);
    }
  }
}

.selectYear {
  color: #fff;
  display: flex;
  align-items: center;
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  svg {
    width: 24px;
    height: 24px;
    margin-left: 4px;
  }
}
