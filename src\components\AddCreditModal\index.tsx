import Dialog from "@/components/Dialog";
import { HandCoinsCircle } from "@/components/svgs";
import { clearInput, createStateObject, handleInputChange, labels } from "@/components/utils/InputHandlers";
import { useState } from "react";
import styles from "./add-credit-modal.module.scss";
import { Input } from "@/components/Input";

type AddCreditModalProps = {
  open: boolean;
  onClose: () => void;
}

const AddCreditModal = ({
  open,
  onClose
}: AddCreditModalProps) => {
  const fields = ["amount", "reason"];
  const [formData, setFormData] = useState(createStateObject(fields));

  return (
    <Dialog
      open={open}
      onClose={onClose}
      size="sm"
      headerIcon={<HandCoinsCircle />}
      headerTitle="Add Credit"
      headerSubtitle="Top up [<EMAIL>]’s account with credit. The added amount will be available for use immediately."
      confirmButtonText="Add Credit"
      confirmButtonOnClick={onClose}
      cancelButtonText="Cancel"
    >
      <div className={styles.formContainer}>
        {fields.map(field => (
          <Input
            key={"credit-" + field}
            label={labels[field]}
            value={formData[field]}
            onChange={(e: any) => {
              handleInputChange(field, e, formData, setFormData);
            }}
            error={formData.errors[field]}
            clear={() => {
              clearInput(field, setFormData);
            }}
            infoTooltipText
          />
        ))}
      </div>
    </Dialog>
  );
};

export default AddCreditModal;
