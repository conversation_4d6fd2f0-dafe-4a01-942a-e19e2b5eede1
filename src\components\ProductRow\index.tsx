import RadioSelect from "../RadioSelect";
import StatusPill from "../StatusPill";
import Tooltip from "../Tooltip";
import { Delete, Pencil } from "../svgs";
import { formatDateWords } from "../utils/formatDate";
import styles from "./product-row.module.scss";

const Toolbar = ({
  singleProduct,
  handleDeleteProduct,
  handleEditProduct,
}: any) => {
  return (
    <td>
      <div className={styles.actionPanel}>
        <Tooltip show text="Edit Product" style={{ marginRight: 12 }}>
          <button
            className={styles.actionButton}
            onClick={(e) => {
              e.stopPropagation();
              handleEditProduct(singleProduct);
            }}
          >
            <Pencil />
          </button>
        </Tooltip>
        <Tooltip show text="Delete Product">
          <button
            className={styles.actionButton}
            onClick={(e) => {
              e.stopPropagation();
              handleDeleteProduct(singleProduct);
            }}
          >
            <Delete />
          </button>
        </Tooltip>
      </div>
    </td>
  );
};

export const CreditRow = ({
  plan,
  handleDeleteProduct,
  handleEditProduct,
  handleChangeStatus,
}: any) => {
  return (
    <tr>
      <td>{plan.credit}</td>
      <td>
        $
        {plan.prices
          .find((price: any) => price.currencyCode === "USD")
          .cost.toFixed(2)}
      </td>
      <td>
        £
        {plan.prices
          .find((price: any) => price.currencyCode === "GBP")
          .cost.toFixed(2)}
      </td>
      <td>
        €
        {plan.prices
          .find((price: any) => price.currencyCode === "EUR")
          .cost.toFixed(2)}
      </td>
      <td>{formatDateWords(plan.dateAdded)}</td>
      <td>
        <div
          style={{
            display: "flex",
            justifyContent: "flex-start",
          }}
        >
          <RadioSelect
            label={<StatusPill status={plan.status} />}
            options={[
              {
                label: <StatusPill status={true} />,
                key: true,
              },
              {
                label: <StatusPill status={false} />,
                key: false,
              },
            ]}
            selected={plan.status}
            onChange={(e: any) => {
              handleChangeStatus(plan, e);
            }}
          />
        </div>
      </td>
      <Toolbar
        singleProduct={plan}
        handleDeleteProduct={handleDeleteProduct}
        handleEditProduct={handleEditProduct}
      />
    </tr>
  );
};

export const ComboRow = ({
  plan,
  handleDeleteProduct,
  handleEditProduct,
  handleChangeStatus,
}: any) => {
  return (
    <tr>
      <td>{plan.planName}</td>
      <td>
        {plan.validity} day{plan.validity > 1 ? "s" : ""}
      </td>
      <td>{plan.dataAllowance} GB</td>
      <td>{plan.smsAllowance}</td>
      <td>{plan.voiceAllowance}</td>
      <td>
        $
        {plan.prices
          .find((price: any) => price.currencyCode === "USD")
          .cost.toFixed(2)}
      </td>
      <td>
        £
        {plan.prices
          .find((price: any) => price.currencyCode === "GBP")
          .cost.toFixed(2)}
      </td>
      <td>
        €
        {plan.prices
          .find((price: any) => price.currencyCode === "EUR")
          .cost.toFixed(2)}
      </td>
      <td>{formatDateWords(plan.dateAdded)}</td>
      <td>
        <div
          style={{
            display: "flex",
            justifyContent: "flex-start",
          }}
        >
          <RadioSelect
            label={<StatusPill status={plan.status} />}
            options={[
              {
                label: <StatusPill status={true} />,
                key: true,
              },
              {
                label: <StatusPill status={false} />,
                key: false,
              },
            ]}
            selected={plan.status}
            onChange={(e: any) => {
              handleChangeStatus(plan, e);
            }}
          />
        </div>
      </td>
      <Toolbar
        singleProduct={plan}
        handleDeleteProduct={handleDeleteProduct}
        handleEditProduct={handleEditProduct}
      />
    </tr>
  );
};

export const DataRow = ({
  plan,
  handleDeleteProduct,
  handleEditProduct,
  handleChangeStatus,
}: any) => {
  return (
    <tr>
      <td>{plan.planName}</td>
      <td>
        {plan.validity} day{plan.validity > 1 ? "s" : ""}
      </td>
      <td>{plan.dataAllowance} GB</td>
      <td>
        $
        {plan.prices
          .find((price: any) => price.currencyCode === "USD")
          .cost.toFixed(2)}
      </td>
      <td>
        £
        {plan.prices
          .find((price: any) => price.currencyCode === "GBP")
          .cost.toFixed(2)}
      </td>
      <td>
        €
        {plan.prices
          .find((price: any) => price.currencyCode === "EUR")
          .cost.toFixed(2)}
      </td>
      <td>{formatDateWords(plan.dateAdded)}</td>
      <td>
        <div
          style={{
            display: "flex",
            justifyContent: "flex-start",
          }}
        >
          <RadioSelect
            label={<StatusPill status={plan.status} />}
            options={[
              {
                label: <StatusPill status={true} />,
                key: true,
              },
              {
                label: <StatusPill status={false} />,
                key: false,
              },
            ]}
            selected={plan.status}
            onChange={(e: any) => {
              handleChangeStatus(plan, e);
            }}
          />
        </div>
      </td>
      <Toolbar
        singleProduct={plan}
        handleDeleteProduct={handleDeleteProduct}
        handleEditProduct={handleEditProduct}
      />
    </tr>
  );
};

export const CallRow = ({
  plan,
  handleDeleteProduct,
  handleEditProduct,
  handleChangeStatus,
}: any) => {
  return (
    <tr>
      <td>{plan.planName}</td>
      <td>
        {plan.validity} day{plan.validity > 1 ? "s" : ""}
      </td>
      <td>{plan.smsAllowance}</td>
      <td>{plan.voiceAllowance}</td>
      <td>
        $
        {plan.prices
          .find((price: any) => price.currencyCode === "USD")
          .cost.toFixed(2)}
      </td>
      <td>
        £
        {plan.prices
          .find((price: any) => price.currencyCode === "GBP")
          .cost.toFixed(2)}
      </td>
      <td>
        €
        {plan.prices
          .find((price: any) => price.currencyCode === "EUR")
          .cost.toFixed(2)}
      </td>
      <td>{formatDateWords(plan.dateAdded)}</td>
      <td>
        <div
          style={{
            display: "flex",
            justifyContent: "flex-start",
          }}
        >
          <RadioSelect
            label={<StatusPill status={plan.status} />}
            options={[
              {
                label: <StatusPill status={true} />,
                key: true,
              },
              {
                label: <StatusPill status={false} />,
                key: false,
              },
            ]}
            selected={plan.status}
            onChange={(e: any) => {
              handleChangeStatus(plan, e);
            }}
          />
        </div>
      </td>
      <Toolbar
        singleProduct={plan}
        handleDeleteProduct={handleDeleteProduct}
        handleEditProduct={handleEditProduct}
      />
    </tr>
  );
};

export const SmsRow = ({
  plan,
  handleDeleteProduct,
  handleEditProduct,
  handleChangeStatus,
}: any) => {
  return (
    <tr>
      <td>{plan.planName}</td>
      <td>
        {plan.validity} day{plan.validity > 1 ? "s" : ""}
      </td>
      <td>{plan.smsAllowance}</td>
      <td>
        $
        {plan.prices
          .find((price: any) => price.currencyCode === "USD")
          .cost.toFixed(2)}
      </td>
      <td>
        £
        {plan.prices
          .find((price: any) => price.currencyCode === "GBP")
          .cost.toFixed(2)}
      </td>
      <td>
        €
        {plan.prices
          .find((price: any) => price.currencyCode === "EUR")
          .cost.toFixed(2)}
      </td>
      <td>{formatDateWords(plan.dateAdded)}</td>
      <td>
        <div
          style={{
            display: "flex",
            justifyContent: "flex-start",
          }}
        >
          <RadioSelect
            label={<StatusPill status={plan.status} />}
            options={[
              {
                label: <StatusPill status={true} />,
                key: true,
              },
              {
                label: <StatusPill status={false} />,
                key: false,
              },
            ]}
            selected={plan.status}
            onChange={(e: any) => {
              handleChangeStatus(plan, e);
            }}
          />
        </div>
      </td>
      <Toolbar
        singleProduct={plan}
        handleDeleteProduct={handleDeleteProduct}
        handleEditProduct={handleEditProduct}
      />
    </tr>
  );
};
