@use "../../styles/theme.scss" as *;

.main {
  width: 100%;
  border-radius: 16px;
  border: 1px solid $lightgrey;
  background: #fff;
  padding: 12px;
  &.portInSection {
    .title {
      font-size: 14px;
      margin-top: 8px;
      font-weight: 600;
    }
    .portInDetails {
      > div {
        margin: 15px 0;
        display: flex;
        align-items: center;
        gap: 10px;
        .title {
          @include greyTitle
        }
        .text {
          @include primaryText
        }
      }
      svg {
        @include circledIcon
      }
    }
  }
  &.fullSize {
    position: absolute;
    top: 0px;
    right: 0px;
    left: 0px;
    bottom: 0px;
    z-index: 999;
    .type {
      font-size: 14px;
      line-height: 18px;
    }
    .subject {
      font-size: 16px;
      line-height: 22px;
      margin-bottom: 16px;
    }
    .description {
      color: #061632;
      font-size: 14px;
      line-height: 18px;
      margin-bottom: 16px;
      overflow: initial;
      * {
        white-space: initial;
        overflow: initial;
        text-overflow: initial;
      }
    }
  }
  &.disabled {
    pointer-events: none;
    cursor: auto;
  }
}

.ticketContent {
  margin-right: 326px;
}

.mainContent {
  width: 100%;
  padding-top: 12px;
  position: relative;
}

.sideSection {
  width: 294px;
  position: absolute;
  top: 12px;
  right: 0px;
  border-radius: 16px;
  border: 1px solid $lightgrey;
  .title {
    padding: 16px 16px 13px 16px;
    border-bottom: 1px solid $lightgrey;
  }
  .ticketFields {
    padding: 16px;
    display: grid;
    grid-template-columns: auto auto;
    grid-column-gap: 15px;
    grid-row-gap: 16px;
    justify-content: start;
    justify-items: start;
    align-items: center;
    .fieldName {
      color: #061632;
      font-size: 14px;
      line-height: 18px;
    }
    .blueInfo {
      background: $lightblue;
      border-radius: 8px;
      padding: 6px 12px;
      color: #0c2c64;
      font-size: 12px;
      line-height: 15px;
    }
    .creationDate {
      color: #124191;
      font-size: 14px;
      line-height: 18px;
    }
  }
}

.summary {
  display: flex;
  align-items: baseline;
}

.info {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 12px 0px;
}
.dueDate {
  color: #838ca0;
  font-size: 12px;
  line-height: 15px;
}

.typeContainer {
  display: flex;
  align-items: center;
  font-size: 12px;
  line-height: 15px;
}
.type {
  color: #061632;
  margin-bottom: 2px;
}
.date {
  color: #838ca0;
}
.typeIcon {
  width: 30px;
  height: 30px;
  background: #f1f6fd;
  border-radius: 30px;
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  svg {
    width: 16px;
    height: 16px;
    color: #2e70e5;
  }
}

.subject {
  color: #061632;
  font-size: 14px;
  font-weight: 700;
  line-height: 18px;
  margin-bottom: 18px;
}

.description {
  color: #667085;
  font-size: 12px;
  line-height: 15px;
  overflow: hidden;
  margin-bottom: 15px;
  * {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.overview {
  .top {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .bottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 12px;
  }
}

.buttons {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex-grow: 1;
}

.subtitle {
  color: #838ca0;
  font-size: 14px;
  line-height: 18px;
  margin-bottom: 6px;
}

.attachmentsContainer {
  margin-bottom: 16px;
  position: relative;
}

.arrow {
  width: 38px !important;
  height: 38px !important;
  background: #fff !important;
  box-shadow: 0px 0px 10px 0px #00000026 !important;
  border-radius: 100px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  position: absolute !important;
  z-index: 9999 !important;
  transition: opacity 0.1s ease !important;
  &:global(.slick-disabled) {
    opacity: 0 !important;
    cursor: auto !important;
  }
  &::before {
    display: none !important;
  }
  &.next {
    right: 7px !important;
  }
  &.previous {
    left: 7px !important;
    svg {
      transform: rotate(180deg);
    }
  }
  svg {
    color: #2e70e5 !important;
    width: 24px !important;
    height: 24px !important;
  }
}
