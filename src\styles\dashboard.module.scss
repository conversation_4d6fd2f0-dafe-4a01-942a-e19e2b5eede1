@use "../styles/theme.scss" as *;

.main {
  width: 100%;
  padding: 24px;
}

.top {
  display: flex;
  width: 100%;
  justify-content: flex-end;
}

.daily {
  background-color: #fff;
  border-radius: 16px;
  padding: 24px 16px;
  margin-bottom: 12px;

  .title {
    line-height: 22px;
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 13px;
  }

  .itemsContainer {
    display: flex;
    flex-wrap: wrap;
  }
}

.insights {
  background-color: #fff;
  border-radius: 16px;
  padding: 24px 16px;

  .topBar {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    .timeFrameButtons {
      display: flex;
      align-items: center;

      .timeButton {
        display: flex;
        align-items: center;
        height: 40px;
        background-color: #e8f0fc;
        border-radius: 8px;
        color: #1857c3;
        font-size: 14px;
        font-weight: 700;
        line-height: 18px;
        padding: 0px 16px;
        border: 2px solid #e8f0fc;
        cursor: pointer;

        &:hover {
          border-color: #a7b1c0;
        }

        &.active {
          border-color: #1857c3;
          cursor: auto;
        }
      }
    }
  }

  .title {
    line-height: 22px;
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 13px;
  }

  .itemsContainer {
    display: flex;
    flex-direction: column;
    gap: 16px;

    @media (min-width: 1200px) {
      display: grid;
      grid-template-columns: 1fr 1fr;
    }

    @media (min-width: 1540px) {
      grid-template-columns: 1fr 1fr 1fr;
    }
  }
}

.chartContainer {
  border: 1.5px solid #dfe2e7;
  border-radius: 16px;
  padding: 24px;

  .top {
    width: 100%;
    display: flex;
    justify-content: space-between;

    .legend {
      .legendItem {
        .dot {
          width: 6px;
          height: 6px;
          border-radius: 12px;
          margin-right: 6px;
        }

        display: flex;
        align-items: center;
        font-size: 12px;
        font-weight: 400;
        line-height: 15px;
        color: #667085;
      }
    }
  }

  .chartTitle {
    font-size: 16px;
    font-weight: 400;
    line-height: 22px;
    margin-bottom: 12px;
  }
}
