import Dialog from "@/components/Dialog";
import { WarningCircle } from "@/components/svgs";

type DeleteVoiceMailModalProps = {
  onClose: () => void;
  open: boolean;
  phoneNumber: string;
}

const DeleteVoiceMailModal = ({
  onClose,
  open,
  phoneNumber,
}: DeleteVoiceMailModalProps) => {

  return (
    <Dialog
      open={open}
      onClose={onClose}
      size="sm"
      headerIcon={<WarningCircle />}
      headerTitle="Delete Voicemail?"
      headerSubtitle={
        <>
          Number: {phoneNumber}
          <br />
          <br />
          This action cannot be undone.
        </>
      }
      confirmButtonText="Yes, Delete"
      confirmButtonOnClick={onClose}
      confirmButtonVariant="destructive"
      cancelButtonText="Cancel"
    />
  );
};

export default DeleteVoiceMailModal;
