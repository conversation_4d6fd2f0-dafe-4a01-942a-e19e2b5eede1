import Dialog from "@/components/Dialog";
import { WarningCircle } from "@/components/svgs";
import styles from "./delete-did-number-modal.module.scss";

type DeleteDIDNumberModalProps = {
  onClose: () => void;
  open: boolean;
  phoneNumber: string;
};

const DeleteDIDNumberModal = ({
  onClose,
  open,
  phoneNumber,
}: DeleteDIDNumberModalProps) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      size="sm"
      headerIcon={<WarningCircle />}
      headerTitle="Delete DID Number?"
      headerSubtitle={
        <>
          Number: {phoneNumber}
          <br />
          Once deleted, it cannot be recovered.
        </>
      }
      confirmButtonText="Yes, Delete DID Number"
      confirmButtonOnClick={onClose}
      confirmButtonVariant="destructive"
      cancelButtonText="Cancel"
    />
  );
};

export default DeleteDIDNumberModal;
