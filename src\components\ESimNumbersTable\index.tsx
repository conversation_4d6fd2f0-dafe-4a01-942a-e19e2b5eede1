import { useEffect, useState } from "react";
import Button from "../Button";
import NumberSkeleton from "../NumberSkeleton";
import TableControl from "../TableControl";
import styles from "./esim-numbers-table.module.scss";
import { ApiGet, ApiPatch, ApiPut } from "src/pages/api/api";
import { useDispatch } from "react-redux";
import Checkbox from "../Checkbox";
import SearchSelect from "../SearchSelect";
import Search from "../Search";
import DatePicker from "../DatePicker";
import { padArrayToLength } from "../utils/padArray";
import { Delete } from "../svgs";
import StatusPill from "../StatusPill";
import AvailabilityPill from "../AvailabilityPill";
import RadioSelect from "../RadioSelect";
import { formatDateWords } from "../utils/formatDate";
import { formatNumber } from "../utils/formatNumber";
import { countryListAlpha2 } from "../utils/countryList";
import { filterList } from "../utils/searchAndFilter";
import DeleteNumberModal from "../DeleteNumberModal";
import DeactivateNumberModal from "../DeactivateNumberModal";
import ActiveNumberModal from "../ActiveNumberModal";
import { NumbereSimsFields } from "../utils/numberEsimFields";
import ESimStatusPill from "../ESimStatusPill";
import RemoveFiltersBar from "../RemoveFiltersBar";
import { getEsimNumbers } from "../utils/dummy-numbers";

const CountryDisplay = ({ country }: any) => {
  return country ? (
    <div className={styles.country}>
      <div
        className={styles.flag}
        style={{
          backgroundImage: `url(https://public-gist-bucket.s3.eu-north-1.amazonaws.com/flag-icons/png/${countryListAlpha2.find((item: any) => item.name.toLowerCase() === country.toLowerCase())?.code}.png)`,
        }}
      />
      {country}
    </div>
  ) : (
    <>-</>
  );
};

const emptyFilters: { [key: string]: any } = {
  status: [],
  country: [],
  number: [],
  availability: [],
  registeredTo: [],
  period: [],
  purchasedOn: {
    start: null,
    end: null,
    startTime: {
      hh: "09",
      mm: "00",
    },
    endTime: {
      hh: "21",
      mm: "00",
    },
  },
  type: [],
  city: [],
};

const ESimNumbersTable = () => {
  const dispatch = useDispatch();

  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [filteredNumbers, setFilteredNumbers] = useState([] as any);
  const [initialLoading, setInitialLoading] = useState(false);
  const [filters, setFilters] = useState(emptyFilters);

  const [activeNumber, setActiveNumber] = useState(null as any);
  const [showDeleteNumberModal, setShowDeleteNumberModal] = useState(false);
  const [DeactivateModal, setDeactivateModal] = useState(false);
  const [ActiveModal, setActiveModal] = useState(false);
  const [CheckedNumber, setCheckedNumber] = useState([] as any);
  const [QueryDisplay, setQueryDisplay] = useState("");
  const [dateRange, setDateRange] = useState({
    start: null,
    end: null,
  } as any);

  const [numbers, setNumbers] = useState([] as any);

  const repopulateNumbers = () => {
    /*setInitialLoading(true);
    ApiGet("/numbers").then((response) => {
      setNumbers(response.data.esimNumberResponse);
      setInitialLoading(false);
    });*/
    setNumbers(getEsimNumbers(itemsPerPage));
  };

  useEffect(repopulateNumbers, []);

  useEffect(() => {
    setFilteredNumbers(filterList(numbers, filters));
    setCurrentPage(1);
  }, [numbers, filters]);

  const formatDataItem = (item: any, key: string) => {
    if (key === "country") {
      return <CountryDisplay country={item.country} />;
    } else if (key === "purchasedOn") {
      return item[key] ? formatDateWords(item[key]) : "-";
    } else if (key === "availability") {
      return <AvailabilityPill status={item.availability} />;
    } else if (key === "status") {
      return (
        <RadioSelect
          label={<StatusPill status={item.status} />}
          options={[true, false].map((singleOption: any) => ({
            label: <StatusPill status={singleOption} />,
            key: singleOption,
          }))}
          selected={item.status}
          onChange={(selection: any) => {
            handleChangeDidStatus(item, selection);
          }}
        />
      );
    } else if (key === "period") {
      return item[key] ? `${item[key]} days` : "-";
    } else {
      return item[key] || "-";
    }
  };

  const getAllOfType = (all: any, key: string) => {
    let allUsers = [] as any;
    all.forEach((log: any) => {
      if (key === "country") {
        if (!allUsers.some((item: any) => item.key === log[key])) {
          allUsers.push({
            key: log[key],
            label: <CountryDisplay country={log[key]} />,
          });
        }
      } else {
        if (!allUsers.some((item: any) => item.key === log[key])) {
          if (key === "status") {
            allUsers.push({
              key: log[key],
              label: <StatusPill status={log[key]} />,
            });
          } else if (key === "availability") {
            allUsers.push({
              key: log[key],
              label: <AvailabilityPill status={log[key]} />,
            });
          } else {
            allUsers.push({ key: log[key], label: log[key] });
          }
        }
      }
    });
    console.log(allUsers);
    return allUsers;
  };

  const handleDeleteNumber = (esim: any) => {
    setActiveNumber(esim);
    setShowDeleteNumberModal(true);
  };

  const removeCheckList = (item: any) => {
    if (CheckedNumber.includes(item)) {
      setCheckedNumber(CheckedNumber.filter((change: any) => change !== item));
    }
  };

  const handleChangeDidStatus = (number: any, selection: any) => {
    ApiPut(`/esims/${number.msisdn}/${selection ? "activate" : "deactivate"}`)
      .then((response) => {
        dispatch({
          type: "notify",
          payload: {
            error: false,
            message: response.data.message,
          },
        });
        repopulateNumbers();
      })
      .catch((error) => {
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: error.response.data.message,
          },
        });
      });
  };

  return (
    <>
      <RemoveFiltersBar
        filters={filters}
        setFilters={setFilters}
        resetFilters={() => {
          setFilters(emptyFilters);
        }}
      />
      <div className={styles.panel}>
        <DeleteNumberModal
          show={showDeleteNumberModal}
          setShow={setShowDeleteNumberModal}
          multiNumber={CheckedNumber}
          esim={activeNumber}
          selection="e-sims"
          remove={removeCheckList}
          repopulate={repopulateNumbers}
        />
        <DeactivateNumberModal
          show={DeactivateModal}
          setShow={setDeactivateModal}
          multiNumber={CheckedNumber}
          selection="e-sims"
          remove={removeCheckList}
          repopulate={repopulateNumbers}
        />

        <ActiveNumberModal
          show={ActiveModal}
          setShow={setActiveModal}
          multiNumber={CheckedNumber}
          selection="e-sims"
          remove={removeCheckList}
          repopulate={repopulateNumbers}
        />
        <div className={`${styles.tableContainer} table-scroll`}>
          <div
            className={
              CheckedNumber.length < 1 ? styles.hideDiv : styles.newHead
            }
          >
            <div style={{ display: "flex", gap: "12px" }}>
              <Checkbox
                checked={CheckedNumber.length > 0}
                onClick={() => {
                  setCheckedNumber([]);
                }}
              />
              {CheckedNumber.length} Number{CheckedNumber.length > 1 ? "s" : ""}{" "}
              selected
            </div>
            <div style={{ display: "flex", gap: "16px" }}>
              <Button
                onClick={() => {
                  setActiveModal(true);
                }}
                style={{
                  height: 40,
                  minWidth: "initial",
                  padding: "0 24px",
                }}
              >
                Activate
              </Button>
              <Button
                onClick={() => {
                  setDeactivateModal(true);
                }}
                style={{
                  height: 40,
                  minWidth: "initial",
                  padding: "0 24px",
                }}
              >
                Deactivate
              </Button>
              <Button
                color="delete"
                onClick={() => {
                  setShowDeleteNumberModal(true);
                }}
                style={{
                  height: 40,
                  minWidth: "initial",
                  padding: "0 24px",
                }}
              >
                Delete
              </Button>
            </div>
          </div>
          <table>
            <thead>
              <tr className={CheckedNumber.length > 0 ? styles.hideTh : ""}>
                {/*<th>
                {" "}
                <Checkbox
                  checked={CheckedNumber.length > 0}
                  onClick={() => {
                    setCheckedNumber(numbers);
                  }}
                />
                </th>*/}
                {NumbereSimsFields.map((field) => {
                  if (
                    field.filter === "search-select" ||
                    field.filter === "select"
                  ) {
                    return (
                      <th>
                        <SearchSelect
                          label={field.label}
                          selected={filters[field.key]}
                          options={getAllOfType(numbers, field.key)}
                          setSelected={(state: any) => {
                            setFilters({
                              ...filters,
                              [field.key]: state,
                            });
                          }}
                          grid={
                            field.key === "country" ||
                            field.key === "city" ||
                            field.key === "number"
                          }
                          twoColumnGrid={
                            field.key === "country" ||
                            field.key === "city" ||
                            field.key === "number"
                          }
                          search={field.filter === "search-select"}
                        />
                      </th>
                    );
                  } else if (field.filter === "search") {
                    return (
                      <th>
                        <Search
                          label={field.label}
                          data={filteredNumbers}
                          setFilteredData={setFilteredNumbers}
                          setQueryDisplay={setQueryDisplay}
                          placeholder={`Search ${field.label}`}
                        />
                      </th>
                    );
                  } else if (field.filter === "date") {
                    return (
                      <th>
                        <DatePicker
                          label={field.label}
                          masterFrom={filters[field.key].start}
                          masterUntil={filters[field.key].end}
                          startTime={filters[field.key].startTime}
                          endTime={filters[field.key].endTime}
                          onChange={(
                            newFrom: Date,
                            newUntil: Date,
                            newStartTime: any,
                            newEndTime: any
                          ) => {
                            setFilters({
                              ...filters,
                              [field.key]: {
                                start: newFrom,
                                end: newUntil,
                                startTime: newStartTime,
                                endTime: newEndTime,
                              },
                            });
                          }}
                        />
                      </th>
                    );
                  } else {
                    return <th>{field.label}</th>;
                  }
                })}

                <th />
              </tr>
            </thead>
            <tbody>
              {!initialLoading
                ? padArrayToLength(
                    filteredNumbers.slice(
                      (currentPage - 1) * itemsPerPage,
                      currentPage * itemsPerPage
                    ),
                    itemsPerPage,
                    null
                  ).map((item: any) => {
                    if (item === null) {
                      return (
                        <tr
                          style={{
                            visibility: "hidden",
                            pointerEvents: "none",
                          }}
                        ></tr>
                      );
                    } else {
                      return (
                        <tr>
                          {/*<td>
                          <div className={styles.checkboxWrapper}>
                            <Checkbox
                              checked={CheckedNumber.includes(item)}
                              onClick={() => {
                                if (CheckedNumber.includes(item)) {
                                  // setChecked(0);
                                  setCheckedNumber(
                                    CheckedNumber.filter(
                                      (change: any) => change !== item
                                    )
                                  );
                                } else {
                                  setCheckedNumber([...CheckedNumber, item]);
                                }
                              }}
                            />
                          </div>
                            </td>*/}
                          {NumbereSimsFields.map((field: any) => (
                            <td>
                              <div
                                style={{
                                  display: "flex",
                                  justifyContent: "flex-start",
                                }}
                              >
                                {formatDataItem(item, field.key)}
                              </div>
                            </td>
                          ))}
                          <td>
                            <div className={styles.actionBox}>
                              <button
                                className={styles.actionButton}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleDeleteNumber(item);
                                }}
                              >
                                <Delete />
                              </button>
                            </div>
                          </td>
                        </tr>
                      );
                    }
                  })
                : Array.from({ length: itemsPerPage }, (v, i) => i).map((i) => (
                    <NumberSkeleton
                      key={"number-skeleton-" + i}
                      noOfStandard={9}
                    />
                  ))}
            </tbody>
          </table>
        </div>
        <div style={{ marginTop: "16px" }}>
          <TableControl
            show={true}
            itemsPerPage={itemsPerPage}
            setItemsPerPage={setItemsPerPage}
            currentPage={currentPage}
            setCurrentPage={setCurrentPage}
            numberOfPages={Math.ceil(filteredNumbers.length / itemsPerPage)}
            label="numbers"
          />
        </div>
      </div>
    </>
  );
};

export default ESimNumbersTable;
