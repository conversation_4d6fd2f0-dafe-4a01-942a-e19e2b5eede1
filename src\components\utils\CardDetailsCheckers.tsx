/*
 *    checkName:
 *        Tests string contains alphabetic and international characters (and ,.'-) only
 *        Used primarily for name input fields
 */
export const checkName = (str: any) => {
  if (str === "") {
    return true;
  } else {
    return /^[a-zA-ZàáâäãåąčćęèéêëėįìíîïłńòóôöõøùúûüųūÿýżźñçčšžÀÁÂÄÃÅĄĆČĖĘÈÉÊËÌÍÎÏĮŁŃÒÓÔÖÕØÙÚÛÜŲŪŸÝŻŹÑßÇŒÆČŠŽ∂ð ,.'-]+$/u.test(
      str
    );
  }
};

/*
 *    isNumeric:
 *        Tests string only contains numbers
 */
export const isNumeric = (str: any) => {
  if (typeof str != "string") return false;
  return /^\d+$/.test(str);
};

/*
 *    validateEmail:
 *        Check if valid email
 */
export const validateEmail = (email: string) => {
  return email.match(
    /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
  );
};

/*
 *    checkAddress:
 *        Same as checkName but including numbers
 *        Used for addrress fields
 */
export const checkAddress = (str: any) => {
  if (str === "") {
    return true;
  } else {
    return /^[0-9a-zA-ZàáâäãåąčćęèéêëėįìíîïłńòóôöõøùúûüųūÿýżźñçčšžÀÁÂÄÃÅĄĆČĖĘÈÉÊËÌÍÎÏĮŁŃÒÓÔÖÕØÙÚÛÜŲŪŸÝŻŹÑßÇŒÆČŠŽ∂ð ,.'-]+$/u.test(
      str
    );
  }
};

/*
 *    checkPostcode:
 *        Only allow postcode valid characters
 *        Numbers, alphabetic, and "-"
 */
export const checkPostcode = (str: any) => {
  if (str === "") {
    return true;
  } else {
    return /^[0-9a-zA-Z -]+$/u.test(str);
  }
};

/*
 *    checkPostcode:
 *        Checks for valid phone country code
 *        only allows numbers and "+"
 */
export const checkCode = (str: string) => {
  if (str === "") {
    return true;
  } else {
    return /^[+0-9]+$/u.test(str);
  }
};
