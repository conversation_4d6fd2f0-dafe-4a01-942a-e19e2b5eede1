import React from "react";
import styles from "./toggle-button-group.module.scss";

interface Option {
  label: string;
  key: string;
}

interface ToggleButtonGroupProps {
  options: Option[];
  onChange: (selected: string) => void;
  label?: string;
  selected: string | undefined;
}

const ToggleButtonGroup: React.FC<ToggleButtonGroupProps> = ({
  options,
  onChange,
  selected,
  label
}) => {
  const handleClick = (value: string) => {
    onChange(value);
  };

  return (
    <div className={styles.container}>
      {label && <p className={styles.label}>{label}</p>}
      <div className={styles.toggleButtonGroup}>
        {options.map(({ label, key }) => (
          <button
            key={key}
            className={`${styles.button} ${selected === key ? styles.selected : ""
              }`}
            onClick={() => handleClick(key)}
          >
            {label}
          </button>
        ))}
      </div>
    </div>
  );
};

export default ToggleButtonGroup;
