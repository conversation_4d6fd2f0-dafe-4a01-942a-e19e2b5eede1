import styles from "./checkbox-dropdown-input.module.scss";
import { ControlledMenu, MenuItem, useMenuState } from "@szhsin/react-menu";
import "@szhsin/react-menu/dist/index.css";
import "@szhsin/react-menu/dist/transitions/slide.css";
import { ChevronDown, InfoCircle } from "../svgs";
import { useRef, useState, useEffect, ReactElement } from "react";
import { Collapse } from "@mui/material";
import Checkbox from "../Checkbox";
import Button from "../Button";
import { Input } from "../Input";

type Option = {
  label: string | ReactElement;
  value: string;
};

type CheckboxDropdownInputProps = {
  options: Option[];
  selected: string[];
  onChange: (values: string[]) => void;
  placeholder?: string;
  disabled?: boolean;
  readonly?: boolean;
  id?: string;
  error?: string;
  label?: string;
  searchOption?: boolean;
  infoTooltipText?: boolean;
};

const CheckboxDropdownInput = ({
  options,
  selected = [],
  onChange,
  placeholder = "Choose",
  disabled,
  readonly,
  id,
  error,
  label,
  infoTooltipText,
  searchOption
}: CheckboxDropdownInputProps) => {
  const ref = useRef(null);
  const [menuProps, toggleMenu] = useMenuState({ transition: true });
  const [tempSelected, setTempSelected] = useState<string[]>(selected);
  const [searchQuery, setSearchQuery] = useState('')
  const [filteredOptions, setFilteredOptions] = useState<Option[]>(options)

  // Update tempSelected when selected prop changes
  useEffect(() => {
    setTempSelected(selected);
  }, [selected]);

  useEffect(() => {
    if (searchQuery.length > 0) {
      const filteredItems = options.filter((option:Option) => option.value.toLowerCase().startsWith(searchQuery.toLowerCase()))
      console.log(filteredItems)
      setFilteredOptions(filteredItems)
    } else {
      setFilteredOptions(options)
    }
  }, [searchQuery])

  const getDisplayValue = () => {
    if (tempSelected.length === 0) return placeholder;
    if (tempSelected.length === 1) return options.find(opt => opt.value === tempSelected[0])?.label;
    return `${tempSelected.length} items selected`;
  };

  const handleCheckboxChange = (value: string) => {
    const newSelected = tempSelected.includes(value)
      ? tempSelected.filter(v => v !== value)
      : [...tempSelected, value];
    setTempSelected(newSelected);
  };

  const handleApply = () => {
    onChange(tempSelected);
    toggleMenu(false);
  };

  const handleCancel = () => {
    setTempSelected(selected);
    toggleMenu(false);
  };

  return (
    <div className={styles.box} style={{ maxWidth: '320px' }}>
      <div className={styles.label}>
        <span>{label}</span>
        {infoTooltipText && (
          <span style={{ color: '#838CA0' }}>
            <InfoCircle />
          </span>
        )}
      </div>
      <div
        ref={ref}
        className={`${styles.menuButton} 
          ${tempSelected.length > 0 ? styles.selected : ''}
          ${(disabled || readonly) ? styles.disabled : ''}
          ${readonly ? styles.readonly : ''}
          ${menuProps.state === "open" || menuProps.state === "opening"
            ? styles.iconOpen
            : styles.iconClosed}
          ${error ? styles.error : ''}
          checkbox-dropdown-input`}
        onClick={() => !disabled && !readonly && toggleMenu(true)}
      >
        <span>{getDisplayValue()}</span>
        {!readonly && (
          <div className={styles.chevronContainer}>
            <ChevronDown />
          </div>
        )}
      </div>
      <Collapse in={!!error}>
        <p className={styles.errorText} id={`${id}-error`}>
          {error || <br />}
        </p>
      </Collapse>
      <ControlledMenu
        {...menuProps}
        anchorRef={ref}
        onClose={handleCancel}
        align="start"
        viewScroll="initial"
        position="anchor"
        direction="bottom"
        onItemClick={(e) => (e.keepOpen = true)}
        menuClassName={styles.menu}>
        {
          searchOption && (
            <Input 
              placeholder="Search"
              value={searchQuery}
              onChange={(e: any) => setSearchQuery(e.target.value) }
            />
          )
        }
        {filteredOptions.map((option) => (
          <MenuItem
            key={option.value}
            className={styles.menuItem}
            onClick={(e) => {
              e.keepOpen = true;
              handleCheckboxChange(option.value);
            }}
          >
            <span className={styles.checkboxContainer}>
              <Checkbox
                checked={tempSelected.includes(option.value)}
                label={option.label}

              />
            </span>
            <span>{option.label}</span>
          </MenuItem>
        ))}
        <div className={styles.actions}>
          <Button
            onClick={handleApply}
            style={{ height: 34, paddingInline: 16 }}
          >
            Apply
          </Button>
          <Button
            onClick={handleCancel}
            color="secondary"
            style={{ height: 34, paddingInline: 16 }}
          >
            Cancel
          </Button>
        </div>
      </ControlledMenu>
    </div>
  );
};

export default CheckboxDropdownInput; 