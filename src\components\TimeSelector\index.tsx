import { CaretDown, CaretUp } from "../svgs";
import { zeroPad } from "../utils/dateHelpers";
import styles from "./time-selector.module.scss";

const TimeSelector = ({ time, setTime }: any) => {
  const increaseHour = () => {
    let hour = parseInt(time.hh);
    hour++;
    if (hour === 24) {
      hour = 0;
    }
    setTime({
      ...time,
      hh: zeroPad(hour),
    });
  };
  const decreaseHour = () => {
    let hour = parseInt(time.hh);
    hour--;
    if (hour === -1) {
      hour = 23;
    }
    setTime({
      ...time,
      hh: zeroPad(hour),
    });
  };
  const increaseMinute = () => {
    let minute = parseInt(time.mm);
    minute++;
    if (minute === 60) {
      minute = 0;
    }
    setTime({
      ...time,
      mm: zeroPad(minute),
    });
  };
  const decreaseMinute = () => {
    let minute = parseInt(time.mm);
    minute--;
    if (minute === -1) {
      minute = 59;
    }
    setTime({
      ...time,
      mm: zeroPad(minute),
    });
  };

  return (
    <div className={styles.timeGroup}>
      <div className={styles.unitColumn}>
        <div className={styles.timeButton} onClick={increaseHour}>
          <CaretUp />
        </div>
        <div className={styles.timeLabel}>{time.hh}</div>
        <div className={styles.timeButton} onClick={decreaseHour}>
          <CaretDown />
        </div>
      </div>
      <div className={styles.timeDivider}>:</div>
      <div className={styles.unitColumn}>
        <div className={styles.timeButton} onClick={increaseMinute}>
          <CaretUp />
        </div>
        <div className={styles.timeLabel}>{time.mm}</div>
        <div className={styles.timeButton} onClick={decreaseMinute}>
          <CaretDown />
        </div>
      </div>
    </div>
  );
};

export default TimeSelector;
