import styles from "./pagination.module.scss";
import { Collapse, Fade } from "@mui/material";
import {
  <PERSON>Back,
  ArrowRight,
  CaretLeft,
  CaretLeftSmall,
  CaretRight,
  CaretRightSmall,
} from "../svgs";
import { useState, useEffect } from "react";

const Pagination = ({
  currentPage,
  setCurrentPage,
  numberOfPages,
  small,
  loading,
}: any) => {
  const [pages, setPages] = useState([] as any);

  useEffect(() => {
    if (numberOfPages <= 6) {
      setPages(Array.from(Array(numberOfPages).keys()));
    } else if (pages.length === 0 || currentPage < 5) {
      setPages([0, 1, 2, 3, 4, -1, numberOfPages - 1]);
    } else if (currentPage > numberOfPages - 4) {
      setPages([
        0,
        -1,
        numberOfPages - 5,
        numberOfPages - 4,
        numberOfPages - 3,
        numberOfPages - 2,
        numberOfPages - 1,
      ]);
    } else {
      setPages([
        0,
        -1,
        currentPage - 2,
        currentPage - 1,
        currentPage,
        -1,
        numberOfPages - 1,
      ]);
    }
  }, [currentPage, numberOfPages]);

  return (
    <div className={`${styles.pageNumbers} ${small && styles.small}`}>
      <Fade in={currentPage !== 1}>
        <div
          className={styles.pageArrowButton}
          style={{ marginRight: 4 }}
          onClick={() => {
            if (currentPage !== 1 && !loading) {
              setCurrentPage(currentPage - 1);
            }
          }}
        >
          {small ? <CaretLeftSmall /> : <ArrowBack />}
          <span style={{ marginLeft: 8 }}>Previous</span>
        </div>
      </Fade>
      {pages.map((num: number, i: number) => {
        if (num === -1) {
          return (
            <div key={i} className={styles.dots}>
              •••
            </div>
          );
        } else {
          return (
            <div
              className={`${styles.pageNumber} ${
                currentPage === num + 1 && styles.activePageNumber
              }`}
              onClick={() => {
                setCurrentPage(num + 1);
              }}
              key={`pagination-button-` + num}
            >
              {num + 1}
            </div>
          );
        }
      })}
      {currentPage !== numberOfPages && numberOfPages !== 0 && (
        <Fade in={currentPage !== numberOfPages && numberOfPages !== 0}>
          <div
            className={styles.pageArrowButton}
            onClick={() => {
              if (currentPage !== numberOfPages && !loading) {
                setCurrentPage(currentPage + 1);
              }
            }}
          >
            <span style={{ marginRight: 8 }}>Next</span>
            {small ? <CaretRightSmall /> : <ArrowRight />}
          </div>
        </Fade>
      )}
    </div>
  );
};

export default Pagination;
