import styles from "./search-bar.module.scss";
import { MagnifyingGlass } from "../svgs";
import { useState } from "react";

const SearchBar = ({
  placeholder,
  onSubmit,
  id,
  loading,
  query = "",
  setQuery,
}: any) => {
  const [showButton, setShowButton] = useState(false);
  const handleFocus = () => {
    setShowButton(true);
  };

  const handleBlur = () => {
    setShowButton(false);
  };

  return (
    <div className={styles.main}>
      <MagnifyingGlass
        color={loading ? "rgba(26, 26, 26, 0.38)" : "currentColor"}
      />
      <input
        placeholder={placeholder}
        className={styles.input}
        value={query}
        onChange={(e) => setQuery(e.target.value)}
        onKeyDown={(event) => {
          if (event.key === "Enter") {
            event.preventDefault();
            onSubmit();
          }
        }}
        disabled={loading}
        id={id}
        onFocus={handleFocus}
        onBlur={handleBlur}
      />
    </div>
  );
};

export default SearchBar;
