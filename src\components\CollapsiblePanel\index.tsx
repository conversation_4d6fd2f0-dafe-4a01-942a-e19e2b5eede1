import { Collapse } from "@mui/material";
import { useImperativeHandle, useRef, useState } from "react";
import styles from "./collapsible-panel.module.scss";
import { CaretDown } from "@/components/svgs";
import React from "react";


type CollabsiblePanelProps = {
  title: string;
  children: React.ReactNode;
  summaryWhenClosed?: React.ReactNode;
  headerWhenOpen?: React.ReactNode;
  open?: boolean;
  initialOpen?: boolean;
  actionBtn?: React.ReactNode;
}

const CollapsiblePanel = React.forwardRef<any, CollabsiblePanelProps>((props, ref) => {
  const { title, children, summaryWhenClosed, headerWhenOpen, open, initialOpen, actionBtn } = props;
  const [internalOpen, setInternalOpen] = useState(open ?? initialOpen ?? true);
  const isOpen = open ?? internalOpen;

  useImperativeHandle(ref, () => ({
    open: () => setInternalOpen(true),
    close: () => setInternalOpen(false),
    toggle: () => setInternalOpen(!internalOpen)
  }))

  return (
    <div className={styles.panel}>
      <div className={styles.panelTopBar} onClick={() => open === undefined && setInternalOpen(!internalOpen)}>
        <h2 className={styles.title}>{title}</h2>
        <div className={styles.topContent}>
          {isOpen ? (headerWhenOpen) : (summaryWhenClosed)}
        </div>
        { actionBtn }
        {/* indicator icon */}
        <div className={`${styles.caret} ${isOpen ? styles.isOpen : ''}`}>
          {<CaretDown />}
        </div>
      </div>

      <Collapse in={isOpen}>
        <div className={styles.content}>
          {children}
        </div>
      </Collapse>
    </div>
  )
})

export default CollapsiblePanel