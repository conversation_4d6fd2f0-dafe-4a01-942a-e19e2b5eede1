import styles from "./ticket.module.scss";
import Checkbox from "../Checkbox";
import {
  ChevronDown,
  ChevronDownLg,
  ArrowRight,
  Home,
  Spanner,
  Coin,
  Cube,
} from "../svgs";
import { Collapse } from "@mui/material";
import { useEffect, useState } from "react";
import Button from "../Button";
import TicketSelect from "../TicketSelect";
import Priority from "../Priority";
import Category from "../Category";
import Assignee from "../Assignee";
import StatusBadge from "../StatusBadge";
import Initials from "../Initials";
import formatDate from "../utils/formatDate";
import { motion } from "framer-motion";
import { useDispatch, useSelector } from "react-redux";
import TicketType from "../TicketType";
import Tooltip from "../Tooltip";
import { Link } from "react-router-dom";

const Ticket = ({
  ticket,
  handleTicketUpdate,
  handleSelectTicket,
  selectedTickets,
  disabled,
  agents,
}: any) => {
  const [selected, setSelected] = useState(false);
  const { ticketOpen } = useSelector((state: any) => state);

  const [showBody, setShowBody] = useState(false);

  const dispatch = useDispatch();

  useEffect(() => {
    if (selectedTickets.includes(ticket.id)) {
      setSelected(true);
    } else {
      setSelected(false);
    }
  }, [selectedTickets]);

  return (
    <motion.div
      layout
      animate="in"
      variants={{
        in: {
          opacity: 1,
        },
        out: {
          opacity: 0,
        },
      }}
      transition={{
        type: "tween",
        ease: "easeInOut",
        duration: 0.3,
      }}
      key={"motion-ticket-" + ticket.id}
      className={`${styles.main} ${disabled && styles.disabled} ${
        ticketOpen && styles.ticketOpen
      }`}
      onClick={(e: any) => {
        if (
          e.target.closest(".szh-menu") ||
          e.target.id === "customer-summary"
        ) {
          return;
        }
        dispatch({
          type: "set",
          ticketOpen: true,
          ticket: ticket,
        });
      }}
    >
      <div className={styles.summary}>
        <div style={{ width: 24 }}>
          <Checkbox
            checked={selected}
            onClick={() => handleSelectTicket(ticket)}
          />
        </div>
        <div className={styles.overview}>
          <div className={styles.top}>
            <TicketType status="Support" />
            <div className={styles.id}>#{ticket.id}</div>
            <div className={styles.date}>{formatDate(ticket.creationDate)}</div>
          </div>
          <div className={styles.bottom}>
            <div className={styles.subject}>{ticket.subject}</div>
            <div className={styles.email}>{ticket.customerEmail}</div>
          </div>
        </div>
        <div className={styles.dataBar}>
          {ticket.type !== "DID Request" && ticket.type !== "Porting" && (
            <>
              <TicketSelect
                disabled={disabled}
                label="Priority"
                selected={ticket.priority}
                onChange={(option: string) => {
                  handleTicketUpdate("priority", option, ticket);
                }}
                options={[
                  {
                    key: 4,
                    label: <Priority priority="Urgent" />,
                    displayLabel: <Priority priority="Urgent" />,
                  },
                  {
                    key: 3,
                    label: <Priority priority="High" />,
                    displayLabel: <Priority priority="High" />,
                  },
                  {
                    key: 2,
                    label: <Priority priority="Medium" />,
                    displayLabel: <Priority priority="Medium" />,
                  },
                  {
                    key: 1,
                    label: <Priority priority="Low" />,
                    displayLabel: <Priority priority="Low" />,
                  },
                ]}
              />
              <Tooltip show text={ticket.category}>
                <TicketSelect
                  disabled={disabled}
                  label="Category"
                  options={[
                    {
                      key: "Mobilise",
                      label: <Category category="Mobilise" />,
                      displayLabel: <Home />,
                    },
                    {
                      key: "Technical Support",
                      label: <Category category="Technical Support" />,
                      displayLabel: <Spanner />,
                    },
                    {
                      key: "Finance",
                      label: <Category category="Finance" />,
                      displayLabel: <Coin />,
                    },
                    {
                      key: "Product Management",
                      label: <Category category="Product Management" />,
                      displayLabel: <Cube />,
                    },
                  ]}
                  selected={ticket.category}
                  onChange={(option: string) => {
                    handleTicketUpdate("category", option, ticket);
                  }}
                />
              </Tooltip>
            </>
          )}
          {/*<TicketSelect
            disabled={disabled}
            label="Assignee"
            options={agents.map((agent: any) => ({
              key: agent.freshdeskId,
              label: <Assignee name={agent.firstName + " " + agent.lastName} />,
              displayLabel: (
                <Initials>
                  {agent.firstName.slice(0, 1)}
                  {agent.lastName.slice(0, 1)}
                </Initials>
              ),
            }))}
            selected={ticket.assignee}
            onChange={(option: string) => {
              handleTicketUpdate("responder_id", option, ticket);
            }}
          />*/}
          <TicketSelect
            disabled={disabled}
            label="Status"
            options={[
              {
                key: 2,
                label: <StatusBadge status="Open" />,
                displayLabel: <StatusBadge status="Open" />,
              },
              {
                key: 3,
                label: <StatusBadge status="Pending" />,
                displayLabel: <StatusBadge status="Pending" />,
              },
              {
                key: 4,
                label: <StatusBadge status="Resolved" />,
                displayLabel: <StatusBadge status="Resolved" />,
              },
              {
                key: 5,
                label: <StatusBadge status="Closed" />,
                displayLabel: <StatusBadge status="Closed" />,
              },
            ]}
            selected={ticket.status}
            onChange={(option: string) => {
              handleTicketUpdate("status", option, ticket);
            }}
          />
        </div>
        <div
          onClick={(e: any) => {
            e.stopPropagation();
            setShowBody((prev: boolean) => !prev);
          }}
          className={`${styles.expand} ${showBody && styles.rotate}`}
        >
          <ChevronDownLg />
        </div>
      </div>
      <Collapse in={showBody}>
        <div className={styles.body}>
          <div className={styles.left}>
            <h5 className={styles.issueHeading}>Issue:</h5>
            <div
              className={styles.issueContent}
              dangerouslySetInnerHTML={{ __html: ticket.description }}
            />
          </div>
          <Link
            to={`/customer-details/${ticket.mid}`}
            className={styles.customerSummary}
            id="customer-summary"
          >
            Customer Summary <ArrowRight />
          </Link>
        </div>
      </Collapse>
    </motion.div>
  );
};

export default Ticket;
