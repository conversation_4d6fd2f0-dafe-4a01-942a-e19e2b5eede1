@mixin stripedTablePanel {
  background: #fff;
  width: 100%;
  border-radius: 24px;
  padding: 24px;
  display: flex;
  flex-direction: column;
  table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0px;
    white-space: nowrap;
    tbody {
      tr {
        height: 45px;
        background: #f2f2f2;
        &:nth-child(odd) {
          background: #fff;
        }
        td {
          font-size: 14px;
          font-weight: 400;
          line-height: 21px;
          padding: 0 16px;
        }
        td:first-child {
          padding-left: 24px;
        }
        td:last-child {
          padding-right: 24px;
        }
      }
      &:before {
        content: "@";
        display: block;
        line-height: 12px;
        text-indent: -99999px;
      }
    }
    thead {
      tr {
        th:first-child {
          padding-left: 24px;
        }
      }
    }
    th {
      font-size: 14px;
      font-weight: 600;
      line-height: 21px;
      text-align: start;
      border-bottom: 1px solid $disabled;
      padding: 0 16px;
      padding-bottom: 21px;
    }
  }
}

@mixin tableHeadFilterButton {
  border: none;
  font-size: 14px;
  font-weight: 600;
  line-height: 21px;
  display: flex;
  align-items: center;
  margin-right: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  -webkit-user-select: none; /* Safari */
  -ms-user-select: none; /* IE 10 and IE 11 */
  user-select: none;
  svg {
    margin-left: 7px;
    transition: transform 0.2s ease;
  }
  &:hover {
    color: $orange;
  }
  &.iconOpen {
    svg {
      transform: rotate(180deg);
    }
  }
}

@mixin animatedSelection {
  .selectionWrapper {
    display: flex;
    align-items: center;
    margin-bottom: 18px;
    h2 {
      font-size: 20px;
      line-height: 30px;
      font-weight: 700;
      margin-right: 24px;
    }
  }

  .selection {
    height: 47px;
    border-radius: 1000px;
    color: $black;
    font-size: 14px;
    padding: 0 24px;
    display: flex;
    align-items: center;
    position: relative;
    cursor: pointer;
    transition: all 0.2s ease;
    &:hover {
      color: $dark-orange;
    }
    span {
      position: relative;
      z-index: 6;
    }
  }

  .background {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 1000px;
    background-color: $light-orange;
    z-index: 5;
    left: 0;
  }

  .activeSelection {
    text-shadow: 0px 0px 0.5px $black;
    cursor: auto;
    &:hover {
      color: $black;
    }
  }
}

@mixin panel {
  background: #fff;
  border-radius: 16px;
  padding: 12px 16px;

  .panelTopBar {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .actions {
      width: 60%;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      .collapseArrows {
        margin-left: 15px;
        cursor: pointer;
      }
    }
    h4 {
      font-size: 16px;
      font-weight: 700;
      line-height: 22px;
    }
  }
}

@mixin overviewCardsWrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

@mixin searchPanelForm {
  .fields {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-top: 20px;
  }

  .actions {
    display: flex;
    justify-content: end;
  }
}

@mixin buttonReset {
  all: unset;
  appearance: none;
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  color: inherit;
  font: inherit;
  text-align: inherit;
  line-height: inherit;
  cursor: pointer;
}
