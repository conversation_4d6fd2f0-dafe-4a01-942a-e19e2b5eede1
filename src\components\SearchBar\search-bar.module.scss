@use "../../styles/theme.scss" as *;

.main {
  width: 100%;
  max-width: 325px;
  display: flex;
  align-items: center;
  background: #fff;
  height: 100%;
  position: relative;

  svg {
    position: absolute;
    left: 15px;
    color: #838ca0;
  }
}

.input {
  border: none;
  font-size: 14px;
  color: #061632;
  width: 100%;
  background-color: #f1f6fd;
  caret-color: #2e70e5;
  border: 1px solid $lightgrey;
  border-radius: 15px;
  outline: none;
  height: 40px;
  padding: 20px;
  padding-left: 43px;
  &:disabled {
    color: $disabled-text;
    background: none;
  }
  &:focus,
  &:hover {
    border-color: #2e70e5 !important;
    border-width: 2px;
  }
  &::placeholder {
    color: #667085;
  }
}
