import styles from "../../styles/tickets.module.scss";
import { useState, useRef } from "react";
import SwitchBar from "@/components/SwitchBar";
import Title from "@/components/Title";
import CollapsiblePanel from "@/components/CollapsiblePanel";
import InsightList from "@/components/InsightList";
import InsightCard from "@/components/InsightCard";
import Button from "@/components/Button";
import { Plus } from "@/components/svgs";
import TicketsTable from "@/components/TicketsTable/TicketsTable";
import CreateTicketModal from "@/components/CreateTicketModal";

const Tickets = () => {
  const [tab, setTab] = useState("overview");
  const [showCreateModal, setShowCreateModal] = useState(false);
  const overviewPanelRef = useRef<any>(null);

  return (
    <>
      {showCreateModal && (
        <CreateTicketModal
          show={showCreateModal}
          setShow={setShowCreateModal}
        />
      )}
      <Title>Tickets</Title>
      <SwitchBar
        options={[
          {
            label: "Overview",
            id: "overview",
          },
          {
            label: "Unassigned",
            id: "unassigned",
          },
        ]}
        selected={tab}
        setSelected={setTab}
        layoutId="tickets-type-switch"
      />
      <div className={styles.main}>
        {tab === "overview" ? (
          <>
            <CollapsiblePanel
              title="Overview"
              summaryWhenClosed={
                <div style={{ marginLeft: 22 }}>
                  <InsightList insights={overviewStats} />
                </div>
              }
              actionBtn={
                <Button
                  style={{ marginRight: 10 }}
                  onClick={(e) => {
                    e.stopPropagation();
                    setShowCreateModal(true);
                  }}
                >
                  <Plus /> Create Ticket
                </Button>
              }
              ref={overviewPanelRef}
            >
              <div className={styles.overview}>
                {overviewStats.map((stat, index) => (
                  <InsightCard
                    key={index}
                    title={stat.title}
                    value={stat.value.toString()}
                  />
                ))}
              </div>
            </CollapsiblePanel>
            <TicketsTable tab={tab} />
          </>
        ) : (
          <>
            <TicketsTable tab={tab} />
          </>
        )}
      </div>
    </>
  );
};

export default Tickets;

const overviewStats = [
  { title: "Unassigned", value: "63,629" },
  { title: "Due Today", value: "63,629" },
  { title: "Overdue", value: "63,629" },
  { title: "Open", value: "63,629" },
  { title: "Pending", value: "63,629" },
];
