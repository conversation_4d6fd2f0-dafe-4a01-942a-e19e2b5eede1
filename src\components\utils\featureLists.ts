export const throttles = [
  {
    code: "APEX128",
    name: "",
    description: "",
  },
  {
    code: "APEX256",
    name: "",
    description: "",
  },
  {
    code: "APEX512",
    name: "",
    description: "",
  },
  {
    code: "APEX3",
    name: "",
    description: "",
  },
  {
    code: "APEX6",
    name: "",
    description: "",
  },
  {
    code: "APEX12",
    name: "",
    description: "",
  },
  {
    code: "APEXBLOCK",
    name: "",
    description: "",
  },
];

export const toggleFeatures = [
  {
    code: "NIRMAPEX",
    name: "Block International Roaming Except Mexico/Canada",
    description:
      "Block International Roaming Except Mexico/Canada - Compatible with all APEX plans (APEX Unlimited, APEX Mobile Select, APEX Exclusive, and Custom Plans).",
  },
  {
    code: "TRKSOCV56",
    name: "DUCCS alerts suppression",
    description:
      "Blocks DUCCS notice for international alerts and other automated AT&T messages",
  },
  {
    code: "SMRTBLOCK",
    name: "Mobile Security Call Protect Block",
    description: "Mobile Security Call Protect Block",
  },
  {
    code: "CIBL",
    name: "Caller ID Block",
    description: "Blocks name and number on caller id",
  },
  {
    code: "ZZNOILD2",
    name: "International Long Distance Block",
    description:
      "Blocks international LD calls from the US.  Must delete ILDSMXCAO bundled SOC when adding blocking SOC for block to occur.  Blocks calls to Canada & Mexico even if included in price plan.",
  },
  {
    code: "NOILDTEXT",
    name: "Blocking international texting",
    description: "Blocks international text from the US.  ",
  },
  {
    code: "SMARTBCRU",
    name: "ActiveArmour Basic",
    description:
      "AT&T ActiveArmour Basic blocks spam and fraud calls when activated.  Add this SOC to the line and the end user will receive a text to activate the service",
  },
  {
    code: "DSABR2",
    name: "Stream Saver",
    description: "Video Management",
  },
];

export const boltons = [
  {
    code: "AZIRRLHDF",
    name: "AT&T International Day Pass for Resale*",
    description: "AT&T International Day Pass for Resale*",
  },
  {
    code: "APIDV10GB",
    name: "PassPort Data/Voice 10GB MRC",
    description: "must be added with API250VM to enable data on smartphones.  ",
  },
  {
    code: "AZIR100VM",
    name: "AT&T International Data Bolt On 1GB Voice",
    description: "must be added with AZIRDV1GB to enable voice on smartphones",
  },
  {
    code: "APEXILD",
    name: "AT&T Business World Connect Advantage",
    description: "International Long Distance Business Advantage Plan",
  },
  {
    code: "AZIDX10GB",
    name: "PassPort DataOnly 10GB",
    description: "for data only devices",
  },
  {
    code: "API250VM",
    name: "PassPort Voice/Data 10GB MRC",
    description: "must be added with APIDV10GB to enable voice on smartphones",
  },
];
