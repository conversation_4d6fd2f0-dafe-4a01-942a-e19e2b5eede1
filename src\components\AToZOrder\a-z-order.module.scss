@use "../../styles/theme.scss" as *;
@import "../../styles/mixins.module.scss";

.menuButton {
  @include tableHeadFilterButton;
}

.box {
  height: 100%;
}

.menuItem {
  display: flex;
  align-items: center;
  transition: all 0.1s ease;
  color: $black;
  padding: 0;
  line-height: 21px;
  padding: 0;
  margin-bottom: 18px;
  background: none !important;
  font-size: 14px;
  font-weight: 400;
  cursor: auto;
  .itemLabel {
    display: flex;
    align-items: center;
    svg {
      margin-right: 8px;
    }
  }
  &:last-of-type {
    margin-bottom: 0px;
  }
  &:hover {
    background: none !important;
  }
}
