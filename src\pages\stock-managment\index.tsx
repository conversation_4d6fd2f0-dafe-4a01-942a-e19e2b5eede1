import CollapsiblePanel from "@/components/CollapsiblePanel";
import InsightCard from "@/components/InsightCard";
import Title from "@/components/Title";
import styles from "@/styles/orders.module.scss";
import stockStyles from "@/styles/stock-managment.module.scss";
import InsightList from "@/components/InsightList";
import {
  handleInputChange,
  createStateObject,
  labels,
  clearInput,
} from "@/components/utils/InputHandlers";
import { useEffect, useRef, useState } from "react";
import { Input } from "@/components/Input";
import Button from "@/components/Button";
import { Download, Export, MagnifyingGlass, QrCode } from "@/components/svgs";
import { getStockESIM } from "@/components/utils/dataCreator";
import { stockesimFields } from "@/components/utils/stockesimFields";
import { formatDateWithTime } from "@/components/utils/formatDate";
import UserSkeleton from "@/components/UserSkeleton";
import TableControl from "@/components/TableControl";
import Tag from "@/components/Tag";
import CountryDisplay from "@/components/CountryDisplay";
import SendQrModal from "@/components/SendQrModal/SendQrModal";
import SwitchBar from "@/components/SwitchBar";
import StockVoipTab from "@/components/StockVoipTab/StockVoipTab";
import AvailabilityPill from "@/components/AvailabilityPill";
import { useSelector } from "react-redux";
import CheckboxDropdownInput from "@/components/CheckboxDropdownInput";

type AvailabilityDropdownProps = {
  value: string[];
  onChange: (e: { target: { value: string[] } }) => void;
  error?: string;
};

const AvailabilityDropdown = ({ value, onChange, error }: AvailabilityDropdownProps) => {
  return (
    <CheckboxDropdownInput
      options={selectOptionsByField.availability}
      label={labels.availability}
      selected={value}
      onChange={(values) => onChange({ target: { value: values || '' } })}
      error={error}
      infoTooltipText
    />
  );
};

const StockManagment = () => {
  const searchStockFields = ["iccid", "msisdn", "carrier", "availability"];
  const [data, setData] = useState(createStateObject(searchStockFields));
  const [loading, setLoading] = useState(true);
  const [stock, setStock] = useState([]);
  const [showQrModal, setShowQrModal] = useState(false);
  const [stockType, setStockType] = useState("esim");
  const [selectedRow, setSelectedRow] = useState({
    availability: "",
    email: "",
  });

  const { productType } = useSelector((state: any) => state);

  const populate = (itemsPerPage: number) => {
    setLoading(true);

    setTimeout(() => {
      setStock(getStockESIM(itemsPerPage));
      setLoading(false);
    }, 500);
  };

  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [numberOfPages, setNumberOfPages] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  useEffect(() => {
    populate(itemsPerPage);
  }, [itemsPerPage, currentPage]);

  const handleQrModal = (email: string, availability: string) => {
    setSelectedRow({
      email,
      availability,
    });
    setShowQrModal(true);
  };

  const searchPanelRef = useRef<any>(null);
  const overviewPanelRef = useRef<any>(null);

  const [showSearchResults, setShowSearchResults] = useState(false);

  const formatDataItem = (item: any, key: string) => {
    if (key === "uploaded") {
      return formatDateWithTime(item[key]);
    } else if (key === "country") {
      if (item[key] !== "-") {
        return <CountryDisplay country={item[key].countryCode} />;
      } else {
        return item[key];
      }
    } else if (key === "availability") {
      return <AvailabilityPill status={item.availability} />;
    } else {
      return item[key];
    }
  };

  return (
    <>
      <Title>Stock Management</Title>
      {(productType === "voip-esim" || productType === "mvne") && (
        <SwitchBar
          options={[
            {
              label: "eSIM",
              id: "esim",
            },
            {
              label: "VoIP",
              id: "voip",
            },
          ]}
          selected={stockType}
          setSelected={setStockType}
          layoutId="stock-type-switch"
        />
      )}
      {/* Overview */}
      <div
        className={styles.main}
        style={{ paddingTop: stockType !== "esim" ? 0 : "16px" }}
      >
        {stockType === "esim" ? (
          <>
            <CollapsiblePanel
              title="Overview"
              summaryWhenClosed={
                <div style={{ marginLeft: 22 }}>
                  <InsightList insights={overviewStats} />
                </div>
              }
              ref={overviewPanelRef}
            >
              <div className={styles.overview}>
                {overviewStats.map((stat, index) => (
                  <InsightCard
                    key={index}
                    title={stat.title}
                    value={stat.value.toString()}
                  />
                ))}
              </div>
            </CollapsiblePanel>

            {/* Search orders */}
            <div style={{ marginTop: 16 }}>
              <CollapsiblePanel
                title="Search Stock"
                summaryWhenClosed={
                  <div
                    style={{
                      display: "flex",
                      flex: 1,
                      justifyContent: "space-between",
                      marginLeft: 16,
                      marginRight: 8,
                    }}
                  >
                    <Tag text="3 filters applied" />
                    <Button color="secondary">Clear Filters</Button>
                  </div>
                }
                ref={searchPanelRef}
              >
                <div className={styles.fields}>
                  {searchStockFields.map((prop) => {
                    if (["iccid", "msisdn"].includes(prop)) {
                      return (
                        <Input
                          key={"orders-" + prop}
                          label={labels[prop]}
                          value={data[prop]}
                          onChange={(e: any) => {
                            handleInputChange(prop, e, data, setData);
                          }}
                          error={data.errors[prop]}
                          clear={() => {
                            clearInput(prop, setData);
                          }}
                          infoTooltipText
                        />
                      );
                    } else if (["carrier"].includes(prop)) {
                      return (
                        <CheckboxDropdownInput
                          key={"stock-tab-" + prop}
                          options={selectOptionsByField[prop]}
                          label={labels[prop]}
                          selected={data[prop]}
                          onChange={(values) => {
                          handleInputChange(
                            prop,
                            values,
                            data,
                            setData,
                            'select'
                          )}}
                          error={data.errors[prop]}
                          infoTooltipText
                      />
                      );
                    } else if (["availability"].includes(prop)) {
                      return (
                        <AvailabilityDropdown
                          key={"orders-" + prop}
                          value={data[prop]}
                          onChange={(e: any) => handleInputChange(prop, e, data, setData)}
                          error={data.errors[prop]}
                        />
                      );
                    }
                  })}
                </div>
                <div className={styles.actions}>
                  <Button
                    color="blue"
                    onClick={() => {
                      searchPanelRef.current.close();
                      overviewPanelRef.current.close();
                      setShowSearchResults(true);
                    }}
                  >
                    <MagnifyingGlass /> Search
                  </Button>
                </div>
              </CollapsiblePanel>
            </div>

            {showSearchResults && (
              <div className={styles.panel}>
                <div className={styles.panelTopBar}>
                  <h4>eSIM Stock</h4>
                  <div className={styles.actions}>
                    <Button color="secondary" style={{ marginRight: "8px" }}>
                      <Download /> Import eSIM Batch
                    </Button>
                    <Button color="secondary">
                      <Export /> Export to CSV
                    </Button>
                  </div>
                </div>
                <SendQrModal
                  show={showQrModal}
                  close={() => setShowQrModal(false)}
                  selected={selectedRow}
                />
                <div className={`${styles.tableContainer} table-scroll`}>
                  <table>
                    <thead>
                      <tr>
                        {stockesimFields.map((field: any) => (
                          <th>{field.label}</th>
                        ))}
                        <th></th>
                      </tr>
                    </thead>
                    <tbody>
                      {!loading
                        ? stock.map((item: any, i: number) => {
                            return (
                              <tr key={`order-${item.iccid}`}>
                                {stockesimFields.map((field: any) => (
                                  <td key={`order-${item.iccid}-${field.key}`}>
                                    <div
                                      style={{
                                        display: "flex",
                                        justifyContent: "flex-start",
                                      }}
                                    >
                                      {formatDataItem(item, field.key)}
                                    </div>
                                  </td>
                                ))}
                                <td>
                                  {item.availability === "Pending" ||
                                  item.availability === "Available" ? (
                                    <span
                                      className={stockStyles.qrCode}
                                      onClick={() =>
                                        handleQrModal(
                                          item.email,
                                          item.availability
                                        )
                                      }
                                    >
                                      <QrCode />
                                    </span>
                                  ) : (
                                    ""
                                  )}
                                </td>
                              </tr>
                            );
                          })
                        : Array.from({ length: itemsPerPage }, (v, i) => i).map(
                            (i) => (
                              <UserSkeleton
                                key={"order-skeleton-" + i}
                                noOfStandard={8}
                              />
                            )
                          )}
                    </tbody>
                  </table>
                </div>
                <div style={{ marginTop: "16px" }}>
                  <TableControl
                    show
                    itemsPerPage={itemsPerPage}
                    setItemsPerPage={(val: any) => setItemsPerPage(val)}
                    currentPage={currentPage}
                    setCurrentPage={setCurrentPage}
                    numberOfPages={numberOfPages}
                    label="orders"
                    loading={loading}
                  />
                </div>
              </div>
            )}
          </>
        ) : (
          <StockVoipTab />
        )}
      </div>
    </>
  );
};

export default StockManagment;

const overviewStats = [
  { title: "Total eSIM SStock", value: "63,629" },
  { title: "Available Stock", value: "63,629" },
  { title: "Allocated Stock", value: "63,629" },
  { title: "Active Subscriptions", value: "63,629" },
];

const selectOptionsByField: Record<string, any> = {
  carrier: [
    { label: "AT&T", value: "AT&T" },
    { label: "Orange", value: "Orange" },
    { label: "Vodafone", value: "Vodafone" },
  ],
  availability: [
    { label: "Available", value: "Available" },
    { label: "Pending", value: "Pending" },
    { label: "Registered", value: "Registered" }
  ],
};
