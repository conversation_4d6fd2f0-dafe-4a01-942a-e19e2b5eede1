import Dialog from "@/components/Dialog";
import { PlusCircle, UploadSimple } from "@/components/svgs";
import styles from "./add-provider-modal.module.scss";
import { Input } from "@/components/Input";
import { useState } from "react";
import { clearInput, createStateObject, handleInputChange, labels } from "@/components/utils/InputHandlers";
import ToggleButtonGroup from "@/components/ToggleButtonGroup";
import Button from "../Button";

type AddProviderModalProps = {
  open: boolean;
  onClose: () => void;
  onAdd?: (data: any) => void;
};

const AddProviderModal = ({
  open,
  onClose,
  onAdd
}: AddProviderModalProps) => {
  const fields = ["providerName", "prefix", "mode", "inBoundIp", "outBoundIp", "allowCodecs", "status", "type"];

  const [formData, setFormData] = useState(createStateObject(fields));

  const handleSubmit = () => {
    const data = {
      ...formData,
      allowCodecs: formData.allowCodecs === "Yes",
    };

    if (onAdd) {
      onAdd(data);
    }
    onClose();
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      size="sm"
      headerIcon={<PlusCircle />}
      headerTitle="Add Provider"
      confirmButtonText="Add Provider"
      confirmButtonOnClick={handleSubmit}
      cancelButtonText="Cancel"
    >
      <div className={styles.formContainer}>
        {fields.map(field => {
          if (["status", "allowCodecs", "type"].includes(field)) {
            return (
              <div key={field} style={{ marginBottom: 16 }}>
                <ToggleButtonGroup
                  label={labels[field]}
                  options={toggleOptionsByField[field as keyof typeof toggleOptionsByField]}
                  selected={formData[field]}
                  onChange={(e: any) => handleInputChange(field, e, formData, setFormData, "select")}
                />
              </div>
            );
          }

          return (
            <Input
              key={field}
              label={labels[field]}
              value={formData[field]}
              onChange={(e: any) => handleInputChange(field, e, formData, setFormData)}
              error={formData.errors[field]}
              clear={() => clearInput(field, setFormData)}
              infoTooltipText
            />
          );
        })}
      </div>

      <div style={{ width: 320 }}>
        <Button
          color="secondary"
          style={{ width: "100%" }}
        > <UploadSimple /> Upload Rates</Button>
      </div>
    </Dialog>
  );
};

export default AddProviderModal;

const toggleOptionsByField = {
  status: [
    { label: "Active", key: "Active" },
    { label: "Inactive", key: "Inactive" }
  ],
  allowCodecs: [
    { label: "Yes", key: "Yes" },
    { label: "No", key: "No" }
  ],
  type: [
    { label: "SMS", key: "SMS" },
    { label: "Call", key: "Call" }
  ]
}; 