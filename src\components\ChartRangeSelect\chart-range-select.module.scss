@use "../../styles/theme.scss" as *;
@import "../../styles/mixins.module.scss";

.menuItem {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  transition: all 0.1s ease;
  color: #1857c3;
  line-height: 18px;
  font-size: 14px;
  padding: 0px 16px;
  height: 40px;
  font-weight: 700;
  cursor: pointer;
  border: 2px solid transparent;
  background-color: #e8f0fc;
  margin-right: 8px;
  &:hover {
    border-color: #a7b1c0;
  }
  &.selected {
    border-color: #1857c3;
    cursor: auto;
  }
}

.container {
  display: flex;
  align-items: center;
}
