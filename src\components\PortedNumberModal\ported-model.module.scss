@use "../../styles/theme.scss" as *;

.main {
  margin: 0 auto;
  max-width: 150%;
  display: flex;
  height: 130%;
  flex-direction: column;
  justify-content: center;
  h3 {
    line-height: 36px;
    font-weight: 700;
    font-size: 24px;
    margin-bottom: 24px;
  }
}

.modalContent {
  display: flex;
  gap: 24px;
  justify-content: center;
  background-color: #f7f6f6;
  border-radius: 10px;
  padding: 24px;
  max-width: 120%;
  margin-bottom: 12px;
  font-size: 14px;
  line-height: 21px;

  p {
    margin-bottom: 12px;
    &:last-child {
      margin-bottom: 0px;
    }
  }
}
