@use "../../styles/theme.scss" as *;

.ticketSummary {
  padding: 24px;
  background: $off-white;
  box-shadow: 0px 0px 30px 0px rgba(8, 7, 87, 0.1);
  border-radius: 24px;
  margin-bottom: 24px;
  .top {
    display: flex;
    width: 100%;
    align-items: center;
    margin-bottom: 8px;
  }
  .ticketNo {
    margin-left: 12px;
    font-size: 12px;
    line-height: 18px;
  }
  .subject {
    margin-bottom: 12px;
    font-size: 20px;
    line-height: 30px;
    font-weight: 600;
  }
  .issueLabel {
    font-size: 12px;
    line-height: 18px;
    margin-bottom: 8px;
  }
  .issueContent {
    font-size: 14px;
    line-height: 21px;
    margin-bottom: 12px;
  }
}
.date {
  font-size: 12px;
  line-height: 18px;
  color: #797979;
  margin-left: auto;
}

.dataBar {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
