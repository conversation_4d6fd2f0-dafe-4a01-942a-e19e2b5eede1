import Title from "@/components/Title";
import styles from "../../styles/settings.module.scss"
import { useState } from "react";
import { Input } from "@/components/Input";
import Button from "@/components/Button";
import { labels } from "@/components/utils/InputHandlers";
import Toggle from "@/components/Toggle";
import EditSettingsUserModal from "@/components/EditSettingsUserModal";
import ChangePasswordModal from "@/components/ChangePasswordModal";

const userData = {
  firstName: "Marry",
  lastName: "melon",
  email: "<EMAIL>"
}

const Settings = () => {
  const fields = ["firstName", "lastName", "email"] as const;
  const [twoFA, setTwoFA] = useState(true);

  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isChangePasswordOpen, setIsChangePasswordModalOpen] = useState(false);

  return (
    <div className={styles.container}>
      <EditSettingsUserModal
        open={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        userData={userData}
      />
      <ChangePasswordModal
        open={isChangePasswordOpen}
        onClose={() => setIsChangePasswordModalOpen(false)}
      />

      <Title>Settings</Title>
      <div className={styles.main}>
        <h2>My Profile</h2>
        <div className={styles.userRole}>Admin</div>

        <div className={styles.formContainer}>
          {fields.map(field => (
            <Input
              key={"settings-" + field}
              label={labels[field]}
              value={userData[field]}
              noClear
              disabled
            />
          ))}
          <div className={styles.twoFAToggleContainer}>
            <div className={styles.label}>Two Factor Authentication</div>
            <div className={styles.toggleLabel}>{twoFA ? 'Enabled' : 'Disabled'}</div>
            <Toggle on={twoFA} onChange={() => setTwoFA(prev => !prev)} />
          </div>
        </div>

        <div className={styles.buttonContainer}>
          <Button onClick={() => setIsEditModalOpen(true)}>Edit Details</Button>
          <Button onClick={() => setIsChangePasswordModalOpen(true)}>Change Password</Button>
        </div>
      </div>
    </div>
  );
};

export default Settings;