@use "../styles/theme.scss" as *;

.main {
  padding: 100px 50px;
  font-family: Roboto;

  .tableContainer {
    overflow-x: auto;
  }

  table {
    border-spacing: 0px;
    border: 1px solid $lightgrey;
    text-align: start;
    color: #061632;
    width: fit-content;
    border-radius: 4px;

    tr {
      display: flex;
      width: fit-content;
    }

    th {
      padding: 10px 12px;
      text-align: start;
      background: $lightblue;
      font-size: 12px;
      font-weight: 700;
      line-height: 14.06px;
      white-space: nowrap;
      border: 1px solid $lightgrey;
      position: relative;
      text-overflow: ellipsis;
      overflow-x: hidden;
    }

    tbody {
      td {
        padding: 10px 12px;
        font-size: 12px;
        font-weight: 400;
        line-height: 15.6px;
        background: #fff;
        border: 1px solid $lightgrey;
        text-overflow: ellipsis;
        overflow-x: hidden;
      }
    }

    .resizer {
      position: absolute;
      top: 0;
      height: 100%;
      width: 5px;
      background: #2e70e5;
      cursor: ew-resize;
      user-select: none;
      touch-action: none;
    }

    .resizer.ltr {
      right: 0;
    }

    .resizer.rtl {
      left: 0;
    }

    .resizer.isResizing {
      opacity: 1;
    }

    @media (hover: hover) {
      .resizer {
        opacity: 0;
      }

      *:hover > .resizer {
        opacity: 1;
      }
    }
  }
}
