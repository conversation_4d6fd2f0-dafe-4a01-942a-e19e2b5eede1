import { faker } from "@faker-js/faker";

export const createReport = (size: number) => {
  return Array.from({ length: size }).map(() => createEntry());
};

const createEntry = () => {
  return {
    userMid: faker.string.numeric(8),
    email: faker.internet.email(),
    timestamp: faker.date.recent({ days: 100 }),
    source: faker.phone.number("+###########"),
    destination: faker.phone.number("+###########"),
    cost: faker.number.int(5),
    chargeMethod: "allowance",
  };
};
