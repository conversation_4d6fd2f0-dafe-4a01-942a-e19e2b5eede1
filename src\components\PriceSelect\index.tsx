import styles from "./price-select.module.scss";
import { ControlledMenu, useMenuState } from "@szhsin/react-menu";
import "@szhsin/react-menu/dist/index.css";
import "@szhsin/react-menu/dist/transitions/slide.css";
import { ChevronDown } from "../svgs";
import { useEffect, useRef, useState } from "react";
import Button from "../Button";
import RangeSwitch from "../RangeSwitch";
import { Input } from "../Input";
import { createPortal } from "react-dom";

const PriceSelect = ({ label, selected, setSelected }: any) => {
  const ref = useRef(null);

  const [menuProps, toggleMenu] = useMenuState({ transition: true });

  const [stagedChanges, setStagedChanges] = useState(selected);

  const [type, setType] = useState("specific");

  useEffect(() => {
    console.log(selected);
    setStagedChanges(selected);
  }, [selected]);

  const reset = () => {
    toggleMenu(false);
  };

  useEffect(() => {
    if (type === "specific") {
      setStagedChanges({
        ...stagedChanges,
        to: "",
      });
    }
  }, [type]);

  return (
    <div className={`${styles.box} multi-select select`}>
      <div
        ref={ref}
        className={`${styles.menuButton} ${
          menuProps.state === "open" || menuProps.state === "opening"
            ? styles.iconOpen
            : styles.iconClosed
        }`}
        onClick={() => {
          if (menuProps.state === "closing") {
            toggleMenu(false);
          } else {
            toggleMenu(true);
          }
        }}
      >
        {label}
        <ChevronDown />
      </div>
      {createPortal(
        <div className="multi-select select">
          <ControlledMenu
            {...menuProps}
            anchorRef={ref}
            onClose={() => {
              reset();
              setStagedChanges(selected);
            }}
            align="start"
            position="auto"
            viewScroll="close"
            onItemClick={(e) => (e.keepOpen = true)}
          >
            <div className={`${styles.container} modal-scroll`}>
              <RangeSwitch small role={type} setRole={setType} />
              <div
                className={`${styles.grid} ${type === "range" && styles.range}`}
              >
                <Input
                  label={type === "range" ? "Min Price" : "Price"}
                  placeholder={type === "range" ? "Min Price" : "Price"}
                  value={stagedChanges?.from}
                  onChange={(e: any) => {
                    setStagedChanges({
                      ...stagedChanges,
                      from:
                        e.target.value === ""
                          ? e.target.value
                          : parseFloat(e.target.value),
                    });
                  }}
                  white
                  number
                />
                {type === "range" && (
                  <Input
                    label="Max Price"
                    placeholder="Max Price"
                    value={stagedChanges?.to}
                    onChange={(e: any) => {
                      setStagedChanges({
                        ...stagedChanges,
                        to:
                          e.target.value === ""
                            ? e.target.value
                            : parseFloat(e.target.value),
                      });
                    }}
                    white
                    number
                  />
                )}
              </div>
            </div>
            <div className={styles.buttons}>
              <Button
                color="quaternary"
                style={{
                  marginRight: "auto",
                  opacity:
                    stagedChanges?.from !== "" || selected.to !== "" ? 1 : 0,
                  pointerEvents:
                    stagedChanges?.from !== "" || selected.to !== ""
                      ? "all"
                      : "none",
                }}
                onClick={() => {
                  setStagedChanges({ from: "", to: "" });
                  setSelected({ from: "", to: "" });
                  toggleMenu(false);
                }}
              >
                Clear All
              </Button>
              <Button
                onClick={() => {
                  setStagedChanges(selected);
                  reset();
                }}
                style={{ marginRight: 12, marginLeft: 20, minWidth: 0 }}
                color="secondary"
              >
                Cancel
              </Button>
              <Button
                style={{ minWidth: 0 }}
                onClick={() => {
                  setSelected(stagedChanges);
                  reset();
                }}
              >
                Apply
              </Button>
            </div>
          </ControlledMenu>
        </div>,
        document.getElementById("root")!,
      )}
    </div>
  );
};

export default PriceSelect;
