import Dialog from "@/components/Dialog";
import { FloppyDisk, PencilCircle } from "@/components/svgs";
import {
  clearInput,
  createStateObject,
  createStateObjectWithInitialState,
  handleInputChange,
  labels,
} from "@/components/utils/InputHandlers";
import { useState } from "react";
import styles from "./edit-data-product-modal.module.scss";
import { Input } from "@/components/Input";
import ToggleButtonGroup from "@/components/ToggleButtonGroup";
import SelectInput from "@/components/SelectInput";
import { getCountryOptions } from "../utils/getCountryOptions";

type EditDataProductModalProps = {
  productData: any;
  open: boolean;
  onClose: () => void;
};

const EditDataProductModal = ({
  open,
  onClose,
  productData,
}: EditDataProductModalProps) => {
  const fields = [
    "country",
    "validity",
    "dataAllowance",
    "usdPrice",
    "gbpPrice",
    "euroPrice",
    "status",
  ];
  const [formData, setFormData] = useState(
    createStateObjectWithInitialState(createStateObject(fields), {
      country: productData.country.code,
      validity: productData.validity,
      dataAllowance: productData.dataAllowance,
      usdPrice: productData.usdPrice,
      gbpPrice: productData.gbpPrice,
      euroPrice: productData.euroPrice,
      status: productData.status,
    })
  );

  return (
    <Dialog
      open={open}
      onClose={onClose}
      size="sm"
      headerIcon={<PencilCircle />}
      headerTitle="Edit Data Product"
      confirmButtonText="Save Changes"
      confirmButtonOnClick={onClose}
      confirmButtonIcon={<FloppyDisk />}
      cancelButtonText="Cancel"
    >
      <div className={styles.formContainer}>
        {fields.map((field) => {
          if (["country", "validity"].includes(field)) {
            return (
              <SelectInput
                key={"product-" + field}
                options={selectOptionsByField[field]}
                label={labels[field]}
                selected={formData[field]}
                onChange={(e: any) => {
                  handleInputChange(
                    field,
                    { target: { value: e } },
                    formData,
                    setFormData
                  );
                }}
                error={formData.errors[field]}
                infoTooltipText
              />
            );
          } else if (["status"].includes(field)) {
            return (
              <ToggleButtonGroup
                key={"product-" + field}
                selected={formData[field] ? "active" : undefined}
                options={[
                  {
                    label: "Active",
                    key: "active",
                  },
                  {
                    label: "Inactive",
                    key: "inactive",
                  },
                ]}
                label={labels[field]}
                onChange={(value) => {
                  handleInputChange(
                    field,
                    { target: { value } },
                    formData,
                    setFormData
                  );
                }}
              />
            );
          } else {
            if (["usdPrice", "gbpPrice", "euroPrice"].includes(field)) {
              // We only want to render the price fields group once when we encounter the first price field (usdPrice)
              // This avoids duplicate rendering while keeping all price fields together in the form
              if (field === "usdPrice") {
                return (
                  <div key={"price-fields"} className={styles.priceFields}>
                    {["usdPrice", "gbpPrice", "euroPrice"].map((priceField) => (
                      <Input
                        key={"product-" + priceField}
                        label={labels[priceField]}
                        value={formData[priceField]}
                        onChange={(e: any) => {
                          handleInputChange(
                            priceField,
                            e,
                            formData,
                            setFormData
                          );
                        }}
                        error={formData.errors[priceField]}
                        clear={() => {
                          clearInput(priceField, setFormData);
                        }}
                        infoTooltipText
                        number
                      />
                    ))}
                  </div>
                );
              }
              return null; // Skip other price fields since they're rendered together above
            }

            return (
              <Input
                key={"product-" + field}
                label={labels[field]}
                value={formData[field]}
                onChange={(e: any) => {
                  handleInputChange(field, e, formData, setFormData);
                }}
                error={formData.errors[field]}
                clear={() => {
                  clearInput(field, setFormData);
                }}
                infoTooltipText
                number={field === "dataAllowance"}
              />
            );
          }
        })}
      </div>
    </Dialog>
  );
};

export default EditDataProductModal;

const selectOptionsByField: Record<string, any> = {
  country: getCountryOptions(),
  validity: ["30days", "7days", "5days", "1day"],
};
