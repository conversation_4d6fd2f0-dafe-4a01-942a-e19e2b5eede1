import Dialog from "@/components/Dialog";
import { LockCircle } from "@/components/svgs";
import { createStateObject, handleInputChange, labels } from "@/components/utils/InputHandlers";
import { useState } from "react";
import { Input } from "@/components/Input";


type ChangePasswordModalProps = {
	open: boolean;
	onClose: () => void;
}

const ChangePasswordModal = ({
	open,
	onClose,
}: ChangePasswordModalProps) => {
	const fields = ["currentPassword", "newPassword", "confirmPassword"];
	const [formData, setFormData] = useState(createStateObject(fields))

	return (
		<Dialog
			open={open}
			onClose={onClose}
			size="sm"
			headerIcon={<LockCircle />}
			headerTitle="Change Password"
			confirmButtonText="Change Password"
			confirmButtonOnClick={onClose}
			cancelButtonText="Cancel"
		>
			{fields.map(field => {
				return (
					<Input
						key={"user-" + field}
						label={labels[field]}
						value={formData[field]}
						onChange={(e: any) => {
							handleInputChange(field, e, formData, setFormData)
						}}
						error={formData.errors[field]}
						infoTooltipText
						password
					/>
				)
			})}
		</Dialog>
	)
}

export default ChangePasswordModal