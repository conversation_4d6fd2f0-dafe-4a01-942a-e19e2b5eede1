import { useEffect, useMemo, useState } from "react";
import styles from "./customer-product.module.scss";
import { Funnel, HandCoi<PERSON>, Minus, PlusFilled, Tag } from "../svgs";
import { filterList } from "../utils/searchAndFilter";
import { exampleCustomerDetails } from "../utils/exampleCustomerDetails";
import IconButton from "../IconButton";
import SearchBar from "../SearchBar";
import ProductsMenu from "../ProductsMenu";
import AddTravellerCustomerProductModal from "../AddTravellerCustomerProductModal";
import AddCreditModal from "../AddCreditModal";
import DebitAccountModal from "../DebitAccountModal";
import { travellerProductStates } from "../utils/travellerProductStates";
import TravellerCustomerPlanTile from "../TravellerCustomerPlanTile";

const TravellerProductContainer = ({
  repopulate,
}: any) => {
  const datetimeToUTC = (fromDate: any) => {
    return new Date(
      Date.UTC(
        fromDate.getFullYear(),
        fromDate.getMonth(),
        fromDate.getDate(),
        fromDate.getHours(),
        fromDate.getMinutes(),
        fromDate.getSeconds(),
        fromDate.getMilliseconds()
      )
    );
  };

  const data = { ...exampleCustomerDetails };

  const [subSelection, setSubSelection] = useState("active");

  const allSubs = useMemo(() => {
    let allPlans = [
      ...data.comboSubscriptions.map((item: any) => ({
        ...item,
        planType: "combo",
      })),
      ...data.esimSubscriptions.map((item: any) => ({
        ...item,
        planType: "data",
      })),
      ...data.phoneNumberSubscriptions.map((item: any) => ({
        ...item,
        planType: "phone",
      })),
    ];

    return [
      ...data.deactivatedPhoneSubscriptions.map((item: any) => ({
        ...item,
        planType: "phone",
        deactivatedPhone: true,
      })),
      ...allPlans,
    ];
  }, [data, subSelection]);

  const [filteredSubs, setFilteredSubs] = useState([] as any);

  const [filters, setFilters] = useState({
    classification: [],
    countries: [],
    region: [],
  } as any);

  useEffect(() => {
    setFilteredSubs(filterList(allSubs, filters));
  }, [filters]);

  const [showAddCustomerProductModal, setShowAddCustomerProductModal] = useState(false);
  const [showAddCreditModal, setShowAddCreditModal] = useState(false);
  const [showDebitAccountModal, setShowDebitAccountModal] = useState(false);

  return (
    <>
      {showAddCustomerProductModal && (
        <AddTravellerCustomerProductModal
          open={showAddCustomerProductModal}
          onClose={() => setShowAddCustomerProductModal(false)}
          nextStepReview
          onContinue={() => setShowAddCustomerProductModal(false)}
        />
      )}
      {showAddCreditModal && (
        <AddCreditModal
          open={showAddCreditModal}
          onClose={() => setShowAddCreditModal(false)}
        />
      )}
      {showDebitAccountModal && (
        <DebitAccountModal
          open={showDebitAccountModal}
          onClose={() => setShowDebitAccountModal(false)}
        />
      )}
      <div className={styles.panelTopBar}>
        <h4>Products</h4>
        <div className={styles.actions}>
          <SearchBar placeholder="Search" />
          <IconButton style={{ margin: "0px 8px" }}>
            <Funnel />
          </IconButton>
          <ProductsMenu
            data={{
              icon: (<IconButton><PlusFilled /></IconButton>),
              items: [
                {
                  label: "Add Product",
                  onClick: () => setShowAddCustomerProductModal(true),
                  icon: <Tag width={20} height={20} />
                },
                {
                  label: "Add Credit",
                  onClick: () => setShowAddCreditModal(true),
                  icon: <HandCoins width={20} height={20} />
                },
                {
                  label: "Debit Account",
                  onClick: () => setShowDebitAccountModal(true),
                  icon: <Minus width={20} height={20} />
                },
              ]
            }}
          />
        </div>
      </div>
      {filteredSubs.length ? (
        <div className={`${styles.plansContainer} modal-scroll`}>
          {travellerProductStates.map((item: any, index: number) => (
            <TravellerCustomerPlanTile
              data={item}
              key={`plan-tile-${index}`}
            />
          ))}
        </div>
      ) : (
        <div className={styles.noneFound}>
          <img src="/none_found.svg" />
          <h3>No {subSelection.replaceAll("-", "")} plans</h3>
        </div>
      )}
    </>
  );
};

export default TravellerProductContainer;
