import Dialog from "@/components/Dialog";
import { WarningCircle } from "@/components/svgs";
import styles from './delete-voip-provider-modal.module.scss'
import StatusPill from "@/components/StatusPill";

type DeleteVoipProviderModalProps = {
  onClose: () => void;
  open: boolean;
  providerData: any;
}

const DeleteVoipProviderModal = ({
  onClose,
  open,
  providerData,
}: DeleteVoipProviderModalProps) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      size="sm"
      headerIcon={<WarningCircle />}
      headerTitle="Delete Provider?"
      headerSubtitle="Once removed, it cannot be recovered"
      confirmButtonText="Yes, Delete Provider"
      confirmButtonOnClick={onClose}
      confirmButtonVariant="destructive"
      cancelButtonText="Cancel"
    >
      <div className={styles.tableContainer}>
        <table className={styles.table}>
          <tbody>
            <tr>
              <td className={styles.label}>Provider</td>
              <td className={styles.value}>{providerData.name}</td>
            </tr>
            <tr>
              <td className={styles.label}>Prefix</td>
              <td className={styles.value}>{providerData.prefix}</td>
            </tr>
            <tr>
              <td className={styles.label}>Mode</td>
              <td className={styles.value}>{providerData.mode}</td>
            </tr>
            <tr>
              <td className={styles.label}>Inbound IP</td>
              <td className={styles.value}>{providerData.inBoundIp}</td>
            </tr>
            <tr>
              <td className={styles.label}>Outbound IP</td>
              <td className={styles.value}>{providerData.outBoundIp}</td>
            </tr>
            <tr>
              <td className={styles.label}>Allow Codecs</td>
              <td className={styles.value}>{providerData.allowCodecs}</td>
            </tr>
            <tr>
              <td className={styles.label}>Type</td>
              <td className={styles.value}>{providerData.providerType}</td>
            </tr>
            <tr>
              <td className={styles.label}>Status</td>
              <td className={styles.value}>
                <StatusPill
                  status={providerData.status === 1 ? "Active" : "Inactive"}
                  color={providerData.status === 1 ? "active" : "inactive"}
                />
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </Dialog>
  )
}

export default DeleteVoipProviderModal
