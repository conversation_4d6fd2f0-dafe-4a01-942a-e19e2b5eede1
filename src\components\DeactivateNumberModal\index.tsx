import { useState } from "react";
import { useDispatch } from "react-redux";
import Modal from "../Modal";
import { Delete } from "../svgs";
import styles from "./deactivate-number.module.scss";
import {
  numbersModalFields,
  eSimModalFields,
} from "../utils/numberModalFields";
import AvailabilityPill from "../AvailabilityPill";
import { ApiPatch } from "../../pages/api/api";
import { countryListAlpha2 } from "../utils/countryList";
import { formatNumber } from "../utils/formatNumber";

const CountryDisplay = ({ country }: any) => {
  return (
    <div className={styles.country}>
      <div
        className={styles.flag}
        style={{
          backgroundImage: `url(https://public-gist-bucket.s3.eu-north-1.amazonaws.com/flag-icons/png/${countryListAlpha2.find((item: any) => item.name.toLowerCase() === country.toLowerCase())?.code}.png)`,
        }}
      />
      {country}
    </div>
  );
};

const DeactivateNumberModal = ({
  show,
  setShow,
  multiNumber,
  remove,
  selection,
  repopulate,
}: any) => {
  const dispatch = useDispatch();

  const [loading, setLoading] = useState(false);
  const multiNumbers = multiNumber?.map((item: any) => {
    return item.number.replace(/\s+/g, "").replace("+", "");
  });

  const deactivateNumber = () => {
    setLoading(true);
    ApiPatch(`/agent/deactivate-number`, {
      didNumbers: multiNumbers,
    })
      .then((res) => {
        dispatch({
          type: "notify",
          payload: {
            error: false,
            message: res.data.message,
          },
        });
        setLoading(false);
        setShow(false);
        repopulate();
      })
      .catch((err) => {
        setLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: err.response.data.message,
          },
        });
      });
  };
  const deactivateEsim = () => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
      setShow(false);
      dispatch({
        type: "notify",
        payload: {
          error: false,
          message: `${multiNumber.length} eSims Deactivated.`,
        },
      });
    }, 2000);
  };

  return (
    <Modal
      saveButton={
        selection === "did-numbers" ? (
          <>Yes, Deactivate DID Number{multiNumber?.length > 1 && "s"}</>
        ) : (
          <>Yes, Deactivate eSim{multiNumber?.length > 1 && "s"}</>
        )
      }
      cancelButton="No"
      image="/bulk_edit_confirm_graphic.svg"
      show={show}
      close={() => {
        setShow(false);
      }}
      proceed={selection === "did-numbers" ? deactivateNumber : deactivateEsim}
      loading={loading}
    >
      <div className={styles.panel}>
        <h3 style={{ marginBottom: "32px" }}>
          Deactivate {multiNumber?.length}{" "}
          {selection === "did-numbers" ? "DID Number" : "eSIM"}
          {multiNumber?.length > 1 && "s"}?
        </h3>
        <div className={`${styles.tableContainer} table-scroll`}>
          {multiNumber?.length > 0 && (
            <table>
              <thead>
                <tr>
                  {selection === "did-numbers"
                    ? numbersModalFields.map((field: any) => (
                        <th key={field.key}>{field.label}</th>
                      ))
                    : eSimModalFields.map((field: any) => (
                        <th key={field.key}>{field.label}</th>
                      ))}

                  <th />
                </tr>
              </thead>
              <tbody>
                {multiNumber.map((item: any) => (
                  <tr
                    key={
                      selection === "did-numbers" ? item?.number : item?.msisdn
                    }
                  >
                    <td>
                      {selection === "did-numbers"
                        ? formatNumber(item?.number)
                        : item?.msisdn}
                    </td>
                    <td>
                      <CountryDisplay country={item.country} />
                    </td>
                    <td>
                      <AvailabilityPill status={item.availability} />
                    </td>
                    <td className={styles.remove} onClick={() => remove(item)}>
                      remove
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>
      </div>
    </Modal>
  );
};

export default DeactivateNumberModal;
