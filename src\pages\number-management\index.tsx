import { motion } from "framer-motion";
import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import Button from "../../components/Button";
import Menu from "../../components/Menu";
import { Funnel, LogOut, User } from "../../components/svgs";
import { logOut } from "../../components/utils/logOut";
import styles from "../../styles/number-management.module.scss";
import DIDNumbersTable from "@/components/DIDNumbersTable";
import ESimNumbersTable from "@/components/ESimNumbersTable";
import PortinsTable from "@/components/PortinsTable";

const NumberManagement = () => {
  const { userInfo } = useSelector((state: any) => state);

  const dispatch = useDispatch();
  const navigate = useNavigate();

  const [selection, setSelection] = useState("did-numbers");

  return (
    <>
      <div className={styles.top}>
        <h2>Number Management</h2>
        <Menu
          data={{
            label: userInfo ? userInfo.firstName : "",
            items: [
              {
                label: "Profile",
                icon: <User />,
                link: "/user-profile",
              },
              {
                label: "Logout",
                icon: <LogOut />,
                onClick: () => {
                  logOut(dispatch, navigate);
                },
              },
            ],
          }}
        />
      </div>
      <div className={styles.main}>
        <div className={styles.topRow}>
          <div className={styles.selectionWrapper}>
            <div
              className={`${styles.selection} ${
                selection === "did-numbers" && styles.activeSelection
              }`}
              onClick={() => {
                setSelection("did-numbers");
                //setFilters(emptyFilters);
              }}
            >
              <span>DID Numbers</span>
              {selection === "did-numbers" && (
                <motion.div
                  className={styles.background}
                  layoutId="underline"
                />
              )}
            </div>
            <div
              className={`${styles.selection} ${
                selection === "e-sims" && styles.activeSelection
              }`}
              onClick={() => {
                setSelection("e-sims");
                //setFilters(emptyFilters);
              }}
            >
              <span>eSIMs</span>
              {selection === "e-sims" && (
                <motion.div
                  className={styles.background}
                  layoutId="underline"
                />
              )}
            </div>
            <div
              className={`${styles.selection} ${
                selection === "ported-numbers" && styles.activeSelection
              }`}
              onClick={() => {
                setSelection("ported-numbers");
                //setFilters(emptyFilters);
              }}
            >
              <span>Ported Numbers</span>
              {selection === "ported-numbers" && (
                <motion.div
                  className={styles.background}
                  layoutId="underline"
                />
              )}
            </div>
          </div>
          <div className={styles.buttons}>
            <Button color="tertiary">
              <Funnel />
              Filters
            </Button>
          </div>
        </div>
        {selection === "did-numbers" ? (
          <DIDNumbersTable />
        ) : selection === "e-sims" ? (
          <ESimNumbersTable />
        ) : (
          <PortinsTable />
        )}
      </div>
    </>
  );
};

export default NumberManagement;
