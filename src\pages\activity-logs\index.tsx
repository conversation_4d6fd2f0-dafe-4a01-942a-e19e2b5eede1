import { useState, useEffect, useRef } from "react";
import Title from "@/components/Title";
import styles from "@/styles/activity-logs.module.scss";
import TableControl from "@/components/TableControl";
import UserSkeleton from "@/components/UserSkeleton";
import { formatDateWithTime } from "@/components/utils/formatDate";
import Button from "@/components/Button";
import { Export } from "@/components/svgs";
import { faker } from "@faker-js/faker";
import CollapsiblePanel from "@/components/CollapsiblePanel";
import { Input } from "@/components/Input";
import {
  clearInput,
  createStateObject,
  handleInputChange,
  labels,
} from "@/components/utils/InputHandlers";
import { MagnifyingGlass } from "@/components/svgs";
import Tag from "@/components/Tag";
import DatePicker from "@/components/DatePicker";
import CheckboxDropdownInput from "@/components/CheckboxDropdownInput";

const ActivityLogs = () => {
  const [loading, setLoading] = useState(true);
  const [logs, setLogs] = useState<any[]>([]);
  const [itemsPerPage, setItemsPerPage] = useState(11);
  const [numberOfPages, setNumberOfPages] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const searchPanelRef = useRef<any>(null);
  const searchFields = ["user", "email", "dateTime", "activity", "ipAddress"];
  const [data, setData] = useState(createStateObject(searchFields));
  const [showSearchResults, setShowSearchResults] = useState(false);

  const populate = (itemsPerPage: number) => {
    setLoading(true);
    setTimeout(() => {
      setLogs(getActivityLogs(itemsPerPage));
      setLoading(false);
    }, 500);
  };

  useEffect(() => {
    populate(itemsPerPage);
  }, [itemsPerPage, currentPage]);

  const formatDataItem = (item: any, key: string) => {
    if (key === "dateTime") {
      return formatDateWithTime(item[key]);
    }
    return item[key];
  };

  return (
    <>
      <Title>Activity Logs</Title>
      <div className={styles.main}>
        {/* Search Panel */}
        <div style={{ marginBottom: 16 }}>
          <CollapsiblePanel
            title="Search Activity Logs"
            summaryWhenClosed={
              <div
                style={{
                  display: "flex",
                  flex: 1,
                  justifyContent: "space-between",
                  marginLeft: 16,
                  marginRight: 8,
                }}
              >
                <Tag text="5 filters applied" />
                <Button
                  color="secondary"
                  onClick={() => setData(createStateObject(searchFields))}
                >
                  Clear Filters
                </Button>
              </div>
            }
            ref={searchPanelRef}
          >
            <div className={styles.fields}>
              {searchFields.map((prop) => {
                if (prop === "activity") {
                  return (
                    <CheckboxDropdownInput
                      key={`user-${prop}`}
                      options={activityTypes}
                      label={labels[prop]}
                      selected={data[prop]}
                      onChange={(values) => {
                        handleInputChange(
                          "status",
                          values,
                          data,
                          setData,
                          "select"
                        );
                      }}
                      error={data.errors[prop]}
                      infoTooltipText
                    />
                  );
                } else if (prop === "dateTime") {
                  return (
                    <DatePicker
                      key={"activity-logs-" + prop}
                      label={prop}
                      field={prop}
                      placeholder="Choose"
                      masterFrom={null}
                      masterUntil={null}
                      startTime={{ hh: "90", mm: "00" }}
                      endTime={{ hh: "21", mm: "00" }}
                    />
                  );
                }
                return (
                  <Input
                    key={"activity-logs-" + prop}
                    label={labels[prop] || prop}
                    value={data[prop]}
                    onChange={(e: any) => {
                      handleInputChange(prop, e, data, setData);
                    }}
                    error={data.errors[prop]}
                    clear={() => {
                      clearInput(prop, setData);
                    }}
                    infoTooltipText
                  />
                );
              })}
            </div>
            <div className={styles.actions}>
              <Button
                color="blue"
                onClick={() => {
                  searchPanelRef.current.close();
                  setShowSearchResults(true);
                }}
              >
                <MagnifyingGlass /> Search
              </Button>
            </div>
          </CollapsiblePanel>
        </div>

        {/* Table panel */}
        {showSearchResults && (
          <div className={styles.panel}>
            <div className={styles.panelTopBar}>
              <h4>Activity Logs</h4>
              <div className={styles.actions}>
                <Button color="secondary">
                  <Export /> Export to CSV
                </Button>
              </div>
            </div>

            <div className={`${styles.tableContainer} table-scroll`}>
              <table>
                <thead>
                  <tr>
                    {activityLogFields.map((field: any) => (
                      <th key={field.key}>{field.label}</th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {!loading
                    ? logs.map((item: any, i: number) => (
                        <tr key={`log-${i}`}>
                          {activityLogFields.map((field: any) => (
                            <td key={`log-${i}-${field.key}`}>
                              {formatDataItem(item, field.key)}
                            </td>
                          ))}
                        </tr>
                      ))
                    : Array.from({ length: itemsPerPage }, (_, i) => (
                        <UserSkeleton
                          key={`log-skeleton-${i}`}
                          noOfStandard={6}
                        />
                      ))}
                </tbody>
              </table>
            </div>
            <div style={{ marginTop: "16px" }}>
              <TableControl
                show
                itemsPerPage={itemsPerPage}
                setItemsPerPage={setItemsPerPage}
                currentPage={currentPage}
                setCurrentPage={setCurrentPage}
                numberOfPages={numberOfPages}
                label="logs"
                loading={loading}
              />
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default ActivityLogs;

export const activityLogFields = [
  {
    label: "Date & Time",
    key: "dateTime",
    labelStr: "Date & Time",
  },
  {
    label: "IP Address",
    key: "ipAddress",
    labelStr: "IP Address",
  },
  {
    label: "User",
    key: "user",
    labelStr: "User",
  },
  {
    label: "Email Address",
    key: "email",
    labelStr: "Email Address",
  },
  {
    label: "Activity",
    key: "activity",
    labelStr: "Activity",
  },
  {
    label: "Description",
    key: "description",
    labelStr: "Description",
  },
];

export const activityTypes: any = [
  "Activated Subscriber",
  "Cancelled Subscription",
  "Deactivated Virtual Number",
  "Added Credit",
  "Debited Account",
  "Suspended Subscription",
  "Created User",
  "Updated Payment Method",
  "Restored Subscription",
  "Changed Plan",
  "Deleted User",
].map((v) => ({
  label: v,
  value: v,
}));

const getActivityLogs = (count: number) => {
  const logs = [];
  for (let i = 0; i < count; i++) {
    const activity = activityTypes[i % activityTypes.length];
    console.log(activity);
    const user = faker.person.fullName();
    let description = "";

    switch (activity.label) {
      case "Activated Subscriber":
        description = "Activated subscriber [Subscriber Name]";
        break;
      case "Cancelled Subscription":
        description = "Cancelled [Subscription Name] for [Subscriber Name].";
        break;
      case "Deactivated Virtual Number":
        description =
          "Deactivated virtual number [Virtual Number] for [Subscriber Name].";
        break;
      case "Added Credit":
        description =
          "Added [Amount] credit to [Subscriber Name]'s account. Reason: [Reason].";
        break;
      case "Debited Account":
        description =
          "Debited [Amount] from [Subscriber Name]'s account. Reason: [Reason].";
        break;
      case "Suspended Subscription":
        description = "Suspended [Subscription Name] for [Subscriber Name].";
        break;
      case "Created User":
        description = "Created new [Role] user [User Name] ([Email]).";
        break;
      case "Updated Payment Method":
        description = "Updated payment method for [Subscriber Name].";
        break;
      case "Restored Subscription":
        description = "Restored [Subscription Name] for [Subscriber Name].";
        break;
      case "Changed Plan":
        description =
          "Changed plan for [Subscriber Name] from [Old Plan] to [New Plan].";
        break;
      case "Deleted User":
        description = "Deleted user [User Name] ([Email]).";
        break;
    }

    logs.push({
      dateTime: faker.date.recent(),
      ipAddress: faker.internet.ipv4(),
      user,
      email: faker.internet.email(),
      activity: activity.label,
      description,
    });
  }
  return logs;
};
